# 训练与评估工作流

## 概述

本文档详细描述了神农框架的完整训练与评估工作流，包括数据准备、模型训练、性能评估、可解释性分析和结果验证的全过程。工作流设计确保实验的可重现性、科学严谨性和结果的可靠性。

## 工作流概览

```mermaid
graph TD
    A[原始数据] --> B[数据预处理]
    B --> C[特征工程]
    C --> D[数据分割]
    D --> E[模型训练]
    E --> F[模型验证]
    F --> G[超参数优化]
    G --> H[最终评估]
    H --> I[可解释性分析]
    I --> J[结果验证]
    J --> K[性能报告]
```

## 详细工作流

### 1. 数据准备阶段

#### 1.1 数据加载与验证

```python
class DataLoader:
    """
    数据加载和初步验证
    """
    def __init__(self, config):
        self.config = config
        self.validator = DataValidator()
    
    def load_antimicrobial_data(self, data_path):
        """
        加载抗菌活性数据
        """
        # 读取CSV文件
        df = pd.read_csv(data_path)
        
        # 基础验证
        required_columns = ['smiles', 'activity', 'organism', 'assay_type']
        missing_columns = set(required_columns) - set(df.columns)
        if missing_columns:
            raise ValueError(f"缺少必需列: {missing_columns}")
        
        # 数据质量检查
        quality_report = self.validator.validate_data_quality(df)
        self._log_quality_report(quality_report)
        
        return df
    
    def validate_smiles(self, smiles_list):
        """
        验证SMILES字符串的有效性
        """
        valid_smiles = []
        invalid_indices = []
        
        for i, smiles in enumerate(smiles_list):
            try:
                mol = Chem.MolFromSmiles(smiles)
                if mol is not None:
                    # 标准化SMILES
                    canonical_smiles = Chem.MolToSmiles(mol)
                    valid_smiles.append(canonical_smiles)
                else:
                    invalid_indices.append(i)
            except Exception as e:
                print(f"SMILES验证错误 {i}: {smiles}, 错误: {e}")
                invalid_indices.append(i)
        
        print(f"有效SMILES: {len(valid_smiles)}/{len(smiles_list)}")
        print(f"无效SMILES索引: {invalid_indices}")
        
        return valid_smiles, invalid_indices

class DataValidator:
    """
    数据质量验证器
    """
    def validate_data_quality(self, df):
        """
        全面的数据质量验证
        """
        report = {
            'total_samples': len(df),
            'missing_values': df.isnull().sum().to_dict(),
            'duplicate_smiles': df.duplicated('smiles').sum(),
            'activity_distribution': df['activity'].value_counts().to_dict(),
            'unique_organisms': df['organism'].nunique(),
            'unique_assays': df['assay_type'].nunique()
        }
        
        # 检查活性标签分布
        activity_balance = df['activity'].mean()
        if activity_balance < 0.1 or activity_balance > 0.9:
            report['warnings'] = ['数据集类别不平衡']
        
        # 检查SMILES长度分布
        smiles_lengths = df['smiles'].str.len()
        report['smiles_length_stats'] = {
            'mean': smiles_lengths.mean(),
            'min': smiles_lengths.min(),
            'max': smiles_lengths.max(),
            'std': smiles_lengths.std()
        }
        
        return report
```

#### 1.2 特征工程流水线

```python
class FeatureEngineeringPipeline:
    """
    特征工程流水线
    """
    def __init__(self, feature_config):
        self.config = feature_config
        self.calculators = self._initialize_calculators()
        self.preprocessors = self._initialize_preprocessors()
    
    def _initialize_calculators(self):
        """
        初始化特征计算器
        """
        calculators = {}
        
        if 'mordred' in self.config['feature_types']:
            from mordred import Calculator, descriptors
            calculators['mordred'] = Calculator(descriptors, ignore_3D=True)
        
        if 'rdkit' in self.config['feature_types']:
            calculators['rdkit'] = RDKitDescriptors()
        
        if 'custom' in self.config['feature_types']:
            calculators['custom'] = AntimicrobialDescriptors()
        
        return calculators
    
    def compute_all_features(self, smiles_list, save_dir):
        """
        计算所有类型的特征
        """
        features_dict = {}
        
        for feature_type, calculator in self.calculators.items():
            print(f"计算 {feature_type} 特征...")
            
            features = self._compute_features_with_progress(
                calculator, smiles_list, feature_type
            )
            
            # 预处理特征
            processed_features = self._preprocess_features(features, feature_type)
            
            features_dict[feature_type] = processed_features
            
            # 保存中间结果
            self._save_features(processed_features, feature_type, save_dir)
        
        # 组合特征
        combined_features = self._combine_features(features_dict)
        
        # 保存最终特征
        self._save_combined_features(combined_features, save_dir)
        
        return combined_features
    
    def _compute_features_with_progress(self, calculator, smiles_list, feature_type):
        """
        带进度条的特征计算
        """
        features = []
        failed_count = 0
        
        for i, smiles in enumerate(tqdm(smiles_list, desc=f"计算{feature_type}特征")):
            try:
                if feature_type == 'mordred':
                    mol = Chem.MolFromSmiles(smiles)
                    feat = calculator(mol).fill_missing()
                    features.append(feat.values)
                elif feature_type == 'rdkit':
                    feat = calculator.compute(smiles)
                    features.append(feat)
                elif feature_type == 'custom':
                    feat = calculator.compute_antimicrobial_features(smiles)
                    features.append(feat)
                
            except Exception as e:
                print(f"特征计算失败 {smiles}: {e}")
                # 用零向量替代失败的计算
                if features:
                    features.append(np.zeros_like(features[0]))
                failed_count += 1
        
        print(f"{feature_type}特征计算完成，失败样本: {failed_count}")
        return np.array(features)
    
    def _preprocess_features(self, features, feature_type):
        """
        特征预处理
        """
        # 处理缺失值
        imputer = SimpleImputer(strategy='median')
        features_imputed = imputer.fit_transform(features)
        
        # 移除常数特征
        selector = VarianceThreshold(threshold=0.001)
        features_filtered = selector.fit_transform(features_imputed)
        
        # 标准化
        scaler = StandardScaler()
        features_normalized = scaler.fit_transform(features_filtered)
        
        # 保存预处理器
        preprocessor_info = {
            'imputer': imputer,
            'selector': selector,
            'scaler': scaler,
            'original_shape': features.shape,
            'final_shape': features_normalized.shape
        }
        
        return features_normalized, preprocessor_info
```

### 2. 模型训练阶段

#### 2.1 训练器类

```python
class ShennongTrainer:
    """
    神农框架训练器
    """
    def __init__(self, model, config):
        self.model = model
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 优化器
        self.optimizer = self._create_optimizer()
        
        # 学习率调度器
        self.scheduler = self._create_scheduler()
        
        # 损失函数
        self.criteria = self._create_criteria()
        
        # 评估指标
        self.metrics = self._create_metrics()
        
        # 早停机制
        self.early_stopping = EarlyStopping(
            patience=config.early_stopping_patience,
            min_delta=config.early_stopping_min_delta
        )
        
        # 实验跟踪
        self.experiment_tracker = ExperimentTracker(config.experiment_name)
    
    def _create_optimizer(self):
        """
        创建优化器
        """
        if self.config.optimizer == 'adamw':
            return optim.AdamW(
                self.model.parameters(),
                lr=self.config.learning_rate,
                weight_decay=self.config.weight_decay,
                betas=self.config.betas
            )
        elif self.config.optimizer == 'adam':
            return optim.Adam(
                self.model.parameters(),
                lr=self.config.learning_rate,
                weight_decay=self.config.weight_decay
            )
        else:
            raise ValueError(f"不支持的优化器: {self.config.optimizer}")
    
    def _create_scheduler(self):
        """
        创建学习率调度器
        """
        if self.config.scheduler == 'cosine':
            return optim.lr_scheduler.CosineAnnealingLR(
                self.optimizer, 
                T_max=self.config.epochs,
                eta_min=self.config.min_lr
            )
        elif self.config.scheduler == 'reduce_on_plateau':
            return optim.lr_scheduler.ReduceLROnPlateau(
                self.optimizer,
                mode='min',
                factor=0.5,
                patience=10,
                verbose=True
            )
        else:
            return None
    
    def _create_criteria(self):
        """
        创建损失函数
        """
        criteria = {
            'activity': nn.BCELoss(),
            'mechanism': nn.CrossEntropyLoss()
        }
        
        if self.config.use_focal_loss:
            criteria['activity'] = FocalLoss(alpha=0.25, gamma=2.0)
        
        return criteria
    
    def _create_metrics(self):
        """
        创建评估指标
        """
        return {
            'accuracy': accuracy_score,
            'precision': precision_score,
            'recall': recall_score,
            'f1': f1_score,
            'auc': roc_auc_score,
            'mcc': matthews_corrcoef
        }
    
    def train(self, train_loader, val_loader, test_loader=None):
        """
        完整的训练流程
        """
        print("开始训练神农框架...")
        
        # 移动模型到设备
        self.model.to(self.device)
        
        # 训练历史
        history = {
            'train_loss': [],
            'val_loss': [],
            'train_metrics': [],
            'val_metrics': []
        }
        
        best_val_score = float('-inf')
        best_model_state = None
        
        for epoch in range(self.config.epochs):
            print(f"\nEpoch {epoch+1}/{self.config.epochs}")
            
            # 训练阶段
            train_results = self._train_epoch(train_loader)
            
            # 验证阶段
            val_results = self._validate_epoch(val_loader)
            
            # 记录历史
            history['train_loss'].append(train_results['loss'])
            history['val_loss'].append(val_results['loss'])
            history['train_metrics'].append(train_results['metrics'])
            history['val_metrics'].append(val_results['metrics'])
            
            # 学习率调度
            if self.scheduler:
                if isinstance(self.scheduler, optim.lr_scheduler.ReduceLROnPlateau):
                    self.scheduler.step(val_results['loss'])
                else:
                    self.scheduler.step()
            
            # 保存最佳模型
            current_val_score = val_results['metrics']['auc']
            if current_val_score > best_val_score:
                best_val_score = current_val_score
                best_model_state = deepcopy(self.model.state_dict())
            
            # 早停检查
            if self.early_stopping(val_results['loss']):
                print(f"早停触发，在epoch {epoch+1}")
                break
            
            # 记录到实验跟踪器
            self.experiment_tracker.log_epoch(epoch, train_results, val_results)
        
        # 加载最佳模型
        self.model.load_state_dict(best_model_state)
        
        # 最终评估
        final_results = {}
        if test_loader:
            final_results['test'] = self._test_model(test_loader)
        
        # 保存模型
        self._save_model(best_model_state)
        
        # 完整结果
        results = {
            'history': history,
            'best_val_score': best_val_score,
            'final_results': final_results,
            'model_path': self.config.model_save_path
        }
        
        return results
    
    def _train_epoch(self, train_loader):
        """
        单个训练epoch
        """
        self.model.train()
        total_loss = 0
        all_predictions = []
        all_targets = []
        
        progress_bar = tqdm(train_loader, desc="训练")
        
        for batch_idx, batch in enumerate(progress_bar):
            # 移动数据到设备
            batch = self._batch_to_device(batch)
            
            # 前向传播
            self.optimizer.zero_grad()
            results = self.model(
                batch['mol_graph'], 
                batch['features'],
                batch['chemistry_properties']
            )
            
            # 计算损失
            losses = self._compute_losses(results, batch)
            total_loss_batch = sum(losses.values())
            
            # 反向传播
            total_loss_batch.backward()
            
            # 梯度裁剪
            if self.config.gradient_clip_norm:
                torch.nn.utils.clip_grad_norm_(
                    self.model.parameters(), 
                    self.config.gradient_clip_norm
                )
            
            self.optimizer.step()
            
            # 累计统计
            total_loss += total_loss_batch.item()
            predictions = results['predictions']['activity_prediction'].detach().cpu()
            targets = batch['activity'].detach().cpu()
            
            all_predictions.extend(predictions.numpy())
            all_targets.extend(targets.numpy())
            
            # 更新进度条
            progress_bar.set_postfix({
                'loss': total_loss_batch.item(),
                'avg_loss': total_loss / (batch_idx + 1)
            })
        
        # 计算epoch指标
        epoch_metrics = self._compute_metrics(all_predictions, all_targets)
        
        return {
            'loss': total_loss / len(train_loader),
            'metrics': epoch_metrics
        }
    
    def _validate_epoch(self, val_loader):
        """
        验证epoch
        """
        self.model.eval()
        total_loss = 0
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for batch in tqdm(val_loader, desc="验证"):
                batch = self._batch_to_device(batch)
                
                results = self.model(
                    batch['mol_graph'], 
                    batch['features'],
                    batch['chemistry_properties']
                )
                
                losses = self._compute_losses(results, batch)
                total_loss += sum(losses.values()).item()
                
                predictions = results['predictions']['activity_prediction'].cpu()
                targets = batch['activity'].cpu()
                
                all_predictions.extend(predictions.numpy())
                all_targets.extend(targets.numpy())
        
        epoch_metrics = self._compute_metrics(all_predictions, all_targets)
        
        return {
            'loss': total_loss / len(val_loader),
            'metrics': epoch_metrics
        }
    
    def _compute_losses(self, results, batch):
        """
        计算各种损失
        """
        losses = {}
        
        # 活性预测损失
        activity_pred = results['predictions']['activity_prediction']
        activity_target = batch['activity']
        losses['activity'] = self.criteria['activity'](activity_pred, activity_target)
        
        # 机制预测损失 (如果有标签)
        if 'mechanism' in batch and 'mechanism_prediction' in results['predictions']:
            mechanism_pred = results['predictions']['mechanism_prediction']
            mechanism_target = batch['mechanism']
            losses['mechanism'] = self.criteria['mechanism'](mechanism_pred, mechanism_target)
        
        return losses
    
    def _compute_metrics(self, predictions, targets):
        """
        计算评估指标
        """
        predictions = np.array(predictions)
        targets = np.array(targets).astype(int)
        
        # 二分类预测
        binary_predictions = (predictions > 0.5).astype(int)
        
        metrics = {}
        try:
            metrics['accuracy'] = self.metrics['accuracy'](targets, binary_predictions)
            metrics['precision'] = self.metrics['precision'](targets, binary_predictions)
            metrics['recall'] = self.metrics['recall'](targets, binary_predictions)
            metrics['f1'] = self.metrics['f1'](targets, binary_predictions)
            metrics['auc'] = self.metrics['auc'](targets, predictions)
            metrics['mcc'] = self.metrics['mcc'](targets, binary_predictions)
        except Exception as e:
            print(f"指标计算错误: {e}")
            metrics = {key: 0.0 for key in self.metrics.keys()}
        
        return metrics
```

### 3. 模型评估阶段

#### 3.1 综合评估器

```python
class ModelEvaluator:
    """
    模型综合评估器
    """
    def __init__(self, model, config):
        self.model = model
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    def comprehensive_evaluation(self, test_loader, analysis_dir):
        """
        综合评估
        """
        print("开始综合评估...")
        
        # 基础性能评估
        performance_results = self.evaluate_performance(test_loader)
        
        # 可解释性分析
        interpretability_results = self.analyze_interpretability(test_loader)
        
        # 鲁棒性测试
        robustness_results = self.test_robustness(test_loader)
        
        # 化学有效性验证
        chemical_validity = self.validate_chemical_insights(test_loader)
        
        # 生成报告
        report = self.generate_evaluation_report(
            performance_results,
            interpretability_results,
            robustness_results,
            chemical_validity
        )
        
        # 保存结果
        self.save_evaluation_results(report, analysis_dir)
        
        return report
    
    def evaluate_performance(self, test_loader):
        """
        性能评估
        """
        self.model.eval()
        
        all_predictions = []
        all_targets = []
        all_probabilities = []
        prediction_times = []
        
        with torch.no_grad():
            for batch in tqdm(test_loader, desc="性能评估"):
                batch = self._batch_to_device(batch)
                
                # 测量推理时间
                start_time = time.time()
                results = self.model(
                    batch['mol_graph'], 
                    batch['features'],
                    batch['chemistry_properties']
                )
                inference_time = time.time() - start_time
                prediction_times.append(inference_time)
                
                # 收集预测结果
                predictions = results['predictions']['activity_prediction']
                targets = batch['activity']
                
                all_predictions.extend((predictions > 0.5).cpu().numpy())
                all_targets.extend(targets.cpu().numpy())
                all_probabilities.extend(predictions.cpu().numpy())
        
        # 计算详细指标
        performance_metrics = self._compute_detailed_metrics(
            all_targets, all_predictions, all_probabilities
        )
        
        # 添加效率指标
        performance_metrics['inference_time'] = {
            'mean': np.mean(prediction_times),
            'std': np.std(prediction_times),
            'total': sum(prediction_times)
        }
        
        return performance_metrics
    
    def _compute_detailed_metrics(self, targets, predictions, probabilities):
        """
        计算详细的性能指标
        """
        from sklearn.metrics import (
            classification_report, confusion_matrix, 
            precision_recall_curve, roc_curve
        )
        
        # 基础分类指标
        basic_metrics = {
            'accuracy': accuracy_score(targets, predictions),
            'precision': precision_score(targets, predictions),
            'recall': recall_score(targets, predictions),
            'f1': f1_score(targets, predictions),
            'auc': roc_auc_score(targets, probabilities),
            'mcc': matthews_corrcoef(targets, predictions)
        }
        
        # 分类报告
        class_report = classification_report(targets, predictions, output_dict=True)
        
        # 混淆矩阵
        conf_matrix = confusion_matrix(targets, predictions)
        
        # ROC曲线数据
        fpr, tpr, roc_thresholds = roc_curve(targets, probabilities)
        
        # PR曲线数据
        precision_curve, recall_curve, pr_thresholds = precision_recall_curve(
            targets, probabilities
        )
        
        return {
            'basic_metrics': basic_metrics,
            'classification_report': class_report,
            'confusion_matrix': conf_matrix.tolist(),
            'roc_curve': {
                'fpr': fpr.tolist(),
                'tpr': tpr.tolist(),
                'thresholds': roc_thresholds.tolist()
            },
            'pr_curve': {
                'precision': precision_curve.tolist(),
                'recall': recall_curve.tolist(),
                'thresholds': pr_thresholds.tolist()
            }
        }
    
    def analyze_interpretability(self, test_loader):
        """
        可解释性分析
        """
        self.model.eval()
        
        interpretability_results = {
            'feature_importance': [],
            'atom_importance': [],
            'mechanism_analysis': [],
            'case_studies': []
        }
        
        with torch.no_grad():
            for batch_idx, batch in enumerate(tqdm(test_loader, desc="可解释性分析")):
                if batch_idx >= self.config.interpretability_sample_size:
                    break
                
                batch = self._batch_to_device(batch)
                
                results = self.model(
                    batch['mol_graph'], 
                    batch['features'],
                    batch['chemistry_properties']
                )
                
                explanations = results['explanations']
                
                # 特征重要性
                feature_importance = explanations['feature_importance'].cpu().numpy()
                interpretability_results['feature_importance'].append(feature_importance)
                
                # 原子重要性
                atom_importance = explanations['atom_importance'].cpu().numpy()
                interpretability_results['atom_importance'].append(atom_importance)
                
                # 机制分析
                if 'mechanism_prediction' in results['predictions']:
                    mechanism_probs = results['predictions']['mechanism_prediction'].cpu().numpy()
                    interpretability_results['mechanism_analysis'].append(mechanism_probs)
        
        # 聚合分析
        aggregated_analysis = self._aggregate_interpretability_results(
            interpretability_results
        )
        
        return aggregated_analysis
    
    def test_robustness(self, test_loader):
        """
        鲁棒性测试
        """
        robustness_results = {}
        
        # 1. 噪声鲁棒性测试
        robustness_results['noise_robustness'] = self._test_noise_robustness(test_loader)
        
        # 2. 特征删除测试
        robustness_results['feature_dropout'] = self._test_feature_dropout(test_loader)
        
        # 3. 分子修改测试
        robustness_results['molecular_perturbation'] = self._test_molecular_perturbation(test_loader)
        
        return robustness_results
    
    def _test_noise_robustness(self, test_loader):
        """
        噪声鲁棒性测试
        """
        noise_levels = [0.01, 0.05, 0.1, 0.2]
        noise_results = {}
        
        for noise_level in noise_levels:
            predictions_with_noise = []
            original_predictions = []
            
            with torch.no_grad():
                for batch in test_loader:
                    batch = self._batch_to_device(batch)
                    
                    # 原始预测
                    original_results = self.model(
                        batch['mol_graph'], 
                        batch['features'],
                        batch['chemistry_properties']
                    )
                    original_pred = original_results['predictions']['activity_prediction']
                    
                    # 添加噪声
                    noisy_features = batch['features'] + torch.randn_like(batch['features']) * noise_level
                    
                    # 噪声预测
                    noisy_results = self.model(
                        batch['mol_graph'], 
                        noisy_features,
                        batch['chemistry_properties']
                    )
                    noisy_pred = noisy_results['predictions']['activity_prediction']
                    
                    original_predictions.extend(original_pred.cpu().numpy())
                    predictions_with_noise.extend(noisy_pred.cpu().numpy())
            
            # 计算预测一致性
            consistency = np.corrcoef(original_predictions, predictions_with_noise)[0, 1]
            
            noise_results[f'noise_{noise_level}'] = {
                'correlation': consistency,
                'mean_absolute_difference': np.mean(np.abs(
                    np.array(original_predictions) - np.array(predictions_with_noise)
                ))
            }
        
        return noise_results
```

### 4. 可解释性分析

#### 4.1 化学洞察生成器

```python
class ChemicalInsightGenerator:
    """
    化学洞察生成器
    """
    def __init__(self, model, feature_names):
        self.model = model
        self.feature_names = feature_names
        self.mechanism_names = [
            'Cell Wall Disruption', 
            'Protein Synthesis Inhibition',
            'DNA/RNA Interference', 
            'Metabolic Disruption', 
            'Membrane Permeabilization'
        ]
    
    def generate_insights(self, mol_graph, features, prediction_results):
        """
        生成化学洞察
        """
        insights = {}
        
        # 1. 重要原子分析
        insights['important_atoms'] = self._analyze_important_atoms(
            mol_graph, prediction_results['explanations']['atom_importance']
        )
        
        # 2. 关键特征分析
        insights['key_features'] = self._analyze_key_features(
            features, prediction_results['explanations']['feature_importance']
        )
        
        # 3. 机制预测分析
        insights['mechanism_analysis'] = self._analyze_mechanism_prediction(
            prediction_results['predictions']['mechanism_prediction']
        )
        
        # 4. 结构-活性关系
        insights['sar_analysis'] = self._analyze_structure_activity_relationship(
            mol_graph, prediction_results
        )
        
        return insights
    
    def _analyze_important_atoms(self, mol_graph, atom_importance):
        """
        分析重要原子
        """
        # 获取原子重要性排序
        importance_scores = atom_importance.squeeze().cpu().numpy()
        atom_indices = np.argsort(importance_scores)[::-1][:5]  # 前5个重要原子
        
        important_atoms = []
        mol = mol_graph.mol
        
        for idx in atom_indices:
            atom = mol.GetAtomWithIdx(int(idx))
            atom_info = {
                'index': int(idx),
                'symbol': atom.GetSymbol(),
                'formal_charge': atom.GetFormalCharge(),
                'hybridization': str(atom.GetHybridization()),
                'importance_score': float(importance_scores[idx]),
                'neighbors': [neighbor.GetSymbol() for neighbor in atom.GetNeighbors()]
            }
            important_atoms.append(atom_info)
        
        return important_atoms
    
    def _analyze_key_features(self, features, feature_importance):
        """
        分析关键特征
        """
        importance_scores = feature_importance.squeeze().cpu().numpy()
        feature_indices = np.argsort(np.abs(importance_scores))[::-1][:10]
        
        key_features = []
        for idx in feature_indices:
            feature_info = {
                'index': int(idx),
                'name': self.feature_names[idx] if idx < len(self.feature_names) else f'Feature_{idx}',
                'importance_score': float(importance_scores[idx]),
                'value': float(features[idx]),
                'category': self._categorize_feature(self.feature_names[idx] if idx < len(self.feature_names) else '')
            }
            key_features.append(feature_info)
        
        return key_features
    
    def _categorize_feature(self, feature_name):
        """
        特征分类
        """
        if any(keyword in feature_name.lower() for keyword in ['mol', 'weight', 'mass']):
            return 'Constitutional'
        elif any(keyword in feature_name.lower() for keyword in ['topo', 'path', 'connectivity']):
            return 'Topological'
        elif any(keyword in feature_name.lower() for keyword in ['logp', 'polar', 'hydro']):
            return 'Physicochemical'
        elif any(keyword in feature_name.lower() for keyword in ['charge', 'electro']):
            return 'Electronic'
        else:
            return 'Other'
    
    def _analyze_mechanism_prediction(self, mechanism_probs):
        """
        分析抗菌机制预测
        """
        probs = mechanism_probs.squeeze().cpu().numpy()
        mechanism_ranking = np.argsort(probs)[::-1]
        
        mechanism_analysis = []
        for i, mech_idx in enumerate(mechanism_ranking):
            mechanism_info = {
                'rank': i + 1,
                'mechanism': self.mechanism_names[mech_idx],
                'probability': float(probs[mech_idx]),
                'confidence': 'High' if probs[mech_idx] > 0.7 else 'Medium' if probs[mech_idx] > 0.3 else 'Low'
            }
            mechanism_analysis.append(mechanism_info)
        
        return mechanism_analysis
    
    def _analyze_structure_activity_relationship(self, mol_graph, prediction_results):
        """
        结构-活性关系分析
        """
        mol = mol_graph.mol
        
        # 提取分子特性
        molecular_properties = {
            'molecular_weight': Descriptors.MolWt(mol),
            'logp': Descriptors.MolLogP(mol),
            'hbd': Descriptors.NumHDonors(mol),
            'hba': Descriptors.NumHAcceptors(mol),
            'tpsa': Descriptors.TPSA(mol),
            'rotatable_bonds': Descriptors.NumRotatableBonds(mol),
            'aromatic_rings': Descriptors.NumAromaticRings(mol)
        }
        
        # 预测结果
        activity_prob = float(prediction_results['predictions']['activity_prediction'].cpu())
        
        # 结构-活性规则检查
        sar_rules = self._check_sar_rules(molecular_properties, activity_prob)
        
        return {
            'molecular_properties': molecular_properties,
            'activity_prediction': activity_prob,
            'sar_rules': sar_rules
        }
    
    def _check_sar_rules(self, properties, activity_prob):
        """
        检查已知的结构-活性关系规则
        """
        rules_checked = []
        
        # 规则1: 分子量范围
        if 150 <= properties['molecular_weight'] <= 500:
            rules_checked.append({
                'rule': 'Optimal molecular weight range (150-500 Da)',
                'status': 'Satisfied',
                'value': properties['molecular_weight']
            })
        
        # 规则2: LogP范围
        if 0 <= properties['logp'] <= 5:
            rules_checked.append({
                'rule': 'Favorable lipophilicity (LogP 0-5)',
                'status': 'Satisfied',
                'value': properties['logp']
            })
        
        # 规则3: 氢键供体/受体
        if properties['hbd'] <= 5 and properties['hba'] <= 10:
            rules_checked.append({
                'rule': 'Lipinski Rule of Five (HBD≤5, HBA≤10)',
                'status': 'Satisfied',
                'value': f"HBD: {properties['hbd']}, HBA: {properties['hba']}"
            })
        
        # 规则4: 极性表面积
        if properties['tpsa'] <= 140:
            rules_checked.append({
                'rule': 'Favorable polar surface area (≤140 Ų)',
                'status': 'Satisfied',
                'value': properties['tpsa']
            })
        
        # 规则5: 芳香环系统
        if 1 <= properties['aromatic_rings'] <= 3:
            rules_checked.append({
                'rule': 'Optimal aromatic ring count (1-3)',
                'status': 'Satisfied',
                'value': properties['aromatic_rings']
            })
        
        return rules_checked
```

### 5. 结果验证与报告

#### 5.1 结果验证器

```python
class ResultValidator:
    """
    结果验证器
    """
    def __init__(self, config):
        self.config = config
    
    def validate_results(self, model_results, baseline_results=None):
        """
        验证实验结果
        """
        validation_report = {}
        
        # 1. 数值稳定性检查
        validation_report['numerical_stability'] = self._check_numerical_stability(
            model_results
        )
        
        # 2. 预测分布检查
        validation_report['prediction_distribution'] = self._check_prediction_distribution(
            model_results
        )
        
        # 3. 性能一致性检查
        validation_report['performance_consistency'] = self._check_performance_consistency(
            model_results
        )
        
        # 4. 与基线比较 (如果提供)
        if baseline_results:
            validation_report['baseline_comparison'] = self._compare_with_baseline(
                model_results, baseline_results
            )
        
        # 5. 统计显著性检验
        validation_report['statistical_significance'] = self._test_statistical_significance(
            model_results
        )
        
        return validation_report
    
    def _check_numerical_stability(self, results):
        """
        检查数值稳定性
        """
        predictions = results['predictions']
        
        stability_checks = {
            'has_nan': np.any(np.isnan(predictions)),
            'has_inf': np.any(np.isinf(predictions)),
            'prediction_range': [float(np.min(predictions)), float(np.max(predictions))],
            'prediction_std': float(np.std(predictions)),
            'extreme_values': len(predictions[(predictions < 0.01) | (predictions > 0.99)])
        }
        
        return stability_checks
    
    def _check_prediction_distribution(self, results):
        """
        检查预测分布
        """
        predictions = results['predictions']
        
        distribution_stats = {
            'mean': float(np.mean(predictions)),
            'median': float(np.median(predictions)),
            'std': float(np.std(predictions)),
            'skewness': float(scipy.stats.skew(predictions)),
            'kurtosis': float(scipy.stats.kurtosis(predictions)),
            'percentiles': {
                '5th': float(np.percentile(predictions, 5)),
                '25th': float(np.percentile(predictions, 25)),
                '75th': float(np.percentile(predictions, 75)),
                '95th': float(np.percentile(predictions, 95))
            }
        }
        
        return distribution_stats

class ExperimentReporter:
    """
    实验报告生成器
    """
    def __init__(self, config):
        self.config = config
    
    def generate_comprehensive_report(self, results, output_dir):
        """
        生成综合实验报告
        """
        print("生成实验报告...")
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 1. 性能摘要报告
        self._generate_performance_summary(results, output_dir)
        
        # 2. 详细分析报告
        self._generate_detailed_analysis(results, output_dir)
        
        # 3. 可视化图表
        self._generate_visualizations(results, output_dir)
        
        # 4. 可解释性报告
        self._generate_interpretability_report(results, output_dir)
        
        # 5. 技术文档
        self._generate_technical_documentation(results, output_dir)
        
        print(f"报告已保存到: {output_dir}")
    
    def _generate_performance_summary(self, results, output_dir):
        """
        生成性能摘要
        """
        summary = {
            'experiment_info': {
                'timestamp': datetime.now().isoformat(),
                'config': self.config,
                'dataset_size': len(results.get('test_predictions', [])),
            },
            'key_metrics': results.get('performance_metrics', {}),
            'model_complexity': {
                'parameters': results.get('model_parameters', 0),
                'training_time': results.get('training_time', 0),
                'inference_time': results.get('inference_time', {})
            }
        }
        
        # 保存为JSON
        with open(f"{output_dir}/performance_summary.json", 'w') as f:
            json.dump(summary, f, indent=2)
        
        # 保存为Markdown
        self._save_summary_markdown(summary, f"{output_dir}/performance_summary.md")
    
    def _generate_visualizations(self, results, output_dir):
        """
        生成可视化图表
        """
        import matplotlib.pyplot as plt
        import seaborn as sns
        
        # 设置图表样式
        plt.style.use('seaborn-v0_8')
        
        # 1. ROC曲线
        if 'roc_curve' in results:
            self._plot_roc_curve(results['roc_curve'], output_dir)
        
        # 2. PR曲线
        if 'pr_curve' in results:
            self._plot_pr_curve(results['pr_curve'], output_dir)
        
        # 3. 混淆矩阵
        if 'confusion_matrix' in results:
            self._plot_confusion_matrix(results['confusion_matrix'], output_dir)
        
        # 4. 特征重要性
        if 'feature_importance' in results:
            self._plot_feature_importance(results['feature_importance'], output_dir)
        
        # 5. 训练历史
        if 'training_history' in results:
            self._plot_training_history(results['training_history'], output_dir)
    
    def _plot_roc_curve(self, roc_data, output_dir):
        """
        绘制ROC曲线
        """
        plt.figure(figsize=(8, 6))
        plt.plot(roc_data['fpr'], roc_data['tpr'], linewidth=2, 
                label=f"ROC Curve (AUC = {roc_data.get('auc', 0):.3f})")
        plt.plot([0, 1], [0, 1], 'k--', alpha=0.5)
        plt.xlim([0.0, 1.0])
        plt.ylim([0.0, 1.05])
        plt.xlabel('False Positive Rate')
        plt.ylabel('True Positive Rate')
        plt.title('ROC Curve - Antimicrobial Activity Prediction')
        plt.legend(loc="lower right")
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(f"{output_dir}/roc_curve.png", dpi=300, bbox_inches='tight')
        plt.close()
```

## 相关文件

- [[30_神农框架/30.01_神农框架核心架构与创新点]]
- [[30_神农框架/30.02_架构重构计划 (Shennong v2.0)]]
- [[10_研究项目/神农框架 vs Chemprop vs AutoGluon 对比研究]]

---

*创建时间: 2024-01-XX*  
*最后更新: 2024-01-XX*  
*状态: 工作流设计完成* 