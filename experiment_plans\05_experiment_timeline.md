# 神农框架实验时间线

**作者**: ZK  
**邮箱**: <EMAIL>  
**日期**: 2025-06-30  

## 📅 总体时间规划

### 简化方案 (推荐): 4周
```
Week 1: 数据准备与环境搭建
Week 2: 基线模型实现与测试
Week 3: 神农框架实现与对比实验
Week 4: 应用验证与结果分析
```

### 完整方案: 16周
```
Week 1-2:   文献调研与理论分析
Week 3-4:   数据准备与特征工程
Week 5-8:   基线方法实现与测试
Week 9-12:  神农框架实现与核心实验
Week 13-14: 深度分析与消融实验
Week 15-16: 论文撰写与投稿准备
```

## 🚀 简化方案详细时间线 (4周)

### Week 1: 数据准备与环境搭建

#### Day 1-2: 数据收集
**目标**: 获取高质量的抗菌活性数据
```bash
□ 任务1: 下载ChEMBL抗菌活性数据
  - 访问ChEMBL数据库
  - 筛选抗菌相关assay
  - 下载SMILES和MIC数据
  - 预期数据量: 5000-10000个化合物

□ 任务2: 数据质量检查
  - SMILES有效性验证
  - MIC数值范围检查
  - 重复数据去除
  - 缺失值处理

□ 任务3: 数据标准化
  - 统一MIC单位 (μg/mL)
  - 创建二分类标签 (MIC < 16 μg/mL = 活性)
  - 数据格式标准化
```

#### Day 3-4: 特征生成
**目标**: 生成标准化的分子特征
```bash
□ 任务1: Mordred特征生成
  - 使用 scripts/generate_features.py
  - 生成完整的Mordred描述符集
  - 特征标准化和质量检查
  - 保存为 .npz 格式

□ 任务2: 图特征准备
  - 验证分子图生成流程
  - 检查图特征维度
  - 确保与Mordred特征对应

□ 任务3: 数据划分
  - 创建训练/验证/测试集 (70/15/15)
  - 使用scaffold splitting确保化学多样性
  - 保存划分索引
```

#### Day 5-7: 环境配置
**目标**: 搭建完整的实验环境
```bash
□ 任务1: ChemProp安装
  - pip install chemprop
  - 测试训练和预测功能
  - 验证数据格式兼容性

□ 任务2: AutoGluon安装
  - pip install autogluon
  - 测试TabularPredictor
  - 验证Mordred特征输入

□ 任务3: 依赖检查
  - 验证所有Python包版本
  - 测试GPU/CPU环境
  - 准备实验脚本
```

### Week 2: 基线模型实现与测试

#### Day 1-3: ChemProp基线
**目标**: 建立ChemProp性能基线
```bash
□ 任务1: 数据格式转换
  - 转换为ChemProp CSV格式
  - 创建训练/验证/测试文件
  - 验证数据完整性

□ 任务2: 模型训练
  - 使用标准ChemProp参数
  - 分类任务配置
  - 训练3个独立模型 (不同随机种子)

□ 任务3: 性能评估
  - 计算AUC, F1, Precision, Recall
  - 记录训练时间和资源使用
  - 保存预测结果
```

#### Day 4-5: AutoGluon基线
**目标**: 建立AutoGluon性能基线
```bash
□ 任务1: 特征准备
  - 加载Mordred特征
  - 数据格式转换
  - 特征选择和预处理

□ 任务2: 模型训练
  - 配置TabularPredictor
  - 二分类任务设置
  - 训练3个独立模型

□ 任务3: 性能评估
  - 使用相同评估指标
  - 对比ChemProp结果
  - 分析特征重要性
```

#### Day 6-7: 基线结果分析
**目标**: 分析基线性能，为神农框架设定目标
```bash
□ 任务1: 结果汇总
  - 整理所有基线结果
  - 计算统计量 (均值±标准差)
  - 识别性能差异

□ 任务2: 问题诊断
  - 分析失败案例
  - 检查数据质量问题
  - 调整实验参数

□ 任务3: 目标设定
  - 为神农框架设定性能目标
  - 确定关键改进指标
  - 制定下周计划
```

### Week 3: 神农框架实现与对比实验

#### Day 1-3: 神农框架分类版本
**目标**: 实现支持分类任务的神农框架
```bash
□ 任务1: 模型架构修改
  - 修改输出层为分类
  - 调整损失函数 (BCELoss)
  - 实现注意力融合机制

□ 任务2: 训练流程适配
  - 修改数据加载器
  - 调整评估指标
  - 实现早停机制

□ 任务3: 初步测试
  - 单次训练验证
  - 检查模型收敛性
  - 调试技术问题
```

#### Day 4-5: 完整对比实验
**目标**: 执行完整的三方对比实验
```bash
□ 任务1: 实验设计确认
  - 确保相同的数据划分
  - 统一评估指标
  - 设置相同的随机种子

□ 任务2: 批量实验执行
  - 3个方法 × 3次独立运行
  - 5折交叉验证
  - 记录详细日志

□ 任务3: 结果收集
  - 汇总所有实验结果
  - 计算性能指标
  - 保存原始数据
```

#### Day 6-7: 统计分析
**目标**: 进行严格的统计分析
```bash
□ 任务1: 描述性统计
  - 计算均值和标准差
  - 绘制性能对比图
  - 分析结果分布

□ 任务2: 显著性检验
  - McNemar检验 (分类任务)
  - 计算p值和置信区间
  - Bonferroni多重比较校正

□ 任务3: 效应量分析
  - 计算Cohen's d
  - 分析实际意义
  - 识别关键差异
```

### Week 4: 应用验证与结果分析

#### Day 1-2: 虚拟筛选模拟
**目标**: 验证在药物筛选中的实用价值
```bash
□ 任务1: 筛选场景设计
  - 模拟大规模化合物库筛选
  - 设定筛选阈值和目标
  - 准备测试数据集

□ 任务2: 筛选性能评估
  - 计算Top-k精确率
  - 分析富集因子
  - 评估命中率

□ 任务3: 实用性分析
  - 对比随机筛选基线
  - 分析成本效益
  - 评估实际应用价值
```

#### Day 3-4: 效率分析
**目标**: 量化预计算特征的效率优势
```bash
□ 任务1: 推理速度测试
  - 测量每秒处理化合物数
  - 对比不同方法的速度
  - 分析瓶颈环节

□ 任务2: 资源使用分析
  - 测量内存占用
  - 分析GPU/CPU使用率
  - 评估可扩展性

□ 任务3: 成本效益分析
  - 计算预计算特征的一次性成本
  - 分析重复使用的收益
  - 量化总体效率提升
```

#### Day 5-7: 结果整理与报告
**目标**: 完成实验报告和初步论文
```bash
□ 任务1: 结果可视化
  - 制作性能对比图表
  - 绘制虚拟筛选曲线
  - 创建效率对比图

□ 任务2: 实验报告撰写
  - 整理实验方法和结果
  - 分析成功和失败案例
  - 讨论局限性和改进方向

□ 任务3: 论文初稿
  - 撰写Introduction和Methods
  - 整理Results部分
  - 起草Discussion和Conclusion
```

## 📊 里程碑检查点

### Week 1 结束检查
```bash
✓ 数据质量: 至少5000个有效化合物
✓ 特征生成: Mordred特征文件生成成功
✓ 环境配置: ChemProp和AutoGluon正常工作
```

### Week 2 结束检查
```bash
✓ ChemProp基线: AUC > 0.70 (合理基线)
✓ AutoGluon基线: 性能可比较
✓ 结果一致性: 3次运行结果稳定
```

### Week 3 结束检查
```bash
✓ 神农框架: 成功训练并收敛
✓ 对比实验: 完成所有对比实验
✓ 统计分析: 获得显著性检验结果
```

### Week 4 结束检查
```bash
✓ 应用验证: 虚拟筛选结果合理
✓ 效率分析: 量化效率优势
✓ 文档完整: 实验报告和论文初稿
```

## ⚠️ 风险时间点

### 高风险时间点
1. **Day 3-4**: 特征生成可能遇到技术问题
2. **Day 8-10**: ChemProp安装和配置可能有困难
3. **Day 15-17**: 神农框架修改可能需要调试
4. **Day 22-24**: 统计分析可能发现无显著差异

### 应对策略
- 为高风险任务预留额外时间
- 准备备选方案
- 及时寻求技术支持
- 保持灵活的时间安排

## 📋 每日检查清单

### 每日必做
- [ ] 记录实验日志
- [ ] 备份重要数据和代码
- [ ] 检查实验进度
- [ ] 识别和解决问题

### 每周总结
- [ ] 回顾本周目标完成情况
- [ ] 分析遇到的问题和解决方案
- [ ] 调整下周计划
- [ ] 更新实验文档

---

**时间管理建议**: 严格按照时间线执行，但保持一定灵活性。如果某个任务提前完成，可以开始下一个任务；如果遇到困难，及时调整计划。
