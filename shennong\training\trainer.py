# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-27
# 描述: 神农框架训练器

"""
神农框架训练器

提供完整的模型训练、验证和测试流程。
"""

from typing import Dict, Any, Optional, List, Union
import torch
import lightning as L
from lightning.pytorch.callbacks import EarlyStopping, ModelCheckpoint
from lightning.pytorch.loggers import TensorBoardLogger, WandbLogger
import logging
from pathlib import Path

from ..models.shennong_core import ShennongFramework
from ..data.datasets import ShennongDataset, AntibacterialDataset
from ..data.dataloader import ShennongDataLoader, create_train_val_dataloaders
from ..data.splitting import random_split, scaffold_split, stratified_split
from .callbacks import AntibacterialInterpretationCallback

logger = logging.getLogger(__name__)


class ShennongTrainer:
    """
    神农框架专用训练器
    
    集成PyTorch Lightning，提供完整的训练流程。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化神农训练器
        
        Args:
            config: 训练配置字典
        """
        self.config = config
        self.model_config = config.get('model', {})
        self.training_config = config.get('training', {})
        self.data_config = config.get('data', {})
        self.hardware_config = config.get('hardware', {})
        self.logging_config = config.get('logging', {})
        
        # 训练组件
        self.model = None
        self.trainer = None
        self.train_loader = None
        self.val_loader = None
        self.test_loader = None
        
        # 自适应超参数
        self.setup_adaptive_hyperparameters()
        
        logger.info("初始化神农训练器")
    
    def setup_adaptive_hyperparameters(self):
        """根据数据集特征自适应调整超参数"""
        # 获取数据集信息
        data_size = self.data_config.get('dataset_size', 1000)
        descriptor_dim = self.model_config.get('expert_config', {}).get('descriptor_dim', 1613)
        gpu_memory = self.hardware_config.get('max_memory_gb', 16)
        
        # 数据集大小自适应
        if data_size < 500:
            # 小数据集：减小模型复杂度，增加正则化
            self.model_config['graph_config']['hidden_size'] = 200
            self.model_config['expert_config']['output_dim'] = 64
            self.training_config['learning_rate'] = 1e-4
            self.training_config['weight_decay'] = 1e-3
            self.training_config['dropout'] = 0.4
            
        elif data_size < 5000:
            # 中等数据集：标准配置
            self.model_config['graph_config']['hidden_size'] = 300
            self.model_config['expert_config']['output_dim'] = 128
            self.training_config['learning_rate'] = 5e-4
            self.training_config['weight_decay'] = 1e-4
            
        else:
            # 大数据集：增加模型容量
            self.model_config['graph_config']['hidden_size'] = 400
            self.model_config['expert_config']['output_dim'] = 256
            self.training_config['learning_rate'] = 1e-3
            self.training_config['weight_decay'] = 1e-5
        
        # GPU内存自适应
        if gpu_memory < 8:
            self.training_config['batch_size'] = 16
            self.data_config['dataloader']['num_workers'] = 2
        elif gpu_memory < 16:
            self.training_config['batch_size'] = 32
            self.data_config['dataloader']['num_workers'] = 4
        else:
            self.training_config['batch_size'] = 64
            self.data_config['dataloader']['num_workers'] = 8
        
        logger.info(f"自适应超参数设置完成: 数据集大小={data_size}, "
                   f"批次大小={self.training_config['batch_size']}, "
                   f"学习率={self.training_config['learning_rate']}")
    
    def prepare_data(
        self, 
        dataset: Union[ShennongDataset, AntibacterialDataset],
        split_method: str = 'scaffold'
    ) -> tuple[ShennongDataLoader, ShennongDataLoader, Optional[ShennongDataLoader]]:
        """
        准备训练数据
        
        Args:
            dataset: 数据集
            split_method: 数据划分方法
            
        Returns:
            (训练加载器, 验证加载器, 测试加载器)
        """
        logger.info(f"准备数据: 数据集大小={len(dataset)}, 划分方法={split_method}")
        
        # 数据划分
        split_config = self.data_config.get('splitting', {})
        train_ratio = split_config.get('train_ratio', 0.8)
        val_ratio = split_config.get('val_ratio', 0.1)
        test_ratio = split_config.get('test_ratio', 0.1)
        random_state = split_config.get('random_state', 42)
        
        if split_method == 'random':
            train_dataset, val_dataset, test_dataset = random_split(
                dataset, train_ratio, val_ratio, test_ratio, random_state
            )
        elif split_method == 'scaffold':
            train_dataset, val_dataset, test_dataset = scaffold_split(
                dataset, train_ratio, val_ratio, test_ratio
            )
        elif split_method == 'stratified':
            stratify_task = split_config.get('stratify_task', 'activity')
            n_bins = split_config.get('n_bins', 5)
            train_dataset, val_dataset, test_dataset = stratified_split(
                dataset, stratify_task, train_ratio, val_ratio, test_ratio, n_bins, random_state
            )
        else:
            raise ValueError(f"不支持的划分方法: {split_method}")
        
        # 创建数据加载器
        dataloader_config = self.data_config.get('dataloader', {})
        batch_size = self.training_config.get('batch_size', 64)
        
        self.train_loader = ShennongDataLoader(
            dataset=train_dataset,
            batch_size=batch_size,
            shuffle=True,
            **dataloader_config
        )
        
        self.val_loader = ShennongDataLoader(
            dataset=val_dataset,
            batch_size=batch_size,
            shuffle=False,
            **dataloader_config
        ) if val_dataset else None
        
        self.test_loader = ShennongDataLoader(
            dataset=test_dataset,
            batch_size=batch_size,
            shuffle=False,
            **dataloader_config
        ) if test_dataset else None
        
        logger.info(f"数据准备完成: 训练={len(train_dataset)}, "
                   f"验证={len(val_dataset) if val_dataset else 0}, "
                   f"测试={len(test_dataset) if test_dataset else 0}")
        
        return self.train_loader, self.val_loader, self.test_loader
    
    def build_model(self) -> ShennongFramework:
        """
        构建神农模型
        
        Returns:
            神农框架模型
        """
        logger.info("构建神农模型")
        
        # 合并训练配置到模型配置
        model_config = {
            **self.model_config,
            'learning_rate': self.training_config.get('learning_rate', 1e-3),
            'weight_decay': self.training_config.get('weight_decay', 1e-4),
            'scheduler': self.training_config.get('scheduler', {}),
            'loss_weights': self.training_config.get('loss_weights', {})
        }
        
        self.model = ShennongFramework(model_config)
        
        logger.info("神农模型构建完成")
        logger.info(self.model.summary())
        
        return self.model
    
    def setup_callbacks(self) -> List[L.Callback]:
        """
        设置训练回调
        
        Returns:
            回调函数列表
        """
        callbacks = []
        
        # 早停回调
        early_stopping_config = self.training_config.get('early_stopping', {})
        if early_stopping_config.get('enabled', True):
            early_stopping = EarlyStopping(
                monitor=early_stopping_config.get('monitor', 'val_loss'),
                patience=early_stopping_config.get('patience', 20),
                min_delta=early_stopping_config.get('min_delta', 1e-4),
                mode=early_stopping_config.get('mode', 'min'),
                verbose=True
            )
            callbacks.append(early_stopping)
        
        # 模型检查点回调
        checkpoint_config = self.logging_config.get('checkpoint', {})
        if checkpoint_config.get('enabled', True):
            checkpoint_dir = Path(self.logging_config.get('log_dir', 'logs')) / 'checkpoints'
            checkpoint_dir.mkdir(parents=True, exist_ok=True)
            
            model_checkpoint = ModelCheckpoint(
                dirpath=checkpoint_dir,
                filename=checkpoint_config.get('filename', 'shennong-{epoch:02d}-{val_loss:.2f}'),
                monitor=checkpoint_config.get('monitor', 'val_loss'),
                mode=checkpoint_config.get('mode', 'min'),
                save_top_k=checkpoint_config.get('save_top_k', 3),
                save_last=checkpoint_config.get('save_last', True),
                verbose=True
            )
            callbacks.append(model_checkpoint)
        
        # 抗菌解释回调
        interpretation_config = self.config.get('evaluation', {}).get('interpretability', {})
        if interpretation_config.get('enabled', True):
            interpretation_callback = AntibacterialInterpretationCallback()
            callbacks.append(interpretation_callback)
        
        return callbacks
    
    def setup_loggers(self) -> List[L.pytorch.loggers.Logger]:
        """
        设置日志记录器
        
        Returns:
            日志记录器列表
        """
        loggers = []
        
        # TensorBoard日志
        tensorboard_config = self.logging_config.get('tensorboard', {})
        if tensorboard_config.get('enabled', True):
            log_dir = Path(self.logging_config.get('log_dir', 'logs'))
            experiment_name = self.logging_config.get('experiment_name', 'shennong_experiment')
            
            tb_logger = TensorBoardLogger(
                save_dir=log_dir,
                name=experiment_name,
                log_graph=tensorboard_config.get('log_graph', True)
            )
            loggers.append(tb_logger)
        
        # Weights & Biases日志
        wandb_config = self.logging_config.get('wandb', {})
        if wandb_config.get('enabled', False):
            wandb_logger = WandbLogger(
                project=wandb_config.get('project', 'shennong-framework'),
                entity=wandb_config.get('entity'),
                tags=wandb_config.get('tags', ['antibacterial', 'chemprop', 'attention']),
                config=self.config
            )
            loggers.append(wandb_logger)
        
        return loggers
    
    def fit(
        self, 
        dataset: Union[ShennongDataset, AntibacterialDataset],
        split_method: str = 'scaffold'
    ):
        """
        训练模型
        
        Args:
            dataset: 训练数据集
            split_method: 数据划分方法
        """
        logger.info("开始训练神农模型")
        
        # 准备数据
        train_loader, val_loader, test_loader = self.prepare_data(dataset, split_method)
        
        # 构建模型
        if self.model is None:
            self.build_model()
        
        # 设置回调和日志
        callbacks = self.setup_callbacks()
        loggers = self.setup_loggers()
        
        # 配置训练器
        trainer_config = {
            'max_epochs': self.training_config.get('max_epochs', 100),
            'accelerator': self.hardware_config.get('accelerator', 'auto'),
            'devices': self.hardware_config.get('devices', 'auto'),
            'precision': self.hardware_config.get('precision', 32),
            'callbacks': callbacks,
            'logger': loggers,
            'gradient_clip_val': self.training_config.get('gradient_clip_val', 1.0),
            'gradient_clip_algorithm': self.training_config.get('gradient_clip_algorithm', 'norm'),
            'deterministic': True,
            'enable_progress_bar': True,
            'enable_model_summary': True
        }
        
        # 分布式训练配置
        strategy = self.hardware_config.get('strategy')
        if strategy:
            trainer_config['strategy'] = strategy
            trainer_config['num_nodes'] = self.hardware_config.get('num_nodes', 1)
        
        # 创建训练器
        self.trainer = L.Trainer(**trainer_config)
        
        # 开始训练
        self.trainer.fit(
            model=self.model,
            train_dataloaders=train_loader,
            val_dataloaders=val_loader
        )
        
        logger.info("模型训练完成")
    
    def test(self, test_dataset: Optional[ShennongDataset] = None) -> Dict[str, float]:
        """
        测试模型
        
        Args:
            test_dataset: 测试数据集
            
        Returns:
            测试结果
        """
        if self.trainer is None or self.model is None:
            raise ValueError("模型尚未训练，请先调用fit()方法")
        
        # 使用提供的测试集或训练时划分的测试集
        if test_dataset is not None:
            test_loader = ShennongDataLoader(
                dataset=test_dataset,
                batch_size=self.training_config.get('batch_size', 64),
                shuffle=False,
                **self.data_config.get('dataloader', {})
            )
        else:
            test_loader = self.test_loader
        
        if test_loader is None:
            raise ValueError("没有可用的测试数据集")
        
        # 执行测试
        test_results = self.trainer.test(
            model=self.model,
            dataloaders=test_loader
        )
        
        logger.info("模型测试完成")
        return test_results[0] if test_results else {}
    
    def predict(
        self, 
        dataset: ShennongDataset,
        return_attention: bool = False
    ) -> Dict[str, Any]:
        """
        使用训练好的模型进行预测
        
        Args:
            dataset: 预测数据集
            return_attention: 是否返回注意力权重
            
        Returns:
            预测结果
        """
        if self.model is None:
            raise ValueError("模型尚未训练，请先调用fit()方法")
        
        # 创建预测数据加载器
        predict_loader = ShennongDataLoader(
            dataset=dataset,
            batch_size=self.training_config.get('batch_size', 64),
            shuffle=False,
            **self.data_config.get('dataloader', {})
        )
        
        # 执行预测
        if self.trainer is None:
            # 创建简单的预测训练器
            self.trainer = L.Trainer(
                accelerator=self.hardware_config.get('accelerator', 'auto'),
                devices=self.hardware_config.get('devices', 'auto'),
                logger=False,
                enable_progress_bar=True
            )
        
        predictions = self.trainer.predict(
            model=self.model,
            dataloaders=predict_loader
        )
        
        # 整理预测结果
        all_predictions = []
        for batch_pred in predictions:
            all_predictions.append(batch_pred)
        
        # 合并批次结果
        combined_predictions = {}
        if all_predictions:
            for key in all_predictions[0].keys():
                if key == 'attention_weights' and not return_attention:
                    continue
                
                batch_values = [pred[key] for pred in all_predictions if key in pred]
                if batch_values and torch.is_tensor(batch_values[0]):
                    combined_predictions[key] = torch.cat(batch_values, dim=0)
                else:
                    combined_predictions[key] = batch_values
        
        logger.info(f"预测完成: {len(dataset)}个样本")
        return combined_predictions
    
    def save_model(self, path: str):
        """保存模型"""
        if self.model is None:
            raise ValueError("模型尚未训练")
        
        self.model.save_model(path, include_optimizer=False)
        logger.info(f"模型已保存到: {path}")
    
    def load_model(self, path: str):
        """加载模型"""
        self.model = ShennongFramework.load_model(path)
        logger.info(f"模型已从 {path} 加载")
