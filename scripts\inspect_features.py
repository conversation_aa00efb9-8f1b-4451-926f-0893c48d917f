#!/usr/bin/env python3
# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-30
# 描述: 特征文件检查工具

"""
特征文件检查工具

用于检查和分析由generate_features.py生成的特征文件。
"""

import argparse
import numpy as np
import pandas as pd
import logging
from pathlib import Path
from typing import Dict, Any

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def inspect_features(features_path: str) -> Dict[str, Any]:
    """检查特征文件"""
    logger.info(f"🔍 检查特征文件: {features_path}")
    
    try:
        # 加载特征文件
        data = np.load(features_path, allow_pickle=True)
        
        # 基本信息
        info = {
            'file_path': features_path,
            'file_size_mb': Path(features_path).stat().st_size / (1024 * 1024),
            'keys': list(data.keys()),
            'feature_types': data['feature_types'].tolist() if 'feature_types' in data else [],
            'num_molecules': len(data['mol_ids']) if 'mol_ids' in data else 0,
            'normalization': str(data['normalization']) if 'normalization' in data else 'unknown'
        }
        
        # 特征维度信息
        feature_shapes = {}
        for key in data.keys():
            if key.startswith('features_'):
                feat_type = key.replace('features_', '')
                feature_shapes[feat_type] = data[key].shape
        
        info['feature_shapes'] = feature_shapes
        
        # 元数据
        if 'metadata' in data:
            metadata = data['metadata'].item()
            info['metadata'] = metadata
        
        # 显示信息
        logger.info("📊 特征文件信息:")
        logger.info(f"   文件大小: {info['file_size_mb']:.2f} MB")
        logger.info(f"   分子数量: {info['num_molecules']}")
        logger.info(f"   特征类型: {info['feature_types']}")
        logger.info(f"   标准化方法: {info['normalization']}")
        
        logger.info("📐 特征维度:")
        for feat_type, shape in feature_shapes.items():
            logger.info(f"   {feat_type}: {shape}")
        
        if 'metadata' in info:
            logger.info("📋 生成信息:")
            metadata = info['metadata']
            logger.info(f"   成功率: {metadata.get('successful_molecules', 0)}/{metadata.get('total_molecules', 0)}")
            logger.info(f"   生成耗时: {metadata.get('generation_time', 0):.2f} 秒")
        
        return info
        
    except Exception as e:
        logger.error(f"❌ 检查特征文件失败: {e}")
        return {}

def export_feature_summary(features_path: str, output_path: str = None):
    """导出特征摘要"""
    try:
        data = np.load(features_path, allow_pickle=True)
        
        # 创建摘要DataFrame
        summary_data = []
        
        if 'mol_ids' in data:
            mol_ids = data['mol_ids']
            
            for i, mol_id in enumerate(mol_ids):
                row = {'mol_id': mol_id}
                
                # 添加各类型特征的统计信息
                for key in data.keys():
                    if key.startswith('features_'):
                        feat_type = key.replace('features_', '')
                        features = data[key][i]
                        
                        row[f'{feat_type}_mean'] = np.mean(features)
                        row[f'{feat_type}_std'] = np.std(features)
                        row[f'{feat_type}_min'] = np.min(features)
                        row[f'{feat_type}_max'] = np.max(features)
                        row[f'{feat_type}_nonzero'] = np.count_nonzero(features)
                
                summary_data.append(row)
        
        df = pd.DataFrame(summary_data)
        
        if output_path:
            df.to_csv(output_path, index=False)
            logger.info(f"✅ 特征摘要已保存到: {output_path}")
        
        return df
        
    except Exception as e:
        logger.error(f"❌ 导出特征摘要失败: {e}")
        return None

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="特征文件检查工具")
    parser.add_argument('--features_path', type=str, required=True, help='特征文件路径')
    parser.add_argument('--export_summary', type=str, help='导出特征摘要CSV文件路径')
    parser.add_argument('--verbose', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 检查特征文件
    info = inspect_features(args.features_path)
    
    if not info:
        return
    
    # 导出摘要
    if args.export_summary:
        export_feature_summary(args.features_path, args.export_summary)
    
    logger.info("🎉 检查完成!")

if __name__ == "__main__":
    main()
