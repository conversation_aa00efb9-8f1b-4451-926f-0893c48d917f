# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-29
# 描述: 神农框架药效团分析器

"""
神农框架药效团分析器

结合注意力权重分析分子中的药效团特征，识别对抗菌活性重要的化学基团。
"""

import logging
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from rdkit import Chem
from rdkit.Chem import rdMolDescriptors, Fragments, Descriptors

logger = logging.getLogger(__name__)


class PharmacophoreAnalyzer:
    """
    药效团分析器
    
    结合AI注意力权重和化学知识，识别和解释药效团特征。
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化药效团分析器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        
        # 药效团特征定义
        self.pharmacophore_features = self._initialize_pharmacophore_features()
        
        # 抗菌药效团模式
        self.antibacterial_pharmacophores = self._initialize_antibacterial_pharmacophores()
        
        logger.info("药效团分析器初始化完成")
    
    def analyze_pharmacophores(
        self,
        smiles: str,
        predicted_mechanism: str,
        attention_weights: Dict[str, np.ndarray]
    ) -> Dict[str, Any]:
        """
        分析药效团特征
        
        Args:
            smiles: 分子SMILES
            predicted_mechanism: 预测机制
            attention_weights: 注意力权重
            
        Returns:
            药效团分析结果
        """
        mol = Chem.MolFromSmiles(smiles)
        if mol is None:
            return {'error': '无效的SMILES结构'}
        
        analysis = {
            'identified_pharmacophores': self._identify_pharmacophores(mol),
            'attention_pharmacophore_correlation': self._correlate_attention_pharmacophores(
                mol, attention_weights
            ),
            'mechanism_specific_features': self._analyze_mechanism_specific_features(
                mol, predicted_mechanism
            ),
            'pharmacophore_importance_ranking': self._rank_pharmacophore_importance(
                mol, attention_weights, predicted_mechanism
            ),
            'chemical_interpretation': self._interpret_pharmacophore_chemistry(mol)
        }
        
        return analysis
    
    def _initialize_pharmacophore_features(self) -> Dict[str, Dict[str, Any]]:
        """初始化药效团特征定义"""
        return {
            'hydrogen_bond_donor': {
                'smarts': '[!#6;!H0]',
                'description': '氢键供体',
                'importance': 'high',
                'chemical_meaning': '能够提供氢原子形成氢键的基团'
            },
            'hydrogen_bond_acceptor': {
                'smarts': '[!$([#6,F,Cl,Br,I,o,s,nX3,#7v5,#15v5,#16v4,#16v6,*+1,*+2,*+3])]',
                'description': '氢键受体',
                'importance': 'high',
                'chemical_meaning': '能够接受氢原子形成氢键的基团'
            },
            'aromatic_ring': {
                'smarts': 'c1ccccc1',
                'description': '芳香环',
                'importance': 'medium',
                'chemical_meaning': '提供π-π相互作用和疏水性接触'
            },
            'positive_ionizable': {
                'smarts': '[+,+2,+3]',
                'description': '正电荷中心',
                'importance': 'high',
                'chemical_meaning': '在生理pH下带正电荷的基团'
            },
            'negative_ionizable': {
                'smarts': '[-,-2,-3]',
                'description': '负电荷中心',
                'importance': 'high',
                'chemical_meaning': '在生理pH下带负电荷的基团'
            },
            'hydrophobic': {
                'smarts': '[C,c]',
                'description': '疏水基团',
                'importance': 'medium',
                'chemical_meaning': '提供疏水性相互作用'
            }
        }
    
    def _initialize_antibacterial_pharmacophores(self) -> Dict[str, Dict[str, Any]]:
        """初始化抗菌药效团模式"""
        return {
            'beta_lactam': {
                'smarts': '[C;R]1[C;R][N;R][C;R](=O)[C;R]1',
                'mechanism': 'cell_wall_synthesis',
                'description': 'β-内酰胺环',
                'importance': 'critical',
                'examples': ['青霉素', '头孢菌素']
            },
            'quinolone_core': {
                'smarts': 'c1cc2c(cc1)c(=O)c(cn2)C(=O)O',
                'mechanism': 'dna_replication',
                'description': '喹诺酮核心',
                'importance': 'critical',
                'examples': ['环丙沙星', '左氧氟沙星']
            },
            'aminoglycoside': {
                'smarts': '[N+]([H])([H])[C]',
                'mechanism': 'protein_synthesis',
                'description': '氨基糖苷',
                'importance': 'critical',
                'examples': ['链霉素', '庆大霉素']
            },
            'sulfonamide': {
                'smarts': '[S](=O)(=O)[N]',
                'mechanism': 'metabolic_pathway',
                'description': '磺胺基',
                'importance': 'critical',
                'examples': ['磺胺甲恶唑']
            }
        }
    
    def _identify_pharmacophores(self, mol: Chem.Mol) -> List[Dict[str, Any]]:
        """识别分子中的药效团特征"""
        identified = []
        
        # 检查通用药效团特征
        for feature_name, feature_info in self.pharmacophore_features.items():
            try:
                pattern = Chem.MolFromSmarts(feature_info['smarts'])
                if pattern:
                    matches = mol.GetSubstructMatches(pattern)
                    if matches:
                        identified.append({
                            'type': feature_name,
                            'description': feature_info['description'],
                            'count': len(matches),
                            'atom_indices': [list(match) for match in matches],
                            'importance': feature_info['importance'],
                            'chemical_meaning': feature_info['chemical_meaning']
                        })
            except:
                continue
        
        # 检查抗菌特异性药效团
        for pharmacophore_name, pharmacophore_info in self.antibacterial_pharmacophores.items():
            try:
                pattern = Chem.MolFromSmarts(pharmacophore_info['smarts'])
                if pattern:
                    matches = mol.GetSubstructMatches(pattern)
                    if matches:
                        identified.append({
                            'type': f'antibacterial_{pharmacophore_name}',
                            'description': pharmacophore_info['description'],
                            'count': len(matches),
                            'atom_indices': [list(match) for match in matches],
                            'importance': 'critical',
                            'mechanism': pharmacophore_info['mechanism'],
                            'examples': pharmacophore_info['examples']
                        })
            except:
                continue
        
        return identified
    
    def _correlate_attention_pharmacophores(
        self,
        mol: Chem.Mol,
        attention_weights: Dict[str, np.ndarray]
    ) -> Dict[str, Any]:
        """关联注意力权重与药效团特征"""
        
        # 合并注意力权重
        combined_weights = self._combine_attention_weights(attention_weights)
        if combined_weights is None:
            return {'error': '无有效注意力权重'}
        
        # 识别药效团
        pharmacophores = self._identify_pharmacophores(mol)
        
        # 计算每个药效团的注意力分数
        pharmacophore_attention = []
        
        for pharmacophore in pharmacophores:
            attention_scores = []
            
            for atom_indices in pharmacophore['atom_indices']:
                for atom_idx in atom_indices:
                    if atom_idx < len(combined_weights):
                        attention_scores.append(combined_weights[atom_idx])
            
            if attention_scores:
                avg_attention = np.mean(attention_scores)
                max_attention = np.max(attention_scores)
                
                pharmacophore_attention.append({
                    'pharmacophore': pharmacophore['type'],
                    'description': pharmacophore['description'],
                    'average_attention': float(avg_attention),
                    'max_attention': float(max_attention),
                    'attention_rank': 0,  # 将在后面计算
                    'ai_importance': self._classify_attention_importance(avg_attention)
                })
        
        # 按注意力权重排序
        pharmacophore_attention.sort(key=lambda x: x['average_attention'], reverse=True)
        
        # 分配排名
        for i, item in enumerate(pharmacophore_attention):
            item['attention_rank'] = i + 1
        
        return {
            'pharmacophore_attention_scores': pharmacophore_attention,
            'top_attended_pharmacophore': pharmacophore_attention[0] if pharmacophore_attention else None,
            'attention_pharmacophore_consistency': self._calculate_consistency(pharmacophore_attention)
        }
    
    def _analyze_mechanism_specific_features(self, mol: Chem.Mol, mechanism: str) -> Dict[str, Any]:
        """分析机制特异性药效团特征"""
        
        mechanism_features = []
        
        # 检查与特定机制相关的药效团
        for pharmacophore_name, pharmacophore_info in self.antibacterial_pharmacophores.items():
            if pharmacophore_info['mechanism'] == mechanism:
                try:
                    pattern = Chem.MolFromSmarts(pharmacophore_info['smarts'])
                    if pattern:
                        matches = mol.GetSubstructMatches(pattern)
                        if matches:
                            mechanism_features.append({
                                'pharmacophore': pharmacophore_name,
                                'description': pharmacophore_info['description'],
                                'count': len(matches),
                                'relevance': 'direct',
                                'examples': pharmacophore_info['examples']
                            })
                except:
                    continue
        
        # 分析机制支持度
        mechanism_support = len(mechanism_features) > 0
        support_score = len(mechanism_features) / max(1, len([
            p for p in self.antibacterial_pharmacophores.values() 
            if p['mechanism'] == mechanism
        ]))
        
        return {
            'mechanism_specific_features': mechanism_features,
            'mechanism_support': mechanism_support,
            'support_score': float(support_score),
            'interpretation': self._interpret_mechanism_support(mechanism, mechanism_features)
        }
    
    def _rank_pharmacophore_importance(
        self,
        mol: Chem.Mol,
        attention_weights: Dict[str, np.ndarray],
        mechanism: str
    ) -> List[Dict[str, Any]]:
        """对药效团重要性进行排名"""
        
        pharmacophores = self._identify_pharmacophores(mol)
        combined_weights = self._combine_attention_weights(attention_weights)
        
        importance_ranking = []
        
        for pharmacophore in pharmacophores:
            # 计算注意力分数
            attention_score = 0.0
            if combined_weights is not None:
                attention_scores = []
                for atom_indices in pharmacophore['atom_indices']:
                    for atom_idx in atom_indices:
                        if atom_idx < len(combined_weights):
                            attention_scores.append(combined_weights[atom_idx])
                
                if attention_scores:
                    attention_score = np.mean(attention_scores)
            
            # 计算化学重要性分数
            chemical_score = self._calculate_chemical_importance(pharmacophore, mechanism)
            
            # 综合重要性分数
            total_score = attention_score * 0.6 + chemical_score * 0.4
            
            importance_ranking.append({
                'pharmacophore': pharmacophore['type'],
                'description': pharmacophore['description'],
                'attention_score': float(attention_score),
                'chemical_score': float(chemical_score),
                'total_score': float(total_score),
                'rank': 0,  # 将在排序后分配
                'interpretation': self._interpret_pharmacophore_importance(
                    pharmacophore, attention_score, chemical_score
                )
            })
        
        # 按总分排序
        importance_ranking.sort(key=lambda x: x['total_score'], reverse=True)
        
        # 分配排名
        for i, item in enumerate(importance_ranking):
            item['rank'] = i + 1
        
        return importance_ranking
    
    def _interpret_pharmacophore_chemistry(self, mol: Chem.Mol) -> Dict[str, Any]:
        """解释药效团的化学意义"""
        
        # 计算基本化学性质
        mw = Descriptors.MolWt(mol)
        logp = Descriptors.MolLogP(mol)
        hbd = rdMolDescriptors.CalcNumHBD(mol)
        hba = rdMolDescriptors.CalcNumHBA(mol)
        
        # 分析药效团分布
        pharmacophores = self._identify_pharmacophores(mol)
        
        # 统计药效团类型
        pharmacophore_counts = {}
        for p in pharmacophores:
            p_type = p['type']
            if p_type not in pharmacophore_counts:
                pharmacophore_counts[p_type] = 0
            pharmacophore_counts[p_type] += p['count']
        
        # 生成化学解释
        interpretations = []
        
        if pharmacophore_counts.get('hydrogen_bond_donor', 0) > 0:
            interpretations.append(f"含有{pharmacophore_counts['hydrogen_bond_donor']}个氢键供体，有利于与靶蛋白结合")
        
        if pharmacophore_counts.get('hydrogen_bond_acceptor', 0) > 0:
            interpretations.append(f"含有{pharmacophore_counts['hydrogen_bond_acceptor']}个氢键受体，增强分子间相互作用")
        
        if pharmacophore_counts.get('aromatic_ring', 0) > 0:
            interpretations.append(f"含有{pharmacophore_counts['aromatic_ring']}个芳香环，提供π-π堆积作用")
        
        # 检查抗菌特异性特征
        antibacterial_features = [p for p in pharmacophores if p['type'].startswith('antibacterial_')]
        if antibacterial_features:
            feature_names = [f['description'] for f in antibacterial_features]
            interpretations.append(f"含有抗菌特异性结构: {', '.join(feature_names)}")
        
        return {
            'basic_properties': {
                'molecular_weight': float(mw),
                'logp': float(logp),
                'hbd': int(hbd),
                'hba': int(hba)
            },
            'pharmacophore_distribution': pharmacophore_counts,
            'chemical_interpretations': interpretations,
            'drug_likeness_assessment': self._assess_pharmacophore_drug_likeness(pharmacophore_counts)
        }
    
    def _combine_attention_weights(self, attention_weights: Dict[str, np.ndarray]) -> Optional[np.ndarray]:
        """合并多层注意力权重"""
        valid_weights = []
        
        for weights in attention_weights.values():
            if weights is not None and len(weights) > 0:
                if weights.ndim > 1:
                    weights = weights.mean(axis=tuple(range(weights.ndim - 1)))
                valid_weights.append(weights)
        
        if not valid_weights:
            return None
        
        min_length = min(len(w) for w in valid_weights)
        truncated_weights = [w[:min_length] for w in valid_weights]
        return np.mean(truncated_weights, axis=0)
    
    def _classify_attention_importance(self, attention_score: float) -> str:
        """分类注意力重要性"""
        if attention_score > 0.7:
            return '极高'
        elif attention_score > 0.5:
            return '高'
        elif attention_score > 0.3:
            return '中等'
        else:
            return '低'
    
    def _calculate_consistency(self, pharmacophore_attention: List[Dict[str, Any]]) -> float:
        """计算注意力-药效团一致性"""
        if len(pharmacophore_attention) < 2:
            return 1.0
        
        # 计算注意力分数的变异系数
        attention_scores = [p['average_attention'] for p in pharmacophore_attention]
        mean_score = np.mean(attention_scores)
        std_score = np.std(attention_scores)
        
        if mean_score == 0:
            return 0.0
        
        cv = std_score / mean_score
        consistency = max(0, 1 - cv)  # 变异系数越小，一致性越高
        
        return float(consistency)
    
    def _calculate_chemical_importance(self, pharmacophore: Dict[str, Any], mechanism: str) -> float:
        """计算药效团的化学重要性"""
        base_importance = {
            'critical': 1.0,
            'high': 0.8,
            'medium': 0.6,
            'low': 0.4
        }
        
        importance = pharmacophore.get('importance', 'medium')
        score = base_importance.get(importance, 0.5)
        
        # 如果是机制特异性药效团，增加分数
        if pharmacophore.get('mechanism') == mechanism:
            score += 0.2
        
        return min(1.0, score)
    
    def _interpret_mechanism_support(self, mechanism: str, features: List[Dict[str, Any]]) -> str:
        """解释机制支持度"""
        if not features:
            return f"分子结构中未发现{mechanism}机制的典型药效团特征"
        
        feature_names = [f['description'] for f in features]
        return f"分子含有{len(features)}个支持{mechanism}机制的药效团: {', '.join(feature_names)}"
    
    def _interpret_pharmacophore_importance(
        self,
        pharmacophore: Dict[str, Any],
        attention_score: float,
        chemical_score: float
    ) -> str:
        """解释药效团重要性"""
        p_type = pharmacophore['type']
        description = pharmacophore['description']
        
        if attention_score > 0.7 and chemical_score > 0.7:
            return f"{description}既受到AI模型高度关注，又具有重要的化学意义"
        elif attention_score > 0.7:
            return f"{description}受到AI模型高度关注，可能是活性的关键区域"
        elif chemical_score > 0.7:
            return f"{description}具有重要的化学意义，但AI关注度相对较低"
        else:
            return f"{description}在当前预测中重要性较低"
    
    def _assess_pharmacophore_drug_likeness(self, pharmacophore_counts: Dict[str, int]) -> Dict[str, Any]:
        """评估药效团的药物相似性"""
        drug_like_score = 0.0
        assessments = []
        
        # 氢键特征评估
        hbd_count = pharmacophore_counts.get('hydrogen_bond_donor', 0)
        hba_count = pharmacophore_counts.get('hydrogen_bond_acceptor', 0)
        
        if hbd_count <= 5:
            drug_like_score += 0.25
            assessments.append("氢键供体数量适宜")
        else:
            assessments.append("氢键供体数量过多")
        
        if hba_count <= 10:
            drug_like_score += 0.25
            assessments.append("氢键受体数量适宜")
        else:
            assessments.append("氢键受体数量过多")
        
        # 芳香性评估
        aromatic_count = pharmacophore_counts.get('aromatic_ring', 0)
        if 1 <= aromatic_count <= 3:
            drug_like_score += 0.25
            assessments.append("芳香环数量适中")
        elif aromatic_count == 0:
            assessments.append("缺乏芳香环结构")
        else:
            assessments.append("芳香环数量过多")
        
        # 电荷特征评估
        pos_charge = pharmacophore_counts.get('positive_ionizable', 0)
        neg_charge = pharmacophore_counts.get('negative_ionizable', 0)
        
        if pos_charge + neg_charge <= 2:
            drug_like_score += 0.25
            assessments.append("电荷分布合理")
        else:
            assessments.append("电荷基团过多")
        
        return {
            'drug_likeness_score': float(drug_like_score),
            'assessments': assessments,
            'overall_assessment': '良好' if drug_like_score > 0.75 else '一般' if drug_like_score > 0.5 else '较差'
        }
