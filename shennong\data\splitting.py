# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-27
# 描述: 神农框架数据划分模块

"""
神农框架数据划分

提供多种数据划分策略，包括随机划分、分子骨架划分、分层划分等。
针对抗菌化合物数据的特点进行优化。
"""

from typing import List, Tuple, Optional, Dict, Any, Union
import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split, StratifiedShuffleSplit
from sklearn.cluster import KMeans
import logging

# RDKit导入
try:
    from rdkit import Chem
    from rdkit.Chem.Scaffolds import MurckoScaffold
    from rdkit import DataStructs
    from rdkit.Chem import rdMolDescriptors
except ImportError:
    raise ImportError("RDKit未安装，请运行: pip install rdkit-pypi")

from .datasets import ShennongDataset, AntibacterialDataset

logger = logging.getLogger(__name__)


def random_split(
    dataset: ShennongDataset,
    train_ratio: float = 0.8,
    val_ratio: float = 0.1,
    test_ratio: float = 0.1,
    random_state: Optional[int] = None
) -> Tuple[ShennongDataset, ShennongDataset, ShennongDataset]:
    """
    随机划分数据集
    
    Args:
        dataset: 输入数据集
        train_ratio: 训练集比例
        val_ratio: 验证集比例
        test_ratio: 测试集比例
        random_state: 随机种子
        
    Returns:
        (训练集, 验证集, 测试集)
    """
    # 验证比例
    if abs(train_ratio + val_ratio + test_ratio - 1.0) > 1e-6:
        raise ValueError("训练、验证、测试集比例之和必须为1")
    
    dataset_size = len(dataset)
    indices = list(range(dataset_size))
    
    # 第一次划分：分离测试集
    if test_ratio > 0:
        train_val_indices, test_indices = train_test_split(
            indices,
            test_size=test_ratio,
            random_state=random_state
        )
    else:
        train_val_indices = indices
        test_indices = []
    
    # 第二次划分：分离训练集和验证集
    if val_ratio > 0 and len(train_val_indices) > 0:
        val_size = val_ratio / (train_ratio + val_ratio)
        train_indices, val_indices = train_test_split(
            train_val_indices,
            test_size=val_size,
            random_state=random_state
        )
    else:
        train_indices = train_val_indices
        val_indices = []
    
    # 创建子数据集
    train_dataset = dataset.split_by_indices(train_indices)
    val_dataset = dataset.split_by_indices(val_indices) if val_indices else None
    test_dataset = dataset.split_by_indices(test_indices) if test_indices else None
    
    logger.info(f"随机划分完成: 训练集={len(train_indices)}, "
               f"验证集={len(val_indices)}, 测试集={len(test_indices)}")
    
    return train_dataset, val_dataset, test_dataset


def scaffold_split(
    dataset: ShennongDataset,
    train_ratio: float = 0.8,
    val_ratio: float = 0.1,
    test_ratio: float = 0.1,
    include_chirality: bool = False
) -> Tuple[ShennongDataset, ShennongDataset, ShennongDataset]:
    """
    基于分子骨架的划分
    
    使用Murcko骨架确保结构相似的分子不会同时出现在训练集和测试集中。
    
    Args:
        dataset: 输入数据集
        train_ratio: 训练集比例
        val_ratio: 验证集比例
        test_ratio: 测试集比例
        include_chirality: 是否包含手性信息
        
    Returns:
        (训练集, 验证集, 测试集)
    """
    # 验证比例
    if abs(train_ratio + val_ratio + test_ratio - 1.0) > 1e-6:
        raise ValueError("训练、验证、测试集比例之和必须为1")
    
    # 计算每个分子的骨架
    scaffolds = {}
    for i, datapoint in enumerate(dataset.data):
        mol = Chem.MolFromSmiles(datapoint.smiles)
        if mol is None:
            logger.warning(f"无效的SMILES: {datapoint.smiles}")
            scaffold = "invalid"
        else:
            scaffold = MurckoScaffold.MurckoScaffoldSmiles(
                mol=mol, includeChirality=include_chirality
            )
        
        if scaffold not in scaffolds:
            scaffolds[scaffold] = []
        scaffolds[scaffold].append(i)
    
    # 按骨架大小排序（大的骨架优先分配）
    scaffold_sets = list(scaffolds.values())
    scaffold_sets.sort(key=len, reverse=True)
    
    # 贪心分配到各个集合
    train_indices, val_indices, test_indices = [], [], []
    train_size = int(len(dataset) * train_ratio)
    val_size = int(len(dataset) * val_ratio)
    
    for scaffold_set in scaffold_sets:
        # 选择当前最小的集合进行分配
        current_sizes = [len(train_indices), len(val_indices), len(test_indices)]
        target_sizes = [train_size, val_size, len(dataset) - train_size - val_size]
        
        # 计算每个集合的填充程度
        fill_ratios = [
            current / target if target > 0 else float('inf')
            for current, target in zip(current_sizes, target_sizes)
        ]
        
        # 分配到填充程度最低的集合
        min_ratio_idx = fill_ratios.index(min(fill_ratios))
        
        if min_ratio_idx == 0:
            train_indices.extend(scaffold_set)
        elif min_ratio_idx == 1:
            val_indices.extend(scaffold_set)
        else:
            test_indices.extend(scaffold_set)
    
    # 创建子数据集
    train_dataset = dataset.split_by_indices(train_indices)
    val_dataset = dataset.split_by_indices(val_indices) if val_indices else None
    test_dataset = dataset.split_by_indices(test_indices) if test_indices else None
    
    logger.info(f"骨架划分完成: 训练集={len(train_indices)}, "
               f"验证集={len(val_indices)}, 测试集={len(test_indices)}, "
               f"骨架数={len(scaffolds)}")
    
    return train_dataset, val_dataset, test_dataset


def stratified_split(
    dataset: ShennongDataset,
    task_name: str,
    train_ratio: float = 0.8,
    val_ratio: float = 0.1,
    test_ratio: float = 0.1,
    n_bins: int = 5,
    random_state: Optional[int] = None
) -> Tuple[ShennongDataset, ShennongDataset, ShennongDataset]:
    """
    分层划分数据集
    
    根据目标值的分布进行分层采样，确保各个集合中目标值的分布相似。
    
    Args:
        dataset: 输入数据集
        task_name: 用于分层的任务名称
        train_ratio: 训练集比例
        val_ratio: 验证集比例
        test_ratio: 测试集比例
        n_bins: 分层的箱数
        random_state: 随机种子
        
    Returns:
        (训练集, 验证集, 测试集)
    """
    # 验证比例
    if abs(train_ratio + val_ratio + test_ratio - 1.0) > 1e-6:
        raise ValueError("训练、验证、测试集比例之和必须为1")
    
    # 获取目标值
    targets = dataset.get_task_targets(task_name)
    valid_indices = [i for i, target in enumerate(targets) if target is not None]
    valid_targets = [targets[i] for i in valid_indices]
    
    if not valid_targets:
        raise ValueError(f"任务'{task_name}'没有有效的目标值")
    
    # 将连续值离散化为分层标签
    target_bins = pd.cut(valid_targets, bins=n_bins, labels=False, duplicates='drop')
    
    # 分层划分
    indices = np.array(valid_indices)
    
    # 第一次划分：分离测试集
    if test_ratio > 0:
        train_val_indices, test_indices, _, _ = train_test_split(
            indices, target_bins,
            test_size=test_ratio,
            stratify=target_bins,
            random_state=random_state
        )
        
        # 更新分层标签
        train_val_bins = target_bins[np.isin(indices, train_val_indices)]
    else:
        train_val_indices = indices
        test_indices = np.array([])
        train_val_bins = target_bins
    
    # 第二次划分：分离训练集和验证集
    if val_ratio > 0 and len(train_val_indices) > 0:
        val_size = val_ratio / (train_ratio + val_ratio)
        train_indices, val_indices, _, _ = train_test_split(
            train_val_indices, train_val_bins,
            test_size=val_size,
            stratify=train_val_bins,
            random_state=random_state
        )
    else:
        train_indices = train_val_indices
        val_indices = np.array([])
    
    # 创建子数据集
    train_dataset = dataset.split_by_indices(train_indices.tolist())
    val_dataset = dataset.split_by_indices(val_indices.tolist()) if len(val_indices) > 0 else None
    test_dataset = dataset.split_by_indices(test_indices.tolist()) if len(test_indices) > 0 else None
    
    logger.info(f"分层划分完成: 训练集={len(train_indices)}, "
               f"验证集={len(val_indices)}, 测试集={len(test_indices)}")
    
    return train_dataset, val_dataset, test_dataset


def cluster_split(
    dataset: ShennongDataset,
    train_ratio: float = 0.8,
    val_ratio: float = 0.1,
    test_ratio: float = 0.1,
    n_clusters: Optional[int] = None,
    fingerprint_type: str = 'morgan',
    random_state: Optional[int] = None
) -> Tuple[ShennongDataset, ShennongDataset, ShennongDataset]:
    """
    基于分子聚类的划分
    
    使用分子指纹进行聚类，然后按聚类分配数据集。
    
    Args:
        dataset: 输入数据集
        train_ratio: 训练集比例
        val_ratio: 验证集比例
        test_ratio: 测试集比例
        n_clusters: 聚类数，如果为None则自动确定
        fingerprint_type: 指纹类型 ('morgan', 'rdkit', 'topological')
        random_state: 随机种子
        
    Returns:
        (训练集, 验证集, 测试集)
    """
    # 验证比例
    if abs(train_ratio + val_ratio + test_ratio - 1.0) > 1e-6:
        raise ValueError("训练、验证、测试集比例之和必须为1")
    
    # 计算分子指纹
    fingerprints = []
    valid_indices = []
    
    for i, datapoint in enumerate(dataset.data):
        mol = Chem.MolFromSmiles(datapoint.smiles)
        if mol is None:
            continue
        
        if fingerprint_type == 'morgan':
            fp = rdMolDescriptors.GetMorganFingerprintAsBitVect(mol, 2, nBits=2048)
        elif fingerprint_type == 'rdkit':
            fp = Chem.RDKFingerprint(mol)
        elif fingerprint_type == 'topological':
            fp = rdMolDescriptors.GetHashedTopologicalTorsionFingerprintAsBitVect(mol)
        else:
            raise ValueError(f"不支持的指纹类型: {fingerprint_type}")
        
        # 转换为numpy数组
        fp_array = np.zeros((1,))
        DataStructs.ConvertToNumpyArray(fp, fp_array)
        fingerprints.append(fp_array)
        valid_indices.append(i)
    
    if not fingerprints:
        raise ValueError("没有有效的分子用于聚类")
    
    fingerprints = np.array(fingerprints)
    
    # 确定聚类数
    if n_clusters is None:
        n_clusters = min(max(len(valid_indices) // 10, 3), 50)
    
    # 执行聚类
    kmeans = KMeans(n_clusters=n_clusters, random_state=random_state, n_init=10)
    cluster_labels = kmeans.fit_predict(fingerprints)
    
    # 按聚类分配数据集
    cluster_indices = {}
    for idx, cluster_id in zip(valid_indices, cluster_labels):
        if cluster_id not in cluster_indices:
            cluster_indices[cluster_id] = []
        cluster_indices[cluster_id].append(idx)
    
    # 按聚类大小排序
    cluster_sets = list(cluster_indices.values())
    cluster_sets.sort(key=len, reverse=True)
    
    # 贪心分配
    train_indices, val_indices, test_indices = [], [], []
    train_size = int(len(valid_indices) * train_ratio)
    val_size = int(len(valid_indices) * val_ratio)
    
    for cluster_set in cluster_sets:
        current_sizes = [len(train_indices), len(val_indices), len(test_indices)]
        target_sizes = [train_size, val_size, len(valid_indices) - train_size - val_size]
        
        fill_ratios = [
            current / target if target > 0 else float('inf')
            for current, target in zip(current_sizes, target_sizes)
        ]
        
        min_ratio_idx = fill_ratios.index(min(fill_ratios))
        
        if min_ratio_idx == 0:
            train_indices.extend(cluster_set)
        elif min_ratio_idx == 1:
            val_indices.extend(cluster_set)
        else:
            test_indices.extend(cluster_set)
    
    # 创建子数据集
    train_dataset = dataset.split_by_indices(train_indices)
    val_dataset = dataset.split_by_indices(val_indices) if val_indices else None
    test_dataset = dataset.split_by_indices(test_indices) if test_indices else None
    
    logger.info(f"聚类划分完成: 训练集={len(train_indices)}, "
               f"验证集={len(val_indices)}, 测试集={len(test_indices)}, "
               f"聚类数={n_clusters}")
    
    return train_dataset, val_dataset, test_dataset


def antibacterial_aware_split(
    dataset: AntibacterialDataset,
    train_ratio: float = 0.8,
    val_ratio: float = 0.1,
    test_ratio: float = 0.1,
    split_by_strain: bool = True,
    split_by_mechanism: bool = False,
    random_state: Optional[int] = None
) -> Tuple[AntibacterialDataset, AntibacterialDataset, AntibacterialDataset]:
    """
    抗菌化合物感知的数据划分
    
    考虑细菌菌株和抗菌机制的分布，确保划分的合理性。
    
    Args:
        dataset: 抗菌数据集
        train_ratio: 训练集比例
        val_ratio: 验证集比例
        test_ratio: 测试集比例
        split_by_strain: 是否按菌株分层
        split_by_mechanism: 是否按机制分层
        random_state: 随机种子
        
    Returns:
        (训练集, 验证集, 测试集)
    """
    # 创建分层标签
    stratify_labels = []
    
    for datapoint in dataset.data:
        label_parts = []
        
        # 添加菌株信息
        if split_by_strain and hasattr(datapoint, 'bacterial_strain'):
            strain = datapoint.bacterial_strain or 'unknown'
            label_parts.append(f"strain_{strain}")
        
        # 添加机制信息
        if split_by_mechanism and hasattr(datapoint, 'mechanism_labels'):
            mechanisms = datapoint.mechanism_labels or ['unknown']
            # 使用主要机制（第一个）
            main_mechanism = mechanisms[0] if mechanisms else 'unknown'
            label_parts.append(f"mech_{main_mechanism}")
        
        # 组合标签
        if label_parts:
            stratify_labels.append('_'.join(label_parts))
        else:
            stratify_labels.append('default')
    
    # 如果有分层标签，使用分层划分
    if len(set(stratify_labels)) > 1:
        # 创建临时数据集用于分层
        temp_df = pd.DataFrame({
            'index': range(len(dataset)),
            'stratify': stratify_labels
        })
        
        # 分层划分
        train_indices, test_indices = train_test_split(
            temp_df['index'].values,
            test_size=test_ratio,
            stratify=temp_df['stratify'].values,
            random_state=random_state
        )
        
        if val_ratio > 0:
            train_stratify = temp_df.loc[temp_df['index'].isin(train_indices), 'stratify'].values
            val_size = val_ratio / (train_ratio + val_ratio)
            
            train_indices, val_indices = train_test_split(
                train_indices,
                test_size=val_size,
                stratify=train_stratify,
                random_state=random_state
            )
        else:
            val_indices = []
    else:
        # 回退到随机划分
        logger.warning("无法进行分层划分，使用随机划分")
        return random_split(dataset, train_ratio, val_ratio, test_ratio, random_state)
    
    # 创建子数据集
    train_dataset = dataset.split_by_indices(train_indices.tolist())
    val_dataset = dataset.split_by_indices(val_indices.tolist()) if len(val_indices) > 0 else None
    test_dataset = dataset.split_by_indices(test_indices.tolist())
    
    logger.info(f"抗菌感知划分完成: 训练集={len(train_indices)}, "
               f"验证集={len(val_indices)}, 测试集={len(test_indices)}")
    
    return train_dataset, val_dataset, test_dataset
