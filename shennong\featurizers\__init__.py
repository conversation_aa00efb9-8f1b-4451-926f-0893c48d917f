# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-27
# 描述: 神农框架特征化器模块初始化

"""
神农框架特征化器模块

提供分子特征化功能，包括原子特征、键特征、分子描述符等。
集成Chemprop特征化器并扩展抗菌化合物特异性特征。

主要组件:
- atom: 原子特征化器
- bond: 键特征化器
- molecule: 分子特征化器
- adaptive_descriptors: 自适应描述符处理器
- antibacterial: 抗菌特异性特征化器
"""

# 安全导入，避免缺失模块导致的错误
try:
    from .atom import AntibacterialAtomFeaturizer, EnhancedAtomFeaturizer
except ImportError:
    AntibacterialAtomFeaturizer = None
    EnhancedAtomFeaturizer = None

try:
    from .bond import AntibacterialBondFeaturizer, EnhancedBondFeaturizer
except ImportError:
    AntibacterialBondFeaturizer = None
    EnhancedBondFeaturizer = None

from .molecule import MoleculeFeaturizer, MordredFeaturizer, RDKitFeaturizer, MordredDescriptorManager

try:
    from .adaptive_descriptors import AdaptiveDescriptorProcessor
except ImportError:
    AdaptiveDescriptorProcessor = None

try:
    from .antibacterial import AntibacterialFeaturizer, PharmacophoreFeaturizer
except ImportError:
    AntibacterialFeaturizer = None
    PharmacophoreFeaturizer = None

# 动态构建__all__列表，只包含成功导入的组件
__all__ = []

# 分子特征化器（总是可用）
__all__.extend(['MoleculeFeaturizer', 'MordredFeaturizer', 'RDKitFeaturizer', 'MordredDescriptorManager'])

# 可选组件
if AntibacterialAtomFeaturizer is not None:
    __all__.extend(['AntibacterialAtomFeaturizer', 'EnhancedAtomFeaturizer'])

if AntibacterialBondFeaturizer is not None:
    __all__.extend(['AntibacterialBondFeaturizer', 'EnhancedBondFeaturizer'])

if AdaptiveDescriptorProcessor is not None:
    __all__.append('AdaptiveDescriptorProcessor')

if AntibacterialFeaturizer is not None:
    __all__.extend(['AntibacterialFeaturizer', 'PharmacophoreFeaturizer'])
