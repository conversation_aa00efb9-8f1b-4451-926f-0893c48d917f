<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1000" viewBox="0 0 1400 1000" xmlns="http://www.w3.org/2000/svg">
  <!-- 定义样式和渐变 -->
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="inputGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#E8F4FD;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#D1E7DD;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="gnnGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#E8F5E8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#C8E6C9;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="expertGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFF8E1;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFECB3;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="attentionGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FCE4EC;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F8BBD9;stop-opacity:1" />
    </linearGradient>

    <!-- 阴影滤镜 -->
    <filter id="dropShadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="3" dy="3" stdDeviation="4" flood-color="#000000" flood-opacity="0.25"/>
    </filter>

    <!-- 箭头标记 -->
    <marker id="arrow" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto" markerUnits="strokeWidth">
      <polygon points="0 0, 12 4, 0 8" fill="#2E7D32" />
    </marker>

    <marker id="redArrow" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto" markerUnits="strokeWidth">
      <polygon points="0 0, 12 4, 0 8" fill="#C62828" />
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="700" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#1B5E20">
    Shennong Framework: Technical Architecture for Antibacterial Activity Prediction
  </text>

  <!-- 输入层详细 -->
  <g id="detailed-input">
    <rect x="50" y="80" width="300" height="120" rx="10" fill="url(#inputGrad)" stroke="#1565C0" stroke-width="2" filter="url(#dropShadow)"/>
    <text x="200" y="105" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#0D47A1">Input Processing</text>

    <!-- SMILES处理 -->
    <rect x="70" y="120" width="120" height="35" rx="5" fill="#BBDEFB" stroke="#1976D2" stroke-width="1"/>
    <text x="130" y="135" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#0D47A1">SMILES Input</text>
    <text x="130" y="148" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">Molecular Structure</text>

    <!-- 活性标签 -->
    <rect x="210" y="120" width="120" height="35" rx="5" fill="#BBDEFB" stroke="#1976D2" stroke-width="1"/>
    <text x="270" y="135" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#0D47A1">Activity Label</text>
    <text x="270" y="148" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">MIC (μg/mL)</text>

    <!-- 数据增强 -->
    <rect x="120" y="165" width="120" height="25" rx="3" fill="#90CAF9" stroke="#1976D2" stroke-width="1"/>
    <text x="180" y="180" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#333">Data Augmentation</text>
  </g>

  <!-- 特征化层详细 -->
  <g id="detailed-featurization">
    <!-- 图特征化详细 -->
    <rect x="50" y="240" width="280" height="160" rx="8" fill="url(#gnnGrad)" stroke="#2E7D32" stroke-width="2" filter="url(#dropShadow)"/>
    <text x="190" y="265" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" font-weight="bold" fill="#1B5E20">Graph Neural Network Branch</text>

    <!-- 原子特征 -->
    <rect x="70" y="280" width="80" height="30" rx="4" fill="#A5D6A7" stroke="#4CAF50" stroke-width="1"/>
    <text x="110" y="295" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="#1B5E20">Atom Features</text>
    <text x="110" y="305" text-anchor="middle" font-family="Arial, sans-serif" font-size="7" fill="#333">Element, Hybridization</text>

    <!-- 键特征 -->
    <rect x="170" y="280" width="80" height="30" rx="4" fill="#A5D6A7" stroke="#4CAF50" stroke-width="1"/>
    <text x="210" y="295" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="#1B5E20">Bond Features</text>
    <text x="210" y="305" text-anchor="middle" font-family="Arial, sans-serif" font-size="7" fill="#333">Type, Aromaticity</text>

    <!-- GCN层 -->
    <rect x="70" y="325" width="50" height="25" rx="3" fill="#81C784" stroke="#4CAF50" stroke-width="1"/>
    <text x="95" y="340" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">GCN₁</text>

    <rect x="140" y="325" width="50" height="25" rx="3" fill="#81C784" stroke="#4CAF50" stroke-width="1"/>
    <text x="165" y="340" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">GCN₂</text>

    <rect x="210" y="325" width="50" height="25" rx="3" fill="#81C784" stroke="#4CAF50" stroke-width="1"/>
    <text x="235" y="340" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">GCN₃</text>

    <!-- 图池化 -->
    <rect x="120" y="365" width="100" height="25" rx="3" fill="#66BB6A" stroke="#4CAF50" stroke-width="1"/>
    <text x="170" y="380" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#333">Graph Pooling</text>

    <!-- Mordred特征化详细 -->
    <rect x="370" y="240" width="280" height="160" rx="8" fill="url(#expertGrad)" stroke="#F57C00" stroke-width="2" filter="url(#dropShadow)"/>
    <text x="510" y="265" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" font-weight="bold" fill="#E65100">Expert Feature Branch</text>

    <!-- 描述符类别 -->
    <rect x="390" y="280" width="70" height="30" rx="4" fill="#FFCC80" stroke="#FF9800" stroke-width="1"/>
    <text x="425" y="295" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" font-weight="bold" fill="#E65100">Topological</text>
    <text x="425" y="305" text-anchor="middle" font-family="Arial, sans-serif" font-size="7" fill="#333">600+ descriptors</text>

    <rect x="480" y="280" width="70" height="30" rx="4" fill="#FFCC80" stroke="#FF9800" stroke-width="1"/>
    <text x="515" y="295" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" font-weight="bold" fill="#E65100">Geometric</text>
    <text x="515" y="305" text-anchor="middle" font-family="Arial, sans-serif" font-size="7" fill="#333">400+ descriptors</text>

    <rect x="570" y="280" width="70" height="30" rx="4" fill="#FFCC80" stroke="#FF9800" stroke-width="1"/>
    <text x="605" y="295" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" font-weight="bold" fill="#E65100">Electronic</text>
    <text x="605" y="305" text-anchor="middle" font-family="Arial, sans-serif" font-size="7" fill="#333">800+ descriptors</text>

    <!-- 全连接层 -->
    <rect x="390" y="325" width="60" height="25" rx="3" fill="#FFB74D" stroke="#FF9800" stroke-width="1"/>
    <text x="420" y="340" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">Dense₁</text>

    <rect x="470" y="325" width="60" height="25" rx="3" fill="#FFB74D" stroke="#FF9800" stroke-width="1"/>
    <text x="500" y="340" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">Dense₂</text>

    <rect x="550" y="325" width="60" height="25" rx="3" fill="#FFB74D" stroke="#FF9800" stroke-width="1"/>
    <text x="580" y="340" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">Dense₃</text>

    <!-- Dropout -->
    <rect x="480" y="365" width="80" height="25" rx="3" fill="#FFA726" stroke="#FF9800" stroke-width="1"/>
    <text x="520" y="380" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#333">Dropout (0.1)</text>
  </g>

  <!-- 化学导向注意力机制详细 -->
  <g id="detailed-attention">
    <rect x="700" y="240" width="350" height="300" rx="10" fill="url(#attentionGrad)" stroke="#AD1457" stroke-width="2" filter="url(#dropShadow)"/>
    <text x="875" y="265" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#880E4F">Chemistry-Guided Multi-Head Attention</text>

    <!-- 官能团注意力详细 -->
    <rect x="720" y="285" width="300" height="60" rx="6" fill="#F8BBD9" stroke="#E91E63" stroke-width="1"/>
    <text x="870" y="305" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#880E4F">Functional Group Attention (4 heads)</text>

    <rect x="730" y="315" width="60" height="20" rx="2" fill="#F48FB1" stroke="#E91E63" stroke-width="1"/>
    <text x="760" y="327" text-anchor="middle" font-family="Arial, sans-serif" font-size="7" fill="#333">H-bond</text>

    <rect x="800" y="315" width="60" height="20" rx="2" fill="#F48FB1" stroke="#E91E63" stroke-width="1"/>
    <text x="830" y="327" text-anchor="middle" font-family="Arial, sans-serif" font-size="7" fill="#333">Aromatic</text>

    <rect x="870" y="315" width="60" height="20" rx="2" fill="#F48FB1" stroke="#E91E63" stroke-width="1"/>
    <text x="900" y="327" text-anchor="middle" font-family="Arial, sans-serif" font-size="7" fill="#333">Halogen</text>

    <rect x="940" y="315" width="60" height="20" rx="2" fill="#F48FB1" stroke="#E91E63" stroke-width="1"/>
    <text x="970" y="327" text-anchor="middle" font-family="Arial, sans-serif" font-size="7" fill="#333">Polar</text>

    <!-- 分子性质注意力详细 -->
    <rect x="720" y="355" width="300" height="60" rx="6" fill="#F8BBD9" stroke="#E91E63" stroke-width="1"/>
    <text x="870" y="375" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#880E4F">Molecular Property Attention (4 heads)</text>

    <rect x="730" y="385" width="60" height="20" rx="2" fill="#F48FB1" stroke="#E91E63" stroke-width="1"/>
    <text x="760" y="397" text-anchor="middle" font-family="Arial, sans-serif" font-size="7" fill="#333">LogP</text>

    <rect x="800" y="385" width="60" height="20" rx="2" fill="#F48FB1" stroke="#E91E63" stroke-width="1"/>
    <text x="830" y="397" text-anchor="middle" font-family="Arial, sans-serif" font-size="7" fill="#333">Size</text>

    <rect x="870" y="385" width="60" height="20" rx="2" fill="#F48FB1" stroke="#E91E63" stroke-width="1"/>
    <text x="900" y="397" text-anchor="middle" font-family="Arial, sans-serif" font-size="7" fill="#333">Flexibility</text>

    <rect x="940" y="385" width="60" height="20" rx="2" fill="#F48FB1" stroke="#E91E63" stroke-width="1"/>
    <text x="970" y="397" text-anchor="middle" font-family="Arial, sans-serif" font-size="7" fill="#333">Charge</text>

    <!-- 活性相关注意力详细 -->
    <rect x="720" y="425" width="300" height="60" rx="6" fill="#F8BBD9" stroke="#E91E63" stroke-width="1"/>
    <text x="870" y="445" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#880E4F">Activity-Related Attention (3 heads)</text>

    <rect x="750" y="455" width="70" height="20" rx="2" fill="#F48FB1" stroke="#E91E63" stroke-width="1"/>
    <text x="785" y="467" text-anchor="middle" font-family="Arial, sans-serif" font-size="7" fill="#333">ADMET</text>

    <rect x="830" y="455" width="70" height="20" rx="2" fill="#F48FB1" stroke="#E91E63" stroke-width="1"/>
    <text x="865" y="467" text-anchor="middle" font-family="Arial, sans-serif" font-size="7" fill="#333">Toxicity</text>

    <rect x="910" y="455" width="70" height="20" rx="2" fill="#F48FB1" stroke="#E91E63" stroke-width="1"/>
    <text x="945" y="467" text-anchor="middle" font-family="Arial, sans-serif" font-size="7" fill="#333">Selectivity</text>

    <!-- 注意力融合 -->
    <rect x="780" y="500" width="140" height="30" rx="5" fill="#EC407A" stroke="#E91E63" stroke-width="1"/>
    <text x="850" y="520" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#FFF">Attention Fusion</text>
  </g>

  <!-- 特征融合与预测层 -->
  <g id="fusion-prediction">
    <!-- 特征融合详细 -->
    <rect x="1100" y="300" width="200" height="120" rx="8" fill="#E8F5E8" stroke="#4CAF50" stroke-width="2" filter="url(#dropShadow)"/>
    <text x="1200" y="325" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#2E7D32">Feature Fusion</text>

    <!-- 自适应融合 -->
    <rect x="1120" y="340" width="80" height="25" rx="3" fill="#A5D6A7" stroke="#4CAF50" stroke-width="1"/>
    <text x="1160" y="355" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">Adaptive Fusion</text>

    <!-- 特征对齐 -->
    <rect x="1210" y="340" width="80" height="25" rx="3" fill="#A5D6A7" stroke="#4CAF50" stroke-width="1"/>
    <text x="1250" y="355" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">Feature Align</text>

    <!-- 残差连接 -->
    <rect x="1165" y="375" width="90" height="25" rx="3" fill="#81C784" stroke="#4CAF50" stroke-width="1"/>
    <text x="1210" y="390" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">Residual Connect</text>

    <!-- 预测头详细 -->
    <rect x="1100" y="450" width="200" height="100" rx="8" fill="#FFF8E1" stroke="#FF8F00" stroke-width="2" filter="url(#dropShadow)"/>
    <text x="1200" y="475" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#E65100">Prediction Heads</text>

    <!-- 活性预测 -->
    <rect x="1120" y="490" width="70" height="25" rx="3" fill="#FFCC80" stroke="#FF9800" stroke-width="1"/>
    <text x="1155" y="505" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">Activity</text>

    <!-- 不确定性估计 -->
    <rect x="1210" y="490" width="70" height="25" rx="3" fill="#FFCC80" stroke="#FF9800" stroke-width="1"/>
    <text x="1245" y="505" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">Uncertainty</text>

    <!-- 输出 -->
    <rect x="1140" y="525" width="120" height="20" rx="3" fill="#FFB74D" stroke="#FF9800" stroke-width="1"/>
    <text x="1200" y="538" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#333">MIC (μg/mL) ± σ</text>
  </g>

  <!-- 可解释性模块详细 -->
  <g id="detailed-interpretability">
    <rect x="50" y="600" width="1250" height="200" rx="10" fill="#FFF8E1" stroke="#FF8F00" stroke-width="2" filter="url(#dropShadow)"/>
    <text x="675" y="625" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#E65100">Chemical Interpretability Analysis</text>

    <!-- 原子级解释 -->
    <rect x="80" y="650" width="150" height="80" rx="6" fill="#FFECB3" stroke="#FFC107" stroke-width="1"/>
    <text x="155" y="670" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#E65100">Atom-Level</text>
    <text x="155" y="685" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">• Atom Importance</text>
    <text x="155" y="700" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">• Bond Contribution</text>
    <text x="155" y="715" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">• Attention Heatmap</text>

    <!-- 基团级解释 -->
    <rect x="260" y="650" width="150" height="80" rx="6" fill="#FFECB3" stroke="#FFC107" stroke-width="1"/>
    <text x="335" y="670" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#E65100">Group-Level</text>
    <text x="335" y="685" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">• Functional Groups</text>
    <text x="335" y="700" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">• Pharmacophores</text>
    <text x="335" y="715" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">• SMARTS Patterns</text>

    <!-- 性质级解释 -->
    <rect x="440" y="650" width="150" height="80" rx="6" fill="#FFECB3" stroke="#FFC107" stroke-width="1"/>
    <text x="515" y="670" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#E65100">Property-Level</text>
    <text x="515" y="685" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">• Descriptor Ranking</text>
    <text x="515" y="700" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">• SHAP Values</text>
    <text x="515" y="715" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">• Feature Importance</text>

    <!-- 分子级解释 -->
    <rect x="620" y="650" width="150" height="80" rx="6" fill="#FFECB3" stroke="#FFC107" stroke-width="1"/>
    <text x="695" y="670" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#E65100">Molecule-Level</text>
    <text x="695" y="685" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">• SAR Analysis</text>
    <text x="695" y="700" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">• Activity Cliffs</text>
    <text x="695" y="715" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">• Similarity Maps</text>

    <!-- 优化指导 -->
    <rect x="800" y="650" width="150" height="80" rx="6" fill="#FFECB3" stroke="#FFC107" stroke-width="1"/>
    <text x="875" y="670" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#E65100">Optimization</text>
    <text x="875" y="685" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">• Structure Suggestions</text>
    <text x="875" y="700" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">• Property Tuning</text>
    <text x="875" y="715" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">• Lead Optimization</text>

    <!-- 报告生成 -->
    <rect x="980" y="650" width="150" height="80" rx="6" fill="#FFECB3" stroke="#FFC107" stroke-width="1"/>
    <text x="1055" y="670" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#E65100">Report Generation</text>
    <text x="1055" y="685" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">• Chemical Explanation</text>
    <text x="1055" y="700" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">• Confidence Factors</text>
    <text x="1055" y="715" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">• Visualization</text>

    <!-- 验证模块 -->
    <rect x="1160" y="650" width="120" height="80" rx="6" fill="#FFECB3" stroke="#FFC107" stroke-width="1"/>
    <text x="1220" y="670" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#E65100">Validation</text>
    <text x="1220" y="685" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">• Cross-validation</text>
    <text x="1220" y="700" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">• External Test</text>
    <text x="1220" y="715" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">• Benchmark</text>
  </g>

  <!-- 数据流箭头 -->
  <!-- 输入到特征化 -->
  <line x1="200" y1="200" x2="190" y2="240" stroke="#2E7D32" stroke-width="3" marker-end="url(#arrow)"/>
  <line x1="200" y1="200" x2="510" y2="240" stroke="#2E7D32" stroke-width="3" marker-end="url(#arrow)"/>

  <!-- 特征化到注意力 -->
  <line x1="330" y1="320" x2="700" y2="380" stroke="#2E7D32" stroke-width="3" marker-end="url(#arrow)"/>
  <line x1="650" y1="320" x2="700" y2="380" stroke="#2E7D32" stroke-width="3" marker-end="url(#arrow)"/>

  <!-- 注意力到融合 -->
  <line x1="1050" y1="390" x2="1100" y2="360" stroke="#2E7D32" stroke-width="3" marker-end="url(#arrow)"/>

  <!-- 融合到预测 -->
  <line x1="1200" y1="420" x2="1200" y2="450" stroke="#2E7D32" stroke-width="3" marker-end="url(#arrow)"/>

  <!-- 预测到可解释性 -->
  <line x1="1200" y1="550" x2="1200" y2="600" stroke="#C62828" stroke-width="2" marker-end="url(#redArrow)"/>

  <!-- 注意力到可解释性 -->
  <line x1="875" y1="540" x2="875" y2="600" stroke="#C62828" stroke-width="2" stroke-dasharray="5,5" marker-end="url(#redArrow)"/>

  <!-- 技术规格 -->
  <g id="technical-specs">
    <rect x="50" y="850" width="600" height="120" rx="5" fill="#F5F5F5" stroke="#BDBDBD" stroke-width="1"/>
    <text x="350" y="875" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">Technical Specifications</text>

    <text x="70" y="895" font-family="Arial, sans-serif" font-size="9" fill="#333">• Graph Neural Network: 3-layer GCN with 300-dim hidden states</text>
    <text x="70" y="910" font-family="Arial, sans-serif" font-size="9" fill="#333">• Expert Features: 1827 Mordred descriptors with 3-layer MLP</text>
    <text x="70" y="925" font-family="Arial, sans-serif" font-size="9" fill="#333">• Attention Mechanism: 11 specialized heads (4+4+3) with 64-dim each</text>
    <text x="70" y="940" font-family="Arial, sans-serif" font-size="9" fill="#333">• Fusion Strategy: Adaptive weighted fusion with residual connections</text>
    <text x="70" y="955" font-family="Arial, sans-serif" font-size="9" fill="#333">• Output: Regression (MIC) + Uncertainty estimation with confidence intervals</text>
  </g>

  <!-- 创新点标注 -->
  <g id="innovations">
    <rect x="700" y="850" width="650" height="120" rx="5" fill="#E8F5E8" stroke="#4CAF50" stroke-width="1"/>
    <text x="1025" y="875" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#2E7D32">Key Innovations</text>

    <text x="720" y="895" font-family="Arial, sans-serif" font-size="9" fill="#333">✓ Chemistry-guided attention with functional group, property, and activity heads</text>
    <text x="720" y="910" font-family="Arial, sans-serif" font-size="9" fill="#333">✓ Dual-modal architecture combining graph structure and expert knowledge</text>
    <text x="720" y="925" font-family="Arial, sans-serif" font-size="9" fill="#333">✓ Multi-level interpretability from atoms to molecules with optimization guidance</text>
    <text x="720" y="940" font-family="Arial, sans-serif" font-size="9" fill="#333">✓ Focus on antibacterial activity prediction without mechanism speculation</text>
    <text x="720" y="955" font-family="Arial, sans-serif" font-size="9" fill="#333">✓ Chemist-friendly explanations with actionable molecular design insights</text>
  </g>

</svg>