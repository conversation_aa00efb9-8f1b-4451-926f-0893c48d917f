#!/usr/bin/env python3
# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-30
# 描述: 神农框架与ChemProp基线对比实验 (修复版)

"""
神农框架与ChemProp基线对比实验
修复ChemProp 2.2.0 API调用问题
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import roc_auc_score, f1_score, precision_score, recall_score, accuracy_score
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import time
import warnings
warnings.filterwarnings('ignore')

def load_data():
    """加载数据"""
    print("📊 加载数据...")
    
    train_df = pd.read_csv('../../data/examples/train_data.csv')
    test_df = pd.read_csv('../../data/examples/test_data.csv')
    
    print(f"训练集: {len(train_df)} 样本")
    print(f"测试集: {len(test_df)} 样本")
    print(f"训练集活性分布: {train_df['activity'].value_counts().to_dict()}")
    
    return train_df, test_df

def generate_mordred_features(smiles_list):
    """生成Mordred特征"""
    print("📊 生成Mordred分子描述符...")
    
    from mordred import Calculator, descriptors
    from rdkit import Chem
    
    calc = Calculator(descriptors, ignore_3D=True)
    
    mols = [Chem.MolFromSmiles(smi) for smi in smiles_list]
    features = calc.pandas(mols)
    
    # 处理无效值
    features = features.select_dtypes(include=[np.number])
    features = features.fillna(0)
    
    return features

class ShennongNet(nn.Module):
    """神农框架简化神经网络"""
    def __init__(self, input_dim):
        super(ShennongNet, self).__init__()
        self.fc1 = nn.Linear(input_dim, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, 128)
        self.fc4 = nn.Linear(128, 1)
        self.dropout = nn.Dropout(0.3)
        self.relu = nn.ReLU()
        self.sigmoid = nn.Sigmoid()
        
    def forward(self, x):
        x = self.relu(self.fc1(x))
        x = self.dropout(x)
        x = self.relu(self.fc2(x))
        x = self.dropout(x)
        x = self.relu(self.fc3(x))
        x = self.dropout(x)
        x = self.sigmoid(self.fc4(x))
        return x

def train_random_forest(X_train, y_train, X_test, y_test):
    """训练随机森林模型"""
    print("🌲 训练随机森林模型...")
    
    start_time = time.time()
    rf_model = RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=-1)
    rf_model.fit(X_train, y_train)
    train_time = time.time() - start_time
    
    start_time = time.time()
    pred_proba = rf_model.predict_proba(X_test)[:, 1]
    pred = rf_model.predict(X_test)
    pred_time = time.time() - start_time
    
    # 计算指标
    auc = roc_auc_score(y_test, pred_proba)
    f1 = f1_score(y_test, pred)
    precision = precision_score(y_test, pred)
    recall = recall_score(y_test, pred)
    accuracy = accuracy_score(y_test, pred)
    
    print(f"✅ 随机森林完成 (训练: {train_time:.1f}s, 预测: {pred_time:.3f}s)")
    print(f"   AUC: {auc:.4f}, F1: {f1:.4f}, Precision: {precision:.4f}")
    
    return {
        'auc': auc, 'f1': f1, 'precision': precision, 'recall': recall, 'accuracy': accuracy,
        'train_time': train_time, 'pred_time': pred_time
    }

def train_neural_network(X_train, y_train, X_test, y_test):
    """训练神经网络模型"""
    print("🧠 训练神经网络模型 (神农框架简化版)...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 准备数据
    X_train_tensor = torch.FloatTensor(X_train).to(device)
    y_train_tensor = torch.FloatTensor(y_train).unsqueeze(1).to(device)
    X_test_tensor = torch.FloatTensor(X_test).to(device)
    
    train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
    train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
    
    # 初始化模型
    model = ShennongNet(X_train.shape[1]).to(device)
    criterion = nn.BCELoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001)
    
    # 训练
    start_time = time.time()
    model.train()
    for epoch in range(50):
        epoch_loss = 0
        for batch_x, batch_y in train_loader:
            optimizer.zero_grad()
            outputs = model(batch_x)
            loss = criterion(outputs, batch_y)
            loss.backward()
            optimizer.step()
            epoch_loss += loss.item()
        
        if (epoch + 1) % 10 == 0:
            print(f"  Epoch {epoch+1}/50, Loss: {epoch_loss/len(train_loader):.4f}")
    
    train_time = time.time() - start_time
    
    # 预测
    start_time = time.time()
    model.eval()
    with torch.no_grad():
        pred_proba = model(X_test_tensor).cpu().numpy().flatten()
        pred = (pred_proba > 0.5).astype(int)
    pred_time = time.time() - start_time
    
    # 计算指标
    auc = roc_auc_score(y_test, pred_proba)
    f1 = f1_score(y_test, pred)
    precision = precision_score(y_test, pred)
    recall = recall_score(y_test, pred)
    accuracy = accuracy_score(y_test, pred)
    
    print(f"✅ 神经网络完成 (训练: {train_time:.1f}s, 预测: {pred_time:.3f}s)")
    print(f"   AUC: {auc:.4f}, F1: {f1:.4f}, Precision: {precision:.4f}")
    
    return {
        'auc': auc, 'f1': f1, 'precision': precision, 'recall': recall, 'accuracy': accuracy,
        'train_time': train_time, 'pred_time': pred_time
    }

def run_chemprop_baseline():
    """运行ChemProp基线 (使用新API)"""
    print("🧪 运行ChemProp基线 (使用Python API)...")
    
    try:
        import chemprop
        from lightning import pytorch as pl
        
        print("✅ ChemProp导入成功")
        
        # 准备数据
        train_data = pd.read_csv("../../data/examples/chemprop_train.csv")
        test_data = pd.read_csv("../../data/examples/chemprop_test.csv")
        test_labels = pd.read_csv("../../data/examples/test_labels.csv")
        
        print(f"训练数据: {len(train_data)} 样本")
        print(f"测试数据: {len(test_data)} 样本")
        
        # 使用ChemProp的简化训练流程
        print("🚀 训练ChemProp模型...")
        start_time = time.time()
        
        # 创建简单的配置
        from chemprop import data, featurizers, models
        
        # 准备数据
        smiles_train = train_data['smiles'].tolist()
        targets_train = train_data['activity'].values.reshape(-1, 1)
        
        smiles_test = test_data['smiles'].tolist()
        
        # 创建分子数据集
        mol_featurizer = featurizers.SimpleMoleculeMolGraphFeaturizer()
        
        train_dataset = data.MoleculeDataset([
            data.MoleculeDatapoint.from_smi(smi, y) 
            for smi, y in zip(smiles_train, targets_train)
        ])
        
        test_dataset = data.MoleculeDataset([
            data.MoleculeDatapoint.from_smi(smi) 
            for smi in smiles_test
        ])
        
        # 创建数据加载器
        train_loader = data.build_dataloader(train_dataset, batch_size=32, shuffle=True)
        test_loader = data.build_dataloader(test_dataset, batch_size=32, shuffle=False)
        
        # 创建模型
        model = models.MPNN(
            message_hidden_dim=300,
            message_depth=3,
            ffn_hidden_dim=300,
            ffn_num_layers=2,
            output_dim=1,
            task_type='classification'
        )
        
        # 训练模型
        trainer = pl.Trainer(
            max_epochs=10,
            accelerator='gpu' if torch.cuda.is_available() else 'cpu',
            devices=1,
            logger=False,
            enable_checkpointing=False,
            enable_progress_bar=False
        )
        
        trainer.fit(model, train_loader)
        train_time = time.time() - start_time
        
        # 预测
        print("🔮 ChemProp预测...")
        start_time = time.time()
        
        model.eval()
        predictions = []
        with torch.no_grad():
            for batch in test_loader:
                pred = model(batch)
                predictions.extend(torch.sigmoid(pred).cpu().numpy().flatten())
        
        pred_time = time.time() - start_time
        
        # 计算指标
        y_true = test_labels['activity'].values
        y_pred_proba = np.array(predictions)
        y_pred = (y_pred_proba > 0.5).astype(int)
        
        auc = roc_auc_score(y_true, y_pred_proba)
        f1 = f1_score(y_true, y_pred)
        precision = precision_score(y_true, y_pred)
        recall = recall_score(y_true, y_pred)
        accuracy = accuracy_score(y_true, y_pred)
        
        print(f"✅ ChemProp完成 (训练: {train_time:.1f}s, 预测: {pred_time:.3f}s)")
        print(f"   AUC: {auc:.4f}, F1: {f1:.4f}, Precision: {precision:.4f}")
        
        return {
            'auc': auc, 'f1': f1, 'precision': precision, 'recall': recall, 'accuracy': accuracy,
            'train_time': train_time, 'pred_time': pred_time
        }
        
    except Exception as e:
        print(f"❌ ChemProp运行出错: {e}")
        print("🔄 使用简化的图神经网络替代...")
        
        # 使用简化的图神经网络作为替代
        return run_simple_gnn_baseline()

def run_simple_gnn_baseline():
    """运行简化的图神经网络基线"""
    print("🕸️ 运行简化图神经网络基线...")
    
    try:
        import torch_geometric
        from torch_geometric.data import Data, DataLoader
        from torch_geometric.nn import GCNConv, global_mean_pool
        from rdkit import Chem
        from rdkit.Chem import rdMolDescriptors
        
        # 简化的分子图表示
        def smiles_to_graph(smiles):
            mol = Chem.MolFromSmiles(smiles)
            if mol is None:
                return None
            
            # 原子特征 (简化)
            atom_features = []
            for atom in mol.GetAtoms():
                features = [
                    atom.GetAtomicNum(),
                    atom.GetDegree(),
                    atom.GetFormalCharge(),
                    int(atom.GetHybridization()),
                    int(atom.GetIsAromatic())
                ]
                atom_features.append(features)
            
            # 边信息
            edge_indices = []
            for bond in mol.GetBonds():
                i = bond.GetBeginAtomIdx()
                j = bond.GetEndAtomIdx()
                edge_indices.extend([[i, j], [j, i]])
            
            if len(edge_indices) == 0:
                edge_indices = [[0, 0]]  # 自环
            
            return Data(
                x=torch.FloatTensor(atom_features),
                edge_index=torch.LongTensor(edge_indices).t().contiguous()
            )
        
        class SimpleGNN(nn.Module):
            def __init__(self, input_dim=5, hidden_dim=64):
                super(SimpleGNN, self).__init__()
                self.conv1 = GCNConv(input_dim, hidden_dim)
                self.conv2 = GCNConv(hidden_dim, hidden_dim)
                self.classifier = nn.Linear(hidden_dim, 1)
                self.dropout = nn.Dropout(0.3)
                
            def forward(self, data):
                x, edge_index, batch = data.x, data.edge_index, data.batch
                
                x = torch.relu(self.conv1(x, edge_index))
                x = self.dropout(x)
                x = torch.relu(self.conv2(x, edge_index))
                
                x = global_mean_pool(x, batch)
                x = torch.sigmoid(self.classifier(x))
                return x
        
        # 准备数据
        train_data = pd.read_csv("../../data/examples/chemprop_train.csv")
        test_data = pd.read_csv("../../data/examples/chemprop_test.csv")
        test_labels = pd.read_csv("../../data/examples/test_labels.csv")
        
        # 转换为图数据
        train_graphs = []
        train_targets = []
        for _, row in train_data.iterrows():
            graph = smiles_to_graph(row['smiles'])
            if graph is not None:
                train_graphs.append(graph)
                train_targets.append(row['activity'])
        
        test_graphs = []
        for _, row in test_data.iterrows():
            graph = smiles_to_graph(row['smiles'])
            if graph is not None:
                test_graphs.append(graph)
        
        # 创建数据加载器
        train_loader = DataLoader(train_graphs, batch_size=32, shuffle=True)
        test_loader = DataLoader(test_graphs, batch_size=32, shuffle=False)
        
        # 训练模型
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model = SimpleGNN().to(device)
        optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
        criterion = nn.BCELoss()
        
        print("🚀 训练简化GNN模型...")
        start_time = time.time()
        
        model.train()
        for epoch in range(20):
            epoch_loss = 0
            for i, batch in enumerate(train_loader):
                batch = batch.to(device)
                targets = torch.FloatTensor([train_targets[j] for j in range(i*32, min((i+1)*32, len(train_targets)))]).unsqueeze(1).to(device)
                
                optimizer.zero_grad()
                outputs = model(batch)
                loss = criterion(outputs, targets)
                loss.backward()
                optimizer.step()
                epoch_loss += loss.item()
            
            if (epoch + 1) % 5 == 0:
                print(f"  Epoch {epoch+1}/20, Loss: {epoch_loss/len(train_loader):.4f}")
        
        train_time = time.time() - start_time
        
        # 预测
        print("🔮 GNN预测...")
        start_time = time.time()
        
        model.eval()
        predictions = []
        with torch.no_grad():
            for batch in test_loader:
                batch = batch.to(device)
                pred = model(batch)
                predictions.extend(pred.cpu().numpy().flatten())
        
        pred_time = time.time() - start_time
        
        # 计算指标
        y_true = test_labels['activity'].values
        y_pred_proba = np.array(predictions)
        y_pred = (y_pred_proba > 0.5).astype(int)
        
        auc = roc_auc_score(y_true, y_pred_proba)
        f1 = f1_score(y_true, y_pred)
        precision = precision_score(y_true, y_pred)
        recall = recall_score(y_true, y_pred)
        accuracy = accuracy_score(y_true, y_pred)
        
        print(f"✅ 简化GNN完成 (训练: {train_time:.1f}s, 预测: {pred_time:.3f}s)")
        print(f"   AUC: {auc:.4f}, F1: {f1:.4f}, Precision: {precision:.4f}")
        
        return {
            'auc': auc, 'f1': f1, 'precision': precision, 'recall': recall, 'accuracy': accuracy,
            'train_time': train_time, 'pred_time': pred_time
        }
        
    except Exception as e:
        print(f"❌ 简化GNN也失败: {e}")
        return None

def create_comparison_plot(results_summary):
    """创建对比图表"""
    print("📊 创建对比图表...")
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    methods = results_summary['Method']
    colors = ['lightcoral', 'lightblue', 'lightgreen']
    
    # AUC对比
    aucs = results_summary['AUC']
    axes[0,0].bar(methods, aucs, color=colors)
    axes[0,0].set_title('AUC Comparison', fontsize=14, fontweight='bold')
    axes[0,0].set_ylabel('AUC Score')
    axes[0,0].set_ylim(0, 1)
    for i, v in enumerate(aucs):
        axes[0,0].text(i, v + 0.01, f'{v:.3f}', ha='center', fontweight='bold')
    
    # F1对比
    f1s = results_summary['F1']
    axes[0,1].bar(methods, f1s, color=colors)
    axes[0,1].set_title('F1 Score Comparison', fontsize=14, fontweight='bold')
    axes[0,1].set_ylabel('F1 Score')
    axes[0,1].set_ylim(0, 1)
    for i, v in enumerate(f1s):
        axes[0,1].text(i, v + 0.01, f'{v:.3f}', ha='center', fontweight='bold')
    
    # 训练时间对比
    train_times = results_summary['Train_Time']
    axes[1,0].bar(methods, train_times, color=colors)
    axes[1,0].set_title('Training Time Comparison', fontsize=14, fontweight='bold')
    axes[1,0].set_ylabel('Time (seconds)')
    for i, v in enumerate(train_times):
        axes[1,0].text(i, v + max(train_times)*0.01, f'{v:.1f}s', ha='center', fontweight='bold')
    
    # 预测时间对比
    pred_times = results_summary['Pred_Time']
    axes[1,1].bar(methods, pred_times, color=colors)
    axes[1,1].set_title('Prediction Time Comparison', fontsize=14, fontweight='bold')
    axes[1,1].set_ylabel('Time (seconds)')
    for i, v in enumerate(pred_times):
        axes[1,1].text(i, v + max(pred_times)*0.01, f'{v:.3f}s', ha='center', fontweight='bold')
    
    # 调整x轴标签
    for ax in axes.flat:
        ax.tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    plt.savefig('../../data/examples/baseline_comparison_plot_fixed.png', dpi=300, bbox_inches='tight')
    print("💾 图表已保存: ../../data/examples/baseline_comparison_plot_fixed.png")

def main():
    """主函数"""
    print("🧬 神农框架 vs ChemProp 基线对比实验 (修复版)")
    print("=" * 60)
    
    # 加载数据
    train_df, test_df = load_data()
    
    # 生成Mordred特征
    print("\n📊 生成Mordred特征...")
    train_features = generate_mordred_features(train_df['smiles'].tolist())
    test_features = generate_mordred_features(test_df['smiles'].tolist())
    print(f"特征维度: {train_features.shape[1]}")
    
    # 数据预处理
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(train_features)
    X_test_scaled = scaler.transform(test_features)
    y_train = train_df['activity'].values
    y_test = test_df['activity'].values
    
    # 运行实验
    print("\n🚀 开始基线对比实验...")
    
    # 1. 随机森林
    rf_results = train_random_forest(X_train_scaled, y_train, X_test_scaled, y_test)
    
    # 2. 神经网络
    nn_results = train_neural_network(X_train_scaled, y_train, X_test_scaled, y_test)
    
    # 3. ChemProp/GNN
    gnn_results = run_chemprop_baseline()
    
    # 整理结果
    print("\n📊 整理实验结果...")
    
    results_data = {
        'Method': ['Graph Neural Net', 'Random Forest (Mordred)', 'Neural Net (Shennong)'],
        'AUC': [gnn_results['auc'] if gnn_results else 0, rf_results['auc'], nn_results['auc']],
        'F1': [gnn_results['f1'] if gnn_results else 0, rf_results['f1'], nn_results['f1']],
        'Precision': [gnn_results['precision'] if gnn_results else 0, rf_results['precision'], nn_results['precision']],
        'Recall': [gnn_results['recall'] if gnn_results else 0, rf_results['recall'], nn_results['recall']],
        'Accuracy': [gnn_results['accuracy'] if gnn_results else 0, rf_results['accuracy'], nn_results['accuracy']],
        'Train_Time': [gnn_results['train_time'] if gnn_results else 0, rf_results['train_time'], nn_results['train_time']],
        'Pred_Time': [gnn_results['pred_time'] if gnn_results else 0, rf_results['pred_time'], nn_results['pred_time']]
    }
    
    results_summary = pd.DataFrame(results_data)
    
    print("\n🏆 实验结果总结:")
    print(results_summary.round(4))
    
    # 保存结果
    results_summary.to_csv('../../data/examples/baseline_comparison_results_fixed.csv', index=False)
    print("\n💾 结果已保存到: ../../data/examples/baseline_comparison_results_fixed.csv")
    
    # 创建图表
    create_comparison_plot(results_summary)
    
    # 分析结果
    print("\n🔍 结果分析:")
    print("-" * 30)
    
    best_auc_idx = results_summary['AUC'].idxmax()
    best_f1_idx = results_summary['F1'].idxmax()
    
    print(f"🏆 最佳AUC: {results_summary.loc[best_auc_idx, 'Method']} ({results_summary.loc[best_auc_idx, 'AUC']:.4f})")
    print(f"🏆 最佳F1: {results_summary.loc[best_f1_idx, 'Method']} ({results_summary.loc[best_f1_idx, 'F1']:.4f})")
    
    if gnn_results:
        auc_improvement = nn_results['auc'] - gnn_results['auc']
        f1_improvement = nn_results['f1'] - gnn_results['f1']
        print(f"\n📈 神农框架 vs 图神经网络:")
        print(f"   AUC改进: {auc_improvement:+.4f} ({auc_improvement/gnn_results['auc']*100:+.1f}%)")
        print(f"   F1改进: {f1_improvement:+.4f} ({f1_improvement/gnn_results['f1']*100:+.1f}%)")
    
    print(f"\n🧬 神农尝百草，AI识良药 - 基线对比实验完成！")

if __name__ == "__main__":
    main()
