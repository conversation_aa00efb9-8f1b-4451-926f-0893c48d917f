#!/usr/bin/env python3
# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-30
# 描述: 特征生成集成测试

"""
神农框架v2.0特征生成集成测试

测试完整的特征生成→训练流程，验证职责分离架构的正确性。
"""

import unittest
import tempfile
import pandas as pd
import numpy as np
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

class TestFeatureGeneration(unittest.TestCase):
    """特征生成测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建临时目录
        self.temp_dir = tempfile.mkdtemp()
        
        # 创建测试数据
        self.test_data = {
            'smiles': [
                'CCN(CC)CCCC(C)NC1=C2C=CC(=CC2=NC=C1)CF3',  # 氯喹类似物
                'O=C(O)C1=CN(C2CC2)C3=C(C=C(F)C(N4CCNCC4)=C3F)C1=O',  # 环丙沙星
                'CC1([C@@H](N2[C@H](S1)[C@@H](C2=O)NC(=O)CC3=CC=CC=C3)C(=O)O)C',  # 青霉素G
                'O=C(NC(C(O)C1=CC=C([N+]([O-])=O)C=C1)CO)C(Cl)Cl',  # 氯霉素
                'NC1=CC=C(C=C1)S(=O)(=O)NC2=NC=CC=N2',  # 磺胺嘧啶
            ],
            'activity': [0.5, 0.25, 0.1, 2.0, 4.0],
            'mol_id': ['mol_1', 'mol_2', 'mol_3', 'mol_4', 'mol_5']
        }
        
        # 保存测试数据
        self.test_csv = os.path.join(self.temp_dir, 'test_data.csv')
        pd.DataFrame(self.test_data).to_csv(self.test_csv, index=False)
        
        # 特征文件路径
        self.features_path = os.path.join(self.temp_dir, 'test_features.npz')
    
    def tearDown(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_morgan_feature_generation(self):
        """测试Morgan指纹生成"""
        try:
            from scripts.generate_features import generate_features
            
            # 生成Morgan特征
            success = generate_features(
                data_path=self.test_csv,
                save_path=self.features_path,
                feature_generators=['morgan'],
                smiles_column='smiles',
                id_column='mol_id',
                normalize='standard',
                n_jobs=1,  # 单进程避免并发问题
                morgan_radius=2,
                morgan_bits=2048
            )
            
            self.assertTrue(success, "Morgan特征生成失败")
            self.assertTrue(os.path.exists(self.features_path), "特征文件未创建")
            
            # 验证特征文件内容
            data = np.load(self.features_path, allow_pickle=True)
            
            self.assertIn('mol_ids', data, "缺少分子ID")
            self.assertIn('features_morgan', data, "缺少Morgan特征")
            self.assertEqual(len(data['mol_ids']), 5, "分子数量不正确")
            self.assertEqual(data['features_morgan'].shape, (5, 2048), "Morgan特征维度不正确")
            
            print("✅ Morgan特征生成测试通过")
            
        except ImportError as e:
            self.skipTest(f"缺少依赖: {e}")
    
    def test_rdkit_feature_generation(self):
        """测试RDKit描述符生成"""
        try:
            from scripts.generate_features import generate_features
            
            # 生成RDKit特征
            success = generate_features(
                data_path=self.test_csv,
                save_path=self.features_path,
                feature_generators=['rdkit'],
                smiles_column='smiles',
                id_column='mol_id',
                normalize='minmax',
                n_jobs=1
            )
            
            self.assertTrue(success, "RDKit特征生成失败")
            
            # 验证特征文件内容
            data = np.load(self.features_path, allow_pickle=True)
            
            self.assertIn('features_rdkit', data, "缺少RDKit特征")
            self.assertEqual(data['features_rdkit'].shape[0], 5, "分子数量不正确")
            self.assertGreater(data['features_rdkit'].shape[1], 10, "RDKit特征维度过小")
            
            print("✅ RDKit特征生成测试通过")
            
        except ImportError as e:
            self.skipTest(f"缺少依赖: {e}")
    
    def test_combined_feature_generation(self):
        """测试组合特征生成"""
        try:
            from scripts.generate_features import generate_features
            
            # 生成组合特征
            success = generate_features(
                data_path=self.test_csv,
                save_path=self.features_path,
                feature_generators=['morgan', 'rdkit'],
                smiles_column='smiles',
                id_column='mol_id',
                normalize='standard',
                n_jobs=1
            )
            
            self.assertTrue(success, "组合特征生成失败")
            
            # 验证特征文件内容
            data = np.load(self.features_path, allow_pickle=True)
            
            self.assertIn('features_morgan', data, "缺少Morgan特征")
            self.assertIn('features_rdkit', data, "缺少RDKit特征")
            self.assertIn('features_combined', data, "缺少组合特征")
            
            # 验证组合特征维度
            morgan_dim = data['features_morgan'].shape[1]
            rdkit_dim = data['features_rdkit'].shape[1]
            combined_dim = data['features_combined'].shape[1]
            
            self.assertEqual(combined_dim, morgan_dim + rdkit_dim, "组合特征维度不正确")
            
            print("✅ 组合特征生成测试通过")
            
        except ImportError as e:
            self.skipTest(f"缺少依赖: {e}")
    
    def test_feature_loading_in_dataset(self):
        """测试数据集中的特征加载"""
        try:
            from scripts.generate_features import generate_features
            from shennong.data.datasets import ShennongDataset
            from shennong.data.datapoint import ShennongDatapoint
            
            # 先生成特征
            success = generate_features(
                data_path=self.test_csv,
                save_path=self.features_path,
                feature_generators=['morgan'],
                smiles_column='smiles',
                id_column='mol_id',
                n_jobs=1
            )
            
            self.assertTrue(success, "特征生成失败")
            
            # 创建数据点
            datapoints = []
            for i, row in pd.read_csv(self.test_csv).iterrows():
                datapoint = ShennongDatapoint(
                    smiles=row['smiles'],
                    targets=[row['activity']],
                    mol_id=row['mol_id']
                )
                datapoints.append(datapoint)
            
            # 创建数据集并加载预计算特征
            dataset = ShennongDataset(
                data=datapoints,
                features_path=self.features_path
            )
            
            # 验证数据集
            self.assertTrue(dataset.use_precomputed_features, "未启用预计算特征")
            self.assertEqual(len(dataset), 5, "数据集大小不正确")
            
            # 验证数据项
            item = dataset[0]
            self.assertIn('descriptors', item, "缺少描述符")
            self.assertIsInstance(item['descriptors'], np.ndarray, "描述符类型不正确")
            self.assertEqual(item['descriptors'].shape[0], 2048, "描述符维度不正确")
            
            print("✅ 数据集特征加载测试通过")
            
        except ImportError as e:
            self.skipTest(f"缺少依赖: {e}")
    
    def test_feature_inspection(self):
        """测试特征检查工具"""
        try:
            from scripts.generate_features import generate_features
            from scripts.inspect_features import inspect_features
            
            # 生成特征
            success = generate_features(
                data_path=self.test_csv,
                save_path=self.features_path,
                feature_generators=['morgan'],
                smiles_column='smiles',
                id_column='mol_id',
                n_jobs=1
            )
            
            self.assertTrue(success, "特征生成失败")
            
            # 检查特征
            info = inspect_features(self.features_path)
            
            self.assertIsInstance(info, dict, "检查结果类型不正确")
            self.assertIn('num_molecules', info, "缺少分子数量信息")
            self.assertIn('feature_shapes', info, "缺少特征形状信息")
            self.assertEqual(info['num_molecules'], 5, "分子数量不正确")
            
            print("✅ 特征检查工具测试通过")
            
        except ImportError as e:
            self.skipTest(f"缺少依赖: {e}")

class TestIntegrationWorkflow(unittest.TestCase):
    """集成工作流程测试"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        
        # 创建更大的测试数据集
        self.test_data = {
            'smiles': [
                'CCN(CC)CCCC(C)NC1=C2C=CC(=CC2=NC=C1)CF3',
                'O=C(O)C1=CN(C2CC2)C3=C(C=C(F)C(N4CCNCC4)=C3F)C1=O',
                'CC1([C@@H](N2[C@H](S1)[C@@H](C2=O)NC(=O)CC3=CC=CC=C3)C(=O)O)C',
                'O=C(NC(C(O)C1=CC=C([N+]([O-])=O)C=C1)CO)C(Cl)Cl',
                'NC1=CC=C(C=C1)S(=O)(=O)NC2=NC=CC=N2',
                'CCN(CC)CCCC(C)NC1=C2C=CC(=CC2=NC=C1)Cl',
                'O=C(O)C1=CN(C2CC2)C3=C(C=C(F)C(N4CCNCC4)=C3)C1=O',
                'CC1([C@@H](N2[C@H](S1)[C@@H](C2=O)NC(=O)C(C3=CC=CC=C3)N)C(=O)O)C',
            ],
            'activity': [0.5, 0.25, 0.1, 2.0, 4.0, 0.8, 0.3, 0.15],
            'mol_id': [f'mol_{i+1}' for i in range(8)]
        }
        
        self.test_csv = os.path.join(self.temp_dir, 'integration_test_data.csv')
        pd.DataFrame(self.test_data).to_csv(self.test_csv, index=False)
        
        self.features_path = os.path.join(self.temp_dir, 'integration_features.npz')
        self.model_dir = os.path.join(self.temp_dir, 'model_output')
    
    def tearDown(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_end_to_end_workflow(self):
        """测试端到端工作流程"""
        try:
            from scripts.generate_features import generate_features
            
            # 步骤1: 生成特征
            print("🔄 步骤1: 生成特征...")
            success = generate_features(
                data_path=self.test_csv,
                save_path=self.features_path,
                feature_generators=['morgan', 'rdkit'],
                smiles_column='smiles',
                id_column='mol_id',
                normalize='standard',
                n_jobs=1
            )
            
            self.assertTrue(success, "特征生成失败")
            print("✅ 特征生成完成")
            
            # 步骤2: 验证特征文件
            print("🔄 步骤2: 验证特征文件...")
            self.assertTrue(os.path.exists(self.features_path), "特征文件不存在")
            
            data = np.load(self.features_path, allow_pickle=True)
            self.assertEqual(len(data['mol_ids']), 8, "分子数量不正确")
            print("✅ 特征文件验证通过")
            
            # 步骤3: 测试数据加载
            print("🔄 步骤3: 测试数据加载...")
            from shennong.data.loaders import load_csv_data
            
            dataset = load_csv_data(
                csv_path=self.test_csv,
                smiles_column='smiles',
                target_columns=['activity'],
                dataset_type='antibacterial',
                validate_smiles=True,
                compute_descriptors=False,  # 使用预计算特征
                features_path=self.features_path
            )
            
            self.assertIsNotNone(dataset, "数据集创建失败")
            self.assertTrue(dataset.use_precomputed_features, "未使用预计算特征")
            print("✅ 数据加载测试通过")
            
            print("🎉 端到端工作流程测试完成!")
            
        except ImportError as e:
            self.skipTest(f"缺少依赖: {e}")
        except Exception as e:
            self.fail(f"端到端测试失败: {e}")

def run_tests():
    """运行所有测试"""
    print("🧬 神农框架v2.0特征生成集成测试")
    print("=" * 60)
    
    # 创建测试套件
    suite = unittest.TestSuite()
    
    # 添加测试
    suite.addTest(unittest.makeSuite(TestFeatureGeneration))
    suite.addTest(unittest.makeSuite(TestIntegrationWorkflow))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出结果
    if result.wasSuccessful():
        print("\n🎉 所有测试通过!")
        print("✅ 神农框架v2.0重构验证成功")
    else:
        print(f"\n❌ 测试失败: {len(result.failures)} 个失败, {len(result.errors)} 个错误")
        for test, error in result.failures + result.errors:
            print(f"   {test}: {error}")

if __name__ == "__main__":
    run_tests()
