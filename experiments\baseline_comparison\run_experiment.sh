#!/bin/bash
# 神农框架基线对比实验启动脚本
# 作者: ZK
# 日期: 2025-06-30

echo "🧬 神农框架基线对比实验"
echo "=========================="

# 检查conda环境
if [[ "$CONDA_DEFAULT_ENV" != "shennong" ]]; then
    echo "❌ 请先激活shennong环境: conda activate shennong"
    exit 1
fi

echo "✅ 当前环境: $CONDA_DEFAULT_ENV"

# 检查必要的目录
echo "📁 检查目录结构..."
mkdir -p ../../data/examples
mkdir -p ../../models/chemprop_baseline

# 创建示例数据
echo "🗂️ 创建示例数据..."
python create_sample_data.py

# 启动Jupyter Notebook
echo "🚀 启动Jupyter Notebook..."
echo "请在浏览器中打开 baseline_comparison.ipynb 文件"
echo "按 Ctrl+C 停止Jupyter服务器"

jupyter notebook baseline_comparison.ipynb
