# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-29
# 描述: 化学导向注意力机制

"""
化学导向注意力机制

专门设计用于识别对抗菌活性有贡献的分子基团、官能团和性质。
通过多个专门的注意力头，分别关注不同类型的化学特征。
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging

logger = logging.getLogger(__name__)


class FunctionalGroupAttention(nn.Module):
    """
    官能团注意力模块
    
    专门识别对抗菌活性重要的官能团，如氢键基团、芳香环、卤素等。
    """
    
    def __init__(self, input_dim: int, hidden_dim: int = 64):
        super().__init__()
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        
        # 不同类型官能团的注意力头
        self.hydrogen_bond_attention = nn.MultiheadAttention(
            embed_dim=input_dim, num_heads=4, dropout=0.1, batch_first=True
        )
        self.aromatic_attention = nn.MultiheadAttention(
            embed_dim=input_dim, num_heads=4, dropout=0.1, batch_first=True
        )
        self.halogen_attention = nn.MultiheadAttention(
            embed_dim=input_dim, num_heads=4, dropout=0.1, batch_first=True
        )
        self.polar_attention = nn.MultiheadAttention(
            embed_dim=input_dim, num_heads=4, dropout=0.1, batch_first=True
        )
        
        # 融合层
        self.fusion = nn.Linear(input_dim * 4, input_dim)
        self.layer_norm = nn.LayerNorm(input_dim)
        
    def forward(self, x: torch.Tensor, atom_features: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, Dict[str, torch.Tensor]]:
        """
        前向传播
        
        Args:
            x: 输入特征 [batch_size, seq_len, input_dim]
            atom_features: 原子特征，用于指导注意力 [batch_size, seq_len, atom_feat_dim]
            
        Returns:
            attended_features: 注意力加权后的特征
            attention_weights: 各类型注意力权重
        """
        batch_size, seq_len, _ = x.shape
        
        # 氢键基团注意力
        hydrogen_out, hydrogen_weights = self.hydrogen_bond_attention(x, x, x)
        
        # 芳香环注意力
        aromatic_out, aromatic_weights = self.aromatic_attention(x, x, x)
        
        # 卤素注意力
        halogen_out, halogen_weights = self.halogen_attention(x, x, x)
        
        # 极性基团注意力
        polar_out, polar_weights = self.polar_attention(x, x, x)
        
        # 融合所有注意力输出
        fused_features = torch.cat([hydrogen_out, aromatic_out, halogen_out, polar_out], dim=-1)
        fused_features = self.fusion(fused_features)
        fused_features = self.layer_norm(fused_features + x)  # 残差连接
        
        # 收集注意力权重
        attention_weights = {
            'hydrogen_bond': hydrogen_weights,
            'aromatic': aromatic_weights,
            'halogen': halogen_weights,
            'polar': polar_weights
        }
        
        return fused_features, attention_weights


class MolecularPropertyAttention(nn.Module):
    """
    分子性质注意力模块
    
    关注与抗菌活性相关的分子性质，如亲脂性、分子大小、柔性、电荷分布等。
    """
    
    def __init__(self, input_dim: int, property_dim: int = 128):
        super().__init__()
        self.input_dim = input_dim
        self.property_dim = property_dim
        
        # 性质特异性注意力头
        self.lipophilicity_attention = nn.MultiheadAttention(
            embed_dim=input_dim, num_heads=4, dropout=0.1, batch_first=True
        )
        self.size_attention = nn.MultiheadAttention(
            embed_dim=input_dim, num_heads=4, dropout=0.1, batch_first=True
        )
        self.flexibility_attention = nn.MultiheadAttention(
            embed_dim=input_dim, num_heads=4, dropout=0.1, batch_first=True
        )
        self.charge_attention = nn.MultiheadAttention(
            embed_dim=input_dim, num_heads=4, dropout=0.1, batch_first=True
        )
        
        # 性质编码器
        self.property_encoder = nn.Sequential(
            nn.Linear(property_dim, input_dim),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        # 融合层
        self.fusion = nn.Linear(input_dim * 4, input_dim)
        self.layer_norm = nn.LayerNorm(input_dim)
        
    def forward(self, x: torch.Tensor, property_features: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, Dict[str, torch.Tensor]]:
        """
        前向传播
        
        Args:
            x: 输入特征 [batch_size, seq_len, input_dim]
            property_features: 分子性质特征 [batch_size, property_dim]
            
        Returns:
            attended_features: 注意力加权后的特征
            attention_weights: 各性质注意力权重
        """
        batch_size, seq_len, _ = x.shape
        
        # 如果提供了性质特征，将其编码并用作查询
        if property_features is not None:
            property_query = self.property_encoder(property_features).unsqueeze(1)  # [batch_size, 1, input_dim]
            property_query = property_query.expand(-1, seq_len, -1)  # [batch_size, seq_len, input_dim]
        else:
            property_query = x
        
        # 亲脂性注意力
        lipo_out, lipo_weights = self.lipophilicity_attention(property_query, x, x)
        
        # 分子大小注意力
        size_out, size_weights = self.size_attention(property_query, x, x)
        
        # 柔性注意力
        flex_out, flex_weights = self.flexibility_attention(property_query, x, x)
        
        # 电荷分布注意力
        charge_out, charge_weights = self.charge_attention(property_query, x, x)
        
        # 融合所有注意力输出
        fused_features = torch.cat([lipo_out, size_out, flex_out, charge_out], dim=-1)
        fused_features = self.fusion(fused_features)
        fused_features = self.layer_norm(fused_features + x)  # 残差连接
        
        # 收集注意力权重
        attention_weights = {
            'lipophilicity': lipo_weights,
            'size': size_weights,
            'flexibility': flex_weights,
            'charge': charge_weights
        }
        
        return fused_features, attention_weights


class ActivityRelatedAttention(nn.Module):
    """
    活性相关注意力模块
    
    专注于与抗菌活性直接相关的特征，如ADMET性质、毒性、选择性等。
    """
    
    def __init__(self, input_dim: int):
        super().__init__()
        self.input_dim = input_dim
        
        # 活性相关注意力头
        self.admet_attention = nn.MultiheadAttention(
            embed_dim=input_dim, num_heads=4, dropout=0.1, batch_first=True
        )
        self.toxicity_attention = nn.MultiheadAttention(
            embed_dim=input_dim, num_heads=4, dropout=0.1, batch_first=True
        )
        self.selectivity_attention = nn.MultiheadAttention(
            embed_dim=input_dim, num_heads=4, dropout=0.1, batch_first=True
        )
        
        # 融合层
        self.fusion = nn.Linear(input_dim * 3, input_dim)
        self.layer_norm = nn.LayerNorm(input_dim)
        
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, Dict[str, torch.Tensor]]:
        """
        前向传播
        
        Args:
            x: 输入特征 [batch_size, seq_len, input_dim]
            
        Returns:
            attended_features: 注意力加权后的特征
            attention_weights: 各活性注意力权重
        """
        # ADMET注意力
        admet_out, admet_weights = self.admet_attention(x, x, x)
        
        # 毒性注意力
        toxicity_out, toxicity_weights = self.toxicity_attention(x, x, x)
        
        # 选择性注意力
        selectivity_out, selectivity_weights = self.selectivity_attention(x, x, x)
        
        # 融合所有注意力输出
        fused_features = torch.cat([admet_out, toxicity_out, selectivity_out], dim=-1)
        fused_features = self.fusion(fused_features)
        fused_features = self.layer_norm(fused_features + x)  # 残差连接
        
        # 收集注意力权重
        attention_weights = {
            'admet': admet_weights,
            'toxicity': toxicity_weights,
            'selectivity': selectivity_weights
        }
        
        return fused_features, attention_weights


class ChemistryGuidedAttention(nn.Module):
    """
    化学导向注意力机制
    
    整合官能团注意力、分子性质注意力和活性相关注意力，
    为抗菌活性预测提供化学可解释的注意力机制。
    """
    
    def __init__(self, input_dim: int, property_dim: int = 128):
        super().__init__()
        self.input_dim = input_dim
        self.property_dim = property_dim
        
        # 三个主要注意力模块
        self.functional_group_attention = FunctionalGroupAttention(input_dim)
        self.molecular_property_attention = MolecularPropertyAttention(input_dim, property_dim)
        self.activity_related_attention = ActivityRelatedAttention(input_dim)
        
        # 最终融合层
        self.final_fusion = nn.Sequential(
            nn.Linear(input_dim * 3, input_dim * 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(input_dim * 2, input_dim),
            nn.LayerNorm(input_dim)
        )
        
        # 注意力权重融合
        self.attention_weight_fusion = nn.Linear(3, 1)
        
    def forward(self, 
                graph_features: torch.Tensor, 
                expert_features: torch.Tensor,
                property_features: Optional[torch.Tensor] = None,
                atom_features: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, Dict[str, torch.Tensor]]:
        """
        前向传播
        
        Args:
            graph_features: 图神经网络特征 [batch_size, seq_len, input_dim]
            expert_features: 专家特征 [batch_size, expert_dim]
            property_features: 分子性质特征 [batch_size, property_dim]
            atom_features: 原子特征 [batch_size, seq_len, atom_feat_dim]
            
        Returns:
            fused_features: 融合后的特征
            all_attention_weights: 所有注意力权重
        """
        # 将专家特征扩展到序列维度
        batch_size, seq_len, _ = graph_features.shape
        expert_expanded = expert_features.unsqueeze(1).expand(-1, seq_len, -1)
        
        # 初始特征融合
        combined_features = torch.cat([graph_features, expert_expanded], dim=-1)
        combined_features = nn.Linear(combined_features.size(-1), self.input_dim).to(combined_features.device)(combined_features)
        
        # 官能团注意力
        fg_features, fg_weights = self.functional_group_attention(combined_features, atom_features)
        
        # 分子性质注意力
        prop_features, prop_weights = self.molecular_property_attention(combined_features, property_features)
        
        # 活性相关注意力
        act_features, act_weights = self.activity_related_attention(combined_features)
        
        # 最终融合
        all_features = torch.cat([fg_features, prop_features, act_features], dim=-1)
        fused_features = self.final_fusion(all_features)
        
        # 收集所有注意力权重
        all_attention_weights = {
            'functional_group': fg_weights,
            'molecular_property': prop_weights,
            'activity_related': act_weights
        }
        
        return fused_features, all_attention_weights
    
    def get_interpretable_attention(self, attention_weights: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        获取可解释的注意力权重
        
        Args:
            attention_weights: 原始注意力权重
            
        Returns:
            interpretable_weights: 可解释的注意力权重
        """
        interpretable_weights = {}
        
        # 处理官能团注意力
        for group_type, weights in attention_weights['functional_group'].items():
            # 平均多头注意力权重
            avg_weights = weights.mean(dim=1)  # [batch_size, seq_len]
            interpretable_weights[f'functional_group_{group_type}'] = avg_weights
        
        # 处理分子性质注意力
        for prop_type, weights in attention_weights['molecular_property'].items():
            avg_weights = weights.mean(dim=1)
            interpretable_weights[f'molecular_property_{prop_type}'] = avg_weights
        
        # 处理活性相关注意力
        for act_type, weights in attention_weights['activity_related'].items():
            avg_weights = weights.mean(dim=1)
            interpretable_weights[f'activity_related_{act_type}'] = avg_weights
        
        return interpretable_weights
