# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-27
# 描述: 神农框架核心模型

"""
神农框架核心模型

实现神农框架的主要架构，包括双分支设计、生物启发注意力融合和多任务预测。
"""

from typing import Dict, Any, Optional, List, Union, Tuple
import torch
import torch.nn as nn
import torch.nn.functional as F
import logging

from .base import BaseShennongModel
from .graph_branch import AntibacterialGraphBranch
from .expert_branch import AdaptiveExpertBranch
from ..nn.attention import BiologicalAttentionFusion
from ..nn.losses import AntibacterialLoss
from ..antibacterial.mechanisms import ANTIBACTERIAL_MECHANISMS

logger = logging.getLogger(__name__)


class ShennongFramework(BaseShennongModel):
    """
    神农框架主模型

    基于双模态架构的抗菌化合物活性预测框架，专注于识别对抗菌活性有贡献的
    基团、官能团和分子性质，提供化学家友好的可解释性分析。

    架构组成:
    - 图神经网络分支: 处理分子图结构，学习原子和键的表示
    - 专家特征分支: 处理Mordred分子描述符，捕获化学性质
    - 化学导向注意力机制: 识别关键基团和性质对活性的贡献
    - 抗菌活性预测头: 专注于MIC值预测和不确定性估计
    - 化学可解释性模块: 提供原子、基团、性质级别的解释
    """

    def __init__(self, config: Dict[str, Any]):
        """
        初始化神农框架

        Args:
            config: 模型配置字典
        """
        super().__init__(config)

        # 分支配置
        self.graph_config = config.get('graph_config', {})
        self.expert_config = config.get('expert_config', {})
        self.fusion_config = config.get('fusion_config', {})

        # 任务配置
        self.task_config = config.get('task_config', {})
        self.num_tasks = self.task_config.get('num_tasks', 1)
        self.task_type = self.task_config.get('task_type', 'regression')

        # 构建模型组件
        self._build_model()

        # 初始化损失函数
        self._initialize_loss_functions()

        logger.info(f"初始化神农框架: 图分支维度={self.graph_config.get('output_dim', 300)}, "
                   f"专家分支维度={self.expert_config.get('output_dim', 128)}, "
                   f"任务数={self.num_tasks}")

    def _build_model(self):
        """构建模型组件"""
        # 分支1: 图神经网络分支
        self.graph_branch = AntibacterialGraphBranch(self.graph_config)

        # 分支2: 自适应专家特征分支
        self.expert_branch = AdaptiveExpertBranch(self.expert_config)

        # 生物启发注意力融合
        fusion_output_dim = self.fusion_config.get('output_dim', 256)
        self.bio_attention = BiologicalAttentionFusion(
            graph_dim=self.graph_config.get('output_dim', 300),
            expert_dim=self.expert_config.get('output_dim', 128),
            output_dim=fusion_output_dim,
            attention_heads=self.fusion_config.get('attention_heads', 4),
            dropout=self.fusion_config.get('dropout', 0.1),
            mechanism_aware=self.fusion_config.get('mechanism_aware', True),
            num_mechanisms=len(ANTIBACTERIAL_MECHANISMS)
        )

        # 多任务输出头
        self._build_prediction_heads(fusion_output_dim)

        # 初始化权重
        self._initialize_weights()

    def _build_prediction_heads(self, input_dim: int):
        """构建预测头"""
        # 主要活性预测头
        self.activity_predictor = nn.Sequential(
            nn.Linear(input_dim, input_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(input_dim // 2, self.num_tasks)
        )

        # 移除机制分类头 - 专注于活性预测
        self.mechanism_classifier = None

        # 不确定性预测头（如果启用）
        if self.task_config.get('uncertainty_estimation', False):
            self.uncertainty_predictor = nn.Sequential(
                nn.Linear(input_dim, input_dim // 4),
                nn.ReLU(),
                nn.Linear(input_dim // 4, self.num_tasks),
                nn.Softplus()  # 确保输出为正值
            )
        else:
            self.uncertainty_predictor = None

    def _initialize_weights(self):
        """初始化权重"""
        for module in [self.activity_predictor, self.mechanism_classifier, self.uncertainty_predictor]:
            if module is not None:
                for layer in module:
                    if isinstance(layer, nn.Linear):
                        nn.init.xavier_uniform_(layer.weight)
                        if layer.bias is not None:
                            nn.init.zeros_(layer.bias)

    def _initialize_loss_functions(self):
        """初始化损失函数"""
        self.loss_function = AntibacterialLoss(
            task_type=self.task_type,
            num_tasks=self.num_tasks,
            mechanism_weight=self.config.get('mechanism_weight', 0.1),
            uncertainty_weight=self.config.get('uncertainty_weight', 0.05),
            attention_weight=self.config.get('attention_weight', 0.01)
        )

    def forward(
        self,
        batch: Dict[str, Any],
        return_attention: bool = False
    ) -> Dict[str, torch.Tensor]:
        """
        前向传播

        Args:
            batch: 输入批次数据
            return_attention: 是否返回注意力权重

        Returns:
            模型输出字典
        """
        # 提取输入
        mol_graphs = batch['mol_graphs']
        descriptors = batch.get('descriptors')

        # 双分支特征提取
        graph_features = self.graph_branch(mol_graphs)

        if descriptors is not None:
            expert_features = self.expert_branch(descriptors)
        else:
            # 如果没有描述符，使用零向量
            batch_size = len(mol_graphs) if isinstance(mol_graphs, list) else mol_graphs.size(0)
            expert_features = torch.zeros(
                batch_size, self.expert_config.get('output_dim', 128),
                device=self.device
            )

        # 生物启发注意力融合
        fused_features, attention_weights = self.bio_attention(
            graph_features, expert_features, return_attention=return_attention
        )

        # 多任务预测
        outputs = {}

        # 活性预测
        activity_pred = self.activity_predictor(fused_features)
        outputs['activity'] = activity_pred

        # 移除机制分类预测 - 专注于活性预测

        # 不确定性预测
        if self.uncertainty_predictor is not None:
            uncertainty_pred = self.uncertainty_predictor(fused_features)
            outputs['uncertainty'] = uncertainty_pred

        # 注意力权重
        if return_attention:
            outputs['attention_weights'] = attention_weights

        # 中间特征（用于分析）
        if self.training or return_attention:
            outputs['graph_features'] = graph_features
            outputs['expert_features'] = expert_features
            outputs['fused_features'] = fused_features

        return outputs

    def compute_loss(
        self,
        outputs: Dict[str, torch.Tensor],
        batch: Dict[str, Any]
    ) -> Dict[str, torch.Tensor]:
        """
        计算损失

        Args:
            outputs: 模型输出
            batch: 批次数据

        Returns:
            损失字典
        """
        return self.loss_function(outputs, batch)

    def predict(
        self,
        smiles: Union[str, List[str]],
        descriptors: Optional[torch.Tensor] = None,
        return_attention: bool = False,
        return_uncertainty: bool = False,
        batch_size: int = 64
    ) -> Dict[str, Union[torch.Tensor, List]]:
        """
        预测接口

        Args:
            smiles: SMILES字符串或列表
            descriptors: 可选的描述符张量
            return_attention: 是否返回注意力权重
            return_uncertainty: 是否返回不确定性
            batch_size: 批处理大小

        Returns:
            预测结果字典
        """
        self.eval()

        if isinstance(smiles, str):
            smiles = [smiles]

        # 准备数据
        from ..data.datapoints import ShennongDatapoint
        from ..data.datasets import ShennongDataset
        from ..data.dataloader import ShennongDataLoader

        # 创建数据点
        datapoints = []
        for i, smi in enumerate(smiles):
            desc = descriptors[i] if descriptors is not None else None
            datapoint = ShennongDatapoint(
                smiles=smi,
                targets={},  # 预测时不需要目标值
                descriptors=desc
            )
            datapoints.append(datapoint)

        # 创建数据集和加载器
        dataset = ShennongDataset(datapoints, validate_data=False)
        dataloader = ShennongDataLoader(
            dataset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=0
        )

        # 批量预测
        all_predictions = []
        all_attention_weights = []

        with torch.no_grad():
            for batch in dataloader:
                # 移动到设备
                batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v
                        for k, v in batch.items()}

                # 预测
                outputs = self(batch, return_attention=return_attention)

                all_predictions.append(outputs['activity'].cpu())

                if return_attention and 'attention_weights' in outputs:
                    all_attention_weights.append(outputs['attention_weights'])

        # 合并结果
        predictions = torch.cat(all_predictions, dim=0)

        results = {
            'smiles': smiles,
            'activity_predictions': predictions.numpy(),
        }

        # 转换为MIC值（如果是回归任务）
        if self.task_type == 'regression':
            # 假设预测的是log(MIC)，转换为MIC
            mic_values = torch.exp(predictions).numpy()
            results['predicted_mic'] = mic_values

        if return_attention and all_attention_weights:
            results['attention_weights'] = all_attention_weights

        if return_uncertainty and self.uncertainty_predictor is not None:
            # 重新运行以获取不确定性
            with torch.no_grad():
                uncertainty_preds = []
                for batch in dataloader:
                    batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v
                            for k, v in batch.items()}
                    outputs = self(batch)
                    if 'uncertainty' in outputs:
                        uncertainty_preds.append(outputs['uncertainty'].cpu())

                if uncertainty_preds:
                    uncertainties = torch.cat(uncertainty_preds, dim=0)
                    results['uncertainty'] = uncertainties.numpy()

        return results

    @classmethod
    def load_pretrained(
        cls,
        model_name: str,
        device: Optional[str] = None
    ) -> 'ShennongFramework':
        """
        加载预训练模型

        Args:
            model_name: 预训练模型名称
            device: 目标设备

        Returns:
            加载的模型实例
        """
        # 预训练模型路径映射
        pretrained_models = {
            'antibacterial_v1.0': 'models/pretrained/antibacterial_v1.0.pt',
            'gram_positive_v1.0': 'models/pretrained/gram_positive_v1.0.pt',
            'gram_negative_v1.0': 'models/pretrained/gram_negative_v1.0.pt',
        }

        if model_name not in pretrained_models:
            raise ValueError(f"未知的预训练模型: {model_name}")

        model_path = pretrained_models[model_name]

        # 加载模型
        model = cls.load_model(model_path, map_location=device)

        logger.info(f"已加载预训练模型: {model_name}")
        return model

    def get_feature_importance(
        self,
        smiles: Union[str, List[str]],
        method: str = 'attention'
    ) -> Dict[str, Any]:
        """
        获取特征重要性

        Args:
            smiles: SMILES字符串或列表
            method: 重要性计算方法

        Returns:
            特征重要性字典
        """
        if method == 'attention':
            # 基于注意力权重的重要性
            results = self.predict(smiles, return_attention=True)
            return self._analyze_attention_importance(results['attention_weights'])

        elif method == 'gradient':
            # 基于梯度的重要性
            return self._compute_gradient_importance(smiles)

        else:
            raise ValueError(f"不支持的重要性计算方法: {method}")

    def _analyze_attention_importance(self, attention_weights: List[Dict]) -> Dict[str, Any]:
        """分析注意力权重的重要性"""
        # 实现注意力权重分析逻辑
        importance = {
            'graph_branch_importance': [],
            'expert_branch_importance': [],
            'mechanism_importance': []
        }

        # 这里可以添加具体的分析逻辑

        return importance

    def _compute_gradient_importance(self, smiles: List[str]) -> Dict[str, Any]:
        """计算基于梯度的特征重要性"""
        # 实现梯度重要性计算逻辑
        importance = {}

        # 这里可以添加具体的梯度分析逻辑

        return importance
