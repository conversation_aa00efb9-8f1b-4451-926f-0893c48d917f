# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-27
# 描述: 3D描述符必要性分析工具

"""
3D描述符必要性分析工具

分析3D描述符在抗菌化合物预测中的价值和必要性。
"""

from typing import Dict, List, Any, Optional, Tuple
import numpy as np
import logging
from pathlib import Path
import json
import time

logger = logging.getLogger(__name__)

try:
    from rdkit import Chem
    from rdkit.Chem import AllChem, Descriptors3D
    RDKIT_AVAILABLE = True
except ImportError:
    RDKIT_AVAILABLE = False

try:
    from mordred import Calculator, descriptors
    MORDRED_AVAILABLE = True
except ImportError:
    MORDRED_AVAILABLE = False


class Descriptor3DAnalyzer:
    """
    3D描述符分析器
    
    评估3D描述符在抗菌化合物预测中的价值。
    """
    
    def __init__(self):
        """初始化分析器"""
        self.available = RDKIT_AVAILABLE and MORDRED_AVAILABLE
        
        # 抗菌化合物测试集
        self.antibacterial_compounds = [
            # β-内酰胺类 - 需要精确的3D构象
            ("青霉素G", "CC1([C@@H](N2[C@H](S1)[C@@H](C2=O)NC(=O)CC3=CC=CC=C3)C(=O)O)C"),
            ("阿莫西林", "CC1([C@@H](N2[C@H](S1)[C@@H](C2=O)NC(=O)[C@@H](C3=CC=C(C=C3)O)N)C(=O)O)C"),
            ("头孢氨苄", "CC1([C@@H](N2[C@H](S1)[C@@H](C2=O)NC(=O)[C@@H](C3=CC=CC=C3)N)C(=O)O)C"),
            
            # 氟喹诺酮类 - 平面结构重要
            ("环丙沙星", "C1CC1N2C=C(C(=O)C3=CC(=C(C=C32)N4CCNCC4)F)C(=O)O"),
            ("左氧氟沙星", "C[C@H]1COC2=C(N1)C=C(C=C2)N3C=C(C(=O)C4=CC(=C(C=C43)N5CCNCC5)F)C(=O)O"),
            
            # 大环内酯类 - 3D构象关键
            ("红霉素", "CCC1C(C(C(C(=O)C(CC(C(C(C(C(C(=O)O1)C)OC2C(C(C(C(O2)C)O)N(C)C)O)C)OC3C(C(CC(O3)C)N(C)C)O)CC=O)C)C)O)OC"),
            ("阿奇霉素", "CCC1C(C(C(C(=O)C(CC(C(C(C(C(C(=O)O1)C)OC2C(C(C(C(O2)C)O)N(C)C)O)C)OC3C(C(CC(O3)C)N(C)C)O)CC=O)C)C)O)OC"),
            
            # 氨基糖苷类 - 多个手性中心
            ("链霉素", "CNC1C(C(C(C(O1)OC2C(C(C(C(O2)CO)O)O)NC(=N)N)O)O)O"),
            ("庆大霉素", "CNC1C(C(C(C(O1)OC2C(CC(C(O2)CN)O)N)O)O)O"),
            
            # 简单对照组
            ("乙醇", "CCO"),
            ("苯", "c1ccccc1"),
        ]
        
        if not self.available:
            logger.warning("RDKit或Mordred不可用，3D分析功能受限")
    
    def analyze_3d_necessity(self) -> Dict[str, Any]:
        """
        分析3D描述符的必要性
        
        Returns:
            分析结果
        """
        if not self.available:
            return {'error': 'RDKit或Mordred不可用'}
        
        logger.info("开始3D描述符必要性分析...")
        
        results = {
            'computation_analysis': self._analyze_computation_cost(),
            'conformational_analysis': self._analyze_conformational_importance(),
            'descriptor_overlap_analysis': self._analyze_descriptor_overlap(),
            'antibacterial_specific_analysis': self._analyze_antibacterial_specificity(),
            'recommendations': []
        }
        
        # 生成建议
        results['recommendations'] = self._generate_recommendations(results)
        
        return results
    
    def _analyze_computation_cost(self) -> Dict[str, Any]:
        """分析计算成本"""
        logger.info("分析3D描述符计算成本...")
        
        cost_analysis = {
            'time_comparison': {},
            'success_rate_comparison': {},
            'memory_usage': {},
            'stability_analysis': {}
        }
        
        test_molecules = [smiles for _, smiles in self.antibacterial_compounds[:5]]
        
        for i, smiles in enumerate(test_molecules):
            mol = Chem.MolFromSmiles(smiles)
            if mol is None:
                continue
            
            # 2D描述符计算时间
            start_time = time.time()
            try:
                calc_2d = Calculator(descriptors, ignore_3D=True)
                desc_2d = calc_2d(mol)
                time_2d = time.time() - start_time
                success_2d = sum(1 for d in desc_2d if d is not None and not np.isnan(float(d) if isinstance(d, (int, float)) else 0))
                total_2d = len(desc_2d)
            except Exception as e:
                time_2d = float('inf')
                success_2d = 0
                total_2d = 0
            
            # 3D描述符计算时间（需要生成3D构象）
            start_time = time.time()
            try:
                # 生成3D构象
                mol_3d = Chem.AddHs(mol)
                AllChem.EmbedMolecule(mol_3d, randomSeed=42)
                AllChem.OptimizeMolecule(mol_3d)
                
                calc_3d = Calculator(descriptors, ignore_3D=False)
                desc_3d = calc_3d(mol_3d)
                time_3d = time.time() - start_time
                success_3d = sum(1 for d in desc_3d if d is not None and not np.isnan(float(d) if isinstance(d, (int, float)) else 0))
                total_3d = len(desc_3d)
            except Exception as e:
                time_3d = float('inf')
                success_3d = 0
                total_3d = 0
            
            cost_analysis['time_comparison'][f'molecule_{i}'] = {
                '2d_time': time_2d,
                '3d_time': time_3d,
                'time_ratio': time_3d / time_2d if time_2d > 0 else float('inf')
            }
            
            cost_analysis['success_rate_comparison'][f'molecule_{i}'] = {
                '2d_success_rate': success_2d / total_2d if total_2d > 0 else 0,
                '3d_success_rate': success_3d / total_3d if total_3d > 0 else 0,
                '2d_count': total_2d,
                '3d_count': total_3d
            }
        
        # 计算平均值
        if cost_analysis['time_comparison']:
            avg_time_ratio = np.mean([v['time_ratio'] for v in cost_analysis['time_comparison'].values() if v['time_ratio'] != float('inf')])
            cost_analysis['average_time_increase'] = avg_time_ratio
        
        return cost_analysis
    
    def _analyze_conformational_importance(self) -> Dict[str, Any]:
        """分析构象重要性"""
        logger.info("分析构象重要性...")
        
        conformational_analysis = {
            'stereochemistry_importance': {},
            'flexibility_analysis': {},
            'binding_site_relevance': {}
        }
        
        # 分析立体化学重要性
        stereochemical_compounds = [
            ("青霉素G", "CC1([C@@H](N2[C@H](S1)[C@@H](C2=O)NC(=O)CC3=CC=CC=C3)C(=O)O)C"),
            ("左氧氟沙星", "C[C@H]1COC2=C(N1)C=C(C=C2)N3C=C(C(=O)C4=CC(=C(C=C43)N5CCNCC5)F)C(=O)O"),
        ]
        
        for name, smiles in stereochemical_compounds:
            mol = Chem.MolFromSmiles(smiles)
            if mol is None:
                continue
            
            # 检查手性中心
            chiral_centers = Chem.FindMolChiralCenters(mol, includeUnassigned=True)
            
            # 检查旋转键
            rotatable_bonds = Descriptors3D.NumRotatableBonds(mol) if hasattr(Descriptors3D, 'NumRotatableBonds') else 0
            
            conformational_analysis['stereochemistry_importance'][name] = {
                'chiral_centers': len(chiral_centers),
                'rotatable_bonds': rotatable_bonds,
                'stereochemistry_critical': len(chiral_centers) > 0
            }
        
        return conformational_analysis
    
    def _analyze_descriptor_overlap(self) -> Dict[str, Any]:
        """分析描述符重叠度"""
        logger.info("分析2D/3D描述符重叠度...")
        
        overlap_analysis = {
            'unique_3d_descriptors': [],
            'redundant_descriptors': [],
            'information_gain': 0.0
        }
        
        try:
            # 获取2D和3D描述符列表
            calc_2d = Calculator(descriptors, ignore_3D=True)
            calc_3d = Calculator(descriptors, ignore_3D=False)
            
            desc_2d_names = set(str(d) for d in calc_2d.descriptors)
            desc_3d_names = set(str(d) for d in calc_3d.descriptors)
            
            # 找出3D特有的描述符
            unique_3d = desc_3d_names - desc_2d_names
            overlap_analysis['unique_3d_descriptors'] = list(unique_3d)
            overlap_analysis['unique_3d_count'] = len(unique_3d)
            overlap_analysis['total_3d_count'] = len(desc_3d_names)
            overlap_analysis['information_gain'] = len(unique_3d) / len(desc_3d_names)
            
            # 分析3D特有描述符的类型
            descriptor_types = {}
            for desc_name in unique_3d:
                if 'WHIM' in desc_name:
                    descriptor_types['WHIM'] = descriptor_types.get('WHIM', 0) + 1
                elif 'GETAWAY' in desc_name:
                    descriptor_types['GETAWAY'] = descriptor_types.get('GETAWAY', 0) + 1
                elif 'RDF' in desc_name:
                    descriptor_types['RDF'] = descriptor_types.get('RDF', 0) + 1
                elif 'CPSA' in desc_name:
                    descriptor_types['CPSA'] = descriptor_types.get('CPSA', 0) + 1
                else:
                    descriptor_types['Other'] = descriptor_types.get('Other', 0) + 1
            
            overlap_analysis['3d_descriptor_types'] = descriptor_types
            
        except Exception as e:
            logger.error(f"描述符重叠分析失败: {e}")
            overlap_analysis['error'] = str(e)
        
        return overlap_analysis
    
    def _analyze_antibacterial_specificity(self) -> Dict[str, Any]:
        """分析抗菌特异性"""
        logger.info("分析抗菌化合物的3D特异性...")
        
        specificity_analysis = {
            'mechanism_3d_relevance': {},
            'target_binding_analysis': {},
            'pharmacophore_analysis': {}
        }
        
        # 抗菌机制与3D结构的关系
        mechanism_3d_relevance = {
            'cell_wall_synthesis': {
                'description': 'β-内酰胺类需要精确的3D构象与PBP结合',
                '3d_importance': 'high',
                'key_features': ['立体化学', '环张力', '空间取向'],
                'examples': ['青霉素', '头孢菌素']
            },
            'protein_synthesis': {
                'description': '核糖体结合需要特定的3D药效团',
                '3d_importance': 'medium',
                'key_features': ['氢键几何', '疏水相互作用'],
                'examples': ['氨基糖苷类', '大环内酯类']
            },
            'dna_replication': {
                'description': 'DNA结合主要依赖平面结构和电荷分布',
                '3d_importance': 'low',
                'key_features': ['平面性', '电荷分布'],
                'examples': ['氟喹诺酮类']
            },
            'cell_membrane': {
                'description': '膜相互作用主要依赖疏水性和两亲性',
                '3d_importance': 'medium',
                'key_features': ['分子形状', '疏水表面积'],
                'examples': ['多肽类抗生素']
            }
        }
        
        specificity_analysis['mechanism_3d_relevance'] = mechanism_3d_relevance
        
        return specificity_analysis
    
    def _generate_recommendations(self, analysis_results: Dict[str, Any]) -> List[str]:
        """生成建议"""
        recommendations = []
        
        # 基于计算成本分析
        if 'computation_analysis' in analysis_results:
            comp_analysis = analysis_results['computation_analysis']
            if 'average_time_increase' in comp_analysis:
                time_increase = comp_analysis['average_time_increase']
                if time_increase > 10:
                    recommendations.append(f"3D描述符计算时间增加{time_increase:.1f}倍，建议仅在必要时使用")
                elif time_increase > 5:
                    recommendations.append(f"3D描述符计算成本适中（{time_increase:.1f}倍），可考虑选择性使用")
        
        # 基于信息增益分析
        if 'descriptor_overlap_analysis' in analysis_results:
            overlap = analysis_results['descriptor_overlap_analysis']
            if 'information_gain' in overlap:
                info_gain = overlap['information_gain']
                if info_gain < 0.1:
                    recommendations.append(f"3D描述符信息增益较低（{info_gain:.1%}），可能不值得额外计算成本")
                elif info_gain > 0.2:
                    recommendations.append(f"3D描述符提供显著信息增益（{info_gain:.1%}），建议使用")
        
        # 基于抗菌特异性分析
        if 'antibacterial_specific_analysis' in analysis_results:
            specificity = analysis_results['antibacterial_specific_analysis']
            if 'mechanism_3d_relevance' in specificity:
                high_importance_mechanisms = [
                    mech for mech, info in specificity['mechanism_3d_relevance'].items()
                    if info.get('3d_importance') == 'high'
                ]
                if high_importance_mechanisms:
                    recommendations.append(f"对于{', '.join(high_importance_mechanisms)}机制，3D描述符至关重要")
        
        # 默认建议
        if not recommendations:
            recommendations.append("建议进行实际预测性能对比测试以确定3D描述符的价值")
        
        return recommendations
    
    def generate_report(self, output_path: Optional[str] = None) -> Dict[str, Any]:
        """生成完整的3D描述符分析报告"""
        logger.info("生成3D描述符分析报告...")
        
        report = {
            'timestamp': str(np.datetime64('now')),
            'analysis_results': self.analyze_3d_necessity(),
            'summary': {},
            'final_recommendation': ''
        }
        
        # 生成摘要
        analysis = report['analysis_results']
        
        if 'computation_analysis' in analysis:
            comp = analysis['computation_analysis']
            if 'average_time_increase' in comp:
                report['summary']['computation_cost'] = f"{comp['average_time_increase']:.1f}x slower"
        
        if 'descriptor_overlap_analysis' in analysis:
            overlap = analysis['descriptor_overlap_analysis']
            if 'information_gain' in overlap:
                report['summary']['information_gain'] = f"{overlap['information_gain']:.1%}"
        
        # 最终建议
        recommendations = analysis.get('recommendations', [])
        if recommendations:
            report['final_recommendation'] = recommendations[0]
        
        # 保存报告
        if output_path:
            try:
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(report, f, indent=2, ensure_ascii=False, default=str)
                logger.info(f"3D描述符分析报告已保存到: {output_path}")
            except Exception as e:
                logger.error(f"保存报告失败: {e}")
        
        return report


def run_3d_analysis(output_dir: str = "3d_analysis_reports") -> Dict[str, Any]:
    """
    运行完整的3D描述符分析
    
    Args:
        output_dir: 输出目录
        
    Returns:
        分析报告
    """
    # 创建输出目录
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # 运行分析
    analyzer = Descriptor3DAnalyzer()
    report = analyzer.generate_report(
        output_path=str(output_path / "3d_descriptor_analysis.json")
    )
    
    # 打印摘要
    print("\n" + "="*60)
    print("🔬 3D描述符必要性分析报告")
    print("="*60)
    
    if 'analysis_results' in report:
        analysis = report['analysis_results']
        
        # 计算成本
        if 'computation_analysis' in analysis:
            comp = analysis['computation_analysis']
            if 'average_time_increase' in comp:
                print(f"计算成本增加: {comp['average_time_increase']:.1f}倍")
        
        # 信息增益
        if 'descriptor_overlap_analysis' in analysis:
            overlap = analysis['descriptor_overlap_analysis']
            if 'information_gain' in overlap:
                print(f"信息增益: {overlap['information_gain']:.1%}")
                print(f"3D特有描述符: {overlap.get('unique_3d_count', 0)}个")
        
        # 建议
        recommendations = analysis.get('recommendations', [])
        if recommendations:
            print(f"\n💡 主要建议:")
            for i, rec in enumerate(recommendations, 1):
                print(f"{i}. {rec}")
    
    print("="*60)
    
    return report
