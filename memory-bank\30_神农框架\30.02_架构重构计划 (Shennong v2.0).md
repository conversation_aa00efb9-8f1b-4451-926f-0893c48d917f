# 架构重构计划 (Shennong v2.0)

## 重构概述

基于对现有架构的深入分析和与Chemprop、AutoGluon的对比研究，我们制定了神农框架v2.0的全面重构计划。此次重构的核心目标是在保持框架创新优势的同时，提升科学实验的严谨性、计算效率和代码可维护性。

## 重构动机

### 当前架构的局限性 (v1.0)

1. **科学实验严谨性问题**
   - 内部特征计算可能与Chemprop使用的外部特征存在微小差异
   - 难以确保跨模型比较的公平性

2. **计算效率问题**
   - 每个epoch都重复计算Mordred特征
   - 对于8k数据集，训练时间显著延长

3. **代码架构问题**
   - 特征计算与深度学习推理混合在一起
   - 违反了单一职责原则
   - 调试和维护困难

### 重构目标

1. **科学严谨性**: 确保与对比方法使用完全相同的特征
2. **计算效率**: 通过外部预计算大幅提升训练速度
3. **架构清晰**: 实现清晰的关注点分离
4. **实验灵活性**: 支持快速的特征工程实验
5. **创新保持**: 保留所有核心创新点

## 架构重构设计

### v1.0 vs v2.0 对比

#### v1.0 架构 (内部集成)
```python
# 当前架构 - 存在问题
class ShennongV1(nn.Module):
    def __init__(self):
        self.gnn_branch = MPNNEncoder()
        self.mordred_calculator = MordredCalculator()  # 内部计算
        self.fusion_layer = ChemistryGuidedFusion()
    
    def forward(self, mol_graph):
        # 问题1: 每次前向传播都计算特征
        graph_features = self.gnn_branch(mol_graph)
        mordred_features = self.mordred_calculator(mol_graph.smiles)  # 重复计算
        
        # 问题2: 职责混合
        output = self.fusion_layer(graph_features, mordred_features)
        return output
```

#### v2.0 架构 (外部注入)
```python
# 重构后架构 - 解决问题
class ShennongV2(nn.Module):
    def __init__(self):
        self.gnn_branch = MPNNEncoder()
        self.fusion_layer = ChemistryGuidedFusion()  # 保持创新
    
    def forward(self, mol_graph, external_features):
        # 优势1: 使用预计算特征，无重复计算
        graph_features = self.gnn_branch(mol_graph)
        
        # 优势2: 职责清晰，专注于学习
        output = self.fusion_layer(graph_features, external_features)
        return output

# 分离的特征计算
class FeaturePreprocessor:
    def __init__(self):
        self.mordred_calculator = MordredCalculator()
    
    def precompute_features(self, smiles_list):
        # 一次性计算所有特征
        return [self.mordred_calculator(smiles) for smiles in smiles_list]
```

## 详细重构方案

### 1. 数据流重构

#### 新的数据处理流水线

```python
# 重构后的数据处理流水线
class DataPipeline:
    """
    分离的数据处理流水线
    
    职责:
    1. 特征预计算
    2. 数据清洗和验证
    3. 数据集构建
    """
    def __init__(self, feature_configs):
        self.feature_configs = feature_configs
        self.calculators = self._init_calculators()
    
    def _init_calculators(self):
        calculators = {}
        if 'mordred' in self.feature_configs:
            calculators['mordred'] = MordredCalculator()
        if 'rdkit' in self.feature_configs:
            calculators['rdkit'] = RDKitCalculator()
        if 'dragon' in self.feature_configs:
            calculators['dragon'] = DragonCalculator()
        return calculators
    
    def preprocess_dataset(self, smiles_list, activities, save_dir):
        """
        预处理完整数据集
        """
        # 1. 验证SMILES
        valid_smiles, valid_activities = self.validate_smiles(smiles_list, activities)
        
        # 2. 计算所有特征
        features_dict = {}
        for name, calculator in self.calculators.items():
            print(f"计算{name}特征...")
            features = self.compute_features_batch(calculator, valid_smiles)
            features_dict[name] = features
        
        # 3. 保存预计算结果
        self.save_features(features_dict, save_dir)
        
        # 4. 构建数据集
        dataset = self.build_dataset(valid_smiles, features_dict, valid_activities)
        
        return dataset
    
    def compute_features_batch(self, calculator, smiles_list, batch_size=100):
        """
        批量计算特征以提高效率
        """
        features = []
        for i in tqdm(range(0, len(smiles_list), batch_size)):
            batch_smiles = smiles_list[i:i+batch_size]
            batch_features = []
            
            for smiles in batch_smiles:
                try:
                    feat = calculator.calculate(smiles)
                    batch_features.append(feat)
                except Exception as e:
                    print(f"计算特征失败 {smiles}: {e}")
                    batch_features.append(None)
            
            features.extend(batch_features)
        
        return self.handle_missing_features(features)
    
    def save_features(self, features_dict, save_dir):
        """
        保存预计算特征供后续使用
        """
        os.makedirs(save_dir, exist_ok=True)
        
        for name, features in features_dict.items():
            # 保存为多种格式
            np.save(f"{save_dir}/{name}_features.npy", features)
            pd.DataFrame(features).to_csv(f"{save_dir}/{name}_features.csv", index=False)
            
            # 保存元数据
            metadata = {
                'feature_count': features.shape[1],
                'sample_count': features.shape[0],
                'calculator_version': self.get_calculator_version(name),
                'computation_date': datetime.now().isoformat()
            }
            
            with open(f"{save_dir}/{name}_metadata.json", 'w') as f:
                json.dump(metadata, f, indent=2)
```

#### 特征版本管理

```python
class FeatureVersionManager:
    """
    特征版本管理系统
    
    确保实验的可重现性和特征的一致性
    """
    def __init__(self, base_dir="features"):
        self.base_dir = base_dir
    
    def create_feature_version(self, version_name, feature_configs):
        """
        创建特征版本
        """
        version_dir = f"{self.base_dir}/{version_name}"
        os.makedirs(version_dir, exist_ok=True)
        
        # 保存配置
        config_path = f"{version_dir}/config.yaml"
        with open(config_path, 'w') as f:
            yaml.dump(feature_configs, f)
        
        return version_dir
    
    def load_feature_version(self, version_name):
        """
        加载特定版本的特征
        """
        version_dir = f"{self.base_dir}/{version_name}"
        
        # 验证版本存在
        if not os.path.exists(version_dir):
            raise ValueError(f"特征版本 {version_name} 不存在")
        
        # 加载配置
        config_path = f"{version_dir}/config.yaml"
        with open(config_path, 'r') as f:
            config = yaml.load(f, Loader=yaml.FullLoader)
        
        # 加载特征
        features = {}
        for feature_type in config['feature_types']:
            feature_path = f"{version_dir}/{feature_type}_features.npy"
            features[feature_type] = np.load(feature_path)
        
        return features, config
    
    def ensure_consistency(self, version1, version2):
        """
        确保不同版本间的特征一致性
        """
        features1, config1 = self.load_feature_version(version1)
        features2, config2 = self.load_feature_version(version2)
        
        # 检查配置一致性
        assert config1['feature_types'] == config2['feature_types']
        
        # 检查数值一致性
        for feature_type in config1['feature_types']:
            np.testing.assert_allclose(
                features1[feature_type], 
                features2[feature_type],
                rtol=1e-10, 
                err_msg=f"{feature_type}特征不一致"
            )
        
        return True
```

### 2. 模型架构重构

#### 重构后的神农框架

```python
class ShennongV2(nn.Module):
    """
    神农框架 v2.0 - 外部特征注入架构
    
    核心改进:
    1. 分离特征计算与模型推理
    2. 保持所有创新机制
    3. 提供向后兼容性
    """
    def __init__(self, config, compatibility_mode=False):
        super().__init__()
        
        self.config = config
        self.compatibility_mode = compatibility_mode
        
        # 核心组件 (保持不变)
        self.gnn_branch = GNNBranch(
            config.node_dim, config.edge_dim, 
            config.graph_hidden_dim, config.num_gnn_layers
        )
        
        self.expert_branch = ExpertBranch(
            config.expert_input_dim, 
            config.expert_hidden_dim
        )
        
        # 创新机制 (完全保持)
        self.chemistry_attention = ChemistryGuidedAttention(
            config.graph_hidden_dim, 
            config.expert_hidden_dim, 
            config.chemistry_property_dim
        )
        
        self.adaptive_fusion = AdaptiveFeatureFusion(
            config.graph_hidden_dim, 
            config.expert_hidden_dim,
            config.fusion_output_dim
        )
        
        self.antimicrobial_predictor = AntimicrobialSpecializedPredictor(
            config.fusion_output_dim, 
            config.predictor_hidden_dim
        )
        
        self.interpretability = InterpretabilityModule(
            config.graph_hidden_dim, 
            config.expert_hidden_dim
        )
        
        # 向后兼容 (可选)
        if compatibility_mode:
            self.feature_calculator = MordredCalculator()
    
    def forward(self, mol_graph, external_features=None, chemistry_properties=None):
        """
        前向传播 - 支持外部特征注入
        
        Args:
            mol_graph: 分子图数据
            external_features: 预计算的外部特征 (v2.0主要模式)
            chemistry_properties: 化学性质特征
        """
        # 1. 图特征提取 (不变)
        graph_features = self.gnn_branch(mol_graph)
        
        # 2. 专家特征处理
        if external_features is not None:
            # v2.0模式: 使用外部特征
            expert_features = self.expert_branch(external_features)
        elif self.compatibility_mode:
            # 兼容模式: 内部计算 (逐渐废弃)
            warnings.warn("使用兼容模式，建议迁移到外部特征", DeprecationWarning)
            internal_features = self.feature_calculator.calculate_batch(mol_graph.smiles)
            expert_features = self.expert_branch(internal_features)
        else:
            raise ValueError("必须提供external_features或启用compatibility_mode")
        
        # 3. 化学性质特征
        if chemistry_properties is None:
            # 从分子图提取基础化学性质
            chemistry_properties = self.extract_basic_chemistry_properties(mol_graph)
        
        # 4. 核心创新机制 (完全保持)
        attended_graph, attended_expert, gate_weight = self.chemistry_attention(
            graph_features, expert_features, chemistry_properties
        )
        
        fused_features = self.adaptive_fusion(
            attended_graph, attended_expert, gate_weight
        )
        
        prediction_results = self.antimicrobial_predictor(fused_features)
        
        explanations = self.interpretability(
            graph_features, expert_features, mol_graph
        )
        
        return {
            'predictions': prediction_results,
            'explanations': explanations,
            'intermediate_features': {
                'graph_features': graph_features,
                'expert_features': expert_features,
                'fused_features': fused_features,
                'attention_weights': gate_weight
            }
        }
    
    def extract_basic_chemistry_properties(self, mol_graph):
        """
        从分子图提取基础化学性质
        """
        # 实现基础化学性质提取
        properties = []
        for mol in mol_graph.mols:
            prop = extract_chemistry_properties(mol)
            properties.append(prop)
        
        return torch.stack(properties)
```

### 3. 数据加载器重构

#### 新的数据加载器

```python
class ShennongDatasetV2(Dataset):
    """
    神农框架 v2.0 数据集
    
    支持外部特征加载和灵活的特征配置
    """
    def __init__(self, smiles_list, activities, feature_dir, feature_config):
        self.smiles_list = smiles_list
        self.activities = activities
        self.feature_dir = feature_dir
        self.feature_config = feature_config
        
        # 加载预计算特征
        self.features = self._load_features()
        
        # 构建分子图
        self.mol_graphs = self._build_mol_graphs()
        
        # 预计算化学性质
        self.chemistry_properties = self._precompute_chemistry_properties()
    
    def _load_features(self):
        """
        根据配置加载特征
        """
        features_dict = {}
        
        for feature_type in self.feature_config['feature_types']:
            feature_path = f"{self.feature_dir}/{feature_type}_features.npy"
            features_dict[feature_type] = np.load(feature_path)
        
        # 组合特征
        if len(features_dict) == 1:
            combined_features = list(features_dict.values())[0]
        else:
            combined_features = np.concatenate(list(features_dict.values()), axis=1)
        
        return torch.FloatTensor(combined_features)
    
    def _build_mol_graphs(self):
        """
        构建分子图
        """
        mol_graphs = []
        for smiles in self.smiles_list:
            mol = Chem.MolFromSmiles(smiles)
            mol_graph = smiles_to_graph(smiles)
            mol_graphs.append(mol_graph)
        
        return mol_graphs
    
    def _precompute_chemistry_properties(self):
        """
        预计算化学性质
        """
        properties = []
        for smiles in self.smiles_list:
            mol = Chem.MolFromSmiles(smiles)
            prop = extract_chemistry_properties(mol)
            properties.append(prop)
        
        return torch.stack(properties)
    
    def __len__(self):
        return len(self.smiles_list)
    
    def __getitem__(self, idx):
        return {
            'mol_graph': self.mol_graphs[idx],
            'features': self.features[idx],
            'chemistry_properties': self.chemistry_properties[idx],
            'activity': torch.FloatTensor([self.activities[idx]]),
            'smiles': self.smiles_list[idx]
        }

def create_dataloaders(dataset_config, feature_config, batch_size=64):
    """
    创建数据加载器
    """
    # 加载数据
    smiles_list, activities = load_antimicrobial_data(dataset_config['data_path'])
    
    # 数据分割
    train_smiles, test_smiles, train_activities, test_activities = train_test_split(
        smiles_list, activities, test_size=0.2, random_state=42, stratify=activities
    )
    
    train_smiles, val_smiles, train_activities, val_activities = train_test_split(
        train_smiles, train_activities, test_size=0.2, random_state=42, stratify=train_activities
    )
    
    # 创建数据集
    train_dataset = ShennongDatasetV2(
        train_smiles, train_activities, 
        feature_config['feature_dir'], feature_config
    )
    
    val_dataset = ShennongDatasetV2(
        val_smiles, val_activities,
        feature_config['feature_dir'], feature_config
    )
    
    test_dataset = ShennongDatasetV2(
        test_smiles, test_activities,
        feature_config['feature_dir'], feature_config
    )
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset, batch_size=batch_size, 
        shuffle=True, collate_fn=collate_fn
    )
    
    val_loader = DataLoader(
        val_dataset, batch_size=batch_size, 
        shuffle=False, collate_fn=collate_fn
    )
    
    test_loader = DataLoader(
        test_dataset, batch_size=batch_size, 
        shuffle=False, collate_fn=collate_fn
    )
    
    return train_loader, val_loader, test_loader
```

### 4. 实验框架重构

#### 统一的实验框架

```python
class ExperimentFramework:
    """
    统一的实验框架
    
    支持神农框架、Chemprop、AutoGluon的公平对比
    """
    def __init__(self, config):
        self.config = config
        self.feature_manager = FeatureVersionManager(config.feature_base_dir)
        
    def prepare_features(self, smiles_list, version_name):
        """
        为所有模型准备一致的特征
        """
        # 创建特征版本
        version_dir = self.feature_manager.create_feature_version(
            version_name, self.config.feature_config
        )
        
        # 数据预处理
        pipeline = DataPipeline(self.config.feature_config)
        dataset = pipeline.preprocess_dataset(
            smiles_list, activities, version_dir
        )
        
        return version_dir, dataset
    
    def run_shennong_experiment(self, feature_version, data_split):
        """
        运行神农框架实验
        """
        # 加载特征
        features, _ = self.feature_manager.load_feature_version(feature_version)
        
        # 创建数据加载器
        train_loader, val_loader, test_loader = create_dataloaders(
            data_split, {'feature_dir': f"features/{feature_version}", 'feature_types': ['mordred']}
        )
        
        # 创建模型
        model = ShennongV2(self.config.shennong_config)
        
        # 训练
        trainer = ShennongTrainer(model, self.config.training_config)
        results = trainer.train(train_loader, val_loader, test_loader)
        
        return results
    
    def run_chemprop_experiment(self, feature_version, data_split):
        """
        运行Chemprop实验 - 使用相同特征
        """
        # 准备Chemprop格式数据
        chemprop_data = self.prepare_chemprop_data(feature_version, data_split)
        
        # 运行Chemprop
        chemprop_results = run_chemprop_with_features(
            chemprop_data, self.config.chemprop_config
        )
        
        return chemprop_results
    
    def run_autogluon_experiment(self, feature_version, data_split):
        """
        运行AutoGluon实验 - 使用相同特征
        """
        # 准备表格数据
        tabular_data = self.prepare_tabular_data(feature_version, data_split)
        
        # 运行AutoGluon
        autogluon_results = run_autogluon_experiment(
            tabular_data, self.config.autogluon_config
        )
        
        return autogluon_results
    
    def run_comparative_study(self, smiles_list, activities):
        """
        运行完整的三模型对比研究
        """
        # 1. 准备一致的特征
        version_name = f"comparative_study_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        feature_version, dataset = self.prepare_features(smiles_list, version_name)
        
        # 2. 数据分割
        data_split = self.create_stratified_split(smiles_list, activities)
        
        # 3. 运行所有实验
        results = {}
        
        print("运行神农框架实验...")
        results['shennong'] = self.run_shennong_experiment(feature_version, data_split)
        
        print("运行Chemprop实验...")
        results['chemprop'] = self.run_chemprop_experiment(feature_version, data_split)
        
        print("运行AutoGluon实验...")
        results['autogluon'] = self.run_autogluon_experiment(feature_version, data_split)
        
        # 4. 结果对比和分析
        comparison = self.analyze_comparative_results(results)
        
        return comparison
```

## 迁移计划

### 阶段1: 基础设施建设 (第1-2周)

#### 1.1 特征预处理系统
```bash
# 创建特征预处理脚本
python scripts/preprocess_features.py \
    --input data/antimicrobial_8k.csv \
    --output features/v2_0_baseline \
    --feature_types mordred,rdkit \
    --batch_size 100
```

#### 1.2 特征版本管理
```python
# 建立特征版本管理
feature_manager = FeatureVersionManager()

# 创建基线版本
baseline_config = {
    'feature_types': ['mordred'],
    'mordred_version': '1.2.0',
    'normalization': 'standard',
    'missing_value_strategy': 'median_imputation'
}

feature_manager.create_feature_version('v2_0_baseline', baseline_config)
```

### 阶段2: 模型重构 (第3-4周)

#### 2.1 向后兼容实现
```python
# 确保向后兼容
model_v2 = ShennongV2(config, compatibility_mode=True)

# 验证结果一致性
v1_results = model_v1(mol_graph)
v2_results = model_v2(mol_graph, compatibility_mode=True)

assert torch.allclose(v1_results, v2_results, rtol=1e-6)
```

#### 2.2 新接口实现
```python
# 实现新接口
external_features = load_precomputed_features('features/v2_0_baseline/mordred_features.npy')
v2_results = model_v2(mol_graph, external_features)
```

### 阶段3: 数据流重构 (第5-6周)

#### 3.1 数据加载器更新
```python
# 新的数据加载器
train_loader = create_shennong_dataloader(
    smiles_list=train_smiles,
    activities=train_activities,
    feature_version='v2_0_baseline',
    batch_size=64
)
```

#### 3.2 训练流程更新
```python
# 更新训练流程
trainer = ShennongTrainerV2(model_v2, config)
results = trainer.train(train_loader, val_loader, test_loader)
```

### 阶段4: 实验验证 (第7-8周)

#### 4.1 性能基准测试
```python
# 基准测试
benchmark_results = run_benchmark_tests(
    models=['shennong_v1', 'shennong_v2'],
    datasets=['antimicrobial_8k'],
    metrics=['accuracy', 'auc', 'training_time', 'inference_time']
)
```

#### 4.2 三模型对比实验
```python
# 完整对比实验
comparative_results = experiment_framework.run_comparative_study(
    smiles_list, activities
)
```

## 质量保证

### 1. 单元测试

```python
class TestShennongV2(unittest.TestCase):
    def setUp(self):
        self.config = load_test_config()
        self.model = ShennongV2(self.config)
        self.test_data = load_test_data()
    
    def test_forward_pass(self):
        """测试前向传播"""
        mol_graph, features = self.test_data
        results = self.model(mol_graph, features)
        
        self.assertIn('predictions', results)
        self.assertIn('explanations', results)
    
    def test_feature_compatibility(self):
        """测试特征兼容性"""
        # 确保v1和v2使用相同外部特征时结果一致
        pass
    
    def test_backward_compatibility(self):
        """测试向后兼容性"""
        # 测试兼容模式
        pass
```

### 2. 集成测试

```python
def test_end_to_end_pipeline():
    """端到端流水线测试"""
    # 1. 特征预处理
    features = preprocess_features(test_smiles)
    
    # 2. 模型训练
    model = train_shennong_v2(features, test_activities)
    
    # 3. 预测
    predictions = model.predict(test_features)
    
    # 4. 验证
    assert len(predictions) == len(test_activities)

def test_comparative_experiment():
    """对比实验测试"""
    results = run_comparative_study(test_smiles, test_activities)
    
    # 验证所有模型都完成了训练
    assert 'shennong' in results
    assert 'chemprop' in results  
    assert 'autogluon' in results
```

### 3. 性能测试

```python
def benchmark_training_speed():
    """训练速度基准测试"""
    import time
    
    # v1.0训练时间
    start_time = time.time()
    train_shennong_v1(dataset)
    v1_time = time.time() - start_time
    
    # v2.0训练时间
    start_time = time.time()
    train_shennong_v2(dataset_with_precomputed_features)
    v2_time = time.time() - start_time
    
    # v2.0应该显著更快
    assert v2_time < v1_time * 0.5  # 至少快50%

def benchmark_memory_usage():
    """内存使用基准测试"""
    import psutil
    
    # 测试内存使用模式
    v1_memory = measure_memory_usage(train_shennong_v1)
    v2_memory = measure_memory_usage(train_shennong_v2)
    
    # v2.0内存使用应该更稳定
    assert v2_memory['peak'] < v1_memory['peak']
```

## 风险评估与缓解

### 风险识别

1. **兼容性风险**: v2.0与v1.0结果可能存在细微差异
2. **性能风险**: 重构可能引入新的性能问题
3. **时间风险**: 重构可能影响研究进度

### 缓解策略

#### 1. 兼容性保证
```python
def ensure_compatibility():
    """确保兼容性的策略"""
    
    # 严格的回归测试
    test_cases = load_regression_test_cases()
    
    for test_case in test_cases:
        v1_result = shennong_v1.predict(test_case.input)
        v2_result = shennong_v2.predict(test_case.input, test_case.features)
        
        # 允许极小的数值误差
        assert torch.allclose(v1_result, v2_result, rtol=1e-6, atol=1e-8)
```

#### 2. 渐进式迁移
```python
def gradual_migration():
    """渐进式迁移策略"""
    
    # 阶段1: 同时运行v1和v2，对比结果
    run_parallel_validation()
    
    # 阶段2: 逐步切换实验到v2
    migrate_experiments_to_v2()
    
    # 阶段3: 完全废弃v1
    deprecate_v1()
```

#### 3. 回滚方案
```python
def rollback_plan():
    """回滚方案"""
    
    # 保持v1代码分支
    git_maintain_v1_branch()
    
    # 文档化回滚流程
    document_rollback_procedures()
    
    # 自动化回滚脚本
    create_automated_rollback_scripts()
```

## 预期收益

### 1. 科学严谨性提升
- 确保与Chemprop、AutoGluon使用相同特征
- 提高实验结果的可信度
- 增强论文的说服力

### 2. 计算效率提升
- 训练时间减少50-80%
- 内存使用更稳定
- 支持更大规模实验

### 3. 开发效率提升
- 代码更易维护和调试
- 特征工程实验更灵活
- 新功能开发更容易

### 4. 实验灵活性提升
- 快速切换不同特征集
- 支持特征选择实验
- 易于进行消融实验

## 相关文件

- [[30_神农框架/30.01_神农框架核心架构与创新点]]
- [[20_核心概念/特征工程/内部集成 vs 外部注入策略对比]]
- [[10_研究项目/神农框架 vs Chemprop vs AutoGluon 对比研究]]

---

*创建时间: 2024-01-XX*  
*最后更新: 2024-01-XX*  
*状态: 重构计划制定完成* 