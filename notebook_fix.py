# 🔧 Notebook 环境修复代码 - 复制到notebook第一个cell运行

import os
import sys
import warnings
warnings.filterwarnings('ignore')

# 1. 修复工作目录
target_dir = "/mnt/e/新建文件夹/Shennong"
if os.path.exists(target_dir):
    os.chdir(target_dir)
    print(f"✅ 切换到: {os.getcwd()}")
else:
    print(f"❌ 目录不存在: {target_dir}")

# 2. 创建内置数据生成函数
def create_quick_data():
    import numpy as np
    
    np.random.seed(42)
    n_samples = 1000
    
    # 简单的SMILES和活性数据
    active_smiles = ["CC(C)CC1=CC=C(C=C1)C(C)C(=O)O"] * 300
    inactive_smiles = ["CCCCCCCCCCCCCCCC"] * 700
    
    all_smiles = active_smiles + inactive_smiles
    all_activities = [1] * 300 + [0] * 700
    all_mics = (np.random.lognormal(1.5, 0.8, 300).tolist() + 
                np.random.lognormal(4.5, 0.8, 700).tolist())
    
    # 分子特征 (模拟)
    mol_weights = [200 + np.random.normal(0, 50) for _ in range(n_samples)]
    logp_values = [2 + np.random.normal(0, 1) for _ in range(n_samples)]
    
    return {
        'smiles': all_smiles,
        'activity': all_activities,
        'mic_ug_ml': all_mics,
        'mol_weight': mol_weights,
        'logp': logp_values
    }

# 3. 创建数据并保存
try:
    import pandas as pd
    
    # 创建输出目录
    os.makedirs('data/examples', exist_ok=True)
    
    # 生成数据
    data_dict = create_quick_data()
    df = pd.DataFrame(data_dict)
    
    # 简单划分
    train_df = df.iloc[:800].copy()
    test_df = df.iloc[800:].copy()
    
    # 保存文件
    train_df.to_csv('data/examples/train_data.csv', index=False)
    test_df.to_csv('data/examples/test_data.csv', index=False)
    
    print(f"✅ 数据创建完成:")
    print(f"   训练集: {len(train_df)} 样本")
    print(f"   测试集: {len(test_df)} 样本")
    print(f"   活性分布: {train_df['activity'].value_counts().to_dict()}")
    
    # 设置全局变量供notebook使用
    globals()['train_df'] = train_df
    globals()['test_df'] = test_df
    
except Exception as e:
    print(f"❌ 数据创建失败: {e}")
    print("💡 请先安装pandas: pip install pandas")

print("🧬 环境修复完成，可以继续运行notebook其他cells！") 