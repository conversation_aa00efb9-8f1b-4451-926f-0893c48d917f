{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# 🔍 神农框架可解释性分析教程\n",
    "\n",
    "**作者**: ZK  \n",
    "**邮箱**: <EMAIL>  \n",
    "**日期**: 2025-06-29\n",
    "\n",
    "本教程将详细介绍如何使用神农框架的可解释性功能，将AI预测结果转化为化学家能理解的信息。\n",
    "\n",
    "## 📋 教程内容\n",
    "\n",
    "1. **注意力机制解释** - 理解AI模型关注的分子区域\n",
    "2. **Mordred描述符分析** - 解释重要的分子性质\n",
    "3. **抗菌机制解释** - 基于结构特征的机制分析\n",
    "4. **药效团识别** - 识别关键的药效团特征\n",
    "5. **综合化学解释** - 生成化学家友好的解释报告\n",
    "\n",
    "## 🎯 学习目标\n",
    "\n",
    "- 理解AI预测的化学基础\n",
    "- 识别重要的分子特征\n",
    "- 解释抗菌机制的结构基础\n",
    "- 生成可解释的预测报告\n",
    "\n",
    "预计完成时间：**45-60分钟**"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 🔧 环境准备"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "import numpy as np\n",
    "import pandas as pd\n",
    "import matplotlib.pyplot as plt\n",
    "import seaborn as sns\n",
    "from pathlib import Path\n",
    "import warnings\n",
    "warnings.filterwarnings('ignore')\n",
    "\n",
    "# 导入神农框架可解释性模块\n",
    "from shennong.interpretation import (\n",
    "    ChemicalExplainer,\n",
    "    AttentionInterpreter,\n",
    "    DescriptorAnalyzer,\n",
    "    MechanismExplainer,\n",
    "    PharmacophoreAnalyzer\n",
    ")\n",
    "\n",
    "# 导入其他必要模块\n",
    "from shennong.models.shennong_core import ShennongFramework\n",
    "from shennong.featurizers.molecule import MordredFeaturizer\n",
    "from rdkit import Chem\n",
    "from rdkit.Chem import Draw, Descriptors\n",
    "from IPython.display import display, HTML\n",
    "\n",
    "# 设置绘图风格\n",
    "plt.style.use('default')\n",
    "sns.set_palette(\"husl\")\n",
    "\n",
    "print(\"✅ 环境准备完成\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 🧬 示例分子准备\n",
    "\n",
    "我们将使用几个典型的抗菌化合物来演示可解释性分析。"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 准备示例抗菌化合物\n",
    "example_compounds = {\n",
    "    'ciprofloxacin': {\n",
    "        'smiles': 'O=C(O)C1=CN(C2CC2)C3=C(C=C(F)C(N4CCNCC4)=C3F)C1=O',\n",
    "        'name': '环丙沙星',\n",
    "        'mechanism': 'dna_replication',\n",
    "        'activity': 0.25,\n",
    "        'description': '氟喹诺酮类抗生素，通过抑制DNA回旋酶发挥作用'\n",
    "    },\n",
    "    'penicillin_g': {\n",
    "        'smiles': 'CC1([C@@H](N2[C@H](S1)[C@@H](C2=O)NC(=O)CC3=CC=CC=C3)C(=O)O)C',\n",
    "        'name': '青霉素G',\n",
    "        'mechanism': 'cell_wall_synthesis',\n",
    "        'activity': 0.1,\n",
    "        'description': 'β-内酰胺类抗生素，抑制细胞壁合成'\n",
    "    },\n",
    "    'chloramphenicol': {\n",
    "        'smiles': 'O=C(NC(C(O)C1=CC=C([N+]([O-])=O)C=C1)CO)C(Cl)Cl',\n",
    "        'name': '氯霉素',\n",
    "        'mechanism': 'protein_synthesis',\n",
    "        'activity': 2.0,\n",
    "        'description': '抑制蛋白质合成的抗生素'\n",
    "    }\n",
    "}\n",
    "\n",
    "# 显示分子结构\n",
    "print(\"🧪 示例抗菌化合物:\")\n",
    "for compound_id, info in example_compounds.items():\n",
    "    mol = Chem.MolFromSmiles(info['smiles'])\n",
    "    if mol:\n",
    "        print(f\"\\n{info['name']} ({compound_id}):\")\n",
    "        print(f\"  机制: {info['mechanism']}\")\n",
    "        print(f\"  活性: {info['activity']} μg/mL\")\n",
    "        print(f\"  描述: {info['description']}\")\n",
    "        \n",
    "        # 显示分子结构（在Jupyter中）\n",
    "        try:\n",
    "            img = Draw.MolToImage(mol, size=(300, 200))\n",
    "            display(img)\n",
    "        except:\n",
    "            print(\"  (分子结构显示需要在Jupyter环境中)\")\n",
    "\n",
    "print(\"\\n✅ 示例分子准备完成\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 🎯 模拟AI预测结果\n",
    "\n",
    "由于我们可能没有训练好的模型，我们将模拟AI预测的结果，包括注意力权重和描述符。"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def simulate_prediction_results(smiles, true_mechanism, true_activity):\n",
    "    \"\"\"\n",
    "    模拟AI预测结果，包括注意力权重和描述符\n",
    "    \"\"\"\n",
    "    mol = Chem.MolFromSmiles(smiles)\n",
    "    if mol is None:\n",
    "        return None\n",
    "    \n",
    "    num_atoms = mol.GetNumAtoms()\n",
    "    \n",
    "    # 模拟注意力权重（突出重要的化学基团）\n",
    "    attention_weights = {\n",
    "        'graph_attention': np.random.beta(2, 5, num_atoms),  # 偏向较低值\n",
    "        'expert_attention': np.random.beta(2, 5, num_atoms),\n",
    "        'fusion_attention': np.random.beta(2, 5, num_atoms)\n",
    "    }\n",
    "    \n",
    "    # 为特定原子类型增加注意力权重（模拟化学直觉）\n",
    "    for i, atom in enumerate(mol.GetAtoms()):\n",
    "        # 杂原子通常更重要\n",
    "        if atom.GetSymbol() in ['N', 'O', 'F', 'S', 'Cl']:\n",
    "            for key in attention_weights:\n",
    "                attention_weights[key][i] *= 2.0\n",
    "        \n",
    "        # 芳香环原子\n",
    "        if atom.GetIsAromatic():\n",
    "            for key in attention_weights:\n",
    "                attention_weights[key][i] *= 1.5\n",
    "    \n",
    "    # 归一化注意力权重\n",
    "    for key in attention_weights:\n",
    "        attention_weights[key] = attention_weights[key] / np.sum(attention_weights[key])\n",
    "    \n",
    "    # 模拟Mordred描述符\n",
    "    try:\n",
    "        featurizer = MordredFeaturizer()\n",
    "        descriptors = featurizer.featurize(smiles)\n",
    "        descriptor_names = featurizer.get_descriptor_names()\n",
    "    except:\n",
    "        # 如果Mordred不可用，使用基本描述符\n",
    "        descriptors = np.array([\n",
    "            Descriptors.MolWt(mol),\n",
    "            Descriptors.MolLogP(mol),\n",
    "            Descriptors.NumHDonors(mol),\n",
    "            Descriptors.NumHAcceptors(mol),\n",
    "            Descriptors.TPSA(mol),\n",
    "            Descriptors.NumRotatableBonds(mol)\n",
    "        ])\n",
    "        descriptor_names = ['MW', 'LogP', 'HBD', 'HBA', 'TPSA', 'RotBonds']\n",
    "    \n",
    "    # 添加一些噪声到活性预测\n",
    "    predicted_activity = true_activity * (1 + np.random.normal(0, 0.1))\n",
    "    predicted_activity = max(0.01, predicted_activity)  # 确保为正值\n",
    "    \n",
    "    return {\n",
    "        'predicted_activity': predicted_activity,\n",
    "        'predicted_mechanism': true_mechanism,  # 假设预测正确\n",
    "        'confidence': 0.85 + np.random.normal(0, 0.05),\n",
    "        'attention_weights': attention_weights,\n",
    "        'descriptors': descriptors,\n",
    "        'descriptor_names': descriptor_names\n",
    "    }\n",
    "\n",
    "# 为每个示例化合物生成模拟预测结果\n",
    "prediction_results = {}\n",
    "\n",
    "for compound_id, info in example_compounds.items():\n",
    "    results = simulate_prediction_results(\n",
    "        info['smiles'], \n",
    "        info['mechanism'], \n",
    "        info['activity']\n",
    "    )\n",
    "    if results:\n",
    "        prediction_results[compound_id] = results\n",
    "        print(f\"✅ {info['name']} 预测结果生成完成\")\n",
    "\n",
    "print("\\n🎯 AI预测结果模拟完成")
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 🔍 注意力机制解释\n",
    "\n",
    "首先，我们来分析AI模型的注意力权重，理解模型关注的分子区域。"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 初始化注意力解释器\n",
    "attention_interpreter = AttentionInterpreter()\n",
    "\n",
    "# 分析环丙沙星的注意力权重\n",
    "compound_id = 'ciprofloxacin'\n",
    "compound_info = example_compounds[compound_id]\n",
    "prediction = prediction_results[compound_id]\n",
    "\n",
    "print(f\"🔍 分析 {compound_info['name']} 的注意力权重...\")\n",
    "print(f\"SMILES: {compound_info['smiles']}\")\n",
    "\n",
    "# 执行注意力分析\n",
    "attention_analysis = attention_interpreter.interpret_attention(\n",
    "    smiles=compound_info['smiles'],\n",
    "    attention_weights=prediction['attention_weights'],\n",
    "    predicted_mechanism=prediction['predicted_mechanism']\n",
    ")\n",
    "\n",
    "# 显示注意力分析结果\n",
    "print(\"\\n📊 注意力权重统计:\")\n",
    "for layer, stats in attention_analysis['attention_summary'].items():\n",
    "    print(f\"  {layer}:\")\n",
    "    print(f\"    平均值: {stats['mean']:.4f}\")\n",
    "    print(f\"    最大值: {stats['max']:.4f}\")\n",
    "    print(f\"    熵值: {stats['entropy']:.4f}\")\n",
    "\n",
    "print(\"\\n🎯 关键区域识别:\")\n",
    "for i, region in enumerate(attention_analysis['key_regions'][:3]):\n",
    "    print(f\"  区域 {i+1}: {region['description']}\")\n",
    "    print(f\"    原子索引: {region['atom_index']}\")\n",
    "    print(f\"    注意力权重: {region['attention_weight']:.4f}\")\n",
    "    print(f\"    原子类型: {region['atom_type']}\")\n",
    "\n",
    "print(f\"\\n🧬 机制相关性分析:\")\n",
    "mechanism_rel = attention_analysis['mechanism_relevance']\n",
    "print(f\"  相关性分数: {mechanism_rel['relevance_score']:.4f}\")\n",
    "print(f\"  机制支持: {'是' if mechanism_rel['mechanism_support'] else '否'}\")\n",
    "print(f\"  解释: {mechanism_rel['explanation']}\")\n",
    "\n",
    "print(f\"\\n🔗 注意力一致性: {attention_analysis['attention_consistency']:.4f}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 📊 Mordred描述符分析\n",
    "\n",
    "接下来分析Mordred描述符，理解重要的分子性质。"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 初始化描述符分析器\n",
    "descriptor_analyzer = DescriptorAnalyzer()\n",
    "\n",
    "print(f\"📊 分析 {compound_info['name']} 的分子描述符...\")\n",
    "\n",
    "# 执行描述符分析\n",
    "descriptor_analysis = descriptor_analyzer.analyze_descriptors(\n",
    "    smiles=compound_info['smiles'],\n",
    "    descriptor_values=prediction['descriptors'],\n",
    "    descriptor_names=prediction['descriptor_names'],\n",
    "    predicted_activity=prediction['predicted_activity']\n",
    ")\n",
    "\n",
    "# 显示描述符质量\n",
    "quality = descriptor_analysis['descriptor_quality']\n",
    "print(f\"\\n✅ 描述符质量评估:\")\n",
    "print(f\"  总描述符数: {quality['total_descriptors']}\")\n",
    "print(f\"  有效描述符数: {quality['valid_descriptors']}\")\n",
    "print(f\"  成功率: {quality['success_rate']:.2%}\")\n",
    "\n",
    "# 显示重要描述符\n",
    "print(f\"\\n🎯 重要描述符 (前5个):\")\n",
    "for i, desc in enumerate(descriptor_analysis['important_descriptors'][:5]):\n",
    "    print(f\"  {i+1}. {desc['name']}\")\n",
    "    print(f\"     数值: {desc['value']:.4f}\")\n",
    "    print(f\"     重要性: {desc['importance_score']:.4f}\")\n",
    "    print(f\"     类别: {desc['category']}\")\n",
    "    print(f\"     意义: {desc['chemical_meaning']}\")\n",
    "    print()\n",
    "\n",
    "# 显示类别分析\n",
    "print(f\"📈 描述符类别分析:\")\n",
    "for category, stats in descriptor_analysis['category_analysis'].items():\n",
    "    print(f\"  {category}:\")\n",
    "    print(f\"    数量: {stats['count']}\")\n",
    "    print(f\"    平均值: {stats['mean']:.4f}\")\n",
    "    print(f\"    描述: {stats['description']}\")\n",
    "\n",
    "# 显示化学解释\n",
    "if 'key_properties' in descriptor_analysis['chemical_interpretation']:\n",
    "    print(f\"\\n🧪 关键化学性质:\")\n",
    "    for prop in descriptor_analysis['chemical_interpretation']['key_properties']:\n",
    "        print(f\"  • {prop['interpretation']}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 🧬 抗菌机制解释\n",
    "\n",
    "基于分子结构特征解释预测的抗菌机制。"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 初始化机制解释器\n",
    "mechanism_explainer = MechanismExplainer()\n",
    "\n",
    "print(f\"🧬 分析 {compound_info['name']} 的抗菌机制...\")\n",
    "\n",
    "# 执行机制分析\n",
    "mechanism_analysis = mechanism_explainer.explain_mechanism(\n",
    "    smiles=compound_info['smiles'],\n",
    "    predicted_mechanism=prediction['predicted_mechanism'],\n",
    "    predicted_activity=prediction['predicted_activity']\n",
    ")\n",
    "\n",
    "# 显示机制信息\n",
    "mech_info = mechanism_analysis['mechanism_info']\n",
    "print(f\"\\n📋 机制基本信息:\")\n",
    "print(f\"  名称: {mech_info['name']}\")\n",
    "print(f\"  描述: {mech_info['description']}\")\n",
    "print(f\"  靶点: {', '.join(mech_info['targets']) if mech_info['targets'] else '未知'}\")\n",
    "print(f\"  革兰氏特异性: {mech_info['gram_specificity']}\")\n",
    "\n",
    "# 显示结构基础\n",
    "struct_basis = mechanism_analysis['structural_basis']\n",
    "if 'matched_patterns' in struct_basis:\n",
    "    print(f\"\\n🔍 结构特征匹配:\")\n",
    "    for pattern in struct_basis['matched_patterns']:\n",
    "        print(f\"  • {pattern['description']}: {pattern['matches']} 个匹配\")\n",
    "    \n",
    "    print(f\"\\n📊 结构匹配评分:\")\n",
    "    print(f\"  分子量: {struct_basis['molecular_weight']:.1f} Da\")\n",
    "    print(f\"  分子量匹配: {'是' if struct_basis['mw_range_match'] else '否'}\")\n",
    "    print(f\"  结构评分: {struct_basis['structure_score']:.4f}\")\n",
    "    print(f\"  机制支持: {'是' if struct_basis['mechanism_support'] else '否'}\")\n",
    "\n",
    "# 显示机制置信度\n",
    "confidence = mechanism_analysis['mechanism_confidence']\n",
    "print(f\"\\n🎯 机制预测置信度: {confidence:.4f}\")\n",
    "\n",
    "# 显示替代机制\n",
    "if mechanism_analysis['alternative_mechanisms']:\n",
    "    print(f\"\\n🔄 可能的替代机制:\")\n",
    "    for alt in mechanism_analysis['alternative_mechanisms']:\n",
    "        print(f\"  • {alt['mechanism']}: 置信度 {alt['confidence']:.4f}\")\n",
    "        print(f\"    {alt['description']}\")\n",
    "\n",
    "# 显示化学解释\n",
    "print(f\"\\n💡 化学解释:\")\n",
    "print(f\"  {mechanism_analysis['chemical_rationale']}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 💊 药效团分析\n",
    "\n",
    "识别和分析分子中的药效团特征。"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 初始化药效团分析器\n",
    "pharmacophore_analyzer = PharmacophoreAnalyzer()\n",
    "\n",
    "print(f\"💊 分析 {compound_info['name']} 的药效团特征...\")\n",
    "\n",
    "# 执行药效团分析\n",
    "pharmacophore_analysis = pharmacophore_analyzer.analyze_pharmacophores(\n",
    "    smiles=compound_info['smiles'],\n",
    "    predicted_mechanism=prediction['predicted_mechanism'],\n",
    "    attention_weights=prediction['attention_weights']\n",
    ")\n",
    "\n",
    "# 显示识别的药效团\n",
    "print(f\"\\n🔍 识别的药效团特征:\")\n",
    "for i, pharmacophore in enumerate(pharmacophore_analysis['identified_pharmacophores']):\n",
    "    print(f\"  {i+1}. {pharmacophore['description']}\")\n",
    "    print(f\"     类型: {pharmacophore['type']}\")\n",
    "    print(f\"     数量: {pharmacophore['count']}\")\n",
    "    print(f\"     重要性: {pharmacophore['importance']}\")\n",
    "    print(f\"     化学意义: {pharmacophore['chemical_meaning']}\")\n",
    "    if 'mechanism' in pharmacophore:\n",
    "        print(f\"     相关机制: {pharmacophore['mechanism']}\")\n",
    "    print()\n",
    "\n",
    "# 显示注意力-药效团关联\n",
    "attention_corr = pharmacophore_analysis['attention_pharmacophore_correlation']\n",
    "if 'pharmacophore_attention_scores' in attention_corr:\n",
    "    print(f\"🎯 药效团注意力权重排名:\")\n",
    "    for score in attention_corr['pharmacophore_attention_scores'][:5]:\n",
    "        print(f\"  {score['attention_rank']}. {score['description']}\")\n",
    "        print(f\"     平均注意力: {score['average_attention']:.4f}\")\n",
    "        print(f\"     AI重要性: {score['ai_importance']}\")\n",
    "\n",
    "# 显示机制特异性特征\n",
    "mech_features = pharmacophore_analysis['mechanism_specific_features']\n",
    "print(f\"\\n🧬 机制特异性特征:\")\n",
    "print(f\"  机制支持: {'是' if mech_features['mechanism_support'] else '否'}\")\n",
    "print(f\"  支持评分: {mech_features['support_score']:.4f}\")\n",
    "print(f\"  解释: {mech_features['interpretation']}\")\n",
    "\n",
    "if mech_features['mechanism_specific_features']:\n",
    "    print(f\"  特异性特征:\")\n",
    "    for feature in mech_features['mechanism_specific_features']:\n",
    "        print(f\"    • {feature['description']}: {feature['count']} 个\")\n",
    "\n",
    "# 显示化学解释\n",
    "chem_interp = pharmacophore_analysis['chemical_interpretation']\n",
    "print(f\"\\n🧪 化学性质解释:\")\n",
    "props = chem_interp['basic_properties']\n",
    "print(f\"  分子量: {props['molecular_weight']:.1f} Da\")\n",
    "print(f\"  LogP: {props['logp']:.2f}\")\n",
    "print(f\"  氢键供体: {props['hbd']}\")\n",
    "print(f\"  氢键受体: {props['hba']}\")\n",
    "\n",
    "print(f\"\\n💡 化学解释:\")\n",
    "for interpretation in chem_interp['chemical_interpretations']:\n",
    "    print(f\"  • {interpretation}\")\n",
    "\n",
    "# 药物相似性评估\n",
    "drug_assess = chem_interp['drug_likeness_assessment']\n",
    "print(f\"\\n💊 药物相似性评估:\")\n",
    "print(f\"  评分: {drug_assess['drug_likeness_score']:.2f}\")\n",
    "print(f\"  总体评估: {drug_assess['overall_assessment']}\")\n",
    "for assessment in drug_assess['assessments']:\n",
    "    print(f\"  • {assessment}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 🎯 综合化学解释\n",
    "\n",
    "现在我们使用ChemicalExplainer来生成完整的化学解释报告。"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 初始化综合化学解释器\n",
    "chemical_explainer = ChemicalExplainer()\n",
    "\n",
    "print(f\"🎯 生成 {compound_info['name']} 的综合化学解释...\")\n",
    "\n",
    "# 生成完整的化学解释\n",
    "explanation = chemical_explainer.explain_prediction(\n",
    "    smiles=compound_info['smiles'],\n",
    "    predicted_activity=prediction['predicted_activity'],\n",
    "    predicted_mechanism=prediction['predicted_mechanism'],\n",
    "    attention_weights=prediction['attention_weights'],\n",
    "    descriptor_values=prediction['descriptors'],\n",
    "    descriptor_names=prediction['descriptor_names'],\n",
    "    confidence=prediction['confidence']\n",
    ")\n",
    "\n",
    "# 生成并显示化学家友好的报告\n",
    "report = chemical_explainer.generate_report(explanation)\n",
    "print(\"\\n\" + \"=\"*60)\n",
    "print(report)\n",
    "print(\"=\"*60)"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 💡 可解释性总结\n",
    "\n",
    "总结神农框架的可解释性能力和化学家的应用价值。"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "print(\"💡 神农框架可解释性总结\")\n",
    "print(\"=\" * 50)\n",
    "\n",
    "print(\"\\n🔍 可解释性维度:\")\n",
    "print(\"1. **注意力机制解释**\")\n",
    "print(\"   • 识别AI模型关注的分子区域\")\n",
    "print(\"   • 分析注意力权重与化学基团的关联\")\n",
    "print(\"   • 评估不同层注意力的一致性\")\n",
    "\n",
    "print(\"\\n2. **Mordred描述符分析**\")\n",
    "print(\"   • 识别对活性重要的分子性质\")\n",
    "print(\"   • 提供描述符的化学意义解释\")\n",
    "print(\"   • 分析构效关系的定量基础\")\n",
    "\n",
    "print(\"\\n3. **抗菌机制解释**\")\n",
    "print(\"   • 基于结构特征验证预测机制\")\n",
    "print(\"   • 识别机制特异性的化学模式\")\n",
    "print(\"   • 提供替代机制的可能性分析\")\n",
    "\n",
    "print(\"\\n4. **药效团识别**\")\n",
    "print(\"   • 识别关键的药效团特征\")\n",
    "print(\"   • 关联注意力权重与药效团重要性\")\n",
    "print(\"   • 评估药物相似性和成药性\")\n",
    "\n",
    "print(\"\\n🎯 化学家应用价值:\")\n",
    "print(\"• **理解AI决策**: 明确AI模型的预测依据\")\n",
    "print(\"• **验证预测合理性**: 基于化学知识验证结果\")\n",
    "print(\"• **指导分子设计**: 识别关键结构特征进行优化\")\n",
    "print(\"• **机制假设生成**: 为实验验证提供假设\")\n",
    "print(\"• **构效关系洞察**: 深入理解分子性质与活性的关系\")\n",
    "\n",
    "print(\"\\n🔬 实际应用场景:\")\n",
    "print(\"• **先导化合物优化**: 基于解释结果优化分子结构\")\n",
    "print(\"• **机制研究**: 为抗菌机制研究提供计算支持\")\n",
    "print(\"• **虚拟筛选**: 结合可解释性进行智能化合物筛选\")\n",
    "print(\"• **教学培训**: 帮助理解分子设计的化学原理\")\n",
    "\n",
    "print(\"\\n✨ 神农框架的独特优势:\")\n",
    "print(\"• **多层次解释**: 从原子到分子的全方位分析\")\n",
    "print(\"• **生物学导向**: 专注于抗菌活性的机制解释\")\n",
    "print(\"• **化学家友好**: 提供符合化学直觉的解释\")\n",
    "print(\"• **定量与定性结合**: 数值分析与化学解释并重\")\n",
    "\n",
    "print(\"\\n🚀 未来发展方向:\")\n",
    "print(\"• **3D结构解释**: 结合分子3D构象的解释\")\n",
    "print(\"• **动态机制分析**: 考虑分子动力学的解释\")\n",
    "print(\"• **实验验证集成**: 与实验数据的深度整合\")\n",
    "print(\"• **个性化解释**: 针对不同用户需求的定制化解释\")\n",
    "\n",
    "print(\"\\n\" + \"=\" * 50)\n",
    "print(\"🧬 神农尝百草，AI识良药 - 让AI预测更加透明可信！\")\n",
    "print(\"=\" * 50)"
   ]
  }"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",\n",
   "language": "python",\n",
   "name": "python3"\n",
  },\n",
   "language_info": {\n",
   "codemirror_mode": {\n",
    "name": "ipython",\n",
    "version": 3\n",
   },\n",
   "file_extension": ".py",\n",
   "mimetype": "text/x-python",\n",
   "name": "python",\n",
   "nbconvert_exporter": "python",\n",
   "pygments_lexer": "ipython3",\n",
   "version": "3.9.0"\n",
  }\n",
 },\n",
 "nbformat": 4,\n",
 "nbformat_minor": 4\n}