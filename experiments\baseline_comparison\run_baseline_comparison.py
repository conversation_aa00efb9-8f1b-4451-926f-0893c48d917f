#!/usr/bin/env python3
# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-30
# 描述: 神农框架与ChemProp基线对比实验

"""
神农框架与ChemProp基线对比实验
直接运行版本，不需要Jupyter
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import roc_auc_score, f1_score, precision_score, recall_score, accuracy_score
from sklearn.metrics import classification_report, confusion_matrix
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import time
import warnings
warnings.filterwarnings('ignore')

def load_data():
    """加载数据"""
    print("📊 加载数据...")
    
    train_df = pd.read_csv('../../data/examples/train_data.csv')
    test_df = pd.read_csv('../../data/examples/test_data.csv')
    
    print(f"训练集: {len(train_df)} 样本")
    print(f"测试集: {len(test_df)} 样本")
    print(f"训练集活性分布: {train_df['activity'].value_counts().to_dict()}")
    
    return train_df, test_df

def generate_mordred_features(smiles_list):
    """生成Mordred特征"""
    print("📊 生成Mordred分子描述符...")
    
    from mordred import Calculator, descriptors
    from rdkit import Chem
    
    calc = Calculator(descriptors, ignore_3D=True)
    
    mols = [Chem.MolFromSmiles(smi) for smi in smiles_list]
    features = calc.pandas(mols)
    
    # 处理无效值
    features = features.select_dtypes(include=[np.number])
    features = features.fillna(0)
    
    return features

class ShennongNet(nn.Module):
    """神农框架简化神经网络"""
    def __init__(self, input_dim):
        super(ShennongNet, self).__init__()
        self.fc1 = nn.Linear(input_dim, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, 128)
        self.fc4 = nn.Linear(128, 1)
        self.dropout = nn.Dropout(0.3)
        self.relu = nn.ReLU()
        self.sigmoid = nn.Sigmoid()
        
    def forward(self, x):
        x = self.relu(self.fc1(x))
        x = self.dropout(x)
        x = self.relu(self.fc2(x))
        x = self.dropout(x)
        x = self.relu(self.fc3(x))
        x = self.dropout(x)
        x = self.sigmoid(self.fc4(x))
        return x

def train_random_forest(X_train, y_train, X_test, y_test):
    """训练随机森林模型"""
    print("🌲 训练随机森林模型...")
    
    start_time = time.time()
    rf_model = RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=-1)
    rf_model.fit(X_train, y_train)
    train_time = time.time() - start_time
    
    start_time = time.time()
    pred_proba = rf_model.predict_proba(X_test)[:, 1]
    pred = rf_model.predict(X_test)
    pred_time = time.time() - start_time
    
    # 计算指标
    auc = roc_auc_score(y_test, pred_proba)
    f1 = f1_score(y_test, pred)
    precision = precision_score(y_test, pred)
    recall = recall_score(y_test, pred)
    accuracy = accuracy_score(y_test, pred)
    
    print(f"✅ 随机森林完成 (训练: {train_time:.1f}s, 预测: {pred_time:.3f}s)")
    print(f"   AUC: {auc:.4f}, F1: {f1:.4f}, Precision: {precision:.4f}")
    
    return {
        'auc': auc, 'f1': f1, 'precision': precision, 'recall': recall, 'accuracy': accuracy,
        'train_time': train_time, 'pred_time': pred_time
    }

def train_neural_network(X_train, y_train, X_test, y_test):
    """训练神经网络模型"""
    print("🧠 训练神经网络模型 (神农框架简化版)...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 准备数据
    X_train_tensor = torch.FloatTensor(X_train).to(device)
    y_train_tensor = torch.FloatTensor(y_train).unsqueeze(1).to(device)
    X_test_tensor = torch.FloatTensor(X_test).to(device)
    
    train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
    train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
    
    # 初始化模型
    model = ShennongNet(X_train.shape[1]).to(device)
    criterion = nn.BCELoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001)
    
    # 训练
    start_time = time.time()
    model.train()
    for epoch in range(50):
        epoch_loss = 0
        for batch_x, batch_y in train_loader:
            optimizer.zero_grad()
            outputs = model(batch_x)
            loss = criterion(outputs, batch_y)
            loss.backward()
            optimizer.step()
            epoch_loss += loss.item()
        
        if (epoch + 1) % 10 == 0:
            print(f"  Epoch {epoch+1}/50, Loss: {epoch_loss/len(train_loader):.4f}")
    
    train_time = time.time() - start_time
    
    # 预测
    start_time = time.time()
    model.eval()
    with torch.no_grad():
        pred_proba = model(X_test_tensor).cpu().numpy().flatten()
        pred = (pred_proba > 0.5).astype(int)
    pred_time = time.time() - start_time
    
    # 计算指标
    auc = roc_auc_score(y_test, pred_proba)
    f1 = f1_score(y_test, pred)
    precision = precision_score(y_test, pred)
    recall = recall_score(y_test, pred)
    accuracy = accuracy_score(y_test, pred)
    
    print(f"✅ 神经网络完成 (训练: {train_time:.1f}s, 预测: {pred_time:.3f}s)")
    print(f"   AUC: {auc:.4f}, F1: {f1:.4f}, Precision: {precision:.4f}")
    
    return {
        'auc': auc, 'f1': f1, 'precision': precision, 'recall': recall, 'accuracy': accuracy,
        'train_time': train_time, 'pred_time': pred_time
    }

def run_chemprop_baseline():
    """运行ChemProp基线"""
    print("🧪 运行ChemProp基线...")
    
    import subprocess
    
    # 创建模型目录
    chemprop_model_dir = "../../models/chemprop_baseline"
    os.makedirs(chemprop_model_dir, exist_ok=True)
    
    # 训练命令
    train_cmd = [
        "chemprop_train",
        "--data_path", "../../data/examples/chemprop_train.csv",
        "--save_dir", chemprop_model_dir,
        "--dataset_type", "classification",
        "--epochs", "10",
        "--batch_size", "32",
        "--hidden_size", "300",
        "--depth", "3",
        "--quiet"
    ]
    
    try:
        print("🚀 训练ChemProp模型...")
        start_time = time.time()
        result = subprocess.run(train_cmd, capture_output=True, text=True, timeout=300)
        train_time = time.time() - start_time
        
        if result.returncode != 0:
            print(f"❌ ChemProp训练失败: {result.stderr}")
            return None
        
        print(f"✅ ChemProp训练完成 (耗时: {train_time:.1f}s)")
        
        # 预测命令
        pred_path = "../../data/examples/chemprop_predictions.csv"
        predict_cmd = [
            "chemprop_predict",
            "--test_path", "../../data/examples/chemprop_test.csv",
            "--checkpoint_dir", chemprop_model_dir,
            "--preds_path", pred_path
        ]
        
        print("🔮 ChemProp预测...")
        start_time = time.time()
        result = subprocess.run(predict_cmd, capture_output=True, text=True, timeout=60)
        pred_time = time.time() - start_time
        
        if result.returncode != 0:
            print(f"❌ ChemProp预测失败: {result.stderr}")
            return None
        
        # 加载结果
        chemprop_preds = pd.read_csv(pred_path)
        test_labels = pd.read_csv("../../data/examples/test_labels.csv")
        results_df = test_labels.merge(chemprop_preds, on='smiles')
        
        y_true = results_df['activity']
        y_pred_proba = results_df.iloc[:, -1]
        y_pred = (y_pred_proba > 0.5).astype(int)
        
        auc = roc_auc_score(y_true, y_pred_proba)
        f1 = f1_score(y_true, y_pred)
        precision = precision_score(y_true, y_pred)
        recall = recall_score(y_true, y_pred)
        accuracy = accuracy_score(y_true, y_pred)
        
        print(f"✅ ChemProp完成 (训练: {train_time:.1f}s, 预测: {pred_time:.3f}s)")
        print(f"   AUC: {auc:.4f}, F1: {f1:.4f}, Precision: {precision:.4f}")
        
        return {
            'auc': auc, 'f1': f1, 'precision': precision, 'recall': recall, 'accuracy': accuracy,
            'train_time': train_time, 'pred_time': pred_time
        }
        
    except Exception as e:
        print(f"❌ ChemProp运行出错: {e}")
        return None

def create_comparison_plot(results_summary):
    """创建对比图表"""
    print("📊 创建对比图表...")
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    methods = results_summary['Method']
    colors = ['lightcoral', 'lightblue', 'lightgreen']
    
    # AUC对比
    aucs = results_summary['AUC']
    axes[0,0].bar(methods, aucs, color=colors)
    axes[0,0].set_title('AUC Comparison', fontsize=14, fontweight='bold')
    axes[0,0].set_ylabel('AUC Score')
    axes[0,0].set_ylim(0, 1)
    for i, v in enumerate(aucs):
        axes[0,0].text(i, v + 0.01, f'{v:.3f}', ha='center', fontweight='bold')
    
    # F1对比
    f1s = results_summary['F1']
    axes[0,1].bar(methods, f1s, color=colors)
    axes[0,1].set_title('F1 Score Comparison', fontsize=14, fontweight='bold')
    axes[0,1].set_ylabel('F1 Score')
    axes[0,1].set_ylim(0, 1)
    for i, v in enumerate(f1s):
        axes[0,1].text(i, v + 0.01, f'{v:.3f}', ha='center', fontweight='bold')
    
    # 训练时间对比
    train_times = results_summary['Train_Time']
    axes[1,0].bar(methods, train_times, color=colors)
    axes[1,0].set_title('Training Time Comparison', fontsize=14, fontweight='bold')
    axes[1,0].set_ylabel('Time (seconds)')
    for i, v in enumerate(train_times):
        axes[1,0].text(i, v + max(train_times)*0.01, f'{v:.1f}s', ha='center', fontweight='bold')
    
    # 预测时间对比
    pred_times = results_summary['Pred_Time']
    axes[1,1].bar(methods, pred_times, color=colors)
    axes[1,1].set_title('Prediction Time Comparison', fontsize=14, fontweight='bold')
    axes[1,1].set_ylabel('Time (seconds)')
    for i, v in enumerate(pred_times):
        axes[1,1].text(i, v + max(pred_times)*0.01, f'{v:.3f}s', ha='center', fontweight='bold')
    
    # 调整x轴标签
    for ax in axes.flat:
        ax.tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    plt.savefig('../../data/examples/baseline_comparison_plot.png', dpi=300, bbox_inches='tight')
    print("💾 图表已保存: ../../data/examples/baseline_comparison_plot.png")

def main():
    """主函数"""
    print("🧬 神农框架 vs ChemProp 基线对比实验")
    print("=" * 50)
    
    # 加载数据
    train_df, test_df = load_data()
    
    # 生成Mordred特征
    print("\n📊 生成Mordred特征...")
    train_features = generate_mordred_features(train_df['smiles'].tolist())
    test_features = generate_mordred_features(test_df['smiles'].tolist())
    print(f"特征维度: {train_features.shape[1]}")
    
    # 数据预处理
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(train_features)
    X_test_scaled = scaler.transform(test_features)
    y_train = train_df['activity'].values
    y_test = test_df['activity'].values
    
    # 运行实验
    print("\n🚀 开始基线对比实验...")
    
    # 1. 随机森林
    rf_results = train_random_forest(X_train_scaled, y_train, X_test_scaled, y_test)
    
    # 2. 神经网络
    nn_results = train_neural_network(X_train_scaled, y_train, X_test_scaled, y_test)
    
    # 3. ChemProp
    chemprop_results = run_chemprop_baseline()
    
    # 整理结果
    print("\n📊 整理实验结果...")
    
    results_data = {
        'Method': ['ChemProp', 'Random Forest (Mordred)', 'Neural Net (Shennong)'],
        'AUC': [chemprop_results['auc'] if chemprop_results else 0, rf_results['auc'], nn_results['auc']],
        'F1': [chemprop_results['f1'] if chemprop_results else 0, rf_results['f1'], nn_results['f1']],
        'Precision': [chemprop_results['precision'] if chemprop_results else 0, rf_results['precision'], nn_results['precision']],
        'Recall': [chemprop_results['recall'] if chemprop_results else 0, rf_results['recall'], nn_results['recall']],
        'Accuracy': [chemprop_results['accuracy'] if chemprop_results else 0, rf_results['accuracy'], nn_results['accuracy']],
        'Train_Time': [chemprop_results['train_time'] if chemprop_results else 0, rf_results['train_time'], nn_results['train_time']],
        'Pred_Time': [chemprop_results['pred_time'] if chemprop_results else 0, rf_results['pred_time'], nn_results['pred_time']]
    }
    
    results_summary = pd.DataFrame(results_data)
    
    print("\n🏆 实验结果总结:")
    print(results_summary.round(4))
    
    # 保存结果
    results_summary.to_csv('../../data/examples/baseline_comparison_results.csv', index=False)
    print("\n💾 结果已保存到: ../../data/examples/baseline_comparison_results.csv")
    
    # 创建图表
    create_comparison_plot(results_summary)
    
    # 分析结果
    print("\n🔍 结果分析:")
    print("-" * 30)
    
    best_auc_idx = results_summary['AUC'].idxmax()
    best_f1_idx = results_summary['F1'].idxmax()
    
    print(f"🏆 最佳AUC: {results_summary.loc[best_auc_idx, 'Method']} ({results_summary.loc[best_auc_idx, 'AUC']:.4f})")
    print(f"🏆 最佳F1: {results_summary.loc[best_f1_idx, 'Method']} ({results_summary.loc[best_f1_idx, 'F1']:.4f})")
    
    if chemprop_results:
        auc_improvement = nn_results['auc'] - chemprop_results['auc']
        f1_improvement = nn_results['f1'] - chemprop_results['f1']
        print(f"\n📈 神农框架 vs ChemProp:")
        print(f"   AUC改进: {auc_improvement:+.4f} ({auc_improvement/chemprop_results['auc']*100:+.1f}%)")
        print(f"   F1改进: {f1_improvement:+.4f} ({f1_improvement/chemprop_results['f1']*100:+.1f}%)")
    
    print(f"\n🧬 神农尝百草，AI识良药 - 基线对比实验完成！")

if __name__ == "__main__":
    main()
