{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# COCONUT数据采集与初步处理\n", "# 作者: ZK\n", "# 邮箱: <EMAIL>\n", "# 日期: 2025-01-15\n", "# 描述: COCONUT天然产物数据库的下载、解析和初步清洗"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 环境准备与配置加载"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import yaml\n", "import requests\n", "import zipfile\n", "import pandas as pd\n", "import numpy as np\n", "from pathlib import Path\n", "from tqdm import tqdm\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# 添加项目根目录到路径\n", "project_root = Path.cwd().parent.parent\n", "sys.path.append(str(project_root))\n", "\n", "# 加载实验配置\n", "with open('configs/experiment_config.yaml', 'r', encoding='utf-8') as f:\n", "    config = yaml.safe_load(f)\n", "\n", "print(\"实验配置加载完成:\")\n", "print(f\"- 实验名称: {config['experiment_info']['name']}\")\n", "print(f\"- 版本: {config['experiment_info']['version']}\")\n", "print(f\"- 创建日期: {config['experiment_info']['created_date']}\")\n", "\n", "# 设置数据路径\n", "RAW_DATA_DIR = Path('data/raw')\n", "PROCESSED_DATA_DIR = Path('data/processed')\n", "RAW_DATA_DIR.mkdir(parents=True, exist_ok=True)\n", "PROCESSED_DATA_DIR.mkdir(parents=True, exist_ok=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. COCONUT数据库下载"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def download_coconut_data():\n", "    \"\"\"\n", "    下载COCONUT数据库\n", "    \n", "    由于COCONUT数据库较大，这里提供多种下载方式\n", "    \"\"\"\n", "    coconut_urls = {\n", "        'main_db': 'https://coconut.naturalproducts.net/download/coconut_DB.zip',\n", "        'smiles_only': 'https://coconut.naturalproducts.net/download/COCONUT_smiles.txt',\n", "        'csv_format': 'https://coconut.naturalproducts.net/download/COCONUT_DB.csv'\n", "    }\n", "    \n", "    print(\"COCONUT数据库下载选项:\")\n", "    print(\"1. 完整数据库 (ZIP格式, ~2GB)\")\n", "    print(\"2. 仅SMILES数据 (TXT格式, ~50MB)\")\n", "    print(\"3. CSV格式数据 (~500MB)\")\n", "    \n", "    # 检查是否已有数据文件\n", "    existing_files = list(RAW_DATA_DIR.glob('*coconut*'))\n", "    if existing_files:\n", "        print(f\"\\n发现已存在的COCONUT数据文件:\")\n", "        for file in existing_files:\n", "            file_size = file.stat().st_size / (1024*1024)  # MB\n", "            print(f\"- {file.name} ({file_size:.1f} MB)\")\n", "        \n", "        use_existing = input(\"\\n是否使用已存在的文件? (y/n): \").lower().strip()\n", "        if use_existing == 'y':\n", "            return existing_files[0]\n", "    \n", "    # 选择下载选项\n", "    choice = input(\"\\n请选择下载选项 (1/2/3): \").strip()\n", "    \n", "    if choice == '1':\n", "        url = coconut_urls['main_db']\n", "        filename = 'coconut_DB.zip'\n", "    elif choice == '2':\n", "        url = coconut_urls['smiles_only']\n", "        filename = 'COCONUT_smiles.txt'\n", "    elif choice == '3':\n", "        url = coconut_urls['csv_format']\n", "        filename = 'COCONUT_DB.csv'\n", "    else:\n", "        print(\"无效选择，默认下载CSV格式\")\n", "        url = coconut_urls['csv_format']\n", "        filename = 'COCONUT_DB.csv'\n", "    \n", "    file_path = RAW_DATA_DIR / filename\n", "    \n", "    print(f\"\\n开始下载: {filename}\")\n", "    print(f\"URL: {url}\")\n", "    \n", "    try:\n", "        response = requests.get(url, stream=True)\n", "        response.raise_for_status()\n", "        \n", "        total_size = int(response.headers.get('content-length', 0))\n", "        \n", "        with open(file_path, 'wb') as f, tqdm(\n", "            desc=filename,\n", "            total=total_size,\n", "            unit='B',\n", "            unit_scale=True,\n", "            unit_divisor=1024,\n", "        ) as pbar:\n", "            for chunk in response.iter_content(chunk_size=8192):\n", "                if chunk:\n", "                    f.write(chunk)\n", "                    pbar.update(len(chunk))\n", "        \n", "        print(f\"✓ 下载完成: {file_path}\")\n", "        print(f\"文件大小: {file_path.stat().st_size / (1024*1024):.1f} MB\")\n", "        \n", "        return file_path\n", "        \n", "    except Exception as e:\n", "        print(f\"✗ 下载失败: {e}\")\n", "        print(\"\\n备选方案: 请手动下载COCONUT数据并放置在 data/raw/ 目录下\")\n", "        print(\"下载链接: https://coconut.naturalproducts.net/download\")\n", "        return None\n", "\n", "# 执行下载\n", "coconut_file = download_coconut_data()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 数据解析与初步统计"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def parse_coconut_data(file_path):\n", "    \"\"\"\n", "    解析COCONUT数据文件\n", "    \"\"\"\n", "    if file_path is None:\n", "        print(\"未找到COCONUT数据文件\")\n", "        return None\n", "    \n", "    print(f\"解析文件: {file_path.name}\")\n", "    \n", "    try:\n", "        if file_path.suffix == '.zip':\n", "            # 解压ZIP文件\n", "            with zipfile.ZipFile(file_path, 'r') as zip_ref:\n", "                zip_ref.extractall(RAW_DATA_DIR)\n", "                extracted_files = zip_ref.namelist()\n", "                print(f\"解压文件: {extracted_files}\")\n", "                \n", "                # 查找CSV文件\n", "                csv_files = [f for f in extracted_files if f.endswith('.csv')]\n", "                if csv_files:\n", "                    csv_path = RAW_DATA_DIR / csv_files[0]\n", "                    df = pd.read_csv(csv_path)\n", "                else:\n", "                    print(\"ZIP文件中未找到CSV格式数据\")\n", "                    return None\n", "                    \n", "        elif file_path.suffix == '.csv':\n", "            df = pd.read_csv(file_path)\n", "            \n", "        elif file_path.suffix == '.txt':\n", "            # 假设是SMILES格式\n", "            with open(file_path, 'r') as f:\n", "                smiles_list = [line.strip() for line in f if line.strip()]\n", "            df = pd.DataFrame({'smiles': smiles_list})\n", "            \n", "        else:\n", "            print(f\"不支持的文件格式: {file_path.suffix}\")\n", "            return None\n", "        \n", "        print(f\"✓ 数据加载完成\")\n", "        print(f\"数据形状: {df.shape}\")\n", "        print(f\"列名: {list(df.columns)}\")\n", "        \n", "        return df\n", "        \n", "    except Exception as e:\n", "        print(f\"✗ 数据解析失败: {e}\")\n", "        return None\n", "\n", "# 解析数据\n", "coconut_df = parse_coconut_data(coconut_file)\n", "\n", "if coconut_df is not None:\n", "    print(\"\\n数据预览:\")\n", "    print(coconut_df.head())\n", "    \n", "    print(\"\\n数据统计:\")\n", "    print(coconut_df.info())\n", "    \n", "    # 检查SMILES列\n", "    smiles_columns = [col for col in coconut_df.columns if 'smiles' in col.lower()]\n", "    print(f\"\\nSMILES相关列: {smiles_columns}\")\n", "    \n", "    if smiles_columns:\n", "        smiles_col = smiles_columns[0]\n", "        print(f\"使用SMILES列: {smiles_col}\")\n", "        \n", "        # 统计有效SMILES数量\n", "        valid_smiles = coconut_df[smiles_col].dropna()\n", "        print(f\"有效SMILES数量: {len(valid_smiles)}\")\n", "        print(f\"缺失SMILES数量: {len(coconut_df) - len(valid_smiles)}\")\n", "        \n", "        # 显示SMILES示例\n", "        print(\"\\nSMILES示例:\")\n", "        for i, smiles in enumerate(valid_smiles.head(5)):\n", "            print(f\"{i+1}. {smiles}\")\n", "    else:\n", "        print(\"⚠️ 未找到SMILES列，请检查数据格式\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 数据质量检查"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 尝试导入RDKit进行分子验证\n", "try:\n", "    from rdkit import Chem\n", "    from rdkit.Chem import Descriptors, rdMolDescriptors\n", "    rdkit_available = True\n", "    print(\"✓ RDKit可用，将进行分子结构验证\")\n", "except ImportError:\n", "    rdkit_available = False\n", "    print(\"⚠️ RDKit不可用，跳过分子结构验证\")\n", "\n", "def validate_smiles_quality(df, smiles_col, sample_size=1000):\n", "    \"\"\"\n", "    验证SMILES数据质量\n", "    \"\"\"\n", "    if not rdkit_available:\n", "        print(\"RDKit不可用，无法进行分子验证\")\n", "        return df\n", "    \n", "    print(f\"\\n开始SMILES质量检查 (采样 {sample_size} 个分子)...\")\n", "    \n", "    # 采样数据进行快速检查\n", "    sample_df = df.sample(min(sample_size, len(df)), random_state=42)\n", "    \n", "    valid_count = 0\n", "    invalid_smiles = []\n", "    molecular_weights = []\n", "    \n", "    for idx, smiles in tqdm(sample_df[smiles_col].items(), desc=\"验证SMILES\"):\n", "        if pd.isna(smiles):\n", "            continue\n", "            \n", "        mol = Chem.MolFromSmiles(str(smiles))\n", "        if mol is not None:\n", "            valid_count += 1\n", "            mw = Descriptors.MolWt(mol)\n", "            molecular_weights.append(mw)\n", "        else:\n", "            invalid_smiles.append((idx, smiles))\n", "    \n", "    print(f\"\\n质量检查结果:\")\n", "    print(f\"- 采样数量: {len(sample_df)}\")\n", "    print(f\"- 有效SMILES: {valid_count}\")\n", "    print(f\"- 无效SMILES: {len(invalid_smiles)}\")\n", "    print(f\"- 有效率: {valid_count/len(sample_df)*100:.1f}%\")\n", "    \n", "    if molecular_weights:\n", "        mw_array = np.array(molecular_weights)\n", "        print(f\"\\n分子量统计:\")\n", "        print(f\"- 平均值: {mw_array.mean():.1f} Da\")\n", "        print(f\"- 中位数: {np.median(mw_array):.1f} Da\")\n", "        print(f\"- 范围: {mw_array.min():.1f} - {mw_array.max():.1f} Da\")\n", "    \n", "    if invalid_smiles:\n", "        print(f\"\\n无效SMILES示例 (前5个):\")\n", "        for i, (idx, smiles) in enumerate(invalid_smiles[:5]):\n", "            print(f\"{i+1}. Index {idx}: {smiles}\")\n", "    \n", "    return df\n", "\n", "# 执行质量检查\n", "if coconut_df is not None and smiles_columns:\n", "    coconut_df = validate_smiles_quality(coconut_df, smiles_columns[0])\n", "else:\n", "    print(\"跳过质量检查：数据或SMILES列不可用\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 数据保存与记录更新"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 保存处理后的数据\n", "if coconut_df is not None:\n", "    # 保存为CSV格式\n", "    output_path = PROCESSED_DATA_DIR / 'coconut_raw.csv'\n", "    coconut_df.to_csv(output_path, index=False)\n", "    print(f\"✓ 原始数据已保存: {output_path}\")\n", "    \n", "    # 如果有SMILES列，单独保存SMILES数据\n", "    if smiles_columns:\n", "        smiles_col = smiles_columns[0]\n", "        smiles_data = coconut_df[smiles_col].dropna()\n", "        smiles_path = PROCESSED_DATA_DIR / 'coconut_smiles.txt'\n", "        \n", "        with open(smiles_path, 'w') as f:\n", "            for smiles in smiles_data:\n", "                f.write(f\"{smiles}\\n\")\n", "        \n", "        print(f\"✓ SMILES数据已保存: {smiles_path}\")\n", "        print(f\"SMILES数量: {len(smiles_data)}\")\n", "\n", "# 更新实验记忆库\n", "memory_update = f\"\"\"\n", "## 数据采集完成 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n", "\n", "### COCONUT数据统计\n", "- 总分子数量: {len(coconut_df) if coconut_df is not None else 'N/A'}\n", "- 有效SMILES数量: {len(smiles_data) if 'smiles_data' in locals() else 'N/A'}\n", "- 数据文件: {coconut_file.name if coconut_file else 'N/A'}\n", "- 处理状态: {'完成' if coconut_df is not None else '失败'}\n", "\n", "### 下一步计划\n", "- [ ] 数据清洗和标准化\n", "- [ ] Mordred描述符计算\n", "- [ ] 分子骨架提取\n", "\"\"\"\n", "\n", "# 更新记忆库文件\n", "memory_file = Path('exp-memory-bank/data_processing_log.md')\n", "with open(memory_file, 'a', encoding='utf-8') as f:\n", "    f.write(memory_update)\n", "\n", "print(f\"\\n✓ 实验记忆库已更新: {memory_file}\")\n", "\n", "# 生成数据采集报告\n", "report = {\n", "    'timestamp': datetime.now().isoformat(),\n", "    'coconut_file': str(coconut_file) if coconut_file else None,\n", "    'total_molecules': len(coconut_df) if coconut_df is not None else 0,\n", "    'valid_smiles': len(smiles_data) if 'smiles_data' in locals() else 0,\n", "    'data_columns': list(coconut_df.columns) if coconut_df is not None else [],\n", "    'status': 'success' if coconut_df is not None else 'failed'\n", "}\n", "\n", "import json\n", "report_path = Path('results/metrics/data_collection_report.json')\n", "report_path.parent.mkdir(parents=True, exist_ok=True)\n", "with open(report_path, 'w') as f:\n", "    json.dump(report, f, indent=2)\n", "\n", "print(f\"✓ 数据采集报告已保存: {report_path}\")\n", "\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"COCONUT数据采集完成\")\n", "print(\"=\"*60)\n", "print(\"\\n下一步: 运行 02_data_preprocessing.ipynb 进行数据预处理\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}