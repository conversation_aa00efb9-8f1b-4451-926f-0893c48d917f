{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# COCONUT数据预处理与特征工程\n", "# 作者: ZK\n", "# 邮箱: <EMAIL>\n", "# 日期: 2025-01-15\n", "# 描述: COCONUT数据的清洗、标准化和Mordred描述符计算"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 环境准备与数据加载"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import yaml\n", "import pandas as pd\n", "import numpy as np\n", "from pathlib import Path\n", "from tqdm import tqdm\n", "import pickle\n", "import json\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# RDKit导入\n", "try:\n", "    from rdkit import Chem\n", "    from rdkit.Chem import Descriptors, rdMolDescriptors, Scaffolds\n", "    from rdkit.Chem.MolStandardize import rdMolStandardize\n", "    print(\"✓ RDKit导入成功\")\n", "except ImportError as e:\n", "    print(f\"✗ RDKit导入失败: {e}\")\n", "    sys.exit(1)\n", "\n", "# Mordred导入\n", "try:\n", "    from mordred import Calculator, descriptors\n", "    print(\"✓ Mordred导入成功\")\n", "except ImportError as e:\n", "    print(f\"✗ Mordred导入失败: {e}\")\n", "    print(\"请安装: pip install mordred\")\n", "    sys.exit(1)\n", "\n", "# 加载配置\n", "with open('configs/experiment_config.yaml', 'r', encoding='utf-8') as f:\n", "    config = yaml.safe_load(f)\n", "\n", "print(f\"\\n开始数据预处理: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "\n", "# 设置路径\n", "PROCESSED_DATA_DIR = Path('data/processed')\n", "RESULTS_DIR = Path('results')\n", "RESULTS_DIR.mkdir(exist_ok=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 数据加载与初步检查"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 加载COCONUT数据\n", "coconut_files = {\n", "    'csv': PROCESSED_DATA_DIR / 'coconut_raw.csv',\n", "    'smiles': PROCESSED_DATA_DIR / 'coconut_smiles.txt'\n", "}\n", "\n", "# 检查文件存在性\n", "available_files = {k: v for k, v in coconut_files.items() if v.exists()}\n", "\n", "if not available_files:\n", "    print(\"✗ 未找到COCONUT数据文件\")\n", "    print(\"请先运行 01_data_collection.ipynb\")\n", "    sys.exit(1)\n", "\n", "print(\"可用数据文件:\")\n", "for name, path in available_files.items():\n", "    size_mb = path.stat().st_size / (1024*1024)\n", "    print(f\"- {name}: {path.name} ({size_mb:.1f} MB)\")\n", "\n", "# 优先使用CSV文件，否则使用SMILES文件\n", "if 'csv' in available_files:\n", "    print(\"\\n使用CSV格式数据\")\n", "    df = pd.read_csv(available_files['csv'])\n", "    \n", "    # 查找SMILES列\n", "    smiles_cols = [col for col in df.columns if 'smiles' in col.lower()]\n", "    if not smiles_cols:\n", "        print(\"✗ CSV文件中未找到SMILES列\")\n", "        sys.exit(1)\n", "    smiles_col = smiles_cols[0]\n", "    \n", "elif 'smiles' in available_files:\n", "    print(\"\\n使用SMILES文本文件\")\n", "    with open(available_files['smiles'], 'r') as f:\n", "        smiles_list = [line.strip() for line in f if line.strip()]\n", "    df = pd.DataFrame({'smiles': smiles_list})\n", "    smiles_col = 'smiles'\n", "\n", "print(f\"\\n数据加载完成:\")\n", "print(f\"- 总记录数: {len(df):,}\")\n", "print(f\"- SMILES列: {smiles_col}\")\n", "print(f\"- 数据列: {list(df.columns)}\")\n", "\n", "# 检查SMILES数据质量\n", "smiles_data = df[smiles_col].dropna()\n", "print(f\"- 有效SMILES: {len(smiles_data):,}\")\n", "print(f\"- 缺失SMILES: {len(df) - len(smiles_data):,}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 分子标准化与清洗"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def standardize_molecule(smiles):\n", "    \"\"\"\n", "    标准化分子结构\n", "    \n", "    包括:\n", "    1. 去除盐和溶剂\n", "    2. 中性化\n", "    3. 标准化互变异构体\n", "    4. 规范化SMILES\n", "    \"\"\"\n", "    try:\n", "        # 解析SMILES\n", "        mol = Chem.Mo<PERSON>rom<PERSON>(smiles)\n", "        if mol is None:\n", "            return None, \"invalid_smiles\"\n", "        \n", "        # 去除盐和溶剂（保留最大片段）\n", "        mol = rdMolStandardize.FragmentParent(mol)\n", "        \n", "        # 中性化\n", "        uncharger = rdMolStandardize.Uncharger()\n", "        mol = uncharger.uncharge(mol)\n", "        \n", "        # 标准化互变异构体\n", "        te = rdMolStandardize.TautomerEnumerator()\n", "        mol = te.Canonicalize(mol)\n", "        \n", "        # 生成规范SMILES\n", "        canonical_smiles = Chem.MolToSmiles(mol, canonical=True)\n", "        \n", "        return canonical_smiles, \"success\"\n", "        \n", "    except Exception as e:\n", "        return None, f\"error: {str(e)}\"\n", "\n", "print(\"开始分子标准化...\")\n", "print(f\"处理 {len(smiles_data):,} 个分子\")\n", "\n", "# 标准化处理\n", "standardized_results = []\n", "error_counts = {}\n", "\n", "for i, smiles in tqdm(enumerate(smiles_data), total=len(smiles_data), desc=\"标准化分子\"):\n", "    std_smiles, status = standardize_molecule(smiles)\n", "    \n", "    result = {\n", "        'original_index': smiles_data.index[i],\n", "        'original_smiles': smiles,\n", "        'standardized_smiles': std_smiles,\n", "        'status': status\n", "    }\n", "    standardized_results.append(result)\n", "    \n", "    # 统计错误类型\n", "    if status != \"success\":\n", "        error_counts[status] = error_counts.get(status, 0) + 1\n", "\n", "# 创建标准化数据框\n", "std_df = pd.DataFrame(standardized_results)\n", "\n", "print(f\"\\n标准化完成:\")\n", "print(f\"- 成功标准化: {len(std_df[std_df['status'] == 'success']):,}\")\n", "print(f\"- 失败数量: {len(std_df[std_df['status'] != 'success']):,}\")\n", "print(f\"- 成功率: {len(std_df[std_df['status'] == 'success'])/len(std_df)*100:.1f}%\")\n", "\n", "if error_counts:\n", "    print(\"\\n错误类型统计:\")\n", "    for error_type, count in error_counts.items():\n", "        print(f\"- {error_type}: {count:,}\")\n", "\n", "# 过滤成功标准化的分子\n", "valid_std_df = std_df[std_df['status'] == 'success'].copy()\n", "print(f\"\\n有效分子数量: {len(valid_std_df):,}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 去重与最终清洗"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"开始去重处理...\")\n", "\n", "# 基于标准化SMILES去重\n", "before_dedup = len(valid_std_df)\n", "valid_std_df = valid_std_df.drop_duplicates(subset=['standardized_smiles'])\n", "after_dedup = len(valid_std_df)\n", "\n", "print(f\"去重结果:\")\n", "print(f\"- 去重前: {before_dedup:,}\")\n", "print(f\"- 去重后: {after_dedup:,}\")\n", "print(f\"- 重复分子: {before_dedup - after_dedup:,}\")\n", "print(f\"- 去重率: {(before_dedup - after_dedup)/before_dedup*100:.1f}%\")\n", "\n", "# 额外的质量过滤\n", "print(\"\\n应用质量过滤...\")\n", "\n", "def quality_filter(smiles):\n", "    \"\"\"\n", "    质量过滤规则\n", "    \"\"\"\n", "    try:\n", "        mol = Chem.Mo<PERSON>rom<PERSON>(smiles)\n", "        if mol is None:\n", "            return False, \"invalid_mol\"\n", "        \n", "        # 分子量过滤 (50-2000 Da)\n", "        mw = Descriptors.MolWt(mol)\n", "        if mw < 50 or mw > 2000:\n", "            return False, \"molecular_weight\"\n", "        \n", "        # 原子数过滤 (3-200个非氢原子)\n", "        heavy_atoms = mol.GetNumHeavyAtoms()\n", "        if heavy_atoms < 3 or heavy_atoms > 200:\n", "            return False, \"heavy_atoms\"\n", "        \n", "        # 检查是否包含异常元素\n", "        allowed_elements = {'C', 'N', 'O', 'S', 'P', 'F', 'Cl', 'Br', 'I', 'H'}\n", "        mol_elements = {atom.GetSymbol() for atom in mol.GetAtoms()}\n", "        if not mol_elements.issubset(allowed_elements):\n", "            return False, \"unusual_elements\"\n", "        \n", "        return True, \"pass\"\n", "        \n", "    except Exception as e:\n", "        return False, f\"error: {str(e)}\"\n", "\n", "# 应用质量过滤\n", "filter_results = []\n", "filter_counts = {}\n", "\n", "for smiles in tqdm(valid_std_df['standardized_smiles'], desc=\"质量过滤\"):\n", "    passed, reason = quality_filter(smiles)\n", "    filter_results.append(passed)\n", "    \n", "    if not passed:\n", "        filter_counts[reason] = filter_counts.get(reason, 0) + 1\n", "\n", "valid_std_df['quality_pass'] = filter_results\n", "final_df = valid_std_df[valid_std_df['quality_pass']].copy()\n", "\n", "print(f\"\\n质量过滤结果:\")\n", "print(f\"- 过滤前: {len(valid_std_df):,}\")\n", "print(f\"- 过滤后: {len(final_df):,}\")\n", "print(f\"- 过滤掉: {len(valid_std_df) - len(final_df):,}\")\n", "print(f\"- 通过率: {len(final_df)/len(valid_std_df)*100:.1f}%\")\n", "\n", "if filter_counts:\n", "    print(\"\\n过滤原因统计:\")\n", "    for reason, count in filter_counts.items():\n", "        print(f\"- {reason}: {count:,}\")\n", "\n", "print(f\"\\n最终清洗后的分子数量: {len(final_df):,}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. <PERSON><PERSON><PERSON>骨架提取"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"开始提取Murcko骨架...\")\n", "\n", "def extract_murcko_scaffold(smiles):\n", "    \"\"\"\n", "    提取Murcko骨架\n", "    \"\"\"\n", "    try:\n", "        mol = Chem.Mo<PERSON>rom<PERSON>(smiles)\n", "        if mol is None:\n", "            return None\n", "        \n", "        scaffold = Scaffolds.MurckoScaffold.GetScaffoldForMol(mol)\n", "        if scaffold is None:\n", "            return None\n", "            \n", "        scaffold_smiles = Chem.MolToSmiles(scaffold, canonical=True)\n", "        return scaffold_smiles\n", "        \n", "    except Exception as e:\n", "        return None\n", "\n", "# 提取骨架\n", "scaffolds = []\n", "for smiles in tqdm(final_df['standardized_smiles'], desc=\"提取骨架\"):\n", "    scaffold = extract_murcko_scaffold(smiles)\n", "    scaffolds.append(scaffold)\n", "\n", "final_df['murcko_scaffold'] = scaffolds\n", "\n", "# 统计骨架信息\n", "valid_scaffolds = final_df['murcko_scaffold'].dropna()\n", "unique_scaffolds = valid_scaffolds.nunique()\n", "\n", "print(f\"\\n骨架提取结果:\")\n", "print(f\"- 成功提取骨架: {len(valid_scaffolds):,}\")\n", "print(f\"- 失败数量: {len(final_df) - len(valid_scaffolds):,}\")\n", "print(f\"- 唯一骨架数: {unique_scaffolds:,}\")\n", "print(f\"- 平均每骨架分子数: {len(valid_scaffolds)/unique_scaffolds:.1f}\")\n", "\n", "# 显示最常见的骨架\n", "scaffold_counts = valid_scaffolds.value_counts()\n", "print(f\"\\n最常见的5个骨架:\")\n", "for i, (scaffold, count) in enumerate(scaffold_counts.head().items()):\n", "    print(f\"{i+1}. {scaffold} ({count:,} 分子)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. <PERSON><PERSON><PERSON>描述符计算"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"开始计算Mordred描述符...\")\n", "\n", "# 初始化Mordred计算器\n", "calc = Calculator(descriptors, ignore_3D=True)\n", "print(f\"描述符总数: {len(calc.descriptors)}\")\n", "\n", "# 选择用于预训练的分子子集（如果数据量太大）\n", "MAX_MOLECULES = 100000  # 最大处理分子数\n", "\n", "if len(final_df) > MAX_MOLECULES:\n", "    print(f\"\\n数据量较大 ({len(final_df):,})，随机采样 {MAX_MOLECULES:,} 个分子\")\n", "    sample_df = final_df.sample(n=MAX_MOLECULES, random_state=42)\n", "else:\n", "    print(f\"\\n处理全部 {len(final_df):,} 个分子\")\n", "    sample_df = final_df.copy()\n", "\n", "print(f\"实际处理分子数: {len(sample_df):,}\")\n", "\n", "def calculate_mordred_descriptors(smiles_list, desc_type=\"molecule\"):\n", "    \"\"\"\n", "    计算Mordred描述符\n", "    \n", "    Args:\n", "        smiles_list: SMILES列表\n", "        desc_type: 描述符类型标识\n", "    \"\"\"\n", "    print(f\"\\n计算{desc_type}描述符...\")\n", "    \n", "    # 转换为分子对象\n", "    mols = []\n", "    valid_indices = []\n", "    \n", "    for i, smiles in enumerate(tqdm(smiles_list, desc=f\"解析{desc_type}SMILES\")):\n", "        if pd.isna(smiles):\n", "            continue\n", "        mol = Chem.Mo<PERSON>rom<PERSON>(smiles)\n", "        if mol is not None:\n", "            mols.append(mol)\n", "            valid_indices.append(i)\n", "    \n", "    print(f\"有效{desc_type}分子: {len(mols):,}\")\n", "    \n", "    # 计算描述符\n", "    print(f\"计算{desc_type}描述符...\")\n", "    descriptors_df = calc.pandas(mols, quiet=False)\n", "    \n", "    # 处理无效值\n", "    print(f\"处理无效值...\")\n", "    \n", "    # 统计无效值\n", "    invalid_mask = descriptors_df.isin([np.inf, -np.inf]) | descriptors_df.isna()\n", "    invalid_counts = invalid_mask.sum()\n", "    \n", "    print(f\"描述符统计:\")\n", "    print(f\"- 总描述符数: {len(descriptors_df.columns)}\")\n", "    print(f\"- 有无效值的描述符: {(invalid_counts > 0).sum()}\")\n", "    print(f\"- 完全有效的描述符: {(invalid_counts == 0).sum()}\")\n", "    \n", "    # 移除包含过多无效值的描述符（>50%）\n", "    valid_threshold = len(descriptors_df) * 0.5\n", "    valid_descriptors = invalid_counts[invalid_counts <= valid_threshold].index\n", "    \n", "    descriptors_df = descriptors_df[valid_descriptors]\n", "    print(f\"保留描述符数: {len(descriptors_df.columns)}\")\n", "    \n", "    # 填充剩余无效值（使用中位数）\n", "    for col in descriptors_df.columns:\n", "        if descriptors_df[col].dtype in ['float64', 'int64']:\n", "            median_val = descriptors_df[col].median()\n", "            descriptors_df[col] = descriptors_df[col].fillna(median_val)\n", "            descriptors_df[col] = descriptors_df[col].replace([np.inf, -np.inf], median_val)\n", "    \n", "    return descriptors_df, valid_indices\n", "\n", "# 计算完整分子的描述符\n", "mol_descriptors, mol_valid_idx = calculate_mordred_descriptors(\n", "    sample_df['standardized_smiles'].tolist(), \"完整分子\"\n", ")\n", "\n", "# 计算骨架的描述符\n", "scaffold_descriptors, scaffold_valid_idx = calculate_mordred_descriptors(\n", "    sample_df['murcko_scaffold'].tolist(), \"骨架\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. 描述符融合与保存"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"\\n开始描述符融合...\")\n", "\n", "# 确保两个描述符矩阵的索引对齐\n", "common_indices = set(mol_valid_idx) & set(scaffold_valid_idx)\n", "print(f\"共同有效索引数: {len(common_indices)}\")\n", "\n", "if len(common_indices) == 0:\n", "    print(\"✗ 没有共同的有效分子，无法进行融合\")\n", "    sys.exit(1)\n", "\n", "# 重新索引以对齐数据\n", "common_indices = sorted(list(common_indices))\n", "mol_aligned_idx = [mol_valid_idx.index(i) for i in common_indices]\n", "scaffold_aligned_idx = [scaffold_valid_idx.index(i) for i in common_indices]\n", "\n", "mol_desc_aligned = mol_descriptors.iloc[mol_aligned_idx].reset_index(drop=True)\n", "scaffold_desc_aligned = scaffold_descriptors.iloc[scaffold_aligned_idx].reset_index(drop=True)\n", "\n", "print(f\"对齐后分子描述符形状: {mol_desc_aligned.shape}\")\n", "print(f\"对齐后骨架描述符形状: {scaffold_desc_aligned.shape}\")\n", "\n", "# 添加前缀以区分分子和骨架描述符\n", "mol_desc_aligned.columns = [f\"mol_{col}\" for col in mol_desc_aligned.columns]\n", "scaffold_desc_aligned.columns = [f\"scaffold_{col}\" for col in scaffold_desc_aligned.columns]\n", "\n", "# 拼接描述符\n", "fused_descriptors = pd.concat([mol_desc_aligned, scaffold_desc_aligned], axis=1)\n", "print(f\"融合后描述符形状: {fused_descriptors.shape}\")\n", "\n", "# 创建最终数据集\n", "final_sample_df = sample_df.iloc[common_indices].reset_index(drop=True)\n", "final_dataset = pd.concat([final_sample_df, fused_descriptors], axis=1)\n", "\n", "print(f\"\\n最终数据集:\")\n", "print(f\"- 分子数量: {len(final_dataset):,}\")\n", "print(f\"- 总特征数: {len(fused_descriptors.columns):,}\")\n", "print(f\"- 分子描述符: {len(mol_desc_aligned.columns):,}\")\n", "print(f\"- 骨架描述符: {len(scaffold_desc_aligned.columns):,}\")\n", "\n", "# 保存数据\n", "print(\"\\n保存处理后的数据...\")\n", "\n", "# 保存完整数据集\n", "dataset_path = PROCESSED_DATA_DIR / 'coconut_processed_with_descriptors.pkl'\n", "with open(dataset_path, 'wb') as f:\n", "    pickle.dump(final_dataset, f)\n", "print(f\"✓ 完整数据集已保存: {dataset_path}\")\n", "\n", "# 保存SMILES和描述符（用于预训练）\n", "pretraining_data = {\n", "    'smiles': final_dataset['standardized_smiles'].tolist(),\n", "    'scaffolds': final_dataset['murcko_scaffold'].tolist(),\n", "    'descriptors': fused_descriptors.values,\n", "    'descriptor_names': fused_descriptors.columns.tolist(),\n", "    'mol_descriptor_count': len(mol_desc_aligned.columns),\n", "    'scaffold_descriptor_count': len(scaffold_desc_aligned.columns)\n", "}\n", "\n", "pretraining_path = PROCESSED_DATA_DIR / 'coconut_pretraining_data.pkl'\n", "with open(pretraining_path, 'wb') as f:\n", "    pickle.dump(pretraining_data, f)\n", "print(f\"✓ 预训练数据已保存: {pretraining_path}\")\n", "\n", "# 保存处理统计信息\n", "processing_stats = {\n", "    'timestamp': datetime.now().isoformat(),\n", "    'original_molecules': len(df),\n", "    'after_standardization': len(valid_std_df),\n", "    'after_deduplication': after_dedup,\n", "    'after_quality_filter': len(final_df),\n", "    'final_processed': len(final_dataset),\n", "    'unique_scaffolds': unique_scaffolds,\n", "    'total_descriptors': len(fused_descriptors.columns),\n", "    'mol_descriptors': len(mol_desc_aligned.columns),\n", "    'scaffold_descriptors': len(scaffold_desc_aligned.columns),\n", "    'processing_success_rate': len(final_dataset) / len(df) * 100\n", "}\n", "\n", "stats_path = RESULTS_DIR / 'preprocessing_stats.json'\n", "with open(stats_path, 'w') as f:\n", "    json.dump(processing_stats, f, indent=2)\n", "print(f\"✓ 处理统计已保存: {stats_path}\")\n", "\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"COCONUT数据预处理完成\")\n", "print(\"=\"*60)\n", "print(f\"最终处理分子数: {len(final_dataset):,}\")\n", "print(f\"总描述符数: {len(fused_descriptors.columns):,}\")\n", "print(f\"处理成功率: {processing_stats['processing_success_rate']:.1f}%\")\n", "print(\"\\n下一步: 运行 03_model_pretraining.ipynb 进行模型预训练\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}