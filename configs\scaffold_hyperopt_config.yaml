# 神农框架骨架拆分+超参数优化配置
# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-29

# 数据配置 - 骨架拆分
data:
  # 数据集配置
  dataset_type: "antibacterial"
  data_path: "data/antibacterial_dataset.csv"
  
  # 数据列配置
  smiles_column: "smiles"
  target_columns: ["activity"]
  
  # 骨架拆分配置
  splitting:
    method: "scaffold"  # 使用骨架拆分
    train_ratio: 0.8
    val_ratio: 0.1
    test_ratio: 0.1
    include_chirality: false  # 是否包含手性信息
    random_state: 42
    
    # 骨架拆分高级选项
    scaffold_options:
      min_scaffold_size: 2      # 最小骨架大小
      max_scaffold_ratio: 0.3   # 单个骨架最大占比
      balance_datasets: true    # 平衡数据集大小
      
  # 数据预处理
  preprocessing:
    remove_invalid_smiles: true
    remove_duplicates: true
    standardize_smiles: true
    filter_by_properties: true

# 超参数优化配置
hyperopt:
  # 优化引擎
  method: "optuna"  # "optuna", "ray_tune", "grid_search"
  n_trials: 200
  timeout: 7200  # 2小时
  
  # Optuna特定配置
  optuna_config:
    sampler: "TPE"  # "TPE", "Random", "CmaEs"
    pruner: "MedianPruner"  # "MedianPruner", "SuccessiveHalvingPruner"
    study_name: "shennong_scaffold_optimization"
    storage: null  # 可以设置数据库URL进行分布式优化
    
  # 搜索空间定义
  search_space:
    # 图神经网络分支参数
    graph_config:
      hidden_size:
        type: "categorical"
        choices: [200, 300, 400, 500]
      depth:
        type: "int"
        low: 2
        high: 6
      dropout:
        type: "float"
        low: 0.0
        high: 0.4
      
    # 专家特征分支参数
    expert_config:
      hidden_dims:
        type: "categorical"
        choices: 
          - [512, 256, 128]
          - [1024, 512, 256]
          - [256, 128, 64]
          - [1024, 512, 256, 128]
      dropout:
        type: "float"
        low: 0.1
        high: 0.5
      activation:
        type: "categorical"
        choices: ["relu", "gelu", "swish", "leaky_relu"]
        
    # 化学导向注意力参数
    attention_config:
      num_heads:
        type: "categorical"
        choices: [2, 4, 6, 8, 12]
      dropout:
        type: "float"
        low: 0.1
        high: 0.3
      temperature:
        type: "float"
        low: 0.5
        high: 2.0
        
    # 特征融合参数
    fusion_config:
      fusion_method:
        type: "categorical"
        choices: ["concat", "attention", "gated", "bilinear"]
      output_dim:
        type: "categorical"
        choices: [128, 256, 512]
        
    # 训练参数
    training:
      learning_rate:
        type: "float"
        low: 1e-5
        high: 1e-2
        log: true
      batch_size:
        type: "categorical"
        choices: [16, 32, 64, 128]
      weight_decay:
        type: "float"
        low: 1e-6
        high: 1e-3
        log: true
      warmup_epochs:
        type: "int"
        low: 0
        high: 10
        
    # 损失权重
    loss_weights:
      activity_weight:
        type: "float"
        low: 0.8
        high: 1.2
      uncertainty_weight:
        type: "float"
        low: 0.01
        high: 0.1
        log: true
      attention_weight:
        type: "float"
        low: 0.001
        high: 0.01
        log: true
        
    # 正则化参数
    regularization:
      gradient_clip_val:
        type: "float"
        low: 0.5
        high: 2.0
      label_smoothing:
        type: "float"
        low: 0.0
        high: 0.1
        
  # 优化目标
  objective:
    metric: "val_loss"  # "val_loss", "val_r2", "val_rmse"
    direction: "minimize"  # "minimize", "maximize"
    
  # 多目标优化 (可选)
  multi_objective:
    enabled: false
    metrics: ["val_loss", "val_r2"]
    directions: ["minimize", "maximize"]
    
  # 早停配置
  early_stopping:
    enabled: true
    patience: 15
    min_delta: 1e-4
    
  # 结果保存
  save_config:
    save_dir: "hyperopt_results"
    save_best_config: true
    save_all_trials: true
    save_plots: true

# 验证配置
validation:
  # 交叉验证
  cross_validation:
    enabled: true
    n_folds: 5
    stratified: true
    
  # 外部验证集
  external_validation:
    enabled: false
    test_path: null
    
  # 性能指标
  metrics:
    - "r2"
    - "rmse" 
    - "mae"
    - "pearson_r"
    - "spearman_r"

# 可解释性配置
interpretability:
  # 注意力分析
  attention_analysis:
    enabled: true
    save_attention_maps: true
    analyze_attention_patterns: true
    
  # 特征重要性
  feature_importance:
    enabled: true
    method: "shap"  # "shap", "lime", "integrated_gradients"
    
  # 化学解释
  chemical_explanation:
    enabled: true
    generate_reports: true

# 计算资源配置
compute:
  # GPU配置
  gpu:
    enabled: true
    device_ids: [0]  # 使用的GPU ID
    mixed_precision: true
    
  # 并行配置
  parallel:
    num_workers: 4
    pin_memory: true
    
  # 内存管理
  memory:
    max_memory_gb: 16
    clear_cache_every_n_trials: 10

# 日志和监控
logging:
  # 基础日志
  log_level: "INFO"
  log_dir: "logs/scaffold_hyperopt"
  
  # TensorBoard
  tensorboard:
    enabled: true
    log_hyperparams: true
    log_trial_results: true
    
  # Weights & Biases (可选)
  wandb:
    enabled: false
    project: "shennong-scaffold-hyperopt"
    tags: ["scaffold_split", "hyperopt", "antibacterial"]

# 实验管理
experiment:
  name: "scaffold_hyperopt_experiment"
  description: "神农框架骨架拆分超参数优化实验"
  tags: ["scaffold", "hyperopt", "antibacterial", "attention"]
  
  # 版本控制
  version_control:
    track_code_changes: true
    save_git_commit: true
    
  # 可复现性
  reproducibility:
    seed: 42
    deterministic: true
