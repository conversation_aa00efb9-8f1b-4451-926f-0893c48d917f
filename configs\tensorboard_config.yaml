# 神农框架TensorBoard配置示例
# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-29

# TensorBoard增强配置
logging:
  # 基础日志设置
  log_level: "INFO"
  log_dir: "logs"
  experiment_name: "shennong_antibacterial_experiment"
  
  # TensorBoard详细配置
  tensorboard:
    enabled: true
    log_graph: true                    # 记录模型计算图
    log_hyperparams: true             # 记录超参数
    log_model_architecture: true      # 记录模型架构
    
    # 自定义标量记录
    scalars:
      - "train_loss"
      - "val_loss" 
      - "train_activity_loss"
      - "val_activity_loss"
      - "train_uncertainty_loss"
      - "val_uncertainty_loss"
      - "learning_rate"
      
    # 注意力权重记录
    attention_logging:
      enabled: true
      log_every_n_epochs: 5           # 每5个epoch记录一次
      log_attention_heatmaps: true    # 记录注意力热图
      log_attention_statistics: true  # 记录注意力统计
      
    # 分子可视化
    molecular_visualization:
      enabled: true
      log_molecular_attention: true   # 分子注意力可视化
      log_descriptor_importance: true # 描述符重要性
      sample_molecules_per_epoch: 10  # 每epoch可视化的分子数
      
    # 模型性能监控
    performance_monitoring:
      log_gpu_memory: true           # GPU内存使用
      log_training_speed: true       # 训练速度
      log_batch_processing_time: true # 批处理时间
      
  # Weights & Biases集成（可选）
  wandb:
    enabled: false                    # 可以同时使用W&B和TensorBoard
    project: "shennong-framework"
    entity: null
    tags: ["antibacterial", "attention", "dual-modal"]
    sync_tensorboard: true           # 同步TensorBoard日志到W&B

# 训练配置（影响TensorBoard记录）
training:
  # 检查点配置
  checkpoint:
    monitor: "val_loss"
    mode: "min"
    save_top_k: 3
    save_last: true
    filename: "shennong-{epoch:02d}-{val_loss:.2f}"
    
  # 早停配置
  early_stopping:
    enabled: true
    monitor: "val_loss"
    patience: 20
    min_delta: 1e-4
    mode: "min"
    
  # 学习率调度
  lr_scheduler:
    name: "reduce_on_plateau"
    monitor: "val_loss"
    factor: 0.5
    patience: 10
    min_lr: 1e-6

# 可解释性配置（影响TensorBoard可视化）
interpretability:
  # 注意力分析
  attention_analysis:
    enabled: true
    visualize_attention_weights: true
    log_attention_entropy: true
    analyze_attention_patterns: true
    
  # 分子性质分析
  molecular_analysis:
    enabled: true
    log_descriptor_correlations: true
    visualize_molecular_embeddings: true
    track_property_importance: true
    
  # 化学解释
  chemical_explanation:
    enabled: true
    generate_explanation_reports: true
    log_pharmacophore_analysis: true
    track_functional_group_importance: true

# 硬件配置
hardware:
  accelerator: "auto"
  devices: "auto"
  precision: 32
  
# 数据配置
data:
  batch_size: 32
  num_workers: 4
  pin_memory: true
