# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-27
# 描述: 神农框架抗菌靶点信息模块

"""
神农框架抗菌靶点信息

定义细菌靶点和相关信息，为靶点预测提供生物学基础。
"""

from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class BacterialType(Enum):
    """细菌类型枚举"""
    GRAM_POSITIVE = "gram_positive"
    GRAM_NEGATIVE = "gram_negative"
    BOTH = "both"


@dataclass
class BacterialTarget:
    """细菌靶点数据类"""
    name: str
    target_type: str
    description: str
    bacterial_specificity: BacterialType
    associated_mechanisms: List[str]
    known_inhibitors: List[str]
    resistance_mechanisms: List[str]
    clinical_importance: str  # 'high', 'medium', 'low'
    
    def __post_init__(self):
        if not self.resistance_mechanisms:
            self.resistance_mechanisms = []


# 细菌靶点定义
BACTERIAL_TARGETS = {
    'penicillin_binding_proteins': BacterialTarget(
        name='青霉素结合蛋白 (PBPs)',
        target_type='enzyme',
        description='参与细胞壁肽聚糖合成的关键酶，β-内酰胺类抗生素的主要靶点',
        bacterial_specificity=BacterialType.BOTH,
        associated_mechanisms=['cell_wall_synthesis'],
        known_inhibitors=[
            'penicillin', 'ampicillin', 'cephalexin', 'meropenem', 'vancomycin'
        ],
        resistance_mechanisms=[
            'pbp_modification', 'beta_lactamase_production', 'efflux_pumps'
        ],
        clinical_importance='high'
    ),
    
    'dna_gyrase': BacterialTarget(
        name='DNA回旋酶',
        target_type='enzyme',
        description='负责DNA超螺旋结构的酶，氟喹诺酮类抗生素的主要靶点',
        bacterial_specificity=BacterialType.BOTH,
        associated_mechanisms=['dna_replication'],
        known_inhibitors=[
            'ciprofloxacin', 'levofloxacin', 'moxifloxacin', 'nalidixic_acid'
        ],
        resistance_mechanisms=[
            'gyrase_mutation', 'efflux_pumps', 'qnr_genes', 'target_protection'
        ],
        clinical_importance='high'
    ),
    
    'topoisomerase_iv': BacterialTarget(
        name='拓扑异构酶IV',
        target_type='enzyme',
        description='参与DNA复制和分离的酶，氟喹诺酮类的次要靶点',
        bacterial_specificity=BacterialType.BOTH,
        associated_mechanisms=['dna_replication'],
        known_inhibitors=[
            'ciprofloxacin', 'levofloxacin', 'gemifloxacin'
        ],
        resistance_mechanisms=[
            'topoisomerase_mutation', 'efflux_pumps'
        ],
        clinical_importance='high'
    ),
    
    '30s_ribosome': BacterialTarget(
        name='30S核糖体亚基',
        target_type='ribosome',
        description='细菌蛋白质合成的小亚基，氨基糖苷类和四环素类的靶点',
        bacterial_specificity=BacterialType.BOTH,
        associated_mechanisms=['protein_synthesis'],
        known_inhibitors=[
            'streptomycin', 'gentamicin', 'tobramycin', 'tetracycline', 'doxycycline'
        ],
        resistance_mechanisms=[
            'ribosome_modification', 'efflux_pumps', 'enzymatic_inactivation'
        ],
        clinical_importance='high'
    ),
    
    '50s_ribosome': BacterialTarget(
        name='50S核糖体亚基',
        target_type='ribosome',
        description='细菌蛋白质合成的大亚基，大环内酯类和氯霉素类的靶点',
        bacterial_specificity=BacterialType.BOTH,
        associated_mechanisms=['protein_synthesis'],
        known_inhibitors=[
            'erythromycin', 'azithromycin', 'clarithromycin', 'chloramphenicol'
        ],
        resistance_mechanisms=[
            'ribosome_methylation', 'efflux_pumps', 'target_modification'
        ],
        clinical_importance='high'
    ),
    
    'cell_membrane': BacterialTarget(
        name='细胞膜',
        target_type='membrane',
        description='细菌细胞的外膜结构，多肽类抗生素的靶点',
        bacterial_specificity=BacterialType.GRAM_NEGATIVE,
        associated_mechanisms=['cell_membrane'],
        known_inhibitors=[
            'polymyxin_b', 'colistin', 'daptomycin'
        ],
        resistance_mechanisms=[
            'lps_modification', 'efflux_pumps', 'membrane_composition_change'
        ],
        clinical_importance='medium'
    ),
    
    'dihydrofolate_reductase': BacterialTarget(
        name='二氢叶酸还原酶',
        target_type='enzyme',
        description='叶酸代谢途径的关键酶，甲氧苄啶的靶点',
        bacterial_specificity=BacterialType.BOTH,
        associated_mechanisms=['metabolic_pathway'],
        known_inhibitors=[
            'trimethoprim', 'pyrimethamine'
        ],
        resistance_mechanisms=[
            'enzyme_overproduction', 'alternative_pathways', 'efflux_pumps'
        ],
        clinical_importance='medium'
    ),
    
    'dihydropteroate_synthase': BacterialTarget(
        name='二氢蝶酸合成酶',
        target_type='enzyme',
        description='叶酸合成途径的酶，磺胺类药物的靶点',
        bacterial_specificity=BacterialType.BOTH,
        associated_mechanisms=['metabolic_pathway'],
        known_inhibitors=[
            'sulfamethoxazole', 'sulfadiazine', 'sulfisoxazole'
        ],
        resistance_mechanisms=[
            'enzyme_modification', 'alternative_pathways', 'efflux_pumps'
        ],
        clinical_importance='medium'
    ),
    
    'rna_polymerase': BacterialTarget(
        name='RNA聚合酶',
        target_type='enzyme',
        description='细菌转录的关键酶，利福霉素类的靶点',
        bacterial_specificity=BacterialType.BOTH,
        associated_mechanisms=['dna_replication'],
        known_inhibitors=[
            'rifampin', 'rifabutin', 'rifaximin'
        ],
        resistance_mechanisms=[
            'rna_polymerase_mutation', 'efflux_pumps'
        ],
        clinical_importance='high'
    ),
    
    'mycolic_acid_synthesis': BacterialTarget(
        name='分枝菌酸合成酶',
        target_type='enzyme',
        description='分枝杆菌细胞壁成分合成酶，抗结核药物靶点',
        bacterial_specificity=BacterialType.GRAM_POSITIVE,
        associated_mechanisms=['cell_wall_synthesis'],
        known_inhibitors=[
            'isoniazid', 'ethionamide'
        ],
        resistance_mechanisms=[
            'enzyme_mutation', 'efflux_pumps'
        ],
        clinical_importance='high'
    )
}


class TargetPredictor:
    """
    靶点预测器
    
    基于分子特征预测可能的细菌靶点。
    """
    
    def __init__(self):
        """初始化靶点预测器"""
        self.targets = BACTERIAL_TARGETS
        logger.info(f"初始化靶点预测器: {len(self.targets)}个靶点")
    
    def predict_targets(
        self, 
        molecular_features: Dict[str, Any],
        mechanism: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        预测分子的可能靶点
        
        Args:
            molecular_features: 分子特征字典
            mechanism: 已知的抗菌机制
            
        Returns:
            可能靶点列表
        """
        predicted_targets = []
        
        # 如果已知机制，筛选相关靶点
        if mechanism:
            relevant_targets = [
                target for target in self.targets.values()
                if mechanism in target.associated_mechanisms
            ]
        else:
            relevant_targets = list(self.targets.values())
        
        # 基于分子特征评分
        for target in relevant_targets:
            score = self._calculate_target_score(molecular_features, target)
            
            if score > 0.3:  # 阈值
                predicted_targets.append({
                    'target_name': target.name,
                    'target_type': target.target_type,
                    'score': score,
                    'bacterial_specificity': target.bacterial_specificity.value,
                    'clinical_importance': target.clinical_importance
                })
        
        # 按分数排序
        predicted_targets.sort(key=lambda x: x['score'], reverse=True)
        
        return predicted_targets
    
    def _calculate_target_score(
        self, 
        molecular_features: Dict[str, Any], 
        target: BacterialTarget
    ) -> float:
        """
        计算分子对特定靶点的评分
        
        Args:
            molecular_features: 分子特征
            target: 靶点信息
            
        Returns:
            评分 (0-1)
        """
        score = 0.0
        
        # 基于分子量
        mw = molecular_features.get('molecular_weight', 300)
        if target.target_type == 'enzyme':
            # 酶靶点偏好中等分子量
            if 200 <= mw <= 600:
                score += 0.3
        elif target.target_type == 'ribosome':
            # 核糖体靶点偏好较小分子
            if 100 <= mw <= 400:
                score += 0.3
        elif target.target_type == 'membrane':
            # 膜靶点偏好脂溶性分子
            if 300 <= mw <= 800:
                score += 0.3
        
        # 基于LogP
        logp = molecular_features.get('logp', 2.0)
        if target.target_type == 'membrane':
            # 膜靶点需要适度疏水性
            if 1.0 <= logp <= 4.0:
                score += 0.3
        else:
            # 其他靶点偏好适中的疏水性
            if -1.0 <= logp <= 3.0:
                score += 0.2
        
        # 基于氢键供体/受体
        hbd = molecular_features.get('hbd', 2)
        hba = molecular_features.get('hba', 4)
        
        if target.target_type == 'enzyme':
            # 酶靶点需要氢键相互作用
            if 1 <= hbd <= 5 and 2 <= hba <= 8:
                score += 0.2
        
        # 基于极性表面积
        tpsa = molecular_features.get('tpsa', 80)
        if target.bacterial_specificity == BacterialType.GRAM_NEGATIVE:
            # 革兰氏阴性菌需要适度的极性
            if 40 <= tpsa <= 120:
                score += 0.2
        elif target.bacterial_specificity == BacterialType.GRAM_POSITIVE:
            # 革兰氏阳性菌可以接受更高的极性
            if 20 <= tpsa <= 150:
                score += 0.2
        
        return min(score, 1.0)


def get_target_by_name(target_name: str) -> Optional[BacterialTarget]:
    """根据名称获取靶点信息"""
    return BACTERIAL_TARGETS.get(target_name)


def get_all_targets() -> Dict[str, BacterialTarget]:
    """获取所有靶点信息"""
    return BACTERIAL_TARGETS.copy()


def get_targets_by_mechanism(mechanism: str) -> List[BacterialTarget]:
    """根据机制获取相关靶点"""
    return [
        target for target in BACTERIAL_TARGETS.values()
        if mechanism in target.associated_mechanisms
    ]


def get_targets_by_bacterial_type(bacterial_type: BacterialType) -> List[BacterialTarget]:
    """根据细菌类型获取靶点"""
    return [
        target for target in BACTERIAL_TARGETS.values()
        if target.bacterial_specificity in [bacterial_type, BacterialType.BOTH]
    ]


def get_target_by_strain(strain: str) -> List[BacterialTarget]:
    """根据细菌菌株获取相关靶点"""
    # 定义菌株到细菌类型的映射
    strain_mapping = {
        'S.aureus': BacterialType.GRAM_POSITIVE,
        'E.faecalis': BacterialType.GRAM_POSITIVE,
        'S.pneumoniae': BacterialType.GRAM_POSITIVE,
        'E.coli': BacterialType.GRAM_NEGATIVE,
        'P.aeruginosa': BacterialType.GRAM_NEGATIVE,
        'K.pneumoniae': BacterialType.GRAM_NEGATIVE,
        'A.baumannii': BacterialType.GRAM_NEGATIVE
    }
    
    bacterial_type = strain_mapping.get(strain, BacterialType.BOTH)
    return get_targets_by_bacterial_type(bacterial_type)


def analyze_target_druggability(target_name: str) -> Dict[str, Any]:
    """
    分析靶点的成药性
    
    Args:
        target_name: 靶点名称
        
    Returns:
        成药性分析结果
    """
    target = get_target_by_name(target_name)
    if target is None:
        return {'error': f'未知靶点: {target_name}'}
    
    analysis = {
        'target_name': target.name,
        'druggability_score': 0.0,
        'advantages': [],
        'challenges': [],
        'development_status': 'unknown'
    }
    
    # 基于临床重要性评分
    importance_scores = {'high': 0.4, 'medium': 0.3, 'low': 0.2}
    analysis['druggability_score'] += importance_scores.get(target.clinical_importance, 0.2)
    
    # 基于已知抑制剂数量
    inhibitor_count = len(target.known_inhibitors)
    if inhibitor_count > 5:
        analysis['druggability_score'] += 0.3
        analysis['advantages'].append('多个已知抑制剂')
    elif inhibitor_count > 2:
        analysis['druggability_score'] += 0.2
        analysis['advantages'].append('有已知抑制剂')
    else:
        analysis['challenges'].append('缺乏已知抑制剂')
    
    # 基于耐药机制数量
    resistance_count = len(target.resistance_mechanisms)
    if resistance_count > 3:
        analysis['challenges'].append('多种耐药机制')
    elif resistance_count > 1:
        analysis['challenges'].append('存在耐药机制')
    else:
        analysis['advantages'].append('耐药机制较少')
        analysis['druggability_score'] += 0.1
    
    # 基于靶点类型
    if target.target_type == 'enzyme':
        analysis['advantages'].append('酶靶点，易于设计抑制剂')
        analysis['druggability_score'] += 0.2
    elif target.target_type == 'ribosome':
        analysis['advantages'].append('核糖体靶点，已有成功先例')
        analysis['druggability_score'] += 0.1
    elif target.target_type == 'membrane':
        analysis['challenges'].append('膜靶点，设计难度较大')
    
    # 确定开发状态
    if analysis['druggability_score'] >= 0.7:
        analysis['development_status'] = 'highly_attractive'
    elif analysis['druggability_score'] >= 0.5:
        analysis['development_status'] = 'moderately_attractive'
    else:
        analysis['development_status'] = 'challenging'
    
    return analysis
