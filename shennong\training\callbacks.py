# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-27
# 描述: 神农框架训练回调

"""
神农框架训练回调

提供训练过程中的监控、可视化和解释功能。
"""

from typing import Dict, Any, Optional
import torch
import lightning as L
import matplotlib.pyplot as plt
import numpy as np
import logging
from pathlib import Path

logger = logging.getLogger(__name__)


class AntibacterialInterpretationCallback(<PERSON><PERSON>Callback):
    """
    抗菌活性解释回调
    
    在验证阶段分析注意力权重和生物学合理性。
    """
    
    def __init__(
        self,
        log_every_n_epochs: int = 10,
        save_plots: bool = True,
        plot_dir: str = "plots"
    ):
        """
        初始化解释回调
        
        Args:
            log_every_n_epochs: 每隔多少个epoch记录一次
            save_plots: 是否保存图片
            plot_dir: 图片保存目录
        """
        super().__init__()
        
        self.log_every_n_epochs = log_every_n_epochs
        self.save_plots = save_plots
        self.plot_dir = Path(plot_dir)
        
        if self.save_plots:
            self.plot_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"初始化抗菌解释回调: 记录间隔={log_every_n_epochs}epochs")
    
    def on_validation_epoch_end(self, trainer: L.Trainer, pl_module: L.LightningModule):
        """验证epoch结束时的回调"""
        current_epoch = trainer.current_epoch
        
        # 只在指定的epoch进行分析
        if current_epoch % self.log_every_n_epochs != 0:
            return
        
        logger.info(f"Epoch {current_epoch}: 开始抗菌活性解释分析")
        
        try:
            # 注意力权重可视化
            self._visualize_attention_weights(trainer, pl_module, current_epoch)
            
            # 生物学合理性检查
            self._check_biological_plausibility(trainer, pl_module, current_epoch)
            
            # 机制分类准确率监控
            self._log_mechanism_classification_metrics(trainer, pl_module, current_epoch)
            
        except Exception as e:
            logger.warning(f"解释分析失败: {e}")
    
    def _visualize_attention_weights(
        self, 
        trainer: L.Trainer, 
        pl_module: L.LightningModule, 
        epoch: int
    ):
        """可视化注意力权重"""
        # 获取验证数据的一个批次
        val_dataloader = trainer.val_dataloaders
        if val_dataloader is None:
            return
        
        # 获取一个批次的数据
        batch = next(iter(val_dataloader))
        
        # 移动到设备
        batch = {k: v.to(pl_module.device) if isinstance(v, torch.Tensor) else v 
                for k, v in batch.items()}
        
        # 前向传播获取注意力权重
        pl_module.eval()
        with torch.no_grad():
            outputs = pl_module(batch, return_attention=True)
        
        attention_weights = outputs.get('attention_weights', {})
        
        if not attention_weights:
            return
        
        # 创建注意力权重可视化
        self._plot_attention_heatmap(attention_weights, epoch)
        
        # 记录注意力统计信息
        self._log_attention_statistics(attention_weights, trainer.logger, epoch)
    
    def _plot_attention_heatmap(self, attention_weights: Dict[str, Any], epoch: int):
        """绘制注意力权重热图"""
        if not self.save_plots:
            return
        
        try:
            fig, axes = plt.subplots(2, 2, figsize=(12, 10))
            fig.suptitle(f'Attention Weights Analysis - Epoch {epoch}', fontsize=16)
            
            # 绘制不同类型的注意力权重
            attention_types = ['graph_cross_attention', 'expert_cross_attention', 
                             'mechanism_weights', 'head_weights']
            
            for i, attention_type in enumerate(attention_types):
                ax = axes[i // 2, i % 2]
                
                if attention_type in attention_weights:
                    weights = attention_weights[attention_type]
                    
                    if torch.is_tensor(weights):
                        weights_np = weights.detach().cpu().numpy()
                        
                        if weights_np.ndim == 2:
                            im = ax.imshow(weights_np, cmap='viridis', aspect='auto')
                            plt.colorbar(im, ax=ax)
                        elif weights_np.ndim == 1:
                            ax.bar(range(len(weights_np)), weights_np)
                        
                        ax.set_title(f'{attention_type}')
                    else:
                        ax.text(0.5, 0.5, 'No data', ha='center', va='center', transform=ax.transAxes)
                        ax.set_title(f'{attention_type} (No data)')
                else:
                    ax.text(0.5, 0.5, 'Not available', ha='center', va='center', transform=ax.transAxes)
                    ax.set_title(f'{attention_type} (N/A)')
            
            plt.tight_layout()
            
            # 保存图片
            plot_path = self.plot_dir / f'attention_weights_epoch_{epoch}.png'
            plt.savefig(plot_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            logger.info(f"注意力权重图已保存: {plot_path}")
            
        except Exception as e:
            logger.warning(f"注意力权重可视化失败: {e}")
    
    def _log_attention_statistics(
        self, 
        attention_weights: Dict[str, Any], 
        logger_obj: Any, 
        epoch: int
    ):
        """记录注意力权重统计信息"""
        try:
            stats = {}
            
            for name, weights in attention_weights.items():
                if torch.is_tensor(weights):
                    weights_flat = weights.flatten()
                    
                    stats[f'attention/{name}/mean'] = float(weights_flat.mean())
                    stats[f'attention/{name}/std'] = float(weights_flat.std())
                    stats[f'attention/{name}/max'] = float(weights_flat.max())
                    stats[f'attention/{name}/min'] = float(weights_flat.min())
                    
                    # 稀疏性指标
                    sparsity = float((weights_flat.abs() < 0.01).float().mean())
                    stats[f'attention/{name}/sparsity'] = sparsity
            
            # 记录到日志
            if hasattr(logger_obj, 'log_metrics'):
                logger_obj.log_metrics(stats, step=epoch)
            
        except Exception as e:
            logger.warning(f"注意力统计记录失败: {e}")
    
    def _check_biological_plausibility(
        self, 
        trainer: L.Trainer, 
        pl_module: L.LightningModule, 
        epoch: int
    ):
        """检查生物学合理性"""
        try:
            # 这里可以添加生物学合理性检查逻辑
            # 例如：检查注意力权重是否集中在已知的药效团上
            
            # 获取模型的机制预测准确率
            if hasattr(pl_module, 'mechanism_classifier') and pl_module.mechanism_classifier is not None:
                # 计算机制预测的一致性
                consistency_score = self._calculate_mechanism_consistency(trainer, pl_module)
                
                if hasattr(trainer.logger, 'log_metrics'):
                    trainer.logger.log_metrics({
                        'biological/mechanism_consistency': consistency_score
                    }, step=epoch)
            
        except Exception as e:
            logger.warning(f"生物学合理性检查失败: {e}")
    
    def _calculate_mechanism_consistency(
        self, 
        trainer: L.Trainer, 
        pl_module: L.LightningModule
    ) -> float:
        """计算机制预测的一致性"""
        # 简化的一致性计算
        # 实际应用中可以基于已知的药物-机制关系进行验证
        return 0.8  # 占位符
    
    def _log_mechanism_classification_metrics(
        self, 
        trainer: L.Trainer, 
        pl_module: L.LightningModule, 
        epoch: int
    ):
        """记录机制分类指标"""
        try:
            # 如果模型有机制分类器，计算其性能
            if hasattr(pl_module, 'mechanism_classifier') and pl_module.mechanism_classifier is not None:
                # 在验证集上评估机制分类性能
                val_dataloader = trainer.val_dataloaders
                if val_dataloader is None:
                    return
                
                all_mechanism_preds = []
                all_mechanism_targets = []
                
                pl_module.eval()
                with torch.no_grad():
                    for batch in val_dataloader:
                        batch = {k: v.to(pl_module.device) if isinstance(v, torch.Tensor) else v 
                                for k, v in batch.items()}
                        
                        outputs = pl_module(batch)
                        
                        if 'mechanism' in outputs:
                            mechanism_preds = outputs['mechanism']
                            all_mechanism_preds.append(mechanism_preds.cpu())
                        
                        if 'mechanism_labels' in batch:
                            mechanism_targets = batch['mechanism_labels']
                            if isinstance(mechanism_targets, dict):
                                mechanism_targets = mechanism_targets['labels']
                            all_mechanism_targets.append(mechanism_targets.cpu())
                
                if all_mechanism_preds and all_mechanism_targets:
                    # 计算机制分类准确率
                    preds = torch.cat(all_mechanism_preds, dim=0)
                    targets = torch.cat(all_mechanism_targets, dim=0)
                    
                    # 多标签分类准确率
                    pred_labels = (torch.sigmoid(preds) > 0.5).float()
                    accuracy = (pred_labels == targets).float().mean()
                    
                    if hasattr(trainer.logger, 'log_metrics'):
                        trainer.logger.log_metrics({
                            'mechanism/classification_accuracy': float(accuracy)
                        }, step=epoch)
        
        except Exception as e:
            logger.warning(f"机制分类指标记录失败: {e}")


class ModelCheckpointCallback(L.Callback):
    """
    模型检查点回调
    
    扩展的模型保存功能。
    """
    
    def __init__(
        self,
        save_dir: str = "checkpoints",
        save_best_only: bool = True,
        monitor: str = "val_loss",
        mode: str = "min"
    ):
        """
        初始化检查点回调
        
        Args:
            save_dir: 保存目录
            save_best_only: 是否只保存最佳模型
            monitor: 监控指标
            mode: 监控模式
        """
        super().__init__()
        
        self.save_dir = Path(save_dir)
        self.save_best_only = save_best_only
        self.monitor = monitor
        self.mode = mode
        
        self.best_score = float('inf') if mode == 'min' else float('-inf')
        
        self.save_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"初始化检查点回调: 监控={monitor}, 模式={mode}")
    
    def on_validation_epoch_end(self, trainer: L.Trainer, pl_module: L.LightningModule):
        """验证结束时检查是否需要保存模型"""
        current_score = trainer.callback_metrics.get(self.monitor)
        
        if current_score is None:
            return
        
        current_score = float(current_score)
        
        # 检查是否是最佳分数
        is_best = False
        if self.mode == 'min' and current_score < self.best_score:
            is_best = True
            self.best_score = current_score
        elif self.mode == 'max' and current_score > self.best_score:
            is_best = True
            self.best_score = current_score
        
        # 保存模型
        if is_best or not self.save_best_only:
            epoch = trainer.current_epoch
            filename = f"shennong_epoch_{epoch}_{self.monitor}_{current_score:.4f}.pt"
            filepath = self.save_dir / filename
            
            # 保存模型
            pl_module.save_model(str(filepath))
            
            if is_best:
                # 同时保存为最佳模型
                best_filepath = self.save_dir / "best_model.pt"
                pl_module.save_model(str(best_filepath))
                
                logger.info(f"保存最佳模型: {filepath} (分数: {current_score:.4f})")
            else:
                logger.info(f"保存模型检查点: {filepath}")


class LearningRateMonitor(L.Callback):
    """
    学习率监控回调
    
    记录学习率变化。
    """
    
    def __init__(self, logging_interval: str = 'epoch'):
        """
        初始化学习率监控
        
        Args:
            logging_interval: 记录间隔 ('step' 或 'epoch')
        """
        super().__init__()
        self.logging_interval = logging_interval
    
    def on_train_epoch_start(self, trainer: L.Trainer, pl_module: L.LightningModule):
        """训练epoch开始时记录学习率"""
        if self.logging_interval == 'epoch':
            self._log_learning_rate(trainer, pl_module)
    
    def on_train_batch_end(
        self, 
        trainer: L.Trainer, 
        pl_module: L.LightningModule, 
        outputs, 
        batch, 
        batch_idx
    ):
        """训练batch结束时记录学习率"""
        if self.logging_interval == 'step':
            self._log_learning_rate(trainer, pl_module)
    
    def _log_learning_rate(self, trainer: L.Trainer, pl_module: L.LightningModule):
        """记录当前学习率"""
        try:
            optimizer = pl_module.optimizers()
            if optimizer is not None:
                current_lr = optimizer.param_groups[0]['lr']
                
                if hasattr(trainer.logger, 'log_metrics'):
                    trainer.logger.log_metrics({
                        'learning_rate': current_lr
                    }, step=trainer.global_step)
        
        except Exception as e:
            logger.warning(f"学习率记录失败: {e}")
