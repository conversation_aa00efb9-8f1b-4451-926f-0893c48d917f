# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-29
# 描述: 神农框架指纹提取子命令

"""
神农框架指纹提取子命令

提供分子指纹特征提取的命令行接口。
"""

import logging
import numpy as np
import pandas as pd
from pathlib import Path
from typing import List, Dict, Any

try:
    from configargparse import ArgumentParser, Namespace
except ImportError:
    from argparse import ArgumentParser, Namespace

from .utils import validate_file_path, print_banner
from ..models.shennong_core import ShennongFramework

logger = logging.getLogger(__name__)


class FingerprintSubcommand:
    """指纹提取子命令类"""
    
    COMMAND = "fingerprint"
    HELP = "提取分子指纹特征"
    
    @classmethod
    def add(cls, subparsers, parents):
        """添加指纹提取子命令到解析器"""
        parser = subparsers.add_parser(
            cls.COMMAND,
            help=cls.HELP,
            parents=parents,
            description="使用训练好的模型提取分子指纹特征"
        )
        
        # 模型参数
        model_group = parser.add_argument_group("模型参数")
        model_group.add_argument(
            "--model-path",
            type=str,
            required=True,
            help="训练好的模型文件路径"
        )
        
        # 输入数据参数
        input_group = parser.add_argument_group("输入数据参数")
        input_group.add_argument(
            "--input-path",
            type=str,
            help="输入数据CSV文件路径"
        )
        input_group.add_argument(
            "--smiles",
            type=str,
            nargs="+",
            help="直接指定SMILES字符串列表"
        )
        input_group.add_argument(
            "--smiles-column",
            type=str,
            default="smiles",
            help="SMILES列名 (默认: smiles)"
        )
        
        # 提取参数
        extract_group = parser.add_argument_group("提取参数")
        extract_group.add_argument(
            "--layer",
            type=str,
            choices=["graph", "expert", "fusion", "final"],
            default="fusion",
            help="提取特征的层 (默认: fusion)"
        )
        extract_group.add_argument(
            "--batch-size",
            type=int,
            default=32,
            help="批次大小 (默认: 32)"
        )
        extract_group.add_argument(
            "--include-attention",
            action="store_true",
            help="包含注意力权重"
        )
        
        # 输出参数
        output_group = parser.add_argument_group("输出参数")
        output_group.add_argument(
            "--output-path",
            type=str,
            required=True,
            help="指纹特征输出文件路径"
        )
        output_group.add_argument(
            "--output-format",
            type=str,
            choices=["csv", "npy", "h5"],
            default="csv",
            help="输出文件格式 (默认: csv)"
        )
        output_group.add_argument(
            "--include-smiles",
            action="store_true",
            help="在输出中包含SMILES"
        )
        
        # 硬件参数
        hardware_group = parser.add_argument_group("硬件参数")
        hardware_group.add_argument(
            "--device",
            type=str,
            choices=["auto", "cpu", "cuda", "mps"],
            default="auto",
            help="计算设备 (默认: auto)"
        )
        
        parser.set_defaults(func=cls.func)
        return parser
    
    @classmethod
    def func(cls, args: Namespace):
        """执行指纹提取命令"""
        print_banner("🔍 神农框架指纹提取")
        
        # 验证参数
        cls._validate_args(args)
        
        # 加载模型
        model = cls._load_model(args)
        
        # 加载输入数据
        smiles_list = cls._load_input_data(args)
        
        # 提取指纹
        logger.info("开始提取分子指纹...")
        fingerprints = cls._extract_fingerprints(model, smiles_list, args)
        
        # 保存结果
        cls._save_fingerprints(fingerprints, smiles_list, args)
        
        logger.info("指纹提取完成!")
        print("✅ 指纹提取成功完成!")
    
    @classmethod
    def _validate_args(cls, args: Namespace):
        """验证命令行参数"""
        # 验证模型文件
        validate_file_path(args.model_path, must_exist=True)
        
        # 验证输入数据
        if not args.input_path and not args.smiles:
            raise ValueError("必须指定 --input-path 或 --smiles")
        
        if args.input_path and args.smiles:
            raise ValueError("--input-path 和 --smiles 不能同时指定")
        
        if args.input_path:
            validate_file_path(args.input_path, must_exist=True)
        
        # 验证输出路径
        output_path = Path(args.output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 验证数值参数
        if args.batch_size <= 0:
            raise ValueError("batch_size必须大于0")
    
    @classmethod
    def _load_model(cls, args: Namespace) -> ShennongFramework:
        """加载模型"""
        logger.info(f"加载模型: {args.model_path}")
        
        try:
            model = ShennongFramework.load_model(args.model_path)
            
            # 设置设备
            if args.device != "auto":
                model = model.to(args.device)
            
            model.eval()
            logger.info("模型加载完成")
            return model
            
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            raise
    
    @classmethod
    def _load_input_data(cls, args: Namespace) -> List[str]:
        """加载输入数据"""
        if args.smiles:
            logger.info(f"使用命令行指定的 {len(args.smiles)} 个SMILES")
            return args.smiles
        
        elif args.input_path:
            logger.info(f"从文件加载数据: {args.input_path}")
            
            try:
                df = pd.read_csv(args.input_path)
                
                if args.smiles_column not in df.columns:
                    raise ValueError(f"未找到SMILES列: {args.smiles_column}")
                
                smiles_list = df[args.smiles_column].tolist()
                logger.info(f"加载了 {len(smiles_list)} 个SMILES")
                return smiles_list
                
            except Exception as e:
                logger.error(f"数据加载失败: {e}")
                raise
    
    @classmethod
    def _extract_fingerprints(cls, model: ShennongFramework, smiles_list: List[str], args: Namespace) -> Dict[str, Any]:
        """提取分子指纹"""
        try:
            # TODO: 实现指纹提取逻辑
            # 这里需要调用模型的指纹提取方法
            
            fingerprints = {
                'features': np.random.rand(len(smiles_list), 256),  # 占位符
                'attention_weights': None
            }
            
            if args.include_attention:
                fingerprints['attention_weights'] = np.random.rand(len(smiles_list), 4, 64)  # 占位符
            
            logger.info(f"指纹提取完成，处理了 {len(smiles_list)} 个分子")
            return fingerprints
            
        except Exception as e:
            logger.error(f"指纹提取失败: {e}")
            raise
    
    @classmethod
    def _save_fingerprints(cls, fingerprints: Dict[str, Any], smiles_list: List[str], args: Namespace):
        """保存指纹特征"""
        logger.info(f"保存指纹到: {args.output_path}")
        
        try:
            features = fingerprints['features']
            
            if args.output_format == "csv":
                # 创建数据框
                data = {}
                
                if args.include_smiles:
                    data['smiles'] = smiles_list
                
                # 添加特征列
                for i in range(features.shape[1]):
                    data[f'feature_{i}'] = features[:, i]
                
                df = pd.DataFrame(data)
                df.to_csv(args.output_path, index=False)
                
            elif args.output_format == "npy":
                # 保存为numpy数组
                np.save(args.output_path, features)
                
                # 如果包含SMILES，单独保存
                if args.include_smiles:
                    smiles_path = Path(args.output_path).with_suffix('.smiles.txt')
                    with open(smiles_path, 'w') as f:
                        for smiles in smiles_list:
                            f.write(f"{smiles}\n")
                            
            elif args.output_format == "h5":
                # 保存为HDF5格式
                import h5py
                
                with h5py.File(args.output_path, 'w') as f:
                    f.create_dataset('features', data=features)
                    
                    if args.include_smiles:
                        # 将SMILES转换为字节串
                        smiles_bytes = [s.encode('utf-8') for s in smiles_list]
                        f.create_dataset('smiles', data=smiles_bytes)
                    
                    if args.include_attention and fingerprints['attention_weights'] is not None:
                        f.create_dataset('attention_weights', data=fingerprints['attention_weights'])
            
            logger.info("指纹保存完成")
            
        except Exception as e:
            logger.error(f"指纹保存失败: {e}")
            raise
