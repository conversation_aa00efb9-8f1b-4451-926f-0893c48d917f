# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-01-15
# 描述: COCONUT预训练实验完整方案设计

# COCONUT预训练实验完整方案

## 1. 实验背景与目标

### 1.1 研究目标
基于COCONUT天然产物数据库进行分子预训练，构建专门针对天然产物的分子表示学习模型，用于后续抗菌活性预测任务的微调。

### 1.2 核心假设
- 天然产物具有独特的化学空间分布，专门的预训练能够学习到更好的天然产物表示
- 结合分子图结构和Mordred描述符的多模态预训练能够提升模型性能
- 基于scaffold split的严格评估能够真实反映模型的泛化能力

### 1.3 创新点
1. **领域特异性预训练**: 专门针对天然产物化学空间的预训练策略
2. **多模态融合**: 结合D-MPNN图神经网络和Mordred分子描述符
3. **严格评估体系**: 采用scaffold split和多基线对比的评估框架

## 2. 文献调研总结

### 2.1 关键技术文献
1. **CheMeleon (2024)**: 基于Mordred描述符的分子基础模型，证明了描述符预训练的有效性
2. **COCONUT数据库**: 包含40万+天然产物结构，是目前最大的开放天然产物数据库
3. **Scaffold Split**: 确保测试集包含训练集中未见过的分子骨架，提供更严格的泛化能力评估

### 2.2 评估标准参考
- **定量指标**: RMSE, MAE, R², Pearson相关系数
- **统计严谨性**: 多次随机种子重复实验，报告均值和标准差
- **定性分析**: t-SNE可视化、活性悬崖分析、新颖性分析

## 3. 数据处理方案

### 3.1 COCONUT预训练数据
```
数据来源: COCONUT数据库 (https://coconut.naturalproducts.net/)
预期规模: ~400,000个天然产物分子
处理流程:
1. 下载SMILES格式数据
2. RDKit标准化和去重
3. 计算Murcko骨架
4. 生成Mordred描述符 (完整分子 + 骨架)
5. 构建预训练目标向量
```

### 3.2 ChEMBL微调数据
```
数据来源: ChEMBL数据库
目标: 抗菌活性数据 (IC50, MIC, Ki等)
处理流程:
1. 提取特定抗菌靶点数据
2. 活性值标准化 (转换为pIC50)
3. 分子标准化和去重
4. Scaffold split划分 (80%/10%/10%)
```

## 4. 模型架构设计

### 4.1 预训练模型
```
基础架构: D-MPNN (Directed Message Passing Neural Network)
输入: 分子图结构
输出: 拼接的Mordred描述符向量 (分子 + 骨架)
损失函数: MSE Loss
优化器: Adam with learning rate scheduling
```

### 4.2 微调模型
```
预训练权重: 加载COCONUT预训练模型
预测头: 替换为小型MLP (1-2层)
输出: 单一回归值 (pIC50)
正则化: Dropout, Early Stopping
```

## 5. 实验设计

### 5.1 预训练阶段
- **数据**: COCONUT天然产物 (~400K)
- **目标**: 预测Mordred描述符向量
- **验证**: 10%数据用于验证集
- **早停**: 验证损失连续20个epoch不下降

### 5.2 微调阶段
- **数据**: ChEMBL抗菌活性数据
- **划分**: Scaffold split (80%/10%/10%)
- **基线对比**: 
  - 从零训练的D-MPNN
  - 随机森林 + ECFP指纹
  - 原始CheMeleon模型

### 5.3 评估指标
- **定量指标**: RMSE, MAE, R², Pearson R
- **统计检验**: 5次重复实验，报告均值±标准差
- **可视化分析**: t-SNE降维、活性悬崖分析

## 6. 技术实现要点

### 6.1 关键依赖
```python
# 核心包
rdkit-pypi>=2023.9.1
mordred>=1.2.0
torch>=2.0.0
torch-geometric>=2.4.0
scikit-learn>=1.3.0
pandas>=2.0.0
numpy>=1.24.0

# 可视化
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.15.0
```

### 6.2 计算资源需求
- **GPU**: NVIDIA RTX 4090或更高 (24GB+ VRAM)
- **内存**: 32GB+ RAM
- **存储**: 100GB+ 可用空间
- **预计训练时间**: 预训练 2-3天，微调 4-6小时

## 7. 预期结果与成功标准

### 7.1 预训练成功标准
- 验证集MSE损失收敛且稳定
- 学习到的分子表示在t-SNE中显示合理聚类
- 描述符重构精度达到合理水平

### 7.2 微调成功标准
- 相比从零训练提升 >5% RMSE
- 相比随机森林基线具有统计显著性优势
- 在活性悬崖预测上表现良好

### 7.3 学术贡献
- 证明天然产物特异性预训练的有效性
- 建立严格的分子预训练评估标准
- 为抗菌药物发现提供新的计算工具
