# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-27
# 描述: 神农框架生物学解释模块

"""
神农框架生物学解释

提供模型预测结果的生物学解释和可视化功能。
"""

from typing import Dict, List, Optional, Any, Tuple
import numpy as np
import torch
import logging
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class BiologicalExplanation:
    """生物学解释结果"""
    compound_smiles: str
    predicted_activity: float
    predicted_mechanisms: List[str]
    attention_analysis: Dict[str, Any]
    pharmacophore_analysis: Dict[str, Any]
    target_predictions: List[Dict[str, Any]]
    confidence_score: float
    interpretation_summary: str


class BiologicalInterpreter:
    """
    生物学解释器
    
    整合注意力分析、药效团识别和靶点预测，提供综合的生物学解释。
    """
    
    def __init__(self):
        """初始化生物学解释器"""
        self.attention_analyzer = AttentionAnalyzer()
        self.mechanism_explainer = MechanismExplainer()
        
        logger.info("初始化生物学解释器")
    
    def interpret_prediction(
        self,
        smiles: str,
        model_outputs: Dict[str, torch.Tensor],
        molecular_features: Optional[Dict[str, Any]] = None
    ) -> BiologicalExplanation:
        """
        解释模型预测结果
        
        Args:
            smiles: SMILES字符串
            model_outputs: 模型输出
            molecular_features: 分子特征
            
        Returns:
            生物学解释结果
        """
        # 提取预测结果
        predicted_activity = float(model_outputs.get('activity', torch.tensor(0.0)).squeeze())
        
        # 机制预测
        predicted_mechanisms = []
        if 'mechanism' in model_outputs:
            mechanism_probs = torch.softmax(model_outputs['mechanism'], dim=-1)
            top_mechanisms = torch.topk(mechanism_probs, k=3)
            
            mechanism_names = ['cell_wall_synthesis', 'protein_synthesis', 'dna_replication', 
                             'cell_membrane', 'metabolic_pathway']
            
            for idx, prob in zip(top_mechanisms.indices[0], top_mechanisms.values[0]):
                if prob > 0.1:  # 阈值
                    predicted_mechanisms.append({
                        'mechanism': mechanism_names[idx],
                        'probability': float(prob)
                    })
        
        # 注意力分析
        attention_analysis = {}
        if 'attention_weights' in model_outputs:
            attention_analysis = self.attention_analyzer.analyze_attention(
                model_outputs['attention_weights']
            )
        
        # 药效团分析
        pharmacophore_analysis = self._analyze_pharmacophores(smiles, molecular_features)
        
        # 靶点预测
        target_predictions = self._predict_targets(molecular_features, predicted_mechanisms)
        
        # 计算置信度
        confidence_score = self._calculate_confidence(model_outputs, attention_analysis)
        
        # 生成解释摘要
        interpretation_summary = self._generate_summary(
            predicted_activity, predicted_mechanisms, attention_analysis, 
            pharmacophore_analysis, confidence_score
        )
        
        return BiologicalExplanation(
            compound_smiles=smiles,
            predicted_activity=predicted_activity,
            predicted_mechanisms=[m['mechanism'] for m in predicted_mechanisms],
            attention_analysis=attention_analysis,
            pharmacophore_analysis=pharmacophore_analysis,
            target_predictions=target_predictions,
            confidence_score=confidence_score,
            interpretation_summary=interpretation_summary
        )
    
    def _analyze_pharmacophores(
        self, 
        smiles: str, 
        molecular_features: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """分析药效团特征"""
        try:
            from rdkit import Chem
            from rdkit.Chem import rdMolDescriptors
            
            mol = Chem.MolFromSmiles(smiles)
            if mol is None:
                return {'error': 'Invalid SMILES'}
            
            analysis = {
                'aromatic_rings': rdMolDescriptors.CalcNumAromaticRings(mol),
                'hbd': rdMolDescriptors.CalcNumHBD(mol),
                'hba': rdMolDescriptors.CalcNumHBA(mol),
                'rotatable_bonds': rdMolDescriptors.CalcNumRotatableBonds(mol),
                'molecular_weight': rdMolDescriptors.CalcExactMolWt(mol),
                'identified_pharmacophores': []
            }
            
            # 识别常见药效团
            pharmacophore_patterns = {
                'beta_lactam': '[#6]1-[#6](=O)-[#7]-[#6]-1',
                'quinolone': 'c1cc2c(cc1)c(=O)c(cn2)C(=O)O',
                'sulfonamide': '[#16](=O)(=O)[#7]',
                'phenol': 'c1ccc(cc1)O'
            }
            
            for name, pattern in pharmacophore_patterns.items():
                pattern_mol = Chem.MolFromSmarts(pattern)
                if pattern_mol and mol.HasSubstructMatch(pattern_mol):
                    analysis['identified_pharmacophores'].append(name)
            
            return analysis
            
        except ImportError:
            return {'error': 'RDKit not available'}
        except Exception as e:
            return {'error': str(e)}
    
    def _predict_targets(
        self, 
        molecular_features: Optional[Dict[str, Any]], 
        predicted_mechanisms: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """预测可能的靶点"""
        if not molecular_features:
            return []
        
        try:
            from .targets import TargetPredictor
            
            predictor = TargetPredictor()
            
            # 如果有机制预测，使用最可能的机制
            mechanism = None
            if predicted_mechanisms:
                mechanism = predicted_mechanisms[0]['mechanism']
            
            targets = predictor.predict_targets(molecular_features, mechanism)
            return targets[:5]  # 返回前5个最可能的靶点
            
        except Exception as e:
            logger.warning(f"靶点预测失败: {e}")
            return []
    
    def _calculate_confidence(
        self, 
        model_outputs: Dict[str, torch.Tensor], 
        attention_analysis: Dict[str, Any]
    ) -> float:
        """计算预测置信度"""
        confidence = 0.5  # 基础置信度
        
        # 基于不确定性估计
        if 'uncertainty' in model_outputs:
            uncertainty = float(model_outputs['uncertainty'].mean())
            confidence += (1.0 - uncertainty) * 0.3
        
        # 基于注意力权重的集中度
        if 'attention_concentration' in attention_analysis:
            concentration = attention_analysis['attention_concentration']
            confidence += concentration * 0.2
        
        # 基于机制预测的确定性
        if 'mechanism' in model_outputs:
            mechanism_probs = torch.softmax(model_outputs['mechanism'], dim=-1)
            max_prob = float(mechanism_probs.max())
            confidence += max_prob * 0.2
        
        return min(confidence, 1.0)
    
    def _generate_summary(
        self,
        predicted_activity: float,
        predicted_mechanisms: List[Dict[str, Any]],
        attention_analysis: Dict[str, Any],
        pharmacophore_analysis: Dict[str, Any],
        confidence_score: float
    ) -> str:
        """生成解释摘要"""
        summary_parts = []
        
        # 活性预测
        if predicted_activity > 0:
            mic_estimate = np.exp(predicted_activity)
            summary_parts.append(f"预测MIC值: {mic_estimate:.2f} μg/mL")
        else:
            summary_parts.append(f"预测活性评分: {predicted_activity:.3f}")
        
        # 机制预测
        if predicted_mechanisms:
            main_mechanism = predicted_mechanisms[0]
            mechanism_name = main_mechanism['mechanism']
            probability = main_mechanism['probability']
            
            mechanism_names_cn = {
                'cell_wall_synthesis': '细胞壁合成抑制',
                'protein_synthesis': '蛋白质合成抑制',
                'dna_replication': 'DNA复制抑制',
                'cell_membrane': '细胞膜破坏',
                'metabolic_pathway': '代谢途径抑制'
            }
            
            cn_name = mechanism_names_cn.get(mechanism_name, mechanism_name)
            summary_parts.append(f"主要机制: {cn_name} (置信度: {probability:.2f})")
        
        # 药效团信息
        if 'identified_pharmacophores' in pharmacophore_analysis:
            pharmacophores = pharmacophore_analysis['identified_pharmacophores']
            if pharmacophores:
                summary_parts.append(f"识别的药效团: {', '.join(pharmacophores)}")
        
        # 置信度
        confidence_level = "高" if confidence_score > 0.8 else "中" if confidence_score > 0.6 else "低"
        summary_parts.append(f"预测置信度: {confidence_level} ({confidence_score:.2f})")
        
        return "; ".join(summary_parts)


class AttentionAnalyzer:
    """
    注意力权重分析器
    
    分析模型的注意力权重，识别重要的分子特征。
    """
    
    def __init__(self):
        """初始化注意力分析器"""
        logger.info("初始化注意力分析器")
    
    def analyze_attention(self, attention_weights: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析注意力权重
        
        Args:
            attention_weights: 注意力权重字典
            
        Returns:
            注意力分析结果
        """
        analysis = {
            'attention_concentration': 0.0,
            'dominant_features': [],
            'attention_distribution': {},
            'biological_relevance': 0.0
        }
        
        try:
            # 分析注意力集中度
            concentration_scores = []
            
            for component_name, weights in attention_weights.items():
                if torch.is_tensor(weights):
                    # 计算注意力集中度（基于熵）
                    weights_flat = weights.flatten()
                    weights_norm = torch.softmax(weights_flat, dim=0)
                    
                    # 计算熵
                    entropy = -(weights_norm * torch.log(weights_norm + 1e-8)).sum()
                    max_entropy = np.log(len(weights_norm))
                    concentration = 1.0 - float(entropy / max_entropy)
                    
                    concentration_scores.append(concentration)
                    analysis['attention_distribution'][component_name] = {
                        'concentration': concentration,
                        'max_weight': float(weights_flat.max()),
                        'mean_weight': float(weights_flat.mean()),
                        'std_weight': float(weights_flat.std())
                    }
            
            # 整体注意力集中度
            if concentration_scores:
                analysis['attention_concentration'] = np.mean(concentration_scores)
            
            # 识别主导特征
            analysis['dominant_features'] = self._identify_dominant_features(attention_weights)
            
            # 评估生物学相关性
            analysis['biological_relevance'] = self._assess_biological_relevance(attention_weights)
            
        except Exception as e:
            logger.warning(f"注意力分析失败: {e}")
            analysis['error'] = str(e)
        
        return analysis
    
    def _identify_dominant_features(self, attention_weights: Dict[str, Any]) -> List[str]:
        """识别主导特征"""
        dominant_features = []
        
        for component_name, weights in attention_weights.items():
            if torch.is_tensor(weights):
                weights_flat = weights.flatten()
                
                # 找到权重最高的特征
                if len(weights_flat) > 0:
                    max_idx = torch.argmax(weights_flat)
                    max_weight = float(weights_flat[max_idx])
                    
                    if max_weight > 0.1:  # 阈值
                        dominant_features.append(f"{component_name}_{max_idx}")
        
        return dominant_features
    
    def _assess_biological_relevance(self, attention_weights: Dict[str, Any]) -> float:
        """评估注意力权重的生物学相关性"""
        relevance_score = 0.0
        
        # 检查是否关注了已知的重要特征
        important_components = ['pharmacophore', 'mechanism', 'graph_cross']
        
        for component_name in attention_weights.keys():
            for important in important_components:
                if important in component_name.lower():
                    relevance_score += 0.3
                    break
        
        return min(relevance_score, 1.0)


class MechanismExplainer:
    """
    抗菌机制解释器
    
    解释预测的抗菌机制及其生物学基础。
    """
    
    def __init__(self):
        """初始化机制解释器"""
        logger.info("初始化机制解释器")
    
    def explain_mechanism(
        self, 
        mechanism: str, 
        confidence: float,
        molecular_features: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        解释抗菌机制
        
        Args:
            mechanism: 机制名称
            confidence: 置信度
            molecular_features: 分子特征
            
        Returns:
            机制解释结果
        """
        from .mechanisms import get_mechanism_by_name
        
        mechanism_info = get_mechanism_by_name(mechanism)
        if mechanism_info is None:
            return {'error': f'未知机制: {mechanism}'}
        
        explanation = {
            'mechanism_name': mechanism_info.name,
            'description': mechanism_info.description,
            'targets': mechanism_info.targets,
            'examples': mechanism_info.examples,
            'confidence': confidence,
            'supporting_evidence': [],
            'potential_challenges': []
        }
        
        # 分析分子特征与机制的一致性
        if molecular_features:
            consistency = self._analyze_mechanism_consistency(
                mechanism_info, molecular_features
            )
            explanation.update(consistency)
        
        # 添加耐药性信息
        explanation['resistance_mechanisms'] = mechanism_info.resistance_mechanisms
        explanation['gram_specificity'] = mechanism_info.gram_specificity
        
        return explanation
    
    def _analyze_mechanism_consistency(
        self, 
        mechanism_info, 
        molecular_features: Dict[str, Any]
    ) -> Dict[str, Any]:
        """分析分子特征与机制的一致性"""
        consistency = {
            'supporting_evidence': [],
            'potential_challenges': [],
            'consistency_score': 0.0
        }
        
        # 基于分子量分析
        mw = molecular_features.get('molecular_weight', 300)
        
        if mechanism_info.mechanism_type.value == 'cell_wall_synthesis':
            if 150 <= mw <= 600:
                consistency['supporting_evidence'].append('分子量适合细胞壁靶点')
                consistency['consistency_score'] += 0.2
            else:
                consistency['potential_challenges'].append('分子量可能不适合细胞壁靶点')
        
        elif mechanism_info.mechanism_type.value == 'protein_synthesis':
            if 200 <= mw <= 800:
                consistency['supporting_evidence'].append('分子量适合核糖体靶点')
                consistency['consistency_score'] += 0.2
        
        # 基于LogP分析
        logp = molecular_features.get('logp', 2.0)
        
        if mechanism_info.mechanism_type.value == 'cell_membrane':
            if 1.0 <= logp <= 4.0:
                consistency['supporting_evidence'].append('脂溶性适合膜靶点')
                consistency['consistency_score'] += 0.3
            else:
                consistency['potential_challenges'].append('脂溶性可能不适合膜靶点')
        
        # 基于氢键特征
        hbd = molecular_features.get('hbd', 2)
        hba = molecular_features.get('hba', 4)
        
        if mechanism_info.mechanism_type.value in ['dna_replication', 'metabolic_pathway']:
            if hbd >= 1 and hba >= 2:
                consistency['supporting_evidence'].append('氢键特征支持酶结合')
                consistency['consistency_score'] += 0.2
        
        return consistency
