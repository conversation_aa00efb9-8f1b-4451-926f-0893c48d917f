# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-29
# 描述: 神农框架注意力权重解释器

"""
神农框架注意力权重解释器

将神经网络的注意力权重转化为化学家能理解的结构重要性信息。
"""

import logging
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from rdkit import Chem
from rdkit.Chem import rdMolDescriptors, Fragments

logger = logging.getLogger(__name__)


class AttentionInterpreter:
    """
    注意力权重解释器
    
    分析神农框架的多层注意力权重，识别对抗菌活性预测重要的分子区域。
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化注意力解释器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        
        # 注意力权重阈值
        self.attention_threshold = self.config.get('attention_threshold', 0.1)
        
        # 重要区域识别参数
        self.min_region_size = self.config.get('min_region_size', 3)
        self.max_regions = self.config.get('max_regions', 5)
        
        logger.info("注意力权重解释器初始化完成")
    
    def interpret_attention(
        self,
        smiles: str,
        attention_weights: Dict[str, np.ndarray],
        predicted_mechanism: str
    ) -> Dict[str, Any]:
        """
        解释注意力权重的化学意义
        
        Args:
            smiles: 分子SMILES
            attention_weights: 各层注意力权重
            predicted_mechanism: 预测的抗菌机制
            
        Returns:
            注意力权重的化学解释
        """
        mol = Chem.MolFromSmiles(smiles)
        if mol is None:
            return {'error': '无效的SMILES结构'}
        
        analysis = {
            'attention_summary': self._summarize_attention_weights(attention_weights),
            'key_regions': self._identify_key_regions(mol, attention_weights),
            'mechanism_relevance': self._analyze_mechanism_relevance(
                mol, attention_weights, predicted_mechanism
            ),
            'attention_consistency': self._calculate_attention_consistency(attention_weights),
            'functional_groups': self._analyze_functional_group_attention(mol, attention_weights)
        }
        
        return analysis
    
    def _summarize_attention_weights(self, attention_weights: Dict[str, np.ndarray]) -> Dict[str, Any]:
        """汇总注意力权重统计信息"""
        summary = {}
        
        for layer_name, weights in attention_weights.items():
            if weights is not None and len(weights) > 0:
                summary[layer_name] = {
                    'mean': float(np.mean(weights)),
                    'std': float(np.std(weights)),
                    'max': float(np.max(weights)),
                    'min': float(np.min(weights)),
                    'entropy': self._calculate_entropy(weights),
                    'sparsity': self._calculate_sparsity(weights)
                }
        
        return summary
    
    def _identify_key_regions(
        self,
        mol: Chem.Mol,
        attention_weights: Dict[str, np.ndarray]
    ) -> List[Dict[str, Any]]:
        """识别高注意力权重的分子区域"""
        
        # 合并所有注意力权重
        combined_weights = self._combine_attention_weights(attention_weights)
        
        if combined_weights is None or len(combined_weights) == 0:
            return []
        
        # 识别高权重原子
        high_attention_atoms = np.where(combined_weights > self.attention_threshold)[0]
        
        if len(high_attention_atoms) == 0:
            return []
        
        # 按权重排序
        sorted_indices = high_attention_atoms[np.argsort(combined_weights[high_attention_atoms])[::-1]]
        
        key_regions = []
        
        for i, atom_idx in enumerate(sorted_indices[:self.max_regions]):
            if atom_idx >= mol.GetNumAtoms():
                continue
                
            atom = mol.GetAtomWithIdx(int(atom_idx))
            
            # 获取原子环境
            neighbors = [n.GetIdx() for n in atom.GetNeighbors()]
            
            # 分析原子类型和环境
            region_info = {
                'atom_index': int(atom_idx),
                'attention_weight': float(combined_weights[atom_idx]),
                'atom_symbol': atom.GetSymbol(),
                'atom_type': self._classify_atom_type(atom),
                'neighbors': neighbors,
                'in_ring': atom.IsInRing(),
                'aromatic': atom.GetIsAromatic(),
                'description': self._describe_atom_region(atom, mol)
            }
            
            key_regions.append(region_info)
        
        return key_regions
    
    def _analyze_mechanism_relevance(
        self,
        mol: Chem.Mol,
        attention_weights: Dict[str, np.ndarray],
        mechanism: str
    ) -> Dict[str, Any]:
        """分析注意力权重与抗菌机制的相关性"""
        
        # 定义不同机制的关键结构特征
        mechanism_features = {
            'cell_wall_synthesis': {
                'patterns': ['[C;R]1[C;R][N;R][C;R](=O)[C;R]1', '[C;R]1[C;R][C;R][N;R][C;R]1'],  # β-内酰胺环等
                'atoms': ['N', 'O', 'S'],
                'description': '细胞壁合成抑制剂通常含有β-内酰胺环或糖肽结构'
            },
            'protein_synthesis': {
                'patterns': ['[N+]', '[NH3+]', 'c1ccccc1'],  # 氨基糖苷类、氯霉素类
                'atoms': ['N', 'O'],
                'description': '蛋白质合成抑制剂常含有氨基或羟基'
            },
            'dna_replication': {
                'patterns': ['c1cc2c(cc1)c(=O)c(cn2)', '[F]'],  # 喹诺酮类
                'atoms': ['F', 'N', 'O'],
                'description': 'DNA复制抑制剂通常为喹诺酮类化合物'
            },
            'cell_membrane': {
                'patterns': ['[CH2][CH2][CH2]', '[NH3+]'],  # 多粘菌素类
                'atoms': ['N', 'O'],
                'description': '细胞膜破坏剂常为阳离子肽类化合物'
            }
        }
        
        if mechanism not in mechanism_features:
            return {'relevance_score': 0.0, 'explanation': '未知机制'}
        
        features = mechanism_features[mechanism]
        combined_weights = self._combine_attention_weights(attention_weights)
        
        if combined_weights is None:
            return {'relevance_score': 0.0, 'explanation': '无注意力权重数据'}
        
        relevance_score = 0.0
        matched_features = []
        
        # 检查SMARTS模式匹配
        for pattern in features['patterns']:
            try:
                matches = mol.GetSubstructMatches(Chem.MolFromSmarts(pattern))
                if matches:
                    # 计算匹配原子的平均注意力权重
                    match_weights = []
                    for match in matches:
                        for atom_idx in match:
                            if atom_idx < len(combined_weights):
                                match_weights.append(combined_weights[atom_idx])
                    
                    if match_weights:
                        avg_weight = np.mean(match_weights)
                        relevance_score += avg_weight
                        matched_features.append({
                            'pattern': pattern,
                            'matches': len(matches),
                            'avg_attention': avg_weight
                        })
            except:
                continue
        
        # 检查关键原子类型
        for atom_symbol in features['atoms']:
            atom_indices = [atom.GetIdx() for atom in mol.GetAtoms() 
                          if atom.GetSymbol() == atom_symbol]
            if atom_indices:
                atom_weights = [combined_weights[i] for i in atom_indices 
                              if i < len(combined_weights)]
                if atom_weights:
                    avg_weight = np.mean(atom_weights)
                    relevance_score += avg_weight * 0.5  # 权重较低
        
        return {
            'relevance_score': float(relevance_score),
            'matched_features': matched_features,
            'explanation': features['description'],
            'mechanism_support': relevance_score > 0.3
        }
    
    def _analyze_functional_group_attention(
        self,
        mol: Chem.Mol,
        attention_weights: Dict[str, np.ndarray]
    ) -> Dict[str, Any]:
        """分析官能团的注意力权重分布"""
        
        combined_weights = self._combine_attention_weights(attention_weights)
        if combined_weights is None:
            return {}
        
        # 定义重要的官能团
        functional_groups = {
            'aromatic_rings': self._get_aromatic_atoms(mol),
            'hydroxyl_groups': self._get_hydroxyl_atoms(mol),
            'amino_groups': self._get_amino_atoms(mol),
            'carbonyl_groups': self._get_carbonyl_atoms(mol),
            'halogen_atoms': self._get_halogen_atoms(mol),
            'sulfur_atoms': self._get_sulfur_atoms(mol)
        }
        
        group_analysis = {}
        
        for group_name, atom_indices in functional_groups.items():
            if atom_indices:
                group_weights = [combined_weights[i] for i in atom_indices 
                               if i < len(combined_weights)]
                if group_weights:
                    group_analysis[group_name] = {
                        'atom_count': len(atom_indices),
                        'avg_attention': float(np.mean(group_weights)),
                        'max_attention': float(np.max(group_weights)),
                        'total_attention': float(np.sum(group_weights)),
                        'importance_rank': 0  # 将在后面计算
                    }
        
        # 计算重要性排名
        sorted_groups = sorted(group_analysis.items(), 
                             key=lambda x: x[1]['avg_attention'], reverse=True)
        
        for rank, (group_name, _) in enumerate(sorted_groups):
            group_analysis[group_name]['importance_rank'] = rank + 1
        
        return group_analysis
    
    def _combine_attention_weights(self, attention_weights: Dict[str, np.ndarray]) -> Optional[np.ndarray]:
        """合并多层注意力权重"""
        valid_weights = []
        
        for layer_name, weights in attention_weights.items():
            if weights is not None and len(weights) > 0:
                # 确保权重是一维数组
                if weights.ndim > 1:
                    weights = weights.mean(axis=tuple(range(weights.ndim - 1)))
                valid_weights.append(weights)
        
        if not valid_weights:
            return None
        
        # 找到最小长度
        min_length = min(len(w) for w in valid_weights)
        
        # 截断到相同长度并平均
        truncated_weights = [w[:min_length] for w in valid_weights]
        combined = np.mean(truncated_weights, axis=0)
        
        return combined
    
    def _calculate_entropy(self, weights: np.ndarray) -> float:
        """计算注意力权重的熵"""
        # 归一化权重
        weights = weights / (np.sum(weights) + 1e-8)
        # 计算熵
        entropy = -np.sum(weights * np.log(weights + 1e-8))
        return float(entropy)
    
    def _calculate_sparsity(self, weights: np.ndarray) -> float:
        """计算注意力权重的稀疏性"""
        threshold = np.mean(weights)
        sparse_count = np.sum(weights < threshold)
        sparsity = sparse_count / len(weights)
        return float(sparsity)
    
    def _calculate_attention_consistency(self, attention_weights: Dict[str, np.ndarray]) -> float:
        """计算不同层注意力权重的一致性"""
        valid_weights = []
        
        for weights in attention_weights.values():
            if weights is not None and len(weights) > 0:
                if weights.ndim > 1:
                    weights = weights.mean(axis=tuple(range(weights.ndim - 1)))
                valid_weights.append(weights)
        
        if len(valid_weights) < 2:
            return 1.0
        
        # 计算权重之间的相关性
        min_length = min(len(w) for w in valid_weights)
        truncated_weights = [w[:min_length] for w in valid_weights]
        
        correlations = []
        for i in range(len(truncated_weights)):
            for j in range(i + 1, len(truncated_weights)):
                corr = np.corrcoef(truncated_weights[i], truncated_weights[j])[0, 1]
                if not np.isnan(corr):
                    correlations.append(corr)
        
        if correlations:
            return float(np.mean(correlations))
        else:
            return 0.0
    
    def _classify_atom_type(self, atom: Chem.Atom) -> str:
        """分类原子类型"""
        symbol = atom.GetSymbol()
        
        if symbol == 'C':
            if atom.GetIsAromatic():
                return '芳香碳'
            elif atom.GetHybridization() == Chem.HybridizationType.SP3:
                return '饱和碳'
            elif atom.GetHybridization() == Chem.HybridizationType.SP2:
                return '不饱和碳'
            else:
                return '碳原子'
        elif symbol == 'N':
            if atom.GetIsAromatic():
                return '芳香氮'
            elif atom.GetTotalNumHs() > 0:
                return '氨基氮'
            else:
                return '氮原子'
        elif symbol == 'O':
            if len(atom.GetNeighbors()) == 1:
                return '羰基氧'
            else:
                return '醚氧/羟基氧'
        elif symbol in ['F', 'Cl', 'Br', 'I']:
            return '卤素原子'
        elif symbol == 'S':
            return '硫原子'
        else:
            return f'{symbol}原子'
    
    def _describe_atom_region(self, atom: Chem.Atom, mol: Chem.Mol) -> str:
        """描述原子所在的分子区域"""
        descriptions = []
        
        # 原子基本信息
        atom_type = self._classify_atom_type(atom)
        descriptions.append(atom_type)
        
        # 环信息
        if atom.IsInRing():
            ring_info = mol.GetRingInfo()
            atom_rings = ring_info.AtomRings()
            for ring in atom_rings:
                if atom.GetIdx() in ring:
                    ring_size = len(ring)
                    if ring_size == 6 and atom.GetIsAromatic():
                        descriptions.append('苯环')
                    elif ring_size == 5 and atom.GetIsAromatic():
                        descriptions.append('五元芳环')
                    else:
                        descriptions.append(f'{ring_size}元环')
                    break
        
        # 邻近基团
        neighbors = atom.GetNeighbors()
        neighbor_symbols = [n.GetSymbol() for n in neighbors]
        
        if 'O' in neighbor_symbols and atom.GetSymbol() == 'C':
            descriptions.append('含氧基团')
        if 'N' in neighbor_symbols:
            descriptions.append('含氮基团')
        
        return '、'.join(descriptions) if descriptions else '未知区域'
    
    # 官能团识别辅助方法
    def _get_aromatic_atoms(self, mol: Chem.Mol) -> List[int]:
        return [atom.GetIdx() for atom in mol.GetAtoms() if atom.GetIsAromatic()]
    
    def _get_hydroxyl_atoms(self, mol: Chem.Mol) -> List[int]:
        pattern = Chem.MolFromSmarts('[OH]')
        matches = mol.GetSubstructMatches(pattern)
        return [match[0] for match in matches]
    
    def _get_amino_atoms(self, mol: Chem.Mol) -> List[int]:
        pattern = Chem.MolFromSmarts('[NH2,NH1,NH0]')
        matches = mol.GetSubstructMatches(pattern)
        return [match[0] for match in matches]
    
    def _get_carbonyl_atoms(self, mol: Chem.Mol) -> List[int]:
        pattern = Chem.MolFromSmarts('[C]=[O]')
        matches = mol.GetSubstructMatches(pattern)
        return [match[0] for match in matches]
    
    def _get_halogen_atoms(self, mol: Chem.Mol) -> List[int]:
        return [atom.GetIdx() for atom in mol.GetAtoms() 
                if atom.GetSymbol() in ['F', 'Cl', 'Br', 'I']]
    
    def _get_sulfur_atoms(self, mol: Chem.Mol) -> List[int]:
        return [atom.GetIdx() for atom in mol.GetAtoms() if atom.GetSymbol() == 'S']
