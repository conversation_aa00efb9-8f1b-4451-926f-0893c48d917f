#!/usr/bin/env python3
# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-29
# 描述: 神农框架骨架拆分+超参数优化演示

"""
神农框架骨架拆分与超参数优化演示

展示如何使用神农框架进行：
1. 基于Murcko骨架的数据拆分
2. 使用Optuna进行超参数优化
3. 与ChemProp兼容的工作流程
"""

import sys
import os
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, Any, Tuple
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_dependencies():
    """检查必要的依赖"""
    required_packages = {
        'optuna': 'pip install optuna',
        'rdkit': 'conda install -c conda-forge rdkit',
        'sklearn': 'pip install scikit-learn'
    }
    
    missing_packages = []
    for package, install_cmd in required_packages.items():
        try:
            __import__(package)
        except ImportError:
            missing_packages.append((package, install_cmd))
    
    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for package, cmd in missing_packages:
            print(f"   {package}: {cmd}")
        return False
    
    print("✅ 所有依赖包已安装")
    return True

def demonstrate_scaffold_splitting():
    """演示骨架拆分功能"""
    print("\n🧬 骨架拆分演示")
    print("=" * 50)
    
    # 创建示例数据
    sample_data = {
        'smiles': [
            'CCN(CC)CCCC(C)NC1=C2C=CC(=CC2=NC=C1)CF3',  # 氯喹类似物
            'O=C(O)C1=CN(C2CC2)C3=C(C=C(F)C(N4CCNCC4)=C3F)C1=O',  # 环丙沙星
            'CC1([C@@H](N2[C@H](S1)[C@@H](C2=O)NC(=O)CC3=CC=CC=C3)C(=O)O)C',  # 青霉素G
            'O=C(NC(C(O)C1=CC=C([N+]([O-])=O)C=C1)CO)C(Cl)Cl',  # 氯霉素
            'NC1=CC=C(C=C1)S(=O)(=O)NC2=NC=CC=N2',  # 磺胺嘧啶
            'CCN(CC)CCCC(C)NC1=C2C=CC(=CC2=NC=C1)Cl',  # 氯喹
            'O=C(O)C1=CN(C2CC2)C3=C(C=C(F)C(N4CCNCC4)=C3)C1=O',  # 诺氟沙星
            'CC1([C@@H](N2[C@H](S1)[C@@H](C2=O)NC(=O)C(C3=CC=CC=C3)N)C(=O)O)C',  # 氨苄西林
        ],
        'activity': [0.5, 0.25, 0.1, 2.0, 4.0, 0.8, 0.3, 0.15],
        'compound_name': [
            '氯喹类似物', '环丙沙星', '青霉素G', '氯霉素', 
            '磺胺嘧啶', '氯喹', '诺氟沙星', '氨苄西林'
        ]
    }
    
    df = pd.DataFrame(sample_data)
    print(f"📊 示例数据集: {len(df)} 个化合物")
    
    # 分析骨架
    try:
        from rdkit import Chem
        from rdkit.Chem.Scaffolds import MurckoScaffold
        
        scaffolds = {}
        for i, smiles in enumerate(df['smiles']):
            mol = Chem.MolFromSmiles(smiles)
            if mol:
                scaffold = MurckoScaffold.MurckoScaffoldSmiles(mol=mol, includeChirality=False)
                if scaffold not in scaffolds:
                    scaffolds[scaffold] = []
                scaffolds[scaffold].append((i, df.iloc[i]['compound_name']))
        
        print(f"\n🔍 识别出 {len(scaffolds)} 个不同的骨架:")
        for i, (scaffold, compounds) in enumerate(scaffolds.items()):
            print(f"   骨架 {i+1}: {scaffold}")
            for idx, name in compounds:
                print(f"     - {name}")
        
        # 模拟骨架拆分
        print(f"\n📋 骨架拆分结果:")
        scaffold_sizes = [(scaffold, len(compounds)) for scaffold, compounds in scaffolds.items()]
        scaffold_sizes.sort(key=lambda x: x[1], reverse=True)
        
        train_compounds, val_compounds, test_compounds = [], [], []
        train_target, val_target, test_target = len(df) * 0.8, len(df) * 0.1, len(df) * 0.1
        
        for scaffold, size in scaffold_sizes:
            compounds = scaffolds[scaffold]
            if len(train_compounds) < train_target:
                train_compounds.extend(compounds)
                print(f"   训练集: 骨架 {scaffold[:20]}... ({size} 个化合物)")
            elif len(val_compounds) < val_target:
                val_compounds.extend(compounds)
                print(f"   验证集: 骨架 {scaffold[:20]}... ({size} 个化合物)")
            else:
                test_compounds.extend(compounds)
                print(f"   测试集: 骨架 {scaffold[:20]}... ({size} 个化合物)")
        
        print(f"\n✅ 拆分完成:")
        print(f"   训练集: {len(train_compounds)} 个化合物")
        print(f"   验证集: {len(val_compounds)} 个化合物") 
        print(f"   测试集: {len(test_compounds)} 个化合物")
        
        return True
        
    except ImportError:
        print("❌ 需要安装RDKit: conda install -c conda-forge rdkit")
        return False

def demonstrate_hyperparameter_optimization():
    """演示超参数优化功能"""
    print("\n🎯 超参数优化演示")
    print("=" * 50)
    
    try:
        import optuna
        
        # 定义搜索空间
        def objective(trial):
            """Optuna优化目标函数"""
            # 神农框架专门的超参数搜索空间
            config = {
                # 图神经网络分支
                'graph_hidden_size': trial.suggest_categorical('graph_hidden_size', [200, 300, 400]),
                'graph_depth': trial.suggest_int('graph_depth', 2, 5),
                'graph_dropout': trial.suggest_float('graph_dropout', 0.0, 0.3),
                
                # 专家特征分支
                'expert_hidden_dims': trial.suggest_categorical(
                    'expert_hidden_dims', 
                    ['[512,256,128]', '[1024,512,256]', '[256,128,64]']
                ),
                'expert_dropout': trial.suggest_float('expert_dropout', 0.1, 0.5),
                
                # 化学导向注意力
                'attention_heads': trial.suggest_categorical('attention_heads', [2, 4, 6, 8]),
                'attention_dropout': trial.suggest_float('attention_dropout', 0.1, 0.3),
                
                # 训练参数
                'learning_rate': trial.suggest_float('learning_rate', 1e-5, 1e-2, log=True),
                'batch_size': trial.suggest_categorical('batch_size', [32, 64, 128]),
                'weight_decay': trial.suggest_float('weight_decay', 1e-6, 1e-3, log=True),
                
                # 损失权重
                'activity_weight': trial.suggest_float('activity_weight', 0.8, 1.2),
                'uncertainty_weight': trial.suggest_float('uncertainty_weight', 0.01, 0.1),
                'attention_weight': trial.suggest_float('attention_weight', 0.001, 0.01),
            }
            
            # 模拟训练和评估
            # 在实际使用中，这里会调用神农框架的训练函数
            simulated_performance = simulate_training(config)
            
            return simulated_performance['val_loss']
        
        def simulate_training(config: Dict[str, Any]) -> Dict[str, float]:
            """模拟训练过程"""
            # 基于配置计算模拟性能
            base_loss = 1.0
            
            # 图神经网络影响
            if config['graph_hidden_size'] == 300:
                base_loss *= 0.9
            elif config['graph_hidden_size'] == 400:
                base_loss *= 0.85
            
            # 注意力头数影响
            if config['attention_heads'] in [4, 6]:
                base_loss *= 0.92
            
            # 学习率影响
            if 1e-4 <= config['learning_rate'] <= 1e-3:
                base_loss *= 0.88
            
            # 添加随机噪声
            noise = np.random.normal(0, 0.1)
            final_loss = max(0.1, base_loss + noise)
            
            return {
                'val_loss': final_loss,
                'val_r2': 1 - final_loss,
                'val_rmse': final_loss * 2
            }
        
        # 创建Optuna研究
        print("🔍 创建Optuna优化研究...")
        study = optuna.create_study(
            direction='minimize',
            study_name='shennong_scaffold_hyperopt',
            sampler=optuna.samplers.TPESampler(seed=42)
        )
        
        print("🚀 开始超参数优化 (演示模式: 10次试验)...")
        study.optimize(objective, n_trials=10, show_progress_bar=True)
        
        # 显示结果
        print(f"\n✅ 优化完成!")
        print(f"   最佳验证损失: {study.best_value:.4f}")
        print(f"   最佳超参数:")
        
        for key, value in study.best_params.items():
            print(f"     {key}: {value}")
        
        # 显示优化历史
        print(f"\n📈 优化历史:")
        trials = study.trials
        for i, trial in enumerate(trials[-5:]):  # 显示最后5次试验
            print(f"   试验 {len(trials)-4+i}: 损失 = {trial.value:.4f}")
        
        return study.best_params
        
    except ImportError:
        print("❌ 需要安装Optuna: pip install optuna")
        return None

def create_hyperopt_config(best_params: Dict[str, Any]) -> str:
    """创建优化后的配置文件"""
    config_content = f"""# 神农框架超参数优化结果配置
# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-29

# 模型配置 (优化后)
model:
  graph_config:
    hidden_size: {best_params.get('graph_hidden_size', 300)}
    depth: {best_params.get('graph_depth', 3)}
    dropout: {best_params.get('graph_dropout', 0.1)}
    
  expert_config:
    hidden_dims: {best_params.get('expert_hidden_dims', '[512,256,128]')}
    dropout: {best_params.get('expert_dropout', 0.3)}
    
  attention_config:
    num_heads: {best_params.get('attention_heads', 4)}
    dropout: {best_params.get('attention_dropout', 0.2)}

# 训练配置 (优化后)
training:
  learning_rate: {best_params.get('learning_rate', 1e-3)}
  batch_size: {best_params.get('batch_size', 64)}
  weight_decay: {best_params.get('weight_decay', 1e-4)}
  
  loss_weights:
    activity: {best_params.get('activity_weight', 1.0)}
    uncertainty: {best_params.get('uncertainty_weight', 0.05)}
    attention: {best_params.get('attention_weight', 0.01)}

# 数据配置 (骨架拆分)
data:
  splitting:
    method: "scaffold"
    train_ratio: 0.8
    val_ratio: 0.1
    test_ratio: 0.1
    include_chirality: false
"""
    
    config_path = "configs/optimized_config.yaml"
    Path(config_path).parent.mkdir(exist_ok=True)
    
    with open(config_path, 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    return config_path

def show_usage_examples():
    """显示使用示例"""
    print("\n📚 神农框架骨架拆分+超参数优化使用示例")
    print("=" * 60)
    
    examples = [
        ("基础骨架拆分训练", """
shennong train \\
    --data-path data/antibacterial_train.csv \\
    --split-method scaffold \\
    --train-ratio 0.8 \\
    --val-ratio 0.1 \\
    --test-ratio 0.1 \\
    --save-dir models/scaffold_split
        """),
        
        ("超参数优化", """
shennong hyperopt \\
    --data-path data/antibacterial_train.csv \\
    --split-method scaffold \\
    --method optuna \\
    --n-trials 100 \\
    --objective val_loss \\
    --save-dir hyperopt_results/
        """),
        
        ("使用优化后的配置训练", """
shennong train \\
    --data-path data/antibacterial_train.csv \\
    --config configs/optimized_config.yaml \\
    --save-dir models/optimized
        """),
        
        ("ChemProp兼容模式", """
# 使用ChemProp格式数据进行骨架拆分
shennong train \\
    --data-path chemprop_data.csv \\
    --smiles-column smiles \\
    --target-columns activity \\
    --split-method scaffold \\
    --chemprop-compatible \\
    --save-dir models/chemprop_compatible
        """)
    ]
    
    for title, command in examples:
        print(f"\n🔹 {title}:")
        print(command)

def main():
    """主函数"""
    print("🧬 神农框架骨架拆分与超参数优化演示")
    print("=" * 60)
    print("展示神农框架在分子骨架拆分和超参数优化方面的强大功能")
    print("与ChemProp完全兼容，提供更丰富的功能")
    print()
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 演示骨架拆分
    scaffold_success = demonstrate_scaffold_splitting()
    
    # 演示超参数优化
    if scaffold_success:
        best_params = demonstrate_hyperparameter_optimization()
        
        if best_params:
            # 创建优化配置
            config_path = create_hyperopt_config(best_params)
            print(f"\n💾 优化配置已保存到: {config_path}")
    
    # 显示使用示例
    show_usage_examples()
    
    print(f"\n🎉 演示完成!")
    print(f"神农框架提供了:")
    print(f"  ✅ 完整的骨架拆分功能 (基于Murcko骨架)")
    print(f"  ✅ 多种超参数优化引擎 (Optuna, Ray Tune)")
    print(f"  ✅ 化学导向的搜索空间设计")
    print(f"  ✅ 与ChemProp完全兼容的工作流程")
    print(f"  ✅ 自动配置生成和管理")
    
    print(f"\n🧬 神农尝百草，AI识良药 - 骨架拆分让模型更可靠！")

if __name__ == "__main__":
    main()
