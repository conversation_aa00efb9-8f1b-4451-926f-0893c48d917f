#!/usr/bin/env python3
# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-30
# 描述: 神农框架独立特征生成器 - 职责分离架构的核心组件

"""
神农框架特征生成器 v2.0

实现特征工程与模型训练的完全解耦，支持多种特征类型的并行计算和统一存储。
遵循软件工程中的职责分离原则，确保科研实验的严谨性和可复现性。

支持的特征类型:
- morgan: Morgan分子指纹
- rdkit: RDKit描述符
- mordred: Mordred全描述符集
- combined: 组合多种特征类型

使用示例:
    python generate_features.py --data_path data.csv --save_path features.npz --feature_generator morgan,mordred
"""

import argparse
import pandas as pd
import numpy as np
import logging
import sys
import time
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from concurrent.futures import ProcessPoolExecutor, as_completed
from functools import partial
import warnings

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 抑制警告
warnings.filterwarnings('ignore')

def check_dependencies() -> bool:
    """检查必要的依赖包"""
    required_packages = {
        'rdkit': 'conda install -c conda-forge rdkit',
        'mordred': 'pip install mordred',
        'numpy': 'pip install numpy',
        'pandas': 'pip install pandas'
    }

    missing_packages = []
    for package, install_cmd in required_packages.items():
        try:
            if package == 'rdkit':
                from rdkit import Chem
            elif package == 'mordred':
                from mordred import Calculator, descriptors
            else:
                __import__(package)
        except ImportError:
            missing_packages.append((package, install_cmd))

    if missing_packages:
        logger.error("缺少以下依赖包:")
        for package, cmd in missing_packages:
            logger.error(f"  {package}: {cmd}")
        return False

    logger.info("✅ 所有依赖包检查通过")
    return True

def compute_morgan_features(smiles: str, radius: int = 2, n_bits: int = 2048) -> Optional[np.ndarray]:
    """计算Morgan分子指纹"""
    try:
        from rdkit import Chem
        from rdkit.Chem import rdMolDescriptors

        mol = Chem.MolFromSmiles(smiles)
        if mol is None:
            return None

        # 计算Morgan指纹
        fp = rdMolDescriptors.GetMorganFingerprintAsBitVect(mol, radius, nBits=n_bits)
        return np.array(fp)

    except Exception as e:
        logger.warning(f"Morgan特征计算失败 {smiles}: {e}")
        return None

def compute_rdkit_features(smiles: str) -> Optional[np.ndarray]:
    """计算RDKit描述符"""
    try:
        from rdkit import Chem
        from rdkit.Chem import Descriptors, rdMolDescriptors

        mol = Chem.MolFromSmiles(smiles)
        if mol is None:
            return None

        # 计算常用的RDKit描述符
        descriptors_list = [
            Descriptors.MolWt(mol),
            Descriptors.MolLogP(mol),
            Descriptors.NumHDonors(mol),
            Descriptors.NumHAcceptors(mol),
            Descriptors.TPSA(mol),
            Descriptors.NumRotatableBonds(mol),
            Descriptors.NumAromaticRings(mol),
            Descriptors.NumSaturatedRings(mol),
            Descriptors.NumAliphaticRings(mol),
            Descriptors.RingCount(mol),
            Descriptors.FractionCsp3(mol),
            Descriptors.NumHeteroatoms(mol),
            Descriptors.BertzCT(mol),
            rdMolDescriptors.BalabanJ(mol),
            rdMolDescriptors.CalcNumBridgeheadAtoms(mol),
            rdMolDescriptors.CalcNumSpiroAtoms(mol)
        ]

        return np.array(descriptors_list, dtype=np.float32)

    except Exception as e:
        logger.warning(f"RDKit特征计算失败 {smiles}: {e}")
        return None

def compute_mordred_features(smiles: str, calculator=None) -> Optional[np.ndarray]:
    """计算Mordred描述符"""
    try:
        from rdkit import Chem
        from mordred import Calculator, descriptors

        if calculator is None:
            calculator = Calculator(descriptors, ignore_3D=True)

        mol = Chem.MolFromSmiles(smiles)
        if mol is None:
            return None

        # 计算Mordred描述符
        result = calculator(mol)

        # 转换为数值数组，处理缺失值
        features = []
        for value in result:
            if isinstance(value, (int, float)) and not np.isnan(float(value)) and not np.isinf(float(value)):
                features.append(float(value))
            else:
                features.append(0.0)  # 用0填充缺失值

        return np.array(features, dtype=np.float32)

    except Exception as e:
        logger.warning(f"Mordred特征计算失败 {smiles}: {e}")
        return None

def process_single_molecule(args: Tuple[str, str, Dict[str, Any]]) -> Tuple[str, Dict[str, np.ndarray]]:
    """处理单个分子的特征计算"""
    smiles, mol_id, config = args
    features = {}

    # 根据配置计算不同类型的特征
    if 'morgan' in config['feature_types']:
        morgan_feat = compute_morgan_features(
            smiles,
            radius=config.get('morgan_radius', 2),
            n_bits=config.get('morgan_bits', 2048)
        )
        if morgan_feat is not None:
            features['morgan'] = morgan_feat

    if 'rdkit' in config['feature_types']:
        rdkit_feat = compute_rdkit_features(smiles)
        if rdkit_feat is not None:
            features['rdkit'] = rdkit_feat

    if 'mordred' in config['feature_types']:
        mordred_feat = compute_mordred_features(smiles, config.get('mordred_calculator'))
        if mordred_feat is not None:
            features['mordred'] = mordred_feat

    return mol_id, features

def normalize_features(features_dict: Dict[str, np.ndarray], method: str = 'standard') -> Dict[str, np.ndarray]:
    """标准化特征"""
    normalized_features = {}

    for feat_type, feat_array in features_dict.items():
        if method == 'standard':
            # 标准化 (z-score)
            mean = np.mean(feat_array, axis=0)
            std = np.std(feat_array, axis=0)
            std[std == 0] = 1  # 避免除零
            normalized_features[feat_type] = (feat_array - mean) / std
        elif method == 'minmax':
            # 最小-最大标准化
            min_val = np.min(feat_array, axis=0)
            max_val = np.max(feat_array, axis=0)
            range_val = max_val - min_val
            range_val[range_val == 0] = 1  # 避免除零
            normalized_features[feat_type] = (feat_array - min_val) / range_val
        else:
            # 不进行标准化
            normalized_features[feat_type] = feat_array

    return normalized_features

def generate_features(data_path: str,
                     save_path: str,
                     feature_generators: List[str],
                     smiles_column: str = 'smiles',
                     id_column: Optional[str] = None,
                     normalize: str = 'standard',
                     n_jobs: int = -1,
                     chunk_size: int = 100,
                     **kwargs) -> bool:
    """
    主要的特征生成函数

    Args:
        data_path: 输入CSV文件路径
        save_path: 输出特征文件路径
        feature_generators: 特征类型列表
        smiles_column: SMILES列名
        id_column: ID列名
        normalize: 标准化方法
        n_jobs: 并行进程数
        chunk_size: 批处理大小
        **kwargs: 其他参数

    Returns:
        是否成功生成特征
    """
    logger.info(f"🚀 开始特征生成任务")
    logger.info(f"   输入文件: {data_path}")
    logger.info(f"   输出文件: {save_path}")
    logger.info(f"   特征类型: {feature_generators}")

    # 读取数据
    try:
        df = pd.read_csv(data_path)
        logger.info(f"✅ 成功读取数据: {len(df)} 个分子")
    except Exception as e:
        logger.error(f"❌ 读取数据失败: {e}")
        return False

    # 检查SMILES列
    if smiles_column not in df.columns:
        logger.error(f"❌ 未找到SMILES列: {smiles_column}")
        return False

    # 准备分子ID
    if id_column and id_column in df.columns:
        mol_ids = df[id_column].astype(str).tolist()
    else:
        mol_ids = [f"mol_{i}" for i in range(len(df))]

    smiles_list = df[smiles_column].tolist()

    # 准备配置
    config = {
        'feature_types': feature_generators,
        'morgan_radius': kwargs.get('morgan_radius', 2),
        'morgan_bits': kwargs.get('morgan_bits', 2048)
    }

    # 为Mordred创建计算器（避免重复创建）
    if 'mordred' in feature_generators:
        try:
            from mordred import Calculator, descriptors
            config['mordred_calculator'] = Calculator(descriptors, ignore_3D=True)
            logger.info("✅ Mordred计算器初始化完成")
        except Exception as e:
            logger.error(f"❌ Mordred计算器初始化失败: {e}")
            return False

    # 并行计算特征
    logger.info(f"🔄 开始并行特征计算 (进程数: {n_jobs})")
    start_time = time.time()

    # 准备任务参数
    tasks = [(smiles, mol_id, config) for smiles, mol_id in zip(smiles_list, mol_ids)]

    # 存储结果
    all_features = {feat_type: [] for feat_type in feature_generators}
    valid_mol_ids = []
    failed_count = 0

    # 并行处理
    if n_jobs == 1:
        # 单进程模式
        for task in tasks:
            mol_id, features = process_single_molecule(task)
            if features:
                valid_mol_ids.append(mol_id)
                for feat_type in feature_generators:
                    if feat_type in features:
                        all_features[feat_type].append(features[feat_type])
                    else:
                        failed_count += 1
                        break
    else:
        # 多进程模式
        with ProcessPoolExecutor(max_workers=n_jobs if n_jobs > 0 else None) as executor:
            futures = [executor.submit(process_single_molecule, task) for task in tasks]

            for i, future in enumerate(as_completed(futures)):
                try:
                    mol_id, features = future.result()
                    if features and all(feat_type in features for feat_type in feature_generators):
                        valid_mol_ids.append(mol_id)
                        for feat_type in feature_generators:
                            all_features[feat_type].append(features[feat_type])
                    else:
                        failed_count += 1

                    # 进度报告
                    if (i + 1) % 100 == 0:
                        logger.info(f"   已处理: {i + 1}/{len(tasks)} ({(i + 1)/len(tasks)*100:.1f}%)")

                except Exception as e:
                    logger.warning(f"处理分子失败: {e}")
                    failed_count += 1

    elapsed_time = time.time() - start_time
    logger.info(f"✅ 特征计算完成")
    logger.info(f"   成功: {len(valid_mol_ids)} 个分子")
    logger.info(f"   失败: {failed_count} 个分子")
    logger.info(f"   耗时: {elapsed_time:.2f} 秒")

    if len(valid_mol_ids) == 0:
        logger.error("❌ 没有成功计算的特征")
        return False

    # 转换为numpy数组
    logger.info("🔄 转换特征格式...")
    features_arrays = {}
    for feat_type in feature_generators:
        if all_features[feat_type]:
            features_arrays[feat_type] = np.array(all_features[feat_type])
            logger.info(f"   {feat_type}: {features_arrays[feat_type].shape}")

    # 标准化特征
    if normalize != 'none':
        logger.info(f"🔄 标准化特征 (方法: {normalize})...")
        features_arrays = normalize_features(features_arrays, normalize)

    # 保存特征
    logger.info(f"💾 保存特征到: {save_path}")
    try:
        # 创建输出目录
        Path(save_path).parent.mkdir(parents=True, exist_ok=True)

        # 保存数据
        save_data = {
            'mol_ids': np.array(valid_mol_ids),
            'feature_types': feature_generators,
            'normalization': normalize,
            'metadata': {
                'total_molecules': len(df),
                'successful_molecules': len(valid_mol_ids),
                'failed_molecules': failed_count,
                'generation_time': elapsed_time,
                'config': config
            }
        }

        # 添加特征数组
        for feat_type, feat_array in features_arrays.items():
            save_data[f'features_{feat_type}'] = feat_array

        np.savez_compressed(save_path, **save_data)
        logger.info("✅ 特征文件保存成功")

        # 显示文件信息
        file_size = Path(save_path).stat().st_size / (1024 * 1024)  # MB
        logger.info(f"   文件大小: {file_size:.2f} MB")

        return True

    except Exception as e:
        logger.error(f"❌ 保存特征文件失败: {e}")
        return False

def main():
    """主函数 - 命令行接口"""
    parser = argparse.ArgumentParser(
        description="神农框架特征生成器 v2.0 - 独立的分子特征计算工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 生成Morgan指纹
  python generate_features.py --data_path data.csv --save_path morgan_features.npz --feature_generator morgan

  # 生成Mordred描述符
  python generate_features.py --data_path data.csv --save_path mordred_features.npz --feature_generator mordred

  # 生成组合特征
  python generate_features.py --data_path data.csv --save_path combined_features.npz --feature_generator morgan,rdkit,mordred
        """
    )

    # 必需参数
    parser.add_argument('--data_path', type=str, required=True, help='输入CSV文件路径')
    parser.add_argument('--save_path', type=str, required=True, help='输出特征文件路径')
    parser.add_argument('--feature_generator', type=str, required=True, help='特征类型: morgan,rdkit,mordred')

    # 可选参数
    parser.add_argument('--smiles_column', type=str, default='smiles', help='SMILES列名')
    parser.add_argument('--id_column', type=str, default=None, help='分子ID列名')
    parser.add_argument('--normalize', type=str, choices=['standard', 'minmax', 'none'], default='standard')
    parser.add_argument('--n_jobs', type=int, default=-1, help='并行进程数')
    parser.add_argument('--morgan_radius', type=int, default=2, help='Morgan指纹半径')
    parser.add_argument('--morgan_bits', type=int, default=2048, help='Morgan指纹位数')
    parser.add_argument('--verbose', action='store_true', help='详细输出')

    args = parser.parse_args()

    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # 检查依赖
    if not check_dependencies():
        sys.exit(1)

    # 解析特征类型
    feature_generators = [ft.strip() for ft in args.feature_generator.split(',')]

    # 验证特征类型
    valid_types = {'morgan', 'rdkit', 'mordred'}
    invalid_types = set(feature_generators) - valid_types
    if invalid_types:
        logger.error(f"❌ 不支持的特征类型: {invalid_types}")
        sys.exit(1)

    # 验证输入文件
    if not Path(args.data_path).exists():
        logger.error(f"❌ 输入文件不存在: {args.data_path}")
        sys.exit(1)

    # 执行特征生成
    logger.info("🧬 神农框架特征生成器 v2.0")
    logger.info("=" * 50)

    success = generate_features(
        data_path=args.data_path,
        save_path=args.save_path,
        feature_generators=feature_generators,
        smiles_column=args.smiles_column,
        id_column=args.id_column,
        normalize=args.normalize,
        n_jobs=args.n_jobs,
        morgan_radius=args.morgan_radius,
        morgan_bits=args.morgan_bits
    )

    if success:
        logger.info("🎉 特征生成任务完成!")
        logger.info(f"   可以使用 --features_path {args.save_path} 加载特征")
    else:
        sys.exit(1)

if __name__ == "__main__":
    main()
