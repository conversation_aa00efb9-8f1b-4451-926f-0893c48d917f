# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-27
# 描述: 神农框架注意力机制模块

"""
神农框架注意力机制

实现生物启发的注意力机制，用于融合分子图特征和专家特征。
包含多头注意力、机制感知注意力等组件。
"""

from typing import Dict, List, Optional, Tuple, Any
import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import logging

logger = logging.getLogger(__name__)


class MultiHeadAttention(nn.Module):
    """
    多头注意力机制
    
    标准的多头注意力实现，支持自注意力和交叉注意力。
    """
    
    def __init__(
        self,
        d_model: int,
        num_heads: int = 8,
        dropout: float = 0.1,
        bias: bool = True
    ):
        """
        初始化多头注意力
        
        Args:
            d_model: 模型维度
            num_heads: 注意力头数
            dropout: Dropout概率
            bias: 是否使用偏置
        """
        super().__init__()
        
        if d_model % num_heads != 0:
            raise ValueError(f"d_model ({d_model}) 必须能被 num_heads ({num_heads}) 整除")
        
        self.d_model = d_model
        self.num_heads = num_heads
        self.d_k = d_model // num_heads
        
        # 线性变换层
        self.w_q = nn.Linear(d_model, d_model, bias=bias)
        self.w_k = nn.Linear(d_model, d_model, bias=bias)
        self.w_v = nn.Linear(d_model, d_model, bias=bias)
        self.w_o = nn.Linear(d_model, d_model, bias=bias)
        
        self.dropout = nn.Dropout(dropout)
        
        # 初始化权重
        self._init_weights()
    
    def _init_weights(self):
        """初始化权重"""
        for module in [self.w_q, self.w_k, self.w_v, self.w_o]:
            nn.init.xavier_uniform_(module.weight)
            if module.bias is not None:
                nn.init.zeros_(module.bias)
    
    def forward(
        self,
        query: torch.Tensor,
        key: torch.Tensor,
        value: torch.Tensor,
        mask: Optional[torch.Tensor] = None,
        return_attention: bool = False
    ) -> Tuple[torch.Tensor, Optional[torch.Tensor]]:
        """
        前向传播
        
        Args:
            query: 查询张量 [batch_size, seq_len_q, d_model]
            key: 键张量 [batch_size, seq_len_k, d_model]
            value: 值张量 [batch_size, seq_len_v, d_model]
            mask: 注意力掩码 [batch_size, seq_len_q, seq_len_k]
            return_attention: 是否返回注意力权重
            
        Returns:
            (输出张量, 注意力权重)
        """
        batch_size = query.size(0)
        
        # 线性变换并重塑为多头形式
        Q = self.w_q(query).view(batch_size, -1, self.num_heads, self.d_k).transpose(1, 2)
        K = self.w_k(key).view(batch_size, -1, self.num_heads, self.d_k).transpose(1, 2)
        V = self.w_v(value).view(batch_size, -1, self.num_heads, self.d_k).transpose(1, 2)
        
        # 计算注意力
        attention_output, attention_weights = self._scaled_dot_product_attention(
            Q, K, V, mask, return_attention
        )
        
        # 拼接多头输出
        attention_output = attention_output.transpose(1, 2).contiguous().view(
            batch_size, -1, self.d_model
        )
        
        # 最终线性变换
        output = self.w_o(attention_output)
        
        return output, attention_weights if return_attention else None
    
    def _scaled_dot_product_attention(
        self,
        Q: torch.Tensor,
        K: torch.Tensor,
        V: torch.Tensor,
        mask: Optional[torch.Tensor] = None,
        return_attention: bool = False
    ) -> Tuple[torch.Tensor, Optional[torch.Tensor]]:
        """缩放点积注意力"""
        # 计算注意力分数
        scores = torch.matmul(Q, K.transpose(-2, -1)) / math.sqrt(self.d_k)
        
        # 应用掩码
        if mask is not None:
            if mask.dim() == 3:  # [batch_size, seq_len_q, seq_len_k]
                mask = mask.unsqueeze(1)  # [batch_size, 1, seq_len_q, seq_len_k]
            scores = scores.masked_fill(mask == 0, -1e9)
        
        # 计算注意力权重
        attention_weights = F.softmax(scores, dim=-1)
        attention_weights = self.dropout(attention_weights)
        
        # 应用注意力权重
        output = torch.matmul(attention_weights, V)
        
        return output, attention_weights if return_attention else None


class CrossAttention(nn.Module):
    """
    交叉注意力机制
    
    用于融合两个不同模态的特征（如图特征和专家特征）。
    """
    
    def __init__(
        self,
        query_dim: int,
        key_value_dim: int,
        output_dim: int,
        num_heads: int = 4,
        dropout: float = 0.1
    ):
        """
        初始化交叉注意力
        
        Args:
            query_dim: 查询特征维度
            key_value_dim: 键值特征维度
            output_dim: 输出特征维度
            num_heads: 注意力头数
            dropout: Dropout概率
        """
        super().__init__()
        
        self.query_dim = query_dim
        self.key_value_dim = key_value_dim
        self.output_dim = output_dim
        self.num_heads = num_heads
        
        # 投影层
        self.query_proj = nn.Linear(query_dim, output_dim)
        self.key_proj = nn.Linear(key_value_dim, output_dim)
        self.value_proj = nn.Linear(key_value_dim, output_dim)
        
        # 多头注意力
        self.attention = MultiHeadAttention(
            d_model=output_dim,
            num_heads=num_heads,
            dropout=dropout
        )
        
        # 输出投影
        self.output_proj = nn.Linear(output_dim, output_dim)
        self.dropout = nn.Dropout(dropout)
        
    def forward(
        self,
        query_features: torch.Tensor,
        key_value_features: torch.Tensor,
        return_attention: bool = False
    ) -> Tuple[torch.Tensor, Optional[torch.Tensor]]:
        """
        前向传播
        
        Args:
            query_features: 查询特征 [batch_size, query_len, query_dim]
            key_value_features: 键值特征 [batch_size, kv_len, key_value_dim]
            return_attention: 是否返回注意力权重
            
        Returns:
            (融合特征, 注意力权重)
        """
        # 投影到统一维度
        Q = self.query_proj(query_features)
        K = self.key_proj(key_value_features)
        V = self.value_proj(key_value_features)
        
        # 交叉注意力
        attended_features, attention_weights = self.attention(
            Q, K, V, return_attention=return_attention
        )
        
        # 输出投影
        output = self.output_proj(attended_features)
        output = self.dropout(output)
        
        return output, attention_weights


class MechanismAwareAttention(nn.Module):
    """
    机制感知注意力
    
    根据抗菌机制信息调整注意力权重，实现机制导向的特征融合。
    """
    
    def __init__(
        self,
        feature_dim: int,
        mechanism_dim: int = 64,
        num_mechanisms: int = 5,
        num_heads: int = 4,
        dropout: float = 0.1
    ):
        """
        初始化机制感知注意力
        
        Args:
            feature_dim: 特征维度
            mechanism_dim: 机制嵌入维度
            num_mechanisms: 机制类型数量
            num_heads: 注意力头数
            dropout: Dropout概率
        """
        super().__init__()
        
        self.feature_dim = feature_dim
        self.mechanism_dim = mechanism_dim
        self.num_mechanisms = num_mechanisms
        self.num_heads = num_heads
        
        # 机制嵌入
        self.mechanism_embeddings = nn.Embedding(num_mechanisms, mechanism_dim)
        
        # 机制条件的注意力
        self.mechanism_attention = nn.MultiheadAttention(
            embed_dim=mechanism_dim,
            num_heads=2,
            dropout=dropout,
            batch_first=True
        )
        
        # 特征注意力
        self.feature_attention = MultiHeadAttention(
            d_model=feature_dim,
            num_heads=num_heads,
            dropout=dropout
        )
        
        # 机制-特征融合
        self.mechanism_feature_fusion = nn.Sequential(
            nn.Linear(mechanism_dim + feature_dim, feature_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(feature_dim, feature_dim)
        )
        
    def forward(
        self,
        features: torch.Tensor,
        mechanism_ids: Optional[torch.Tensor] = None,
        return_attention: bool = False
    ) -> Tuple[torch.Tensor, Dict[str, torch.Tensor]]:
        """
        前向传播
        
        Args:
            features: 输入特征 [batch_size, seq_len, feature_dim]
            mechanism_ids: 机制ID [batch_size, num_mechanisms]
            return_attention: 是否返回注意力权重
            
        Returns:
            (输出特征, 注意力权重字典)
        """
        batch_size, seq_len, _ = features.shape
        attention_weights = {}
        
        # 特征自注意力
        attended_features, feature_attention_weights = self.feature_attention(
            features, features, features, return_attention=True
        )
        
        if return_attention:
            attention_weights['feature_attention'] = feature_attention_weights
        
        # 如果提供了机制信息，进行机制感知处理
        if mechanism_ids is not None:
            # 获取机制嵌入
            mechanism_embeds = self.mechanism_embeddings(mechanism_ids)  # [batch_size, num_mechanisms, mechanism_dim]
            
            # 机制自注意力
            mechanism_context, mechanism_attention_weights = self.mechanism_attention(
                mechanism_embeds, mechanism_embeds, mechanism_embeds
            )
            
            if return_attention:
                attention_weights['mechanism_attention'] = mechanism_attention_weights
            
            # 聚合机制上下文
            mechanism_context = mechanism_context.mean(dim=1, keepdim=True)  # [batch_size, 1, mechanism_dim]
            mechanism_context = mechanism_context.expand(-1, seq_len, -1)  # [batch_size, seq_len, mechanism_dim]
            
            # 融合机制信息和特征
            combined_features = torch.cat([attended_features, mechanism_context], dim=-1)
            output = self.mechanism_feature_fusion(combined_features)
        else:
            output = attended_features
        
        return output, attention_weights


class BiologicalAttentionFusion(nn.Module):
    """
    生物启发的注意力融合机制
    
    神农框架的核心组件，融合分子图特征和专家特征，
    同时考虑生物学机制信息。
    """
    
    def __init__(
        self,
        graph_dim: int,
        expert_dim: int,
        output_dim: int,
        attention_heads: int = 4,
        dropout: float = 0.1,
        mechanism_aware: bool = True,
        num_mechanisms: int = 5
    ):
        """
        初始化生物启发注意力融合
        
        Args:
            graph_dim: 图特征维度
            expert_dim: 专家特征维度
            output_dim: 输出特征维度
            attention_heads: 注意力头数
            dropout: Dropout概率
            mechanism_aware: 是否启用机制感知
            num_mechanisms: 机制类型数量
        """
        super().__init__()
        
        self.graph_dim = graph_dim
        self.expert_dim = expert_dim
        self.output_dim = output_dim
        self.mechanism_aware = mechanism_aware
        
        # 特征投影层
        self.graph_proj = nn.Linear(graph_dim, output_dim)
        self.expert_proj = nn.Linear(expert_dim, output_dim)
        
        # 多头注意力：分别学习不同生物学特征
        self.attention_heads = nn.ModuleList([
            self._create_attention_head(output_dim, head_type)
            for head_type in ['pharmacophore', 'flexibility', 'charge', 'hydrophobic']
        ])
        
        # 交叉注意力：图特征 -> 专家特征
        self.graph_to_expert_attention = CrossAttention(
            query_dim=output_dim,
            key_value_dim=output_dim,
            output_dim=output_dim,
            num_heads=attention_heads // 2,
            dropout=dropout
        )
        
        # 交叉注意力：专家特征 -> 图特征
        self.expert_to_graph_attention = CrossAttention(
            query_dim=output_dim,
            key_value_dim=output_dim,
            output_dim=output_dim,
            num_heads=attention_heads // 2,
            dropout=dropout
        )
        
        # 机制感知权重
        if mechanism_aware:
            self.mechanism_weights = nn.Linear(
                output_dim * 2,  # 拼接图特征和专家特征
                num_mechanisms
            )
        
        # 最终融合层
        self.fusion_layer = nn.Sequential(
            nn.Linear(output_dim * 2, output_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(output_dim, output_dim)
        )
        
        self.dropout = nn.Dropout(dropout)
        
    def _create_attention_head(self, dim: int, head_type: str) -> nn.Module:
        """创建特定类型的注意力头"""
        return nn.Sequential(
            nn.Linear(dim, dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(dim, dim),
            nn.Sigmoid()  # 注意力权重
        )
    
    def forward(
        self,
        graph_features: torch.Tensor,
        expert_features: torch.Tensor,
        return_attention: bool = False
    ) -> Tuple[torch.Tensor, Dict[str, torch.Tensor]]:
        """
        前向传播
        
        Args:
            graph_features: 图特征 [batch_size, graph_dim]
            expert_features: 专家特征 [batch_size, expert_dim]
            return_attention: 是否返回注意力权重
            
        Returns:
            (融合特征, 注意力权重字典)
        """
        batch_size = graph_features.size(0)
        attention_weights = {}
        
        # 投影到统一维度
        graph_proj = self.graph_proj(graph_features)  # [batch_size, output_dim]
        expert_proj = self.expert_proj(expert_features)  # [batch_size, output_dim]
        
        # 添加序列维度用于注意力计算
        graph_seq = graph_proj.unsqueeze(1)  # [batch_size, 1, output_dim]
        expert_seq = expert_proj.unsqueeze(1)  # [batch_size, 1, output_dim]
        
        # 多头注意力计算
        attention_outputs = []
        head_weights = []
        
        for i, head in enumerate(self.attention_heads):
            # 计算注意力权重
            graph_weight = head(graph_proj)
            expert_weight = head(expert_proj)
            
            # 应用注意力权重
            weighted_graph = graph_proj * graph_weight
            weighted_expert = expert_proj * expert_weight
            
            attention_outputs.append(weighted_graph + weighted_expert)
            
            if return_attention:
                head_weights.append({
                    'graph_weight': graph_weight,
                    'expert_weight': expert_weight
                })
        
        if return_attention:
            attention_weights['head_weights'] = head_weights
        
        # 交叉注意力
        graph_attended, graph_cross_attention = self.graph_to_expert_attention(
            graph_seq, expert_seq, return_attention=return_attention
        )
        expert_attended, expert_cross_attention = self.expert_to_graph_attention(
            expert_seq, graph_seq, return_attention=return_attention
        )
        
        if return_attention:
            attention_weights['graph_cross_attention'] = graph_cross_attention
            attention_weights['expert_cross_attention'] = expert_cross_attention
        
        # 移除序列维度
        graph_attended = graph_attended.squeeze(1)
        expert_attended = expert_attended.squeeze(1)
        
        # 特征融合
        fused_features = torch.cat([graph_attended, expert_attended], dim=-1)
        
        # 机制感知权重
        mechanism_weights = None
        if self.mechanism_aware:
            mechanism_weights = self.mechanism_weights(fused_features)
            if return_attention:
                attention_weights['mechanism_weights'] = mechanism_weights
        
        # 最终融合
        output = self.fusion_layer(fused_features)
        output = self.dropout(output)
        
        return output, attention_weights
