# 实验设计协议

## 协议概述

本协议定义了神农框架与基线模型对比研究的详细实验程序，确保研究的科学性、可重复性和可靠性。实验设计遵循机器学习最佳实践和药物发现领域的标准评估方法。

## 1. 数据集构建协议

### 1.1 数据收集标准

#### 化合物选择标准
- **来源**: ChEMBL数据库 + 同行评议期刊文献
- **活性数据**: 明确的抗菌活性测定结果 (MIC值)
- **质量控制**: 
  - 排除Pan-Assay Interference Compounds (PAINS)
  - 排除反应性化合物
  - 分子量范围: 150-800 Da
  - 重原子数: 10-60

#### 活性阈值定义
- **活性化合物**: MIC ≤ 16 μg/mL
- **非活性化合物**: MIC > 64 μg/mL
- **排除**: 16 < MIC ≤ 64 μg/mL (避免边界模糊)

#### 数据清洗流程
```python
def data_cleaning_protocol(raw_data):
    """
    标准化数据清洗协议
    """
    # 步骤1: 分子标准化
    standardized_mols = []
    for smiles, activity in raw_data:
        mol = Chem.MolFromSmiles(smiles)
        if mol is not None:
            # 去盐处理
            mol = rdMolStandardize.ChargeParent(mol)
            # 标准化
            mol = rdMolStandardize.Normalize(mol)
            # 重新生成SMILES
            std_smiles = Chem.MolToSmiles(mol)
            standardized_mols.append((std_smiles, activity))
    
    # 步骤2: 去重
    unique_compounds = {}
    for smiles, activity in standardized_mols:
        inchi = Chem.MolToInchi(Chem.MolFromSmiles(smiles))
        if inchi not in unique_compounds:
            unique_compounds[inchi] = (smiles, activity)
        else:
            # 冲突解决: 取更可靠的数据源
            existing_activity = unique_compounds[inchi][1]
            if activity_reliability(activity) > activity_reliability(existing_activity):
                unique_compounds[inchi] = (smiles, activity)
    
    # 步骤3: 质量过滤
    filtered_compounds = []
    for smiles, activity in unique_compounds.values():
        if passes_quality_filters(smiles):
            filtered_compounds.append((smiles, activity))
    
    return filtered_compounds

def passes_quality_filters(smiles):
    """
    分子质量过滤器
    """
    mol = Chem.MolFromSmiles(smiles)
    if mol is None:
        return False
    
    # 分子量检查
    mw = Descriptors.MolWt(mol)
    if not (150 <= mw <= 800):
        return False
    
    # PAINS过滤
    if is_pains(mol):
        return False
    
    # 反应性检查
    if is_reactive(mol):
        return False
    
    return True
```

### 1.2 数据分割策略

#### 分层抽样协议
```python
def stratified_split_protocol(data, test_size=0.1, val_size=0.1, random_state=42):
    """
    分层数据分割协议
    """
    # 基于活性的分层
    X = [smiles for smiles, _ in data]
    y = [activity for _, activity in data]
    
    # 第一次分割: 训练+验证 vs 测试
    X_trainval, X_test, y_trainval, y_test = train_test_split(
        X, y, test_size=test_size, stratify=y, random_state=random_state
    )
    
    # 第二次分割: 训练 vs 验证
    val_size_adjusted = val_size / (1 - test_size)
    X_train, X_val, y_train, y_val = train_test_split(
        X_trainval, y_trainval, test_size=val_size_adjusted, 
        stratify=y_trainval, random_state=random_state
    )
    
    return {
        'train': list(zip(X_train, y_train)),
        'val': list(zip(X_val, y_val)),
        'test': list(zip(X_test, y_test))
    }
```

#### 化学多样性验证
```python
def validate_chemical_diversity(train_set, val_set, test_set):
    """
    验证数据集的化学多样性分布
    """
    # 计算Tanimoto相似性分布
    train_fps = [get_morgan_fingerprint(smiles) for smiles, _ in train_set]
    val_fps = [get_morgan_fingerprint(smiles) for smiles, _ in val_set]
    test_fps = [get_morgan_fingerprint(smiles) for smiles, _ in test_set]
    
    # 集合间相似性统计
    train_val_sim = calculate_cross_similarity(train_fps, val_fps)
    train_test_sim = calculate_cross_similarity(train_fps, test_fps)
    val_test_sim = calculate_cross_similarity(val_fps, test_fps)
    
    # 输出多样性报告
    diversity_report = {
        'train_val_similarity': np.mean(train_val_sim),
        'train_test_similarity': np.mean(train_test_sim),
        'val_test_similarity': np.mean(val_test_sim),
        'train_internal_diversity': calculate_internal_diversity(train_fps),
        'val_internal_diversity': calculate_internal_diversity(val_fps),
        'test_internal_diversity': calculate_internal_diversity(test_fps)
    }
    
    return diversity_report
```

### 1.3 交叉验证设计

#### 5折分层交叉验证
```python
def cross_validation_protocol(data, n_folds=5, random_state=42):
    """
    5折分层交叉验证协议
    """
    X = [smiles for smiles, _ in data]
    y = [activity for _, activity in data]
    
    skf = StratifiedKFold(n_splits=n_folds, shuffle=True, random_state=random_state)
    
    cv_splits = []
    for fold_idx, (train_idx, val_idx) in enumerate(skf.split(X, y)):
        train_data = [(X[i], y[i]) for i in train_idx]
        val_data = [(X[i], y[i]) for i in val_idx]
        
        cv_splits.append({
            'fold': fold_idx + 1,
            'train': train_data,
            'val': val_data
        })
    
    return cv_splits
```

## 2. 特征工程协议

### 2.1 分子描述符计算

#### Mordred描述符标准化流程
```python
def compute_molecular_descriptors(smiles_list):
    """
    标准化的分子描述符计算协议
    """
    from mordred import Calculator, descriptors
    
    # 初始化计算器
    calc = Calculator(descriptors, ignore_3D=False)
    
    descriptors_data = []
    valid_indices = []
    
    for idx, smiles in enumerate(smiles_list):
        mol = Chem.MolFromSmiles(smiles)
        if mol is not None:
            # 添加氢原子
            mol = Chem.AddHs(mol)
            
            # 生成3D构象
            if AllChem.EmbedMolecule(mol) == 0:
                AllChem.MMFFOptimizeMolecule(mol)
                
                # 计算描述符
                desc_values = calc(mol)
                descriptors_data.append(desc_values)
                valid_indices.append(idx)
            else:
                # 3D构象生成失败，跳过3D描述符
                mol_2d = Chem.RemoveHs(mol)
                desc_values = calc(mol_2d)
                descriptors_data.append(desc_values)
                valid_indices.append(idx)
    
    # 转换为DataFrame
    desc_df = pd.DataFrame(descriptors_data)
    
    return desc_df, valid_indices
```

#### 描述符预处理流程
```python
def preprocess_descriptors(desc_df, strategy='median'):
    """
    描述符预处理标准协议
    """
    # 1. 处理缺失值
    if strategy == 'median':
        numeric_cols = desc_df.select_dtypes(include=[np.number]).columns
        desc_df[numeric_cols] = desc_df[numeric_cols].fillna(desc_df[numeric_cols].median())
    elif strategy == 'mean':
        numeric_cols = desc_df.select_dtypes(include=[np.number]).columns
        desc_df[numeric_cols] = desc_df[numeric_cols].fillna(desc_df[numeric_cols].mean())
    
    # 2. 移除无效列
    # 移除全为NaN的列
    desc_df = desc_df.dropna(axis=1, how='all')
    
    # 移除常数列
    constant_cols = desc_df.columns[desc_df.nunique() <= 1]
    desc_df = desc_df.drop(constant_cols, axis=1)
    
    # 3. 处理无穷大值
    desc_df = desc_df.replace([np.inf, -np.inf], np.nan)
    desc_df = desc_df.fillna(desc_df.median())
    
    # 4. 特征选择
    # 移除高相关性特征 (>0.95)
    corr_matrix = desc_df.corr().abs()
    upper_triangle = corr_matrix.where(
        np.triu(np.ones(corr_matrix.shape), k=1).astype(bool)
    )
    high_corr_features = [column for column in upper_triangle.columns 
                         if any(upper_triangle[column] > 0.95)]
    desc_df = desc_df.drop(high_corr_features, axis=1)
    
    # 5. 标准化
    scaler = StandardScaler()
    desc_scaled = scaler.fit_transform(desc_df)
    desc_df_scaled = pd.DataFrame(desc_scaled, columns=desc_df.columns, index=desc_df.index)
    
    return desc_df_scaled, scaler
```

### 2.2 化学性质特征

#### 抗菌相关化学性质
```python
def extract_antimicrobial_properties(mol):
    """
    提取抗菌活性相关的化学性质
    """
    properties = {}
    
    # 1. 基本物理化学性质
    properties['molecular_weight'] = Descriptors.MolWt(mol)
    properties['logP'] = Descriptors.MolLogP(mol)
    properties['tpsa'] = Descriptors.TPSA(mol)
    properties['hbd'] = Descriptors.NumHDonors(mol)
    properties['hba'] = Descriptors.NumHAcceptors(mol)
    
    # 2. 官能团指纹
    functional_groups = identify_antimicrobial_pharmacophores(mol)
    properties.update(functional_groups)
    
    # 3. 电子性质
    properties['total_charge'] = Chem.rdmolops.GetFormalCharge(mol)
    properties['aromatic_atoms'] = len([atom for atom in mol.GetAtoms() if atom.GetIsAromatic()])
    
    # 4. 几何性质
    properties['ring_count'] = Descriptors.RingCount(mol)
    properties['aromatic_rings'] = Descriptors.NumAromaticRings(mol)
    
    return properties

def identify_antimicrobial_pharmacophores(mol):
    """
    识别抗菌药效团
    """
    pharmacophores = {}
    
    # 定义抗菌相关的子结构模式
    patterns = {
        'beta_lactam': '[C:1](=[O:2])[N:3]1[C:4][C:5]1',
        'quinolone': 'c1cc2c(cc1)c(=O)cc(n2)C(=O)O',
        'sulfonamide': 'S(=O)(=O)(N)N',
        'phenol': 'c1ccc(cc1)O',
        'primary_amine': '[N:1]([H:2])[H:3]',
        'carboxyl': 'C(=O)O',
        'nitroimidazole': '[n:1]1c([N+](=O)[O-])c[nH]c1',
        'macrolide_lactone': '[C:1](=[O:2])[O:3][C:4]'
    }
    
    for name, pattern in patterns.items():
        try:
            patt = Chem.MolFromSmarts(pattern)
            if patt is not None:
                matches = mol.GetSubstructMatches(patt)
                pharmacophores[f'has_{name}'] = len(matches) > 0
                pharmacophores[f'count_{name}'] = len(matches)
        except:
            pharmacophores[f'has_{name}'] = False
            pharmacophores[f'count_{name}'] = 0
    
    return pharmacophores
```

## 3. 模型训练协议

### 3.1 神农框架训练

#### 超参数设置
```python
SHENNONG_CONFIG = {
    # 网络架构
    'gnn_layers': 3,
    'gnn_hidden_dim': 256,
    'expert_hidden_dim': 128,
    'attention_heads': 8,
    'fusion_dim': 256,
    
    # 训练参数
    'batch_size': 32,
    'learning_rate': 1e-3,
    'weight_decay': 1e-5,
    'max_epochs': 200,
    'patience': 20,
    
    # 损失函数权重
    'activity_loss_weight': 1.0,
    'interpretability_loss_weight': 0.1,
    
    # 数据增强
    'use_augmentation': True,
    'augmentation_prob': 0.1
}
```

#### 训练流程
```python
def train_shennong_protocol(train_data, val_data, config):
    """
    神农框架标准训练协议
    """
    # 1. 数据准备
    train_loader = create_dataloader(train_data, config['batch_size'], shuffle=True)
    val_loader = create_dataloader(val_data, config['batch_size'], shuffle=False)
    
    # 2. 模型初始化
    model = ShennongFramework(config)
    optimizer = torch.optim.Adam(model.parameters(), 
                                lr=config['learning_rate'],
                                weight_decay=config['weight_decay'])
    
    # 3. 学习率调度
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.5, patience=10, verbose=True
    )
    
    # 4. 早停机制
    early_stopping = EarlyStopping(patience=config['patience'], verbose=True)
    
    # 5. 训练循环
    best_model_state = None
    training_history = []
    
    for epoch in range(config['max_epochs']):
        # 训练阶段
        train_loss, train_metrics = train_epoch(model, train_loader, optimizer, config)
        
        # 验证阶段
        val_loss, val_metrics = validate_epoch(model, val_loader, config)
        
        # 学习率调整
        scheduler.step(val_loss)
        
        # 记录历史
        training_history.append({
            'epoch': epoch,
            'train_loss': train_loss,
            'val_loss': val_loss,
            'train_auc': train_metrics['auc'],
            'val_auc': val_metrics['auc']
        })
        
        # 早停检查
        early_stopping(val_loss, model)
        if early_stopping.early_stop:
            print(f"Early stopping at epoch {epoch}")
            break
        
        # 保存最佳模型
        if val_metrics['auc'] == max([h['val_auc'] for h in training_history]):
            best_model_state = model.state_dict().copy()
    
    # 加载最佳模型
    model.load_state_dict(best_model_state)
    
    return model, training_history
```

### 3.2 Chemprop训练协议

#### 配置参数
```python
CHEMPROP_CONFIG = {
    'epochs': 100,
    'batch_size': 50,
    'hidden_size': 300,
    'depth': 3,
    'dropout': 0.0,
    'activation': 'ReLU',
    'learning_rate': 1e-3,
    'num_folds': 5,
    'ensemble_size': 5,
    'features_scaling': True
}
```

#### 训练脚本
```bash
#!/bin/bash
# Chemprop训练标准协议

# 基础训练
python train.py \
    --data_path data/antimicrobial_train.csv \
    --separate_val_path data/antimicrobial_val.csv \
    --dataset_type classification \
    --save_dir models/chemprop \
    --features_path data/mordred_features_train.csv \
    --separate_val_features_path data/mordred_features_val.csv \
    --epochs 100 \
    --batch_size 50 \
    --hidden_size 300 \
    --depth 3 \
    --dropout 0.0 \
    --learning_rate 1e-3 \
    --num_folds 5 \
    --ensemble_size 5 \
    --features_scaling \
    --metric auc \
    --save_smiles_splits \
    --quiet
```

### 3.3 AutoGluon训练协议

#### 配置设置
```python
AUTOGLUON_CONFIG = {
    'time_limit': 3600,  # 1小时
    'presets': 'best_quality',
    'eval_metric': 'roc_auc',
    'problem_type': 'binary',
    'verbosity': 2,
    'ag_args_fit': {
        'num_bag_folds': 5,
        'num_stack_levels': 1
    }
}
```

#### 训练代码
```python
def train_autogluon_protocol(train_data, config):
    """
    AutoGluon标准训练协议
    """
    from autogluon.tabular import TabularPredictor
    
    # 数据准备
    train_df = prepare_autogluon_data(train_data)
    
    # 模型训练
    predictor = TabularPredictor(
        label='activity',
        problem_type=config['problem_type'],
        eval_metric=config['eval_metric'],
        verbosity=config['verbosity']
    ).fit(
        train_df,
        time_limit=config['time_limit'],
        presets=config['presets'],
        ag_args_fit=config['ag_args_fit']
    )
    
    return predictor
```

## 4. 评估协议

### 4.1 性能评估指标

#### 主要指标定义
```python
def calculate_performance_metrics(y_true, y_pred, y_prob):
    """
    计算标准性能评估指标
    """
    metrics = {}
    
    # 基础分类指标
    metrics['accuracy'] = accuracy_score(y_true, y_pred)
    metrics['precision'] = precision_score(y_true, y_pred)
    metrics['recall'] = recall_score(y_true, y_pred)
    metrics['f1'] = f1_score(y_true, y_pred)
    metrics['mcc'] = matthews_corrcoef(y_true, y_pred)
    
    # ROC和PR曲线
    metrics['roc_auc'] = roc_auc_score(y_true, y_prob)
    precision_vals, recall_vals, _ = precision_recall_curve(y_true, y_prob)
    metrics['pr_auc'] = auc(recall_vals, precision_vals)
    
    # 混淆矩阵
    tn, fp, fn, tp = confusion_matrix(y_true, y_pred).ravel()
    metrics['sensitivity'] = tp / (tp + fn)  # 真正率
    metrics['specificity'] = tn / (tn + fp)  # 真负率
    metrics['ppv'] = tp / (tp + fp)  # 阳性预测值
    metrics['npv'] = tn / (tn + fn)  # 阴性预测值
    
    return metrics
```

#### 统计显著性检验
```python
def statistical_significance_tests(model_results):
    """
    模型间统计显著性检验协议
    """
    from scipy.stats import mcnemar, wilcoxon
    
    # McNemar检验 (比较分类错误)
    mcnemar_results = {}
    models = list(model_results.keys())
    
    for i, model1 in enumerate(models):
        for model2 in models[i+1:]:
            # 构建列联表
            correct_1 = model_results[model1]['predictions'] == model_results[model1]['true_labels']
            correct_2 = model_results[model2]['predictions'] == model_results[model2]['true_labels']
            
            # 2x2列联表
            both_correct = np.sum(correct_1 & correct_2)
            only_1_correct = np.sum(correct_1 & ~correct_2)
            only_2_correct = np.sum(~correct_1 & correct_2)
            both_wrong = np.sum(~correct_1 & ~correct_2)
            
            contingency_table = [[both_correct, only_2_correct],
                               [only_1_correct, both_wrong]]
            
            # McNemar检验
            statistic, p_value = mcnemar(contingency_table, exact=True)
            mcnemar_results[f'{model1}_vs_{model2}'] = {
                'statistic': statistic,
                'p_value': p_value,
                'significant': p_value < 0.05
            }
    
    # Wilcoxon符号秩检验 (比较AUC分数)
    wilcoxon_results = {}
    for i, model1 in enumerate(models):
        for model2 in models[i+1:]:
            auc_1 = model_results[model1]['cv_auc_scores']
            auc_2 = model_results[model2]['cv_auc_scores']
            
            statistic, p_value = wilcoxon(auc_1, auc_2)
            wilcoxon_results[f'{model1}_vs_{model2}'] = {
                'statistic': statistic,
                'p_value': p_value,
                'significant': p_value < 0.05
            }
    
    return mcnemar_results, wilcoxon_results
```

### 4.2 可解释性评估

#### 注意力权重分析
```python
def analyze_attention_weights(model, test_data):
    """
    分析化学导向注意力权重
    """
    attention_analysis = []
    
    for mol_data in test_data:
        # 获取注意力权重
        with torch.no_grad():
            attention_weights = model.get_attention_weights(mol_data)
        
        # 分析权重分布
        analysis = {
            'smiles': mol_data['smiles'],
            'graph_attention_mean': torch.mean(attention_weights['graph']).item(),
            'expert_attention_mean': torch.mean(attention_weights['expert']).item(),
            'attention_entropy': calculate_entropy(attention_weights['combined']),
            'max_attention_feature': torch.argmax(attention_weights['combined']).item()
        }
        
        attention_analysis.append(analysis)
    
    return attention_analysis
```

#### 化学解释验证
```python
def validate_chemical_interpretations(model, test_molecules, known_pharmacophores):
    """
    验证化学解释的合理性
    """
    interpretation_scores = []
    
    for mol in test_molecules:
        # 获取模型解释
        interpretation = model.get_interpretation(mol)
        
        # 与已知药效团对比
        overlap_score = calculate_pharmacophore_overlap(
            interpretation['important_features'],
            known_pharmacophores
        )
        
        # 化学合理性评分
        chemical_validity = assess_chemical_validity(interpretation)
        
        interpretation_scores.append({
            'smiles': mol['smiles'],
            'pharmacophore_overlap': overlap_score,
            'chemical_validity': chemical_validity,
            'interpretation_confidence': interpretation['confidence']
        })
    
    return interpretation_scores
```

## 5. 结果报告协议

### 5.1 性能对比表格

#### 标准报告格式
```python
def generate_performance_report(results):
    """
    生成标准化性能报告
    """
    report_data = []
    
    for model_name, metrics in results.items():
        row = {
            'Model': model_name,
            'ROC-AUC (Mean±Std)': f"{metrics['roc_auc_mean']:.3f}±{metrics['roc_auc_std']:.3f}",
            'PR-AUC (Mean±Std)': f"{metrics['pr_auc_mean']:.3f}±{metrics['pr_auc_std']:.3f}",
            'F1-Score (Mean±Std)': f"{metrics['f1_mean']:.3f}±{metrics['f1_std']:.3f}",
            'MCC (Mean±Std)': f"{metrics['mcc_mean']:.3f}±{metrics['mcc_std']:.3f}",
            'Sensitivity': f"{metrics['sensitivity']:.3f}",
            'Specificity': f"{metrics['specificity']:.3f}"
        }
        report_data.append(row)
    
    df_report = pd.DataFrame(report_data)
    return df_report
```

### 5.2 可视化协议

#### 标准图表生成
```python
def generate_standard_plots(results):
    """
    生成标准评估图表
    """
    # 1. ROC曲线对比
    plt.figure(figsize=(8, 6))
    for model_name, model_results in results.items():
        fpr = model_results['fpr']
        tpr = model_results['tpr']
        auc = model_results['roc_auc']
        plt.plot(fpr, tpr, label=f'{model_name} (AUC = {auc:.3f})')
    
    plt.plot([0, 1], [0, 1], 'k--', alpha=0.6)
    plt.xlabel('False Positive Rate')
    plt.ylabel('True Positive Rate')
    plt.title('ROC Curve Comparison')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.savefig('roc_comparison.png', dpi=300, bbox_inches='tight')
    
    # 2. Precision-Recall曲线对比
    plt.figure(figsize=(8, 6))
    for model_name, model_results in results.items():
        precision = model_results['precision']
        recall = model_results['recall']
        pr_auc = model_results['pr_auc']
        plt.plot(recall, precision, label=f'{model_name} (PR-AUC = {pr_auc:.3f})')
    
    plt.xlabel('Recall')
    plt.ylabel('Precision')
    plt.title('Precision-Recall Curve Comparison')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.savefig('pr_comparison.png', dpi=300, bbox_inches='tight')
    
    # 3. 性能雷达图
    create_radar_plot(results)

def create_radar_plot(results):
    """
    创建性能雷达图
    """
    metrics = ['ROC-AUC', 'PR-AUC', 'F1-Score', 'MCC', 'Sensitivity', 'Specificity']
    
    fig, ax = plt.subplots(figsize=(8, 8), subplot_kw=dict(projection='polar'))
    
    angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False).tolist()
    angles += angles[:1]  # 闭合图形
    
    for model_name, model_results in results.items():
        values = [
            model_results['roc_auc'],
            model_results['pr_auc'],
            model_results['f1'],
            model_results['mcc'],
            model_results['sensitivity'],
            model_results['specificity']
        ]
        values += values[:1]  # 闭合图形
        
        ax.plot(angles, values, 'o-', linewidth=2, label=model_name)
        ax.fill(angles, values, alpha=0.25)
    
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(metrics)
    ax.set_ylim(0, 1)
    ax.legend()
    plt.title('Model Performance Radar Chart')
    plt.savefig('radar_comparison.png', dpi=300, bbox_inches='tight')
```

## 6. 质量控制检查清单

### 6.1 数据质量检查
- [ ] 分子结构标准化完成
- [ ] 重复化合物已去除
- [ ] PAINS化合物已过滤
- [ ] 活性阈值定义明确
- [ ] 数据分割符合分层抽样原则
- [ ] 化学多样性分布合理

### 6.2 实验设计检查
- [ ] 对照实验设计合理
- [ ] 超参数搜索空间定义
- [ ] 交叉验证策略实施
- [ ] 评估指标选择适当
- [ ] 统计检验方法正确

### 6.3 结果验证检查
- [ ] 性能指标计算准确
- [ ] 统计显著性检验完成
- [ ] 可解释性分析合理
- [ ] 结果可重现
- [ ] 化学解释验证

### 6.4 报告质量检查
- [ ] 方法描述详细完整
- [ ] 结果表格格式标准
- [ ] 图表质量符合期刊要求
- [ ] 统计数据报告准确
- [ ] 结论支撑证据充分

## 附录

### A. 软件版本要求
```
Python: 3.8+
PyTorch: 1.10+
RDKit: 2021.09+
Mordred: 1.2.0+
Chemprop: 1.4.0+
AutoGluon: 0.4.0+
Scikit-learn: 1.0+
NumPy: 1.21+
Pandas: 1.3+
```

### B. 计算资源规格
- **CPU**: 最少8核，推荐16核
- **内存**: 最少32GB，推荐64GB
- **GPU**: NVIDIA RTX 3080或同等性能
- **存储**: 至少100GB可用空间

### C. 时间预算
- 数据预处理: 1-2天
- 特征计算: 1天
- 模型训练: 3-5天
- 评估分析: 1-2天
- 总计: 约1-2周

---

*协议版本: v1.0*
*创建日期: 2024-01-XX*
*最后审核: 2024-01-XX*
*负责人: [研究团队]* 