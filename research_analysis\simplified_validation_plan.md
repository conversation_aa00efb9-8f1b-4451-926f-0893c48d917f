# 神农框架简化验证计划 - 面向药物筛选应用

**作者**: ZK  
**邮箱**: <EMAIL>  
**日期**: 2025-06-30  

## 🎯 简化目标

**核心假设**: 神农框架 (图+Mordred+注意力) 在抗菌化合物分类任务上优于ChemProp和AutoGluon  
**应用目标**: 证明框架在药物筛选中的实用价值  
**对比范围**: 主要与ChemProp和AutoGluon对比，减少复杂的SOTA方法对比  

## 🔬 简化实验设计

### 核心对比组 (仅3个)
```python
comparison_groups = {
    'ChemProp': 'D-MPNN图神经网络 (成熟基线)',
    'AutoGluon': '自动机器学习 (Mordred特征)',
    'Shennong': '图+Mordred+注意力融合 (我们的方法)'
}
```

### 主要评估任务
```python
# 分类任务为主
tasks = {
    'binary_classification': {
        'task': '活性/非活性二分类',
        'threshold': 'MIC < 16 μg/mL',
        'metrics': ['AUC', 'Precision', 'Recall', 'F1']
    },
    'multi_class': {
        'task': '活性等级分类',
        'classes': ['高活性', '中活性', '低活性', '无活性'],
        'metrics': ['Accuracy', 'Macro-F1', 'Weighted-F1']
    }
}
```

## 📊 实验框架

### 实验1: 基础性能对比 (最重要)
```python
# 数据集
datasets = {
    'primary': 'ChEMBL抗菌活性数据 (二分类)',
    'validation': '独立测试集 (如果有的话)'
}

# 评估指标 (分类为主)
metrics = {
    'classification': ['AUC', 'Precision', 'Recall', 'F1', 'Accuracy'],
    'ranking': ['Top-1%', 'Top-5%', 'Top-10%富集因子']  # 药物筛选相关
}

# 统计要求 (简化)
statistics = {
    'n_runs': 3,  # 减少到3次运行
    'cv_folds': 5,  # 5折交叉验证
    'significance_test': 'McNemar检验'  # 分类任务的配对检验
}
```

### 实验2: 泛化能力验证
```python
# 泛化测试
generalization_tests = {
    'cross_target': '不同细菌靶点的泛化',
    'scaffold_split': '分子骨架划分测试',
    'time_split': '时间外推测试 (如果数据允许)'
}
```

### 实验3: 药物筛选模拟
```python
# 虚拟筛选评估
virtual_screening = {
    'database': '大型化合物库 (如ZINC)',
    'known_actives': '已知活性化合物',
    'evaluation': '富集因子、命中率、早期识别能力'
}
```

## 🛠️ 简化实现方案

### 数据准备 (1周)
```bash
# 简化的数据流程
1. 收集ChEMBL抗菌数据
2. 二分类标签生成 (MIC阈值)
3. 使用现有的generate_features.py生成Mordred特征
4. 标准化训练/测试划分
```

### 模型实现 (1周)
```python
# 只需实现3个模型
models = {
    'ChemProp': '使用官方ChemProp包',
    'AutoGluon': '使用AutoGluon + Mordred特征',
    'Shennong': '修改现有框架支持分类任务'
}
```

### 评估流程 (1周)
```python
# 简化的评估流程
evaluation_pipeline = {
    'step1': '3次独立运行',
    'step2': '5折交叉验证',
    'step3': '性能指标计算',
    'step4': '统计显著性检验',
    'step5': '虚拟筛选模拟'
}
```

## 📈 预期结果与成功标准

### 最低成功标准
```python
success_criteria = {
    'performance': 'AUC > ChemProp + 0.02 且有统计显著性',
    'consistency': '在所有测试中都不显著差于基线',
    'practical': '虚拟筛选富集因子 > 2.0'
}
```

### 理想结果
```python
ideal_results = {
    'performance': 'AUC > ChemProp + 0.05',
    'generalization': '在跨靶点测试中保持优势',
    'efficiency': '预计算特征带来的速度优势'
}
```

## 🎯 药物筛选应用验证

### 实际应用场景模拟
```python
drug_screening_simulation = {
    'scenario': '从100万化合物中筛选抗菌候选物',
    'evaluation': {
        'top_1000_precision': 'Top 1000中真实活性化合物比例',
        'enrichment_factor': '相对随机选择的富集倍数',
        'computational_cost': '筛选100万化合物的计算时间'
    }
}
```

### 实用性指标
```python
practical_metrics = {
    'speed': '每秒处理化合物数量',
    'memory': '内存使用量',
    'scalability': '扩展到更大数据库的能力',
    'interpretability': '预测结果的可解释性'
}
```

## 📝 简化论文结构

### 论文重点
```markdown
1. Introduction (25%)
   - 药物筛选的挑战
   - 多模态学习的必要性
   - 我们方法的核心贡献

2. Methods (25%)
   - 神农框架架构
   - 注意力融合机制
   - 与ChemProp/AutoGluon的对比

3. Results (35%)
   - 分类性能对比
   - 泛化能力验证
   - 虚拟筛选模拟

4. Discussion (15%)
   - 实际应用价值
   - 局限性讨论
   - 未来工作方向
```

### 关键图表 (仅4-5个)
```python
key_figures = {
    'fig1': '神农框架架构图',
    'fig2': '性能对比 (AUC, F1等)',
    'fig3': '虚拟筛选富集曲线',
    'fig4': '注意力权重可视化',
    'fig5': '计算效率对比 (可选)'
}
```

## ⏰ 简化时间线 (6周总计)

| 周次 | 主要任务 | 产出 |
|------|----------|------|
| Week 1 | 数据准备 + ChemProp基线 | 标准数据集 + 基线结果 |
| Week 2 | AutoGluon基线 + 神农框架分类版本 | 3个模型的初步结果 |
| Week 3 | 完整对比实验 + 统计分析 | 核心实验结果 |
| Week 4 | 泛化测试 + 虚拟筛选模拟 | 应用价值验证 |
| Week 5 | 结果分析 + 图表制作 | 完整实验结果 |
| Week 6 | 论文撰写 | 初稿完成 |

## 🔧 技术实现要点

### 神农框架分类适配
```python
# 需要的主要修改
modifications = {
    'loss_function': 'BCELoss或CrossEntropyLoss',
    'output_layer': 'Sigmoid或Softmax',
    'metrics': '分类指标替换回归指标',
    'data_loader': '支持分类标签'
}
```

### ChemProp集成
```python
# 使用官方ChemProp
chemprop_setup = {
    'installation': 'pip install chemprop',
    'data_format': '转换为ChemProp格式',
    'training': '使用相同的训练/测试划分',
    'evaluation': '统一的评估指标'
}
```

### AutoGluon集成
```python
# 使用AutoGluon + Mordred
autogluon_setup = {
    'features': '使用generate_features.py的Mordred特征',
    'model': 'AutoGluon TabularPredictor',
    'config': '默认配置，公平对比'
}
```

## 🎯 关键成功因素

### 1. 实验公平性
- 相同的数据划分
- 相同的评估指标
- 相同的硬件环境

### 2. 实用性导向
- 关注药物筛选相关指标
- 强调计算效率优势
- 提供实际应用案例

### 3. 结果可信度
- 多次运行验证
- 统计显著性检验
- 诚实报告局限性

## 💡 风险缓解

### 主要风险
1. **改进幅度有限**: 专注于统计显著性和实用价值
2. **泛化能力不足**: 多角度验证，诚实讨论适用范围
3. **计算复杂度**: 强调预计算特征的效率优势

### 应对策略
- 设定合理期望 (AUC提升0.02-0.05)
- 强调方法的工程价值
- 关注药物筛选的实际需求

---

**核心思路**: 简化对比，聚焦应用，用实际的药物筛选场景来证明框架价值，而不是追求复杂的学术对比。
