# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-27
# 描述: 测试3D描述符必要性

"""
测试3D描述符必要性

分析3D描述符在抗菌化合物预测中的价值。
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """主函数"""
    print("🔬 3D描述符必要性分析")
    print("="*60)
    
    try:
        from shennong.utils.descriptor_3d_analyzer import run_3d_analysis
        
        # 运行分析
        report = run_3d_analysis()
        
        # 详细分析结果
        if 'analysis_results' in report:
            analysis = report['analysis_results']
            
            print("\n📊 详细分析结果:")
            print("-" * 40)
            
            # 计算成本分析
            if 'computation_analysis' in analysis:
                comp = analysis['computation_analysis']
                print("💰 计算成本分析:")
                
                if 'time_comparison' in comp:
                    for mol_id, times in comp['time_comparison'].items():
                        if times['time_ratio'] != float('inf'):
                            print(f"  {mol_id}: 3D计算时间是2D的 {times['time_ratio']:.1f}倍")
                
                if 'success_rate_comparison' in comp:
                    print("\n📈 成功率对比:")
                    for mol_id, rates in comp['success_rate_comparison'].items():
                        print(f"  {mol_id}: 2D成功率={rates['2d_success_rate']:.1%}, "
                              f"3D成功率={rates['3d_success_rate']:.1%}")
            
            # 描述符重叠分析
            if 'descriptor_overlap_analysis' in analysis:
                overlap = analysis['descriptor_overlap_analysis']
                print(f"\n🔍 描述符重叠分析:")
                print(f"  3D特有描述符: {overlap.get('unique_3d_count', 0)}个")
                print(f"  总3D描述符: {overlap.get('total_3d_count', 0)}个")
                print(f"  信息增益: {overlap.get('information_gain', 0):.1%}")
                
                if '3d_descriptor_types' in overlap:
                    print("  3D描述符类型分布:")
                    for desc_type, count in overlap['3d_descriptor_types'].items():
                        print(f"    {desc_type}: {count}个")
            
            # 抗菌特异性分析
            if 'antibacterial_specific_analysis' in analysis:
                specificity = analysis['antibacterial_specific_analysis']
                print(f"\n🦠 抗菌特异性分析:")
                
                if 'mechanism_3d_relevance' in specificity:
                    for mechanism, info in specificity['mechanism_3d_relevance'].items():
                        importance = info.get('3d_importance', 'unknown')
                        print(f"  {mechanism}: 3D重要性={importance}")
                        print(f"    描述: {info.get('description', 'N/A')}")
        
        # 最终建议
        print(f"\n🎯 最终建议:")
        recommendations = report.get('analysis_results', {}).get('recommendations', [])
        for i, rec in enumerate(recommendations, 1):
            print(f"{i}. {rec}")
        
        return True
        
    except Exception as e:
        print(f"❌ 3D描述符分析失败: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
