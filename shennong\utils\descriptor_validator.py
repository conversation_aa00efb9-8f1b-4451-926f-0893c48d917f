# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-27
# 描述: 神农框架描述符验证工具

"""
神农框架描述符验证工具

提供Mordred描述符的验证、诊断和修复功能。
"""

from typing import Dict, List, Any, Optional, Tuple
import numpy as np
import logging
from pathlib import Path
import json

logger = logging.getLogger(__name__)

try:
    from rdkit import Chem
    RDKIT_AVAILABLE = True
except ImportError:
    RDKIT_AVAILABLE = False

try:
    from mordred import Calculator, descriptors
    import mordred
    MORDRED_AVAILABLE = True
except ImportError:
    MORDRED_AVAILABLE = False


class DescriptorValidator:
    """
    描述符验证器
    
    验证Mordred描述符的计算正确性和一致性。
    """
    
    def __init__(self):
        """初始化验证器"""
        self.available = MORDRED_AVAILABLE and RDKIT_AVAILABLE
        self.test_molecules = self._get_test_molecules()
        self.validation_results = {}
        
        if not self.available:
            logger.warning("RDKit或Mordred不可用，描述符验证功能受限")
    
    def _get_test_molecules(self) -> List[Tuple[str, str]]:
        """获取测试分子"""
        return [
            ("水", "O"),
            ("甲烷", "C"),
            ("乙醇", "CCO"),
            ("苯", "c1ccccc1"),
            ("阿司匹林", "CC(=O)OC1=CC=CC=C1C(=O)O"),
            ("青霉素G", "CC1([C@@H](N2[C@H](S1)[C@@H](C2=O)NC(=O)CC3=CC=CC=C3)C(=O)O)C"),
            ("环丙沙星", "C1CC1N2C=C(C(=O)C3=CC(=C(C=C32)N4CCNCC4)F)C(=O)O"),
            ("咖啡因", "CN1C=NC2=C1C(=O)N(C(=O)N2C)C"),
        ]
    
    def validate_descriptor_count(self, ignore_3D: bool = True) -> Dict[str, Any]:
        """
        验证描述符数量
        
        Args:
            ignore_3D: 是否忽略3D描述符
            
        Returns:
            验证结果
        """
        if not self.available:
            return {'error': 'Mordred或RDKit不可用'}
        
        try:
            # 创建计算器
            calc = Calculator(descriptors, ignore_3D=ignore_3D)
            actual_count = len(calc.descriptors)
            
            # 获取版本信息
            mordred_version = getattr(mordred, '__version__', 'unknown')
            
            # 分析描述符类别
            descriptor_categories = {}
            for desc in calc.descriptors:
                category = desc.__class__.__module__.split('.')[-1]
                if category not in descriptor_categories:
                    descriptor_categories[category] = 0
                descriptor_categories[category] += 1
            
            result = {
                'actual_count': actual_count,
                'expected_count': 1613,  # 硬编码的期望值
                'matches_expected': actual_count == 1613,
                'mordred_version': mordred_version,
                'ignore_3D': ignore_3D,
                'descriptor_categories': descriptor_categories,
                'category_count': len(descriptor_categories)
            }
            
            if not result['matches_expected']:
                result['recommendation'] = f"建议更新配置中的descriptor_dim从1613改为{actual_count}"
            
            logger.info(f"描述符数量验证: {actual_count} (期望: 1613, 匹配: {result['matches_expected']})")
            
            return result
            
        except Exception as e:
            logger.error(f"描述符数量验证失败: {e}")
            return {'error': str(e)}
    
    def validate_descriptor_calculation(self, max_molecules: int = 5) -> Dict[str, Any]:
        """
        验证描述符计算
        
        Args:
            max_molecules: 最大测试分子数
            
        Returns:
            验证结果
        """
        if not self.available:
            return {'error': 'Mordred或RDKit不可用'}
        
        try:
            calc = Calculator(descriptors, ignore_3D=True)
            
            results = {
                'total_molecules': min(max_molecules, len(self.test_molecules)),
                'successful_molecules': 0,
                'failed_molecules': 0,
                'molecule_results': {},
                'overall_statistics': {
                    'total_descriptors': len(calc.descriptors),
                    'successful_calculations': 0,
                    'failed_calculations': 0,
                    'error_types': {}
                }
            }
            
            for i, (name, smiles) in enumerate(self.test_molecules[:max_molecules]):
                try:
                    mol = Chem.MolFromSmiles(smiles)
                    if mol is None:
                        results['molecule_results'][name] = {
                            'success': False,
                            'error': 'Invalid SMILES'
                        }
                        results['failed_molecules'] += 1
                        continue
                    
                    # 计算描述符
                    desc_values = calc(mol)
                    
                    # 分析结果
                    successful = 0
                    failed = 0
                    error_types = {'none': 0, 'nan': 0, 'inf': 0, 'type_error': 0}
                    
                    for value in desc_values:
                        if value is None:
                            failed += 1
                            error_types['none'] += 1
                        elif isinstance(value, (int, float)):
                            if np.isnan(value):
                                failed += 1
                                error_types['nan'] += 1
                            elif np.isinf(value):
                                failed += 1
                                error_types['inf'] += 1
                            else:
                                successful += 1
                        else:
                            try:
                                float_val = float(value)
                                if np.isnan(float_val) or np.isinf(float_val):
                                    failed += 1
                                    error_types['nan'] += 1
                                else:
                                    successful += 1
                            except:
                                failed += 1
                                error_types['type_error'] += 1
                    
                    success_rate = successful / (successful + failed) if (successful + failed) > 0 else 0
                    
                    results['molecule_results'][name] = {
                        'success': True,
                        'smiles': smiles,
                        'successful_descriptors': successful,
                        'failed_descriptors': failed,
                        'success_rate': success_rate,
                        'error_types': error_types
                    }
                    
                    results['successful_molecules'] += 1
                    results['overall_statistics']['successful_calculations'] += successful
                    results['overall_statistics']['failed_calculations'] += failed
                    
                    # 合并错误类型统计
                    for error_type, count in error_types.items():
                        if error_type not in results['overall_statistics']['error_types']:
                            results['overall_statistics']['error_types'][error_type] = 0
                        results['overall_statistics']['error_types'][error_type] += count
                    
                except Exception as e:
                    results['molecule_results'][name] = {
                        'success': False,
                        'error': str(e)
                    }
                    results['failed_molecules'] += 1
            
            # 计算总体成功率
            total_calcs = results['overall_statistics']['successful_calculations'] + results['overall_statistics']['failed_calculations']
            if total_calcs > 0:
                results['overall_statistics']['overall_success_rate'] = results['overall_statistics']['successful_calculations'] / total_calcs
            else:
                results['overall_statistics']['overall_success_rate'] = 0.0
            
            logger.info(f"描述符计算验证完成: {results['successful_molecules']}/{results['total_molecules']} 分子成功")
            
            return results
            
        except Exception as e:
            logger.error(f"描述符计算验证失败: {e}")
            return {'error': str(e)}
    
    def diagnose_environment(self) -> Dict[str, Any]:
        """诊断环境配置"""
        diagnosis = {
            'rdkit_available': RDKIT_AVAILABLE,
            'mordred_available': MORDRED_AVAILABLE,
            'overall_status': 'healthy' if self.available else 'problematic'
        }
        
        if RDKIT_AVAILABLE:
            try:
                import rdkit
                diagnosis['rdkit_version'] = rdkit.__version__
            except:
                diagnosis['rdkit_version'] = 'unknown'
        
        if MORDRED_AVAILABLE:
            try:
                diagnosis['mordred_version'] = getattr(mordred, '__version__', 'unknown')
            except:
                diagnosis['mordred_version'] = 'unknown'
        
        # 测试基本功能
        if self.available:
            try:
                # 测试简单分子
                mol = Chem.MolFromSmiles("CCO")
                calc = Calculator([descriptors.Weight], ignore_3D=True)
                result = calc(mol)
                diagnosis['basic_calculation'] = 'success'
            except Exception as e:
                diagnosis['basic_calculation'] = f'failed: {e}'
                diagnosis['overall_status'] = 'problematic'
        
        return diagnosis
    
    def generate_report(self, output_path: Optional[str] = None) -> Dict[str, Any]:
        """
        生成完整的验证报告
        
        Args:
            output_path: 报告保存路径
            
        Returns:
            完整报告
        """
        logger.info("生成Mordred描述符验证报告...")
        
        report = {
            'timestamp': str(np.datetime64('now')),
            'environment_diagnosis': self.diagnose_environment(),
            'descriptor_count_validation': self.validate_descriptor_count(ignore_3D=True),
            'descriptor_count_validation_3d': self.validate_descriptor_count(ignore_3D=False),
            'calculation_validation': self.validate_descriptor_calculation(),
            'recommendations': []
        }
        
        # 生成建议
        if not report['environment_diagnosis']['overall_status'] == 'healthy':
            report['recommendations'].append("环境配置存在问题，请检查RDKit和Mordred的安装")
        
        if not report['descriptor_count_validation']['matches_expected']:
            actual = report['descriptor_count_validation']['actual_count']
            report['recommendations'].append(f"描述符数量不匹配，建议更新配置中的descriptor_dim为{actual}")
        
        calc_validation = report['calculation_validation']
        if 'overall_statistics' in calc_validation:
            success_rate = calc_validation['overall_statistics'].get('overall_success_rate', 0)
            if success_rate < 0.9:
                report['recommendations'].append(f"描述符计算成功率较低({success_rate:.2%})，建议检查分子质量或使用更稳定的描述符子集")
        
        # 保存报告
        if output_path:
            try:
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(report, f, indent=2, ensure_ascii=False, default=str)
                logger.info(f"验证报告已保存到: {output_path}")
            except Exception as e:
                logger.error(f"保存报告失败: {e}")
        
        return report


def run_descriptor_validation(output_dir: str = "validation_reports") -> Dict[str, Any]:
    """
    运行完整的描述符验证
    
    Args:
        output_dir: 输出目录
        
    Returns:
        验证报告
    """
    # 创建输出目录
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # 运行验证
    validator = DescriptorValidator()
    report = validator.generate_report(
        output_path=str(output_path / "mordred_validation_report.json")
    )
    
    # 打印摘要
    print("\n" + "="*60)
    print("🔬 Mordred描述符验证报告摘要")
    print("="*60)
    
    env = report['environment_diagnosis']
    print(f"环境状态: {env['overall_status']}")
    print(f"RDKit: {'✅' if env['rdkit_available'] else '❌'} {env.get('rdkit_version', 'N/A')}")
    print(f"Mordred: {'✅' if env['mordred_available'] else '❌'} {env.get('mordred_version', 'N/A')}")
    
    count_val = report['descriptor_count_validation']
    print(f"\n描述符数量验证:")
    print(f"实际数量: {count_val.get('actual_count', 'N/A')}")
    print(f"期望数量: {count_val.get('expected_count', 'N/A')}")
    print(f"匹配状态: {'✅' if count_val.get('matches_expected', False) else '❌'}")
    
    calc_val = report['calculation_validation']
    if 'overall_statistics' in calc_val:
        stats = calc_val['overall_statistics']
        success_rate = stats.get('overall_success_rate', 0)
        print(f"\n计算验证:")
        print(f"成功率: {success_rate:.2%}")
        print(f"成功计算: {stats.get('successful_calculations', 0)}")
        print(f"失败计算: {stats.get('failed_calculations', 0)}")
    
    if report['recommendations']:
        print(f"\n💡 建议:")
        for i, rec in enumerate(report['recommendations'], 1):
            print(f"{i}. {rec}")
    
    print("="*60)
    
    return report
