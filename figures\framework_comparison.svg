<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="600" viewBox="0 0 1400 600" xmlns="http://www.w3.org/2000/svg">
  <!-- 定义样式 -->
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="shennongGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#E8F5E8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#C8E6C9;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="chempropGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#E3F2FD;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#BBDEFB;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="otherGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFF3E0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFE0B2;stop-opacity:1" />
    </linearGradient>
    
    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="3" dy="3" stdDeviation="4" flood-color="#000000" flood-opacity="0.25"/>
    </filter>
  </defs>
  
  <!-- 标题 -->
  <text x="700" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#1B5E20">
    Framework Comparison: Shennong vs. Existing Methods
  </text>
  
  <!-- Shennong框架 -->
  <g id="shennong-framework">
    <rect x="50" y="80" width="400" height="450" rx="15" fill="url(#shennongGrad)" stroke="#2E7D32" stroke-width="3" filter="url(#shadow)"/>
    <text x="250" y="110" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#1B5E20">Shennong Framework</text>
    <text x="250" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#2E7D32">(This Work)</text>
    
    <!-- 特性列表 -->
    <text x="70" y="160" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#1B5E20">Key Features:</text>
    
    <circle cx="80" cy="180" r="3" fill="#4CAF50"/>
    <text x="95" y="185" font-family="Arial, sans-serif" font-size="10" fill="#333">Dual-modal architecture (GNN + Expert features)</text>
    
    <circle cx="80" cy="200" r="3" fill="#4CAF50"/>
    <text x="95" y="205" font-family="Arial, sans-serif" font-size="10" fill="#333">Chemistry-guided multi-head attention</text>
    
    <circle cx="80" cy="220" r="3" fill="#4CAF50"/>
    <text x="95" y="225" font-family="Arial, sans-serif" font-size="10" fill="#333">Focus on antibacterial activity prediction</text>
    
    <circle cx="80" cy="240" r="3" fill="#4CAF50"/>
    <text x="95" y="245" font-family="Arial, sans-serif" font-size="10" fill="#333">Multi-level chemical interpretability</text>
    
    <circle cx="80" cy="260" r="3" fill="#4CAF50"/>
    <text x="95" y="265" font-family="Arial, sans-serif" font-size="10" fill="#333">1800+ Mordred descriptors integration</text>
    
    <circle cx="80" cy="280" r="3" fill="#4CAF50"/>
    <text x="95" y="285" font-family="Arial, sans-serif" font-size="10" fill="#333">Uncertainty quantification</text>
    
    <circle cx="80" cy="300" r="3" fill="#4CAF50"/>
    <text x="95" y="305" font-family="Arial, sans-serif" font-size="10" fill="#333">Molecular optimization guidance</text>
    
    <!-- 创新点 -->
    <text x="70" y="340" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#1B5E20">Innovations:</text>
    
    <rect x="80" y="350" width="15" height="10" fill="#FF8F00"/>
    <text x="105" y="360" font-family="Arial, sans-serif" font-size="10" fill="#333">Functional group attention heads</text>
    
    <rect x="80" y="370" width="15" height="10" fill="#FF8F00"/>
    <text x="105" y="380" font-family="Arial, sans-serif" font-size="10" fill="#333">Molecular property attention heads</text>
    
    <rect x="80" y="390" width="15" height="10" fill="#FF8F00"/>
    <text x="105" y="400" font-family="Arial, sans-serif" font-size="10" fill="#333">Activity-related attention heads</text>
    
    <rect x="80" y="410" width="15" height="10" fill="#FF8F00"/>
    <text x="105" y="420" font-family="Arial, sans-serif" font-size="10" fill="#333">Chemist-friendly explanations</text>
    
    <rect x="80" y="430" width="15" height="10" fill="#FF8F00"/>
    <text x="105" y="440" font-family="Arial, sans-serif" font-size="10" fill="#333">No mechanism speculation</text>
    
    <!-- 性能指标 -->
    <text x="70" y="470" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#1B5E20">Performance:</text>
    <text x="80" y="490" font-family="Arial, sans-serif" font-size="10" fill="#333">• R² > 0.85 on antibacterial datasets</text>
    <text x="80" y="505" font-family="Arial, sans-serif" font-size="10" fill="#333">• High interpretability score</text>
    <text x="80" y="520" font-family="Arial, sans-serif" font-size="10" fill="#333">• Robust uncertainty estimation</text>
  </g>
  
  <!-- ChemProp -->
  <g id="chemprop-framework">
    <rect x="500" y="80" width="400" height="450" rx="15" fill="url(#chempropGrad)" stroke="#1976D2" stroke-width="3" filter="url(#shadow)"/>
    <text x="700" y="110" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#0D47A1">ChemProp</text>
    <text x="700" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#1976D2">(Yang et al., 2019)</text>
    
    <!-- 特性列表 -->
    <text x="520" y="160" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#0D47A1">Key Features:</text>
    
    <circle cx="530" cy="180" r="3" fill="#2196F3"/>
    <text x="545" y="185" font-family="Arial, sans-serif" font-size="10" fill="#333">Message-passing neural networks</text>
    
    <circle cx="530" cy="200" r="3" fill="#2196F3"/>
    <text x="545" y="205" font-family="Arial, sans-serif" font-size="10" fill="#333">Graph-based molecular representation</text>
    
    <circle cx="530" cy="220" r="3" fill="#2196F3"/>
    <text x="545" y="225" font-family="Arial, sans-serif" font-size="10" fill="#333">General molecular property prediction</text>
    
    <circle cx="530" cy="240" r="3" fill="#2196F3"/>
    <text x="545" y="245" font-family="Arial, sans-serif" font-size="10" fill="#333">Basic attention mechanism</text>
    
    <circle cx="530" cy="260" r="3" fill="#2196F3"/>
    <text x="545" y="265" font-family="Arial, sans-serif" font-size="10" fill="#333">Transfer learning support</text>
    
    <!-- 限制 -->
    <text x="520" y="300" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#D32F2F">Limitations:</text>
    
    <text x="530" y="320" font-family="Arial, sans-serif" font-size="10" fill="#333">✗ Single-modal (graph only)</text>
    <text x="530" y="335" font-family="Arial, sans-serif" font-size="10" fill="#333">✗ Limited interpretability</text>
    <text x="530" y="350" font-family="Arial, sans-serif" font-size="10" fill="#333">✗ No domain-specific attention</text>
    <text x="530" y="365" font-family="Arial, sans-serif" font-size="10" fill="#333">✗ No expert feature integration</text>
    <text x="530" y="380" font-family="Arial, sans-serif" font-size="10" fill="#333">✗ Generic molecular representation</text>
    
    <!-- 性能指标 -->
    <text x="520" y="410" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#0D47A1">Performance:</text>
    <text x="530" y="430" font-family="Arial, sans-serif" font-size="10" fill="#333">• R² ≈ 0.75-0.80 on general datasets</text>
    <text x="530" y="445" font-family="Arial, sans-serif" font-size="10" fill="#333">• Limited antibacterial specialization</text>
    <text x="530" y="460" font-family="Arial, sans-serif" font-size="10" fill="#333">• Basic attention visualization</text>
    <text x="530" y="475" font-family="Arial, sans-serif" font-size="10" fill="#333">• No uncertainty quantification</text>
    <text x="530" y="490" font-family="Arial, sans-serif" font-size="10" fill="#333">• Widely adopted baseline</text>
  </g>
  
  <!-- 其他方法 -->
  <g id="other-methods">
    <rect x="950" y="80" width="400" height="450" rx="15" fill="url(#otherGrad)" stroke="#F57C00" stroke-width="3" filter="url(#shadow)"/>
    <text x="1150" y="110" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#E65100">Other Methods</text>
    <text x="1150" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#F57C00">(Literature Survey)</text>
    
    <!-- 方法列表 -->
    <text x="970" y="160" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#E65100">Representative Methods:</text>
    
    <!-- MPNN变体 -->
    <rect x="980" y="175" width="100" height="25" rx="3" fill="#FFCC80" stroke="#FF9800" stroke-width="1"/>
    <text x="1030" y="192" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#333">MPNN Variants</text>
    
    <rect x="1090" y="175" width="100" height="25" rx="3" fill="#FFCC80" stroke="#FF9800" stroke-width="1"/>
    <text x="1140" y="192" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#333">Graph Transformers</text>
    
    <rect x="1200" y="175" width="100" height="25" rx="3" fill="#FFCC80" stroke="#FF9800" stroke-width="1"/>
    <text x="1250" y="192" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#333">Dual-Modal GNNs</text>
    
    <!-- 特点对比 -->
    <text x="970" y="230" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#E65100">Common Characteristics:</text>
    
    <text x="980" y="250" font-family="Arial, sans-serif" font-size="10" fill="#333">• Graph neural network based</text>
    <text x="980" y="265" font-family="Arial, sans-serif" font-size="10" fill="#333">• General molecular property prediction</text>
    <text x="980" y="280" font-family="Arial, sans-serif" font-size="10" fill="#333">• Limited domain specialization</text>
    <text x="980" y="295" font-family="Arial, sans-serif" font-size="10" fill="#333">• Basic interpretability methods</text>
    
    <!-- 缺点 -->
    <text x="970" y="325" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#D32F2F">Common Limitations:</text>
    
    <text x="980" y="345" font-family="Arial, sans-serif" font-size="10" fill="#333">✗ No chemistry-guided attention</text>
    <text x="980" y="360" font-family="Arial, sans-serif" font-size="10" fill="#333">✗ Limited expert knowledge integration</text>
    <text x="980" y="375" font-family="Arial, sans-serif" font-size="10" fill="#333">✗ Poor interpretability for chemists</text>
    <text x="980" y="390" font-family="Arial, sans-serif" font-size="10" fill="#333">✗ No molecular optimization guidance</text>
    <text x="980" y="405" font-family="Arial, sans-serif" font-size="10" fill="#333">✗ Generic attention mechanisms</text>
    
    <!-- 性能范围 -->
    <text x="970" y="435" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#E65100">Performance Range:</text>
    <text x="980" y="455" font-family="Arial, sans-serif" font-size="10" fill="#333">• R² ≈ 0.70-0.82 (dataset dependent)</text>
    <text x="980" y="470" font-family="Arial, sans-serif" font-size="10" fill="#333">• Variable interpretability quality</text>
    <text x="980" y="485" font-family="Arial, sans-serif" font-size="10" fill="#333">• Limited uncertainty estimation</text>
    <text x="980" y="500" font-family="Arial, sans-serif" font-size="10" fill="#333">• Inconsistent validation protocols</text>
  </g>
  
  <!-- 对比总结 -->
  <g id="comparison-summary">
    <rect x="50" y="550" width="1300" height="40" rx="5" fill="#F5F5F5" stroke="#BDBDBD" stroke-width="1"/>
    <text x="700" y="570" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">Key Advantages of Shennong Framework</text>
    <text x="700" y="585" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#666">
      ✓ Chemistry-guided attention ✓ Dual-modal architecture ✓ Antibacterial specialization ✓ Multi-level interpretability ✓ Optimization guidance
    </text>
  </g>
  
</svg>
