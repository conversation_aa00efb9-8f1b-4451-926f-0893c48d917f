# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-27
# 描述: 神农框架主包初始化文件

"""
神农框架 (Shennong Framework)
基于Chemprop v2.0的抗菌化合物预测系统

神农框架是一个专门用于抗菌化合物活性预测的端到端深度学习系统。
框架集成了分子图神经网络(GNN)和专家特征分支，通过生物启发的注意力机制实现SOTA性能。

主要模块:
- data: 数据处理和加载
- models: 神经网络模型定义
- nn: 神经网络组件
- training: 训练和优化
- antibacterial: 抗菌专业模块
- utils: 工具函数

示例用法:
    >>> from shennong import ShennongFramework
    >>> model = ShennongFramework.load_pretrained('antibacterial_v1.0')
    >>> predictions = model.predict(['CCN(CC)CCCC(C)NC1=C2C=CC(=CC2=NC=C1)CF3'])
"""

__version__ = "1.0.0"
__author__ = "ZK"
__email__ = "<EMAIL>"

# 核心模块导入
from .models.shennong_core import ShennongFramework
from .data.datasets import AntibacterialDataset, ShennongDatapoint
from .training.trainer import ShennongTrainer
from .utils.config import ShennongConfig

# 抗菌专业模块
from .antibacterial.mechanisms import ANTIBACTERIAL_MECHANISMS
from .antibacterial.interpretation import BiologicalInterpreter

# CLI模块
from .cli.main import main as cli_main

# 版本兼容性检查
def check_dependencies():
    """检查关键依赖的版本兼容性"""
    import warnings

    try:
        import torch
        if torch.__version__ < "2.0.0":
            warnings.warn(
                f"PyTorch版本 {torch.__version__} 可能不兼容，推荐使用 >= 2.0.0",
                UserWarning
            )
    except ImportError:
        raise ImportError("PyTorch未安装，请运行: pip install torch>=2.0.0")

    try:
        import chemprop
        if chemprop.__version__ < "2.0.0":
            warnings.warn(
                f"Chemprop版本 {chemprop.__version__} 可能不兼容，推荐使用 >= 2.0.0",
                UserWarning
            )
    except ImportError:
        raise ImportError(
            "Chemprop未安装，请运行: pip install git+https://github.com/chemprop/chemprop.git@v2.0.0"
        )

    try:
        import rdkit
    except ImportError:
        raise ImportError("RDKit未安装，请运行: pip install rdkit-pypi")

# 自动检查依赖
check_dependencies()

# 公开API
__all__ = [
    # 核心类
    'ShennongFramework',
    'AntibacterialDataset',
    'ShennongDatapoint',
    'ShennongTrainer',
    'ShennongConfig',

    # 抗菌专业模块
    'ANTIBACTERIAL_MECHANISMS',
    'BiologicalInterpreter',

    # 版本信息
    '__version__',
    '__author__',
    '__email__',
]

# 设置日志
import logging
logging.getLogger(__name__).addHandler(logging.NullHandler())

# 欢迎信息
def print_welcome():
    """打印欢迎信息"""
    print(f"""
🧬 神农框架 (Shennong Framework) v{__version__}
基于Chemprop v2.0的抗菌化合物预测系统

作者: {__author__}
邮箱: {__email__}

神农尝百草，AI识良药 🌿
""")

# 可选的欢迎信息显示
import os
if os.getenv('SHENNONG_SHOW_WELCOME', '').lower() in ('true', '1', 'yes'):
    print_welcome()
