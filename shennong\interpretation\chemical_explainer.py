# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-29
# 描述: 神农框架化学可解释性主模块

"""
神农框架化学可解释性主模块

整合注意力机制、描述符分析和机制解释，为化学家提供全面的AI预测解释。
"""

import logging
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from rdkit import Chem
from rdkit.Chem import Descriptors, rdMolDescriptors

from .attention_interpreter import AttentionInterpreter
from .descriptor_analyzer import DescriptorAnalyzer
from .pharmacophore_analyzer import PharmacophoreAnalyzer

logger = logging.getLogger(__name__)


@dataclass
class ChemicalExplanation:
    """
    化学解释结果数据类

    专注于抗菌活性预测的化学解释，包含原子贡献、基团识别、
    分子性质分析等化学家友好的解释信息。
    """
    smiles: str
    predicted_activity: float
    confidence: float

    # 注意力分析 - 识别关键原子和基团
    attention_analysis: Dict[str, Any]

    # 描述符分析 - 重要分子性质分析
    descriptor_analysis: Dict[str, Any]

    # 药效团分析 - 关键药效团特征
    pharmacophore_analysis: Dict[str, Any]

    # 综合解释摘要
    summary: Dict[str, Any]


class ChemicalExplainer:
    """
    化学可解释性分析器

    整合多种解释方法，为化学家提供AI预测的全面化学解释。
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化化学解释器

        Args:
            config: 配置参数
        """
        self.config = config or {}

        # 初始化子模块
        self.attention_interpreter = AttentionInterpreter(
            self.config.get('attention_config', {})
        )
        self.descriptor_analyzer = DescriptorAnalyzer(
            self.config.get('descriptor_config', {})
        )
        self.pharmacophore_analyzer = PharmacophoreAnalyzer(
            self.config.get('pharmacophore_config', {})
        )

        logger.info("化学可解释性分析器初始化完成")

    def explain_prediction(
        self,
        smiles: str,
        predicted_activity: float,
        attention_weights: Dict[str, np.ndarray],
        descriptor_values: np.ndarray,
        descriptor_names: List[str],
        confidence: float = 0.0
    ) -> ChemicalExplanation:
        """
        生成完整的化学解释

        Args:
            smiles: 分子SMILES
            predicted_activity: 预测活性 (MIC, μg/mL)
            attention_weights: 注意力权重字典
            descriptor_values: 描述符值数组
            descriptor_names: 描述符名称列表
            confidence: 预测置信度

        Returns:
            完整的化学解释，包含原子贡献、基团识别、性质分析等
        """
        logger.info(f"开始分析分子: {smiles}")

        # 1. 注意力权重解释 - 识别关键原子和基团
        attention_analysis = self.attention_interpreter.interpret_attention(
            smiles, attention_weights
        )

        # 2. 描述符分析 - 分析重要的分子性质
        descriptor_analysis = self.descriptor_analyzer.analyze_descriptors(
            smiles, descriptor_values, descriptor_names, predicted_activity
        )

        # 3. 药效团分析 - 识别关键药效团特征
        pharmacophore_analysis = self.pharmacophore_analyzer.analyze_pharmacophores(
            smiles, attention_weights
        )

        # 4. 生成综合解释
        summary = self._generate_summary(
            smiles, predicted_activity,
            attention_analysis, descriptor_analysis, pharmacophore_analysis
        )

        return ChemicalExplanation(
            smiles=smiles,
            predicted_activity=predicted_activity,
            confidence=confidence,
            attention_analysis=attention_analysis,
            descriptor_analysis=descriptor_analysis,
            pharmacophore_analysis=pharmacophore_analysis,
            summary=summary
        )

    def _generate_summary(
        self,
        smiles: str,
        predicted_activity: float,
        attention_analysis: Dict[str, Any],
        descriptor_analysis: Dict[str, Any],
        pharmacophore_analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        生成综合解释摘要

        专注于抗菌活性相关的化学特征分析，不包含机制预测。
        """

        # 计算分子基本性质
        mol = Chem.MolFromSmiles(smiles)
        if mol is None:
            return {'error': '无效的SMILES结构'}

        basic_properties = {
            'molecular_weight': Descriptors.MolWt(mol),
            'logp': Descriptors.MolLogP(mol),
            'hbd': rdMolDescriptors.CalcNumHBD(mol),
            'hba': rdMolDescriptors.CalcNumHBA(mol),
            'tpsa': rdMolDescriptors.CalcTPSA(mol),
            'rotatable_bonds': rdMolDescriptors.CalcNumRotatableBonds(mol),
            'aromatic_rings': rdMolDescriptors.CalcNumAromaticRings(mol)
        }

        # 活性等级评估
        activity_level = self._classify_activity(predicted_activity)

        # 关键发现汇总
        key_findings = []

        # 从注意力分析提取关键信息
        if attention_analysis.get('key_regions'):
            key_findings.extend([
                f"模型重点关注{region['description']}"
                for region in attention_analysis['key_regions'][:3]
            ])

        # 从描述符分析提取关键信息
        if descriptor_analysis.get('important_descriptors'):
            top_descriptors = descriptor_analysis['important_descriptors'][:3]
            key_findings.extend([
                f"{desc['name']}({desc['category']})对活性有重要影响"
                for desc in top_descriptors
            ])

        # 从药效团分析提取关键信息
        if pharmacophore_analysis.get('identified_pharmacophores'):
            key_findings.append(
                f"识别出{len(pharmacophore_analysis['identified_pharmacophores'])}个重要药效团特征"
            )

        # 药物相似性评估
        drug_likeness = self._assess_drug_likeness(basic_properties)

        # 生成化学家友好的解释
        chemical_interpretation = self._generate_chemical_interpretation(
            basic_properties, activity_level,
            attention_analysis, descriptor_analysis
        )

        return {
            'basic_properties': basic_properties,
            'activity_level': activity_level,
            'drug_likeness': drug_likeness,
            'key_findings': key_findings,
            'chemical_interpretation': chemical_interpretation,
            'confidence_factors': self._identify_confidence_factors(
                attention_analysis, descriptor_analysis, mechanism_analysis
            )
        }

    def _classify_activity(self, activity: float) -> Dict[str, Any]:
        """分类活性水平"""
        if activity <= 1.0:
            level = "高活性"
            description = "具有强抗菌活性，MIC ≤ 1 μg/mL"
        elif activity <= 4.0:
            level = "中等活性"
            description = "具有中等抗菌活性，1 < MIC ≤ 4 μg/mL"
        elif activity <= 16.0:
            level = "低活性"
            description = "具有较弱抗菌活性，4 < MIC ≤ 16 μg/mL"
        else:
            level = "无活性"
            description = "抗菌活性很弱或无活性，MIC > 16 μg/mL"

        return {
            'level': level,
            'description': description,
            'value': activity,
            'clinical_relevance': level in ["高活性", "中等活性"]
        }

    def _assess_drug_likeness(self, properties: Dict[str, float]) -> Dict[str, Any]:
        """评估药物相似性"""
        violations = []

        # Lipinski五规则
        if properties['molecular_weight'] > 500:
            violations.append("分子量过大 (>500 Da)")
        if properties['logp'] > 5:
            violations.append("脂溶性过高 (LogP >5)")
        if properties['hbd'] > 5:
            violations.append("氢键供体过多 (>5)")
        if properties['hba'] > 10:
            violations.append("氢键受体过多 (>10)")

        # 其他药物相似性规则
        if properties['tpsa'] > 140:
            violations.append("极性表面积过大 (>140 Ų)")
        if properties['rotatable_bonds'] > 10:
            violations.append("可旋转键过多 (>10)")

        drug_like = len(violations) <= 1  # 允许违反一个规则

        return {
            'is_drug_like': drug_like,
            'violations': violations,
            'lipinski_score': 5 - min(4, len([v for v in violations if any(
                keyword in v for keyword in ['分子量', '脂溶性', '氢键供体', '氢键受体']
            )]))
        }

    def _generate_chemical_interpretation(
        self,
        properties: Dict[str, float],
        activity_level: Dict[str, Any],
        attention_analysis: Dict[str, Any],
        descriptor_analysis: Dict[str, Any]
    ) -> str:
        """生成化学家友好的解释文本"""

        interpretation_parts = []

        # 基本性质描述
        mw = properties['molecular_weight']
        logp = properties['logp']

        interpretation_parts.append(
            f"该化合物分子量为{mw:.1f} Da，LogP为{logp:.2f}，"
            f"表现出{activity_level['level']}。"
        )

        # 活性相关解释
        if activity_level['level'] == '高活性':
            interpretation_parts.append("具有强抗菌活性，MIC ≤ 1 μg/mL。")
        elif activity_level['level'] == '中等活性':
            interpretation_parts.append("具有中等抗菌活性，1 < MIC ≤ 4 μg/mL。")
        else:
            interpretation_parts.append("抗菌活性较弱，MIC > 4 μg/mL。")

        # 结构特征解释
        if attention_analysis.get('key_regions'):
            key_region = attention_analysis['key_regions'][0]
            interpretation_parts.append(
                f"模型识别出{key_region['description']}是关键的活性区域。"
            )

        # 描述符解释
        if descriptor_analysis.get('important_descriptors'):
            top_desc = descriptor_analysis['important_descriptors'][0]
            interpretation_parts.append(
                f"{top_desc['name']}是影响活性的重要分子性质。"
            )

        return " ".join(interpretation_parts)

    def _identify_confidence_factors(
        self,
        attention_analysis: Dict[str, Any],
        descriptor_analysis: Dict[str, Any],
        mechanism_analysis: Dict[str, Any]
    ) -> Dict[str, List[str]]:
        """识别影响预测置信度的因素"""

        positive_factors = []
        negative_factors = []

        # 注意力一致性
        if attention_analysis.get('attention_consistency', 0) > 0.7:
            positive_factors.append("注意力权重分布一致")
        else:
            negative_factors.append("注意力权重分布不一致")

        # 描述符质量
        if descriptor_analysis.get('descriptor_quality', 0) > 0.8:
            positive_factors.append("分子描述符质量良好")
        else:
            negative_factors.append("部分描述符计算异常")

        # 机制匹配度
        if mechanism_analysis.get('mechanism_confidence', 0) > 0.6:
            positive_factors.append("结构特征与预测机制匹配")
        else:
            negative_factors.append("结构特征与预测机制匹配度较低")

        return {
            'positive_factors': positive_factors,
            'negative_factors': negative_factors
        }

    def generate_report(self, explanation: ChemicalExplanation) -> str:
        """生成化学家友好的分析报告"""

        report_lines = []

        # 标题
        report_lines.append("🧬 神农框架抗菌活性预测解释报告")
        report_lines.append("=" * 50)

        # 基本信息
        report_lines.append(f"分子SMILES: {explanation.smiles}")
        report_lines.append(f"预测活性: {explanation.predicted_activity:.2f} μg/mL")
        report_lines.append(f"预测置信度: {explanation.confidence:.2%}")
        report_lines.append("")

        # 分子性质
        props = explanation.summary['basic_properties']
        report_lines.append("📊 分子性质:")
        report_lines.append(f"  分子量: {props['molecular_weight']:.1f} Da")
        report_lines.append(f"  LogP: {props['logp']:.2f}")
        report_lines.append(f"  氢键供体: {props['hbd']}")
        report_lines.append(f"  氢键受体: {props['hba']}")
        report_lines.append(f"  极性表面积: {props['tpsa']:.1f} Ų")
        report_lines.append("")

        # 活性评估
        activity = explanation.summary['activity_level']
        report_lines.append(f"🎯 活性评估: {activity['level']}")
        report_lines.append(f"  {activity['description']}")
        report_lines.append("")

        # 药物相似性
        drug_like = explanation.summary['drug_likeness']
        report_lines.append(f"💊 药物相似性: {'符合' if drug_like['is_drug_like'] else '不符合'}")
        if drug_like['violations']:
            report_lines.append("  违反规则:")
            for violation in drug_like['violations']:
                report_lines.append(f"    - {violation}")
        report_lines.append("")

        # 关键发现
        report_lines.append("🔍 关键发现:")
        for finding in explanation.summary['key_findings']:
            report_lines.append(f"  • {finding}")
        report_lines.append("")

        # 化学解释
        report_lines.append("🧪 化学解释:")
        report_lines.append(f"  {explanation.summary['chemical_interpretation']}")
        report_lines.append("")

        # 置信度因素
        conf_factors = explanation.summary['confidence_factors']
        if conf_factors['positive_factors']:
            report_lines.append("✅ 支持因素:")
            for factor in conf_factors['positive_factors']:
                report_lines.append(f"  • {factor}")

        if conf_factors['negative_factors']:
            report_lines.append("⚠️ 不确定因素:")
            for factor in conf_factors['negative_factors']:
                report_lines.append(f"  • {factor}")

        return "\n".join(report_lines)


def create_chemical_explainer(config: Optional[Dict[str, Any]] = None) -> ChemicalExplainer:
    """
    创建化学解释器的工厂函数

    Args:
        config: 配置参数

    Returns:
        化学解释器实例
    """
    return ChemicalExplainer(config)
