<?xml version="1.0" encoding="UTF-8"?>
<svg width="1000" height="700" viewBox="0 0 1000 700" xmlns="http://www.w3.org/2000/svg">
  <!-- 定义样式 -->
  <defs>
    <!-- 渐变定义 -->
    <radialGradient id="attentionGrad" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#FCE4EC;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F8BBD9;stop-opacity:1" />
    </radialGradient>
    
    <linearGradient id="headGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#E1F5FE;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#B3E5FC;stop-opacity:1" />
    </linearGradient>
    
    <!-- 阴影滤镜 -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <!-- 箭头标记 -->
    <marker id="attentionArrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
      <polygon points="0 0, 8 3, 0 6" fill="#E91E63" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="500" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#880E4F">
    Chemistry-Guided Multi-Head Attention Mechanism
  </text>
  
  <!-- 输入特征 -->
  <g id="input-features">
    <rect x="50" y="80" width="120" height="60" rx="8" fill="#E8F5E8" stroke="#4CAF50" stroke-width="2"/>
    <text x="110" y="105" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#2E7D32">Graph Features</text>
    <text x="110" y="120" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#333">[B, N, D_g]</text>
    
    <rect x="200" y="80" width="120" height="60" rx="8" fill="#FFF3E0" stroke="#FF9800" stroke-width="2"/>
    <text x="260" y="105" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#E65100">Expert Features</text>
    <text x="260" y="120" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#333">[B, D_e]</text>
  </g>
  
  <!-- 特征融合 -->
  <rect x="150" y="180" width="100" height="40" rx="5" fill="#F1F8E9" stroke="#689F38" stroke-width="2"/>
  <text x="200" y="205" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#33691E">Feature Fusion</text>
  
  <!-- 化学导向注意力核心 -->
  <g id="attention-core">
    <ellipse cx="500" cy="300" rx="200" ry="120" fill="url(#attentionGrad)" stroke="#AD1457" stroke-width="3" filter="url(#glow)"/>
    <text x="500" y="280" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#880E4F">Chemistry-Guided</text>
    <text x="500" y="300" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#880E4F">Multi-Head</text>
    <text x="500" y="320" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#880E4F">Attention</text>
  </g>
  
  <!-- 官能团注意力头 -->
  <g id="functional-group-heads">
    <text x="150" y="450" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#1976D2">Functional Group Attention</text>
    
    <!-- 氢键基团 -->
    <rect x="50" y="470" width="80" height="50" rx="5" fill="url(#headGrad)" stroke="#2196F3" stroke-width="1"/>
    <text x="90" y="490" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="#0D47A1">H-bond Head</text>
    <text x="90" y="505" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">-OH, -NH, -COOH</text>
    
    <!-- 芳香环 -->
    <rect x="140" y="470" width="80" height="50" rx="5" fill="url(#headGrad)" stroke="#2196F3" stroke-width="1"/>
    <text x="180" y="490" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="#0D47A1">Aromatic Head</text>
    <text x="180" y="505" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">Benzene, Heterocycles</text>
    
    <!-- 卤素 -->
    <rect x="230" y="470" width="80" height="50" rx="5" fill="url(#headGrad)" stroke="#2196F3" stroke-width="1"/>
    <text x="270" y="490" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="#0D47A1">Halogen Head</text>
    <text x="270" y="505" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">F, Cl, Br, I</text>
    
    <!-- 极性基团 -->
    <rect x="320" y="470" width="80" height="50" rx="5" fill="url(#headGrad)" stroke="#2196F3" stroke-width="1"/>
    <text x="360" y="490" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="#0D47A1">Polar Head</text>
    <text x="360" y="505" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">C=O, C=N, S=O</text>
  </g>
  
  <!-- 分子性质注意力头 -->
  <g id="molecular-property-heads">
    <text x="550" y="450" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#388E3C">Molecular Property Attention</text>
    
    <!-- 亲脂性 -->
    <rect x="450" y="470" width="80" height="50" rx="5" fill="#E8F5E8" stroke="#4CAF50" stroke-width="1"/>
    <text x="490" y="490" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="#2E7D32">LogP Head</text>
    <text x="490" y="505" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">Lipophilicity</text>
    
    <!-- 分子大小 -->
    <rect x="540" y="470" width="80" height="50" rx="5" fill="#E8F5E8" stroke="#4CAF50" stroke-width="1"/>
    <text x="580" y="490" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="#2E7D32">Size Head</text>
    <text x="580" y="505" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">MW, Volume</text>
    
    <!-- 柔性 -->
    <rect x="630" y="470" width="80" height="50" rx="5" fill="#E8F5E8" stroke="#4CAF50" stroke-width="1"/>
    <text x="670" y="490" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="#2E7D32">Flex Head</text>
    <text x="670" y="505" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">Rotatable Bonds</text>
    
    <!-- 电荷分布 -->
    <rect x="720" y="470" width="80" height="50" rx="5" fill="#E8F5E8" stroke="#4CAF50" stroke-width="1"/>
    <text x="760" y="490" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="#2E7D32">Charge Head</text>
    <text x="760" y="505" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">Dipole, Partial</text>
  </g>
  
  <!-- 活性相关注意力头 -->
  <g id="activity-related-heads">
    <text x="750" y="580" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#F57C00">Activity-Related Attention</text>
    
    <!-- ADMET -->
    <rect x="650" y="600" width="80" height="50" rx="5" fill="#FFF3E0" stroke="#FF9800" stroke-width="1"/>
    <text x="690" y="620" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="#E65100">ADMET Head</text>
    <text x="690" y="635" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">Absorption, Distribution</text>
    
    <!-- 毒性 -->
    <rect x="740" y="600" width="80" height="50" rx="5" fill="#FFF3E0" stroke="#FF9800" stroke-width="1"/>
    <text x="780" y="620" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="#E65100">Toxicity Head</text>
    <text x="780" y="635" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">Cytotoxicity</text>
    
    <!-- 选择性 -->
    <rect x="830" y="600" width="80" height="50" rx="5" fill="#FFF3E0" stroke="#FF9800" stroke-width="1"/>
    <text x="870" y="620" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="#E65100">Select Head</text>
    <text x="870" y="635" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">Target Specificity</text>
  </g>
  
  <!-- 注意力权重输出 -->
  <rect x="400" y="180" width="200" height="40" rx="5" fill="#F8BBD9" stroke="#E91E63" stroke-width="2"/>
  <text x="500" y="205" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#880E4F">Attention Weights α_ij</text>
  
  <!-- 连接线 -->
  <!-- 输入到融合 -->
  <line x1="110" y1="140" x2="180" y2="180" stroke="#4CAF50" stroke-width="2" marker-end="url(#attentionArrow)"/>
  <line x1="260" y1="140" x2="220" y2="180" stroke="#FF9800" stroke-width="2" marker-end="url(#attentionArrow)"/>
  
  <!-- 融合到注意力核心 -->
  <line x1="200" y1="220" x2="350" y2="280" stroke="#689F38" stroke-width="3" marker-end="url(#attentionArrow)"/>
  
  <!-- 注意力核心到各个头 -->
  <line x1="350" y1="350" x2="200" y2="450" stroke="#E91E63" stroke-width="2" marker-end="url(#attentionArrow)"/>
  <line x1="500" y1="420" x2="550" y2="450" stroke="#E91E63" stroke-width="2" marker-end="url(#attentionArrow)"/>
  <line x1="650" y1="350" x2="750" y2="580" stroke="#E91E63" stroke-width="2" marker-end="url(#attentionArrow)"/>
  
  <!-- 注意力权重输出 -->
  <line x1="500" y1="180" x2="500" y2="180" stroke="#E91E63" stroke-width="2"/>
  
  <!-- 数学公式 -->
  <g id="formulas">
    <rect x="50" y="550" width="350" height="120" rx="5" fill="#F5F5F5" stroke="#BDBDBD" stroke-width="1"/>
    <text x="225" y="575" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#333">Attention Computation</text>
    
    <text x="70" y="595" font-family="Arial, sans-serif" font-size="10" fill="#333">1. Query, Key, Value computation:</text>
    <text x="90" y="610" font-family="Arial, sans-serif" font-size="9" fill="#333">Q = XW_Q, K = XW_K, V = XW_V</text>
    
    <text x="70" y="630" font-family="Arial, sans-serif" font-size="10" fill="#333">2. Chemistry-guided attention weights:</text>
    <text x="90" y="645" font-family="Arial, sans-serif" font-size="9" fill="#333">α_ij = softmax(QK^T / √d_k) ⊙ M_chem</text>
    
    <text x="70" y="660" font-family="Arial, sans-serif" font-size="10" fill="#333">3. Weighted feature aggregation:</text>
    <text x="90" y="675" font-family="Arial, sans-serif" font-size="9" fill="#333">H_out = Σ α_ij V_j</text>
  </g>
  
</svg>
