# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-27
# 描述: 神农框架特征融合模块

"""
神农框架特征融合模块

实现图特征和专家特征的融合策略。
"""

from typing import Dict, Any, Optional, Tuple
import torch
import torch.nn as nn
import torch.nn.functional as F
import logging

try:
    from ..nn.attention import BiologicalAttentionFusion
except ImportError:
    BiologicalAttentionFusion = None

try:
    from ..nn.unified_attention import BiologicallyInformedAttention, AttentionStrategy
except ImportError:
    BiologicallyInformedAttention = None
    AttentionStrategy = None

logger = logging.getLogger(__name__)


class FeatureFusionModule(nn.Module):
    """
    特征融合模块

    融合来自不同分支的特征。
    """

    def __init__(self, config: Dict[str, Any]):
        """
        初始化特征融合模块

        Args:
            config: 融合配置
        """
        super().__init__()

        self.config = config
        self.fusion_type = config.get('fusion_type', 'attention')
        self.graph_dim = config.get('graph_dim', 300)
        self.expert_dim = config.get('expert_dim', 128)
        self.output_dim = config.get('output_dim', 256)

        # 根据融合类型构建模块
        if self.fusion_type == 'attention':
            # 优先使用新的生物学指导注意力
            if BiologicallyInformedAttention is not None:
                self.fusion_layer = BiologicallyInformedAttention(
                    graph_dim=self.graph_dim,
                    expert_dim=self.expert_dim,
                    output_dim=self.output_dim,
                    attention_strategy=config.get('attention_strategy', 'multi_scale'),
                    dropout=config.get('dropout', 0.1)
                )
                logger.info("使用新的生物学指导注意力融合")
            elif BiologicalAttentionFusion is not None:
                self.fusion_layer = BiologicalAttentionFusion(
                    graph_dim=self.graph_dim,
                    expert_dim=self.expert_dim,
                    output_dim=self.output_dim,
                    **config
                )
                logger.info("使用传统生物学注意力融合")
            else:
                raise ImportError("无法导入注意力融合模块")
        elif self.fusion_type == 'concatenation':
            self.fusion_layer = nn.Sequential(
                nn.Linear(self.graph_dim + self.expert_dim, self.output_dim),
                nn.ReLU(),
                nn.Dropout(config.get('dropout', 0.1)),
                nn.Linear(self.output_dim, self.output_dim)
            )
        elif self.fusion_type == 'addition':
            # 需要先对齐维度
            self.graph_proj = nn.Linear(self.graph_dim, self.output_dim)
            self.expert_proj = nn.Linear(self.expert_dim, self.output_dim)
            self.fusion_layer = nn.Sequential(
                nn.ReLU(),
                nn.Dropout(config.get('dropout', 0.1)),
                nn.Linear(self.output_dim, self.output_dim)
            )
        else:
            raise ValueError(f"不支持的融合类型: {self.fusion_type}")

        logger.info(f"初始化特征融合模块: 类型={self.fusion_type}, "
                   f"输出维度={self.output_dim}")

    def forward(
        self,
        graph_features: torch.Tensor,
        expert_features: torch.Tensor,
        structure_info: Optional[Dict[str, Any]] = None,
        return_attention: bool = False
    ) -> Tuple[torch.Tensor, Optional[Dict[str, torch.Tensor]]]:
        """
        前向传播

        Args:
            graph_features: 图特征
            expert_features: 专家特征
            structure_info: 结构信息（用于新注意力机制）
            return_attention: 是否返回注意力权重

        Returns:
            (融合特征, 注意力权重)
        """
        if self.fusion_type == 'attention':
            # 检查是否是新的生物学指导注意力
            if isinstance(self.fusion_layer, type(BiologicallyInformedAttention)) and BiologicallyInformedAttention is not None:
                return self.fusion_layer(
                    graph_features, expert_features, structure_info, return_attention
                )
            else:
                # 传统注意力融合
                return self.fusion_layer(graph_features, expert_features, return_attention)

        elif self.fusion_type == 'concatenation':
            # 拼接融合
            concatenated = torch.cat([graph_features, expert_features], dim=-1)
            fused = self.fusion_layer(concatenated)
            return fused, None

        elif self.fusion_type == 'addition':
            # 加法融合
            graph_proj = self.graph_proj(graph_features)
            expert_proj = self.expert_proj(expert_features)
            added = graph_proj + expert_proj
            fused = self.fusion_layer(added)
            return fused, None

        else:
            raise ValueError(f"不支持的融合类型: {self.fusion_type}")
