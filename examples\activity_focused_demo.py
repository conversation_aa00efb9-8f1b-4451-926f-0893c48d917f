#!/usr/bin/env python3
# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-29
# 描述: 聚焦抗菌活性的神农框架演示

"""
聚焦抗菌活性的神农框架演示

展示如何使用重新设计的神农框架专注于抗菌活性预测，
识别对活性有贡献的基团、官能团和分子性质。
"""

import sys
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from rdkit import Chem
from rdkit.Chem import Descriptors
import matplotlib.pyplot as plt


def analyze_functional_groups(smiles: str):
    """
    分析分子中的官能团
    """
    mol = Chem.MolFromSmiles(smiles)
    if mol is None:
        return {}

    functional_groups = {}

    # 氢键基团
    hydrogen_bond_groups = []
    for atom in mol.GetAtoms():
        if atom.GetSymbol() == 'O' and atom.GetTotalNumHs() > 0:
            hydrogen_bond_groups.append('羟基(-OH)')
        elif atom.GetSymbol() == 'N' and atom.GetTotalNumHs() > 0:
            hydrogen_bond_groups.append('氨基(-NH)')

    # 芳香环
    aromatic_rings = []
    ring_info = mol.GetRingInfo()
    for ring in ring_info.AtomRings():
        if len(ring) == 6:  # 六元环
            is_aromatic = all(mol.GetAtomWithIdx(i).GetIsAromatic() for i in ring)
            if is_aromatic:
                aromatic_rings.append('苯环')

    # 卤素
    halogens = []
    for atom in mol.GetAtoms():
        if atom.GetSymbol() in ['F', 'Cl', 'Br', 'I']:
            halogens.append(f'{atom.GetSymbol()}原子')

    # 极性基团
    polar_groups = []
    for atom in mol.GetAtoms():
        if atom.GetSymbol() == 'O' and atom.GetTotalNumHs() == 0:
            polar_groups.append('羰基(C=O)')
        elif atom.GetSymbol() == 'N' and len(atom.GetNeighbors()) == 3:
            polar_groups.append('叔胺')

    functional_groups = {
        'hydrogen_bond': list(set(hydrogen_bond_groups)),
        'aromatic': list(set(aromatic_rings)),
        'halogen': list(set(halogens)),
        'polar': list(set(polar_groups))
    }

    return functional_groups


def calculate_molecular_properties(smiles: str):
    """
    计算分子性质
    """
    mol = Chem.MolFromSmiles(smiles)
    if mol is None:
        return {}

    properties = {
        'molecular_weight': Descriptors.MolWt(mol),
        'logp': Descriptors.MolLogP(mol),
        'hbd': Descriptors.NumHDonors(mol),
        'hba': Descriptors.NumHAcceptors(mol),
        'tpsa': Descriptors.TPSA(mol),
        'rotatable_bonds': Descriptors.NumRotatableBonds(mol),
        'aromatic_atoms': len([atom for atom in mol.GetAtoms() if atom.GetIsAromatic()]),
        'heavy_atoms': mol.GetNumHeavyAtoms()
    }

    return properties


def simulate_attention_weights(smiles: str):
    """
    模拟注意力权重，突出重要的原子
    """
    mol = Chem.MolFromSmiles(smiles)
    if mol is None:
        return {}

    num_atoms = mol.GetNumAtoms()

    # 基础注意力权重
    attention_weights = {
        'functional_group_attention': np.random.beta(2, 5, num_atoms),
        'molecular_property_attention': np.random.beta(2, 5, num_atoms),
        'activity_related_attention': np.random.beta(2, 5, num_atoms)
    }

    # 为重要原子增加权重
    for i, atom in enumerate(mol.GetAtoms()):
        # 杂原子通常更重要
        if atom.GetSymbol() in ['N', 'O', 'F', 'S', 'Cl']:
            for key in attention_weights:
                attention_weights[key][i] *= 3.0

        # 芳香原子
        if atom.GetIsAromatic():
            for key in attention_weights:
                attention_weights[key][i] *= 2.0

        # 连接多个原子的原子（可能是关键节点）
        if len(atom.GetNeighbors()) >= 3:
            for key in attention_weights:
                attention_weights[key][i] *= 1.5

    # 归一化
    for key in attention_weights:
        attention_weights[key] = attention_weights[key] / np.sum(attention_weights[key])

    return attention_weights


def generate_activity_explanation(smiles: str, activity: float):
    """
    生成活性解释
    """
    # 分析官能团
    functional_groups = analyze_functional_groups(smiles)

    # 计算分子性质
    properties = calculate_molecular_properties(smiles)

    # 模拟注意力权重
    attention_weights = simulate_attention_weights(smiles)

    # 生成解释
    explanation = {
        'smiles': smiles,
        'predicted_activity': activity,
        'functional_groups': functional_groups,
        'molecular_properties': properties,
        'attention_analysis': attention_weights
    }

    return explanation


def print_detailed_explanation(explanation):
    """
    打印详细的化学解释
    """
    print("🧬 神农框架抗菌活性预测解释报告")
    print("=" * 60)
    print(f"分子SMILES: {explanation['smiles']}")
    print(f"预测活性: {explanation['predicted_activity']:.2f} μg/mL")

    # 活性等级
    activity = explanation['predicted_activity']
    if activity <= 1.0:
        level = "高活性"
        color = "🟢"
    elif activity <= 4.0:
        level = "中等活性"
        color = "🟡"
    else:
        level = "低活性"
        color = "🔴"

    print(f"活性等级: {color} {level}")
    print()

    # 官能团分析
    print("🔍 官能团贡献分析:")
    fg = explanation['functional_groups']

    if fg['hydrogen_bond']:
        print(f"  氢键基团: {', '.join(fg['hydrogen_bond'])}")
        print("    → 可能与靶蛋白形成氢键，增强结合亲和力")

    if fg['aromatic']:
        print(f"  芳香环: {', '.join(fg['aromatic'])}")
        print("    → 提供π-π相互作用和疏水性接触")

    if fg['halogen']:
        print(f"  卤素原子: {', '.join(fg['halogen'])}")
        print("    → 增强脂溶性和膜透过性")

    if fg['polar']:
        print(f"  极性基团: {', '.join(fg['polar'])}")
        print("    → 提供极性相互作用位点")

    print()

    # 分子性质分析
    print("📊 分子性质分析:")
    props = explanation['molecular_properties']

    print(f"  分子量: {props['molecular_weight']:.1f} Da")
    if props['molecular_weight'] <= 500:
        print("    → 符合Lipinski规则，有利于口服吸收")
    else:
        print("    → 分子量较大，可能影响膜透过性")

    print(f"  LogP: {props['logp']:.2f}")
    if -2 <= props['logp'] <= 5:
        print("    → 脂水平衡良好，有利于药物活性")
    else:
        print("    → 脂水平衡可能需要优化")

    print(f"  氢键供体/受体: {props['hbd']}/{props['hba']}")
    if props['hbd'] <= 5 and props['hba'] <= 10:
        print("    → 符合Lipinski规则")

    print(f"  极性表面积: {props['tpsa']:.1f} Ų")
    if props['tpsa'] <= 140:
        print("    → 有利于膜透过性")

    print()

    # 注意力分析
    print("🎯 AI注意力分析:")
    attention = explanation['attention_analysis']

    # 找出注意力最高的原子
    mol = Chem.MolFromSmiles(explanation['smiles'])
    for att_type, weights in attention.items():
        top_atoms = np.argsort(weights)[-3:][::-1]  # 前3个最重要的原子
        print(f"  {att_type}:")
        for i, atom_idx in enumerate(top_atoms):
            atom = mol.GetAtomWithIdx(int(atom_idx))
            print(f"    {i+1}. 原子{atom_idx} ({atom.GetSymbol()}) - 权重: {weights[atom_idx]:.4f}")

    print()

    # 优化建议
    print("💡 分子优化建议:")

    if props['molecular_weight'] > 500:
        print("  • 考虑减小分子量以改善药物相似性")

    if props['logp'] > 5:
        print("  • 增加极性基团以降低LogP值")
    elif props['logp'] < 0:
        print("  • 增加疏水性基团以提高LogP值")

    if props['hbd'] > 5:
        print("  • 减少氢键供体数量")

    if props['hba'] > 10:
        print("  • 减少氢键受体数量")

    if not fg['aromatic']:
        print("  • 考虑引入芳香环以增强与靶蛋白的相互作用")

    if activity > 4.0:
        print("  • 当前活性较弱，建议:")
        print("    - 优化关键官能团")
        print("    - 改善分子性质平衡")
        print("    - 考虑生物等排替换")

    print()
    print("✨ 神农框架专注于识别对抗菌活性有贡献的化学特征")
    print("   为化学家提供可解释的AI预测结果！")


def main():
    """主函数"""
    print("🧬 神农框架 - 聚焦抗菌活性预测演示")
    print("=" * 60)
    print("专注于识别对抗菌活性有贡献的基团、官能团和分子性质")
    print()

    # 示例抗菌化合物
    test_compounds = [
        {
            'name': '环丙沙星',
            'smiles': 'O=C(O)C1=CN(C2CC2)C3=C(C=C(F)C(N4CCNCC4)=C3F)C1=O',
            'activity': 0.25
        },
        {
            'name': '青霉素G',
            'smiles': 'CC1([C@@H](N2[C@H](S1)[C@@H](C2=O)NC(=O)CC3=CC=CC=C3)C(=O)O)C',
            'activity': 0.1
        },
        {
            'name': '氯霉素',
            'smiles': 'O=C(NC(C(O)C1=CC=C([N+]([O-])=O)C=C1)CO)C(Cl)Cl',
            'activity': 2.0
        }
    ]

    for i, compound in enumerate(test_compounds):
        print(f"\n📋 示例 {i+1}: {compound['name']}")
        print("-" * 40)

        # 生成解释
        explanation = generate_activity_explanation(
            compound['smiles'],
            compound['activity']
        )

        # 打印详细解释
        print_detailed_explanation(explanation)

        if i < len(test_compounds) - 1:
            print("\n" + "="*60)

    print("\n🎉 演示完成！")
    print("\n🔑 关键特点:")
    print("  ✅ 专注抗菌活性预测，不涉及虚无缥缈的机制预测")
    print("  ✅ 识别对活性有贡献的官能团和分子性质")
    print("  ✅ 提供化学家友好的可解释性分析")
    print("  ✅ 生成实用的分子优化建议")
    print("\n🧬 神农尝百草，AI识良药 - 让预测更加实用可信！")


if __name__ == "__main__":
    main()
