#!/usr/bin/env python3
# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-30
# 描述: 简化的神农框架对比实验

"""
简化的神农框架验证实验

对比组: ChemProp vs AutoGluon vs Shennong
任务: 抗菌化合物分类
目标: 证明在药物筛选中的实用价值
"""

import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from sklearn.metrics import (
    roc_auc_score, precision_score, recall_score, f1_score, 
    accuracy_score, classification_report
)
from sklearn.model_selection import StratifiedKFold
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Any
import json
import subprocess
import tempfile

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SimplifiedExperimentConfig:
    """简化实验配置"""
    
    def __init__(self):
        # 基本设置
        self.n_runs = 3  # 减少到3次运行
        self.cv_folds = 5  # 5折交叉验证
        self.random_seeds = [42, 123, 456]
        
        # 分类任务设置
        self.task_type = 'binary_classification'
        self.activity_threshold = 16.0  # MIC < 16 μg/mL为活性
        
        # 评估指标
        self.classification_metrics = ['auc', 'precision', 'recall', 'f1', 'accuracy']
        self.screening_metrics = ['top1_precision', 'top5_precision', 'enrichment_factor']
        
        # 模型设置
        self.batch_size = 64
        self.epochs = 50  # 减少训练轮数
        self.learning_rate = 1e-3

class ChemPropWrapper:
    """ChemProp模型包装器"""
    
    def __init__(self, config: SimplifiedExperimentConfig):
        self.config = config
        self.model_dir = None
    
    def prepare_data(self, df: pd.DataFrame, output_dir: str) -> str:
        """准备ChemProp格式数据"""
        chemprop_df = df[['smiles', 'activity_binary']].copy()
        chemprop_df.columns = ['smiles', 'activity']
        
        data_path = Path(output_dir) / 'chemprop_data.csv'
        chemprop_df.to_csv(data_path, index=False)
        
        return str(data_path)
    
    def train(self, train_data_path: str, val_data_path: str, model_dir: str):
        """训练ChemProp模型"""
        self.model_dir = model_dir
        
        cmd = [
            'chemprop_train',
            '--data_path', train_data_path,
            '--separate_val_path', val_data_path,
            '--save_dir', model_dir,
            '--dataset_type', 'classification',
            '--epochs', str(self.config.epochs),
            '--batch_size', str(self.config.batch_size),
            '--quiet'
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            logger.info("ChemProp训练完成")
        except subprocess.CalledProcessError as e:
            logger.error(f"ChemProp训练失败: {e}")
            raise
    
    def predict(self, test_data_path: str) -> np.ndarray:
        """ChemProp预测"""
        if not self.model_dir:
            raise ValueError("模型未训练")
        
        output_path = Path(self.model_dir) / 'predictions.csv'
        
        cmd = [
            'chemprop_predict',
            '--test_path', test_data_path,
            '--checkpoint_dir', self.model_dir,
            '--preds_path', str(output_path)
        ]
        
        try:
            subprocess.run(cmd, capture_output=True, text=True, check=True)
            
            # 读取预测结果
            pred_df = pd.read_csv(output_path)
            return pred_df.iloc[:, 1].values  # 第二列是预测概率
            
        except subprocess.CalledProcessError as e:
            logger.error(f"ChemProp预测失败: {e}")
            raise

class AutoGluonWrapper:
    """AutoGluon模型包装器"""
    
    def __init__(self, config: SimplifiedExperimentConfig):
        self.config = config
        self.predictor = None
    
    def train(self, X_train: np.ndarray, y_train: np.ndarray, 
              X_val: np.ndarray, y_val: np.ndarray):
        """训练AutoGluon模型"""
        try:
            from autogluon.tabular import TabularPredictor
            
            # 准备训练数据
            train_df = pd.DataFrame(X_train)
            train_df['activity'] = y_train
            
            # 准备验证数据
            val_df = pd.DataFrame(X_val)
            val_df['activity'] = y_val
            
            # 合并训练和验证数据
            full_df = pd.concat([train_df, val_df], ignore_index=True)
            
            # 创建预测器
            self.predictor = TabularPredictor(
                label='activity',
                problem_type='binary',
                eval_metric='roc_auc'
            )
            
            # 训练模型
            self.predictor.fit(
                full_df,
                time_limit=300,  # 5分钟时间限制
                presets='medium_quality_faster_train'
            )
            
            logger.info("AutoGluon训练完成")
            
        except ImportError:
            logger.error("AutoGluon未安装，请安装: pip install autogluon")
            raise
    
    def predict(self, X_test: np.ndarray) -> np.ndarray:
        """AutoGluon预测"""
        if not self.predictor:
            raise ValueError("模型未训练")
        
        test_df = pd.DataFrame(X_test)
        predictions = self.predictor.predict_proba(test_df)
        
        # 返回正类概率
        if hasattr(predictions, 'iloc'):
            return predictions.iloc[:, 1].values
        else:
            return predictions[:, 1]

class ShennongClassifier(nn.Module):
    """简化的神农分类器"""
    
    def __init__(self, graph_dim: int = 300, mordred_dim: int = 1613, 
                 hidden_dim: int = 128, num_heads: int = 8):
        super().__init__()
        
        # 特征编码器
        self.graph_encoder = nn.Sequential(
            nn.Linear(graph_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
        self.mordred_encoder = nn.Sequential(
            nn.Linear(mordred_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
        # 多头注意力融合
        self.attention = nn.MultiheadAttention(
            embed_dim=hidden_dim,
            num_heads=num_heads,
            batch_first=True
        )
        
        # 分类器
        self.classifier = nn.Sequential(
            nn.Linear(hidden_dim, 64),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(64, 1),
            nn.Sigmoid()
        )
    
    def forward(self, graph_features, mordred_features):
        # 编码特征
        graph_encoded = self.graph_encoder(graph_features).unsqueeze(1)
        mordred_encoded = self.mordred_encoder(mordred_features).unsqueeze(1)
        
        # 注意力融合
        fused_features, _ = self.attention(
            graph_encoded, mordred_encoded, mordred_encoded
        )
        
        # 分类预测
        output = self.classifier(fused_features.squeeze(1))
        return output.squeeze()

class SimplifiedComparison:
    """简化对比实验主类"""
    
    def __init__(self, config: SimplifiedExperimentConfig):
        self.config = config
        self.results = {}
    
    def prepare_classification_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """准备分类数据"""
        # 创建二分类标签
        df['activity_binary'] = (df['mic_value'] < self.config.activity_threshold).astype(int)
        
        # 数据平衡检查
        pos_count = df['activity_binary'].sum()
        neg_count = len(df) - pos_count
        
        logger.info(f"数据分布: 活性化合物 {pos_count}, 非活性化合物 {neg_count}")
        
        return df
    
    def evaluate_classification(self, y_true: np.ndarray, y_pred_proba: np.ndarray) -> Dict[str, float]:
        """评估分类性能"""
        # 转换为二分类预测
        y_pred = (y_pred_proba > 0.5).astype(int)
        
        metrics = {
            'auc': roc_auc_score(y_true, y_pred_proba),
            'precision': precision_score(y_true, y_pred),
            'recall': recall_score(y_true, y_pred),
            'f1': f1_score(y_true, y_pred),
            'accuracy': accuracy_score(y_true, y_pred)
        }
        
        return metrics
    
    def evaluate_virtual_screening(self, y_true: np.ndarray, y_pred_proba: np.ndarray) -> Dict[str, float]:
        """评估虚拟筛选性能"""
        # 按预测概率排序
        sorted_indices = np.argsort(y_pred_proba)[::-1]
        sorted_labels = y_true[sorted_indices]
        
        total_actives = np.sum(y_true)
        total_compounds = len(y_true)
        
        # Top-k精确率
        top1_count = int(0.01 * total_compounds)
        top5_count = int(0.05 * total_compounds)
        
        top1_precision = np.sum(sorted_labels[:top1_count]) / top1_count if top1_count > 0 else 0
        top5_precision = np.sum(sorted_labels[:top5_count]) / top5_count if top5_count > 0 else 0
        
        # 富集因子 (相对于随机选择)
        random_precision = total_actives / total_compounds
        enrichment_factor = top1_precision / random_precision if random_precision > 0 else 0
        
        return {
            'top1_precision': top1_precision,
            'top5_precision': top5_precision,
            'enrichment_factor': enrichment_factor
        }
    
    def run_comparison(self, data_path: str) -> Dict[str, Any]:
        """运行简化对比实验"""
        logger.info("开始简化对比实验")
        
        # 加载数据
        df = pd.read_csv(data_path)
        df = self.prepare_classification_data(df)
        
        # 加载预计算特征
        # features_data = np.load(features_path)  # 需要实现
        
        all_results = {
            'ChemProp': [],
            'AutoGluon': [],
            'Shennong': []
        }
        
        # 交叉验证
        skf = StratifiedKFold(n_splits=self.config.cv_folds, shuffle=True, random_state=42)
        
        for fold, (train_idx, test_idx) in enumerate(skf.split(df, df['activity_binary'])):
            logger.info(f"处理第 {fold+1} 折")
            
            train_df = df.iloc[train_idx]
            test_df = df.iloc[test_idx]
            
            # 进一步划分训练和验证集
            val_size = int(0.2 * len(train_df))
            val_df = train_df.iloc[:val_size]
            train_df = train_df.iloc[val_size:]
            
            # 测试每个模型
            fold_results = {}
            
            # 1. ChemProp
            try:
                fold_results['ChemProp'] = self.test_chemprop(train_df, val_df, test_df)
            except Exception as e:
                logger.error(f"ChemProp测试失败: {e}")
                fold_results['ChemProp'] = None
            
            # 2. AutoGluon (需要Mordred特征)
            try:
                fold_results['AutoGluon'] = self.test_autogluon(train_df, val_df, test_df)
            except Exception as e:
                logger.error(f"AutoGluon测试失败: {e}")
                fold_results['AutoGluon'] = None
            
            # 3. Shennong (需要图特征和Mordred特征)
            try:
                fold_results['Shennong'] = self.test_shennong(train_df, val_df, test_df)
            except Exception as e:
                logger.error(f"Shennong测试失败: {e}")
                fold_results['Shennong'] = None
            
            # 保存折结果
            for method, result in fold_results.items():
                if result is not None:
                    all_results[method].append(result)
        
        # 汇总结果
        summary_results = self.summarize_results(all_results)
        
        return summary_results
    
    def test_chemprop(self, train_df, val_df, test_df) -> Dict[str, float]:
        """测试ChemProp"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 准备数据
            chemprop_wrapper = ChemPropWrapper(self.config)
            
            train_path = chemprop_wrapper.prepare_data(train_df, temp_dir)
            val_path = chemprop_wrapper.prepare_data(val_df, temp_dir)
            test_path = chemprop_wrapper.prepare_data(test_df, temp_dir)
            
            model_dir = Path(temp_dir) / 'chemprop_model'
            model_dir.mkdir()
            
            # 训练和预测
            chemprop_wrapper.train(train_path, val_path, str(model_dir))
            predictions = chemprop_wrapper.predict(test_path)
            
            # 评估
            y_true = test_df['activity_binary'].values
            
            classification_metrics = self.evaluate_classification(y_true, predictions)
            screening_metrics = self.evaluate_virtual_screening(y_true, predictions)
            
            return {**classification_metrics, **screening_metrics}
    
    def test_autogluon(self, train_df, val_df, test_df) -> Dict[str, float]:
        """测试AutoGluon"""
        # 这里需要加载Mordred特征
        # 暂时返回模拟结果
        logger.warning("AutoGluon测试需要实现Mordred特征加载")
        return None
    
    def test_shennong(self, train_df, val_df, test_df) -> Dict[str, float]:
        """测试Shennong"""
        # 这里需要加载图特征和Mordred特征
        # 暂时返回模拟结果
        logger.warning("Shennong测试需要实现特征加载")
        return None
    
    def summarize_results(self, all_results: Dict[str, List]) -> Dict[str, Any]:
        """汇总实验结果"""
        summary = {}
        
        for method, results in all_results.items():
            if not results:
                continue
                
            method_summary = {}
            
            # 计算每个指标的统计量
            all_metrics = set()
            for result in results:
                all_metrics.update(result.keys())
            
            for metric in all_metrics:
                values = [result[metric] for result in results if metric in result]
                if values:
                    method_summary[metric] = {
                        'mean': np.mean(values),
                        'std': np.std(values),
                        'values': values
                    }
            
            summary[method] = method_summary
        
        return summary
    
    def save_results(self, results: Dict[str, Any], output_path: str):
        """保存结果"""
        output_path = Path(output_path)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 保存JSON结果
        with open(output_path / 'comparison_results.json', 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        # 生成简化报告
        self.generate_simple_report(results, output_path / 'comparison_report.md')
        
        logger.info(f"结果已保存到: {output_path}")
    
    def generate_simple_report(self, results: Dict[str, Any], report_path: str):
        """生成简化报告"""
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# 神农框架简化对比实验报告\n\n")
            
            f.write("## 性能对比\n\n")
            f.write("| 方法 | AUC | F1 | Top-1% 精确率 | 富集因子 |\n")
            f.write("|------|-----|----|--------------|---------|\n")
            
            for method, method_results in results.items():
                if 'auc' in method_results:
                    auc = method_results['auc']['mean']
                    f1 = method_results['f1']['mean']
                    top1 = method_results.get('top1_precision', {}).get('mean', 0)
                    ef = method_results.get('enrichment_factor', {}).get('mean', 0)
                    
                    f.write(f"| {method} | {auc:.3f} | {f1:.3f} | {top1:.3f} | {ef:.2f} |\n")
            
            f.write("\n## 结论\n\n")
            f.write("基于以上结果，可以得出以下结论：\n")
            f.write("1. [根据实际结果填写]\n")
            f.write("2. [根据实际结果填写]\n")

def main():
    """主函数"""
    print("🧬 神农框架简化对比实验")
    print("=" * 50)
    
    config = SimplifiedExperimentConfig()
    comparison = SimplifiedComparison(config)
    
    print("⚠️  注意：需要准备以下数据才能运行:")
    print("1. 抗菌活性数据 (CSV格式，包含SMILES和MIC值)")
    print("2. 预计算的图特征和Mordred特征")
    print("3. 安装ChemProp和AutoGluon")
    
    # 示例运行
    # results = comparison.run_comparison('data/antibacterial_data.csv')
    # comparison.save_results(results, 'experiments/results/simplified_comparison')
    
    print("✅ 简化实验框架已准备就绪")

if __name__ == "__main__":
    main()
