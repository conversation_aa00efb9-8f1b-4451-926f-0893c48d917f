# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-27
# 描述: 神农框架损失函数模块

"""
神农框架损失函数

提供多任务学习的损失函数，包括活性预测、机制分类、注意力正则化等。
"""

from typing import Dict, Any, Optional, List
import torch
import torch.nn as nn
import torch.nn.functional as F
import logging

logger = logging.getLogger(__name__)


class AntibacterialLoss(nn.Module):
    """
    抗菌化合物专用损失函数
    
    集成多个任务的损失，包括活性预测、机制分类和正则化项。
    """
    
    def __init__(
        self,
        task_type: str = 'regression',
        num_tasks: int = 1,
        mechanism_weight: float = 0.1,
        uncertainty_weight: float = 0.05,
        attention_weight: float = 0.01
    ):
        """
        初始化抗菌损失函数
        
        Args:
            task_type: 任务类型 ('regression' 或 'classification')
            num_tasks: 任务数量
            mechanism_weight: 机制分类损失权重
            uncertainty_weight: 不确定性损失权重
            attention_weight: 注意力正则化权重
        """
        super().__init__()
        
        self.task_type = task_type
        self.num_tasks = num_tasks
        self.mechanism_weight = mechanism_weight
        self.uncertainty_weight = uncertainty_weight
        self.attention_weight = attention_weight
        
        # 主任务损失函数
        if task_type == 'regression':
            self.main_loss_fn = nn.MSELoss(reduction='none')
        elif task_type == 'classification':
            self.main_loss_fn = nn.CrossEntropyLoss(reduction='none')
        else:
            raise ValueError(f"不支持的任务类型: {task_type}")
        
        # 机制分类损失
        self.mechanism_loss_fn = nn.CrossEntropyLoss(reduction='none')
        
        # 不确定性损失
        self.uncertainty_loss_fn = nn.GaussianNLLLoss(reduction='none')
        
        logger.info(f"初始化抗菌损失函数: 任务类型={task_type}, "
                   f"机制权重={mechanism_weight}, 不确定性权重={uncertainty_weight}")
    
    def forward(
        self, 
        outputs: Dict[str, torch.Tensor], 
        batch: Dict[str, Any]
    ) -> Dict[str, torch.Tensor]:
        """
        计算损失
        
        Args:
            outputs: 模型输出
            batch: 批次数据
            
        Returns:
            损失字典
        """
        losses = {}
        
        # 主任务损失
        activity_loss = self._compute_activity_loss(outputs, batch)
        losses['activity_loss'] = activity_loss
        
        # 机制分类损失
        if 'mechanism' in outputs and self.mechanism_weight > 0:
            mechanism_loss = self._compute_mechanism_loss(outputs, batch)
            losses['mechanism_loss'] = mechanism_loss
        
        # 不确定性损失
        if 'uncertainty' in outputs and self.uncertainty_weight > 0:
            uncertainty_loss = self._compute_uncertainty_loss(outputs, batch)
            losses['uncertainty_loss'] = uncertainty_loss
        
        # 注意力正则化
        if 'attention_weights' in outputs and self.attention_weight > 0:
            attention_reg = self._compute_attention_regularization(outputs)
            losses['attention_regularization'] = attention_reg
        
        return losses
    
    def _compute_activity_loss(
        self, 
        outputs: Dict[str, torch.Tensor], 
        batch: Dict[str, Any]
    ) -> torch.Tensor:
        """计算活性预测损失"""
        predictions = outputs['activity']
        targets = batch['targets']
        target_mask = batch.get('target_mask', {})
        
        if isinstance(targets, dict):
            # 多任务情况
            total_loss = torch.tensor(0.0, device=predictions.device)
            valid_tasks = 0
            
            for task_idx, (task_name, task_targets) in enumerate(targets.items()):
                if task_idx >= predictions.size(-1):
                    continue
                
                task_predictions = predictions[:, task_idx]
                task_mask = target_mask.get(task_name, torch.ones_like(task_targets))
                
                # 只计算有效目标的损失
                valid_mask = task_mask > 0
                if valid_mask.sum() > 0:
                    task_loss = self.main_loss_fn(
                        task_predictions[valid_mask], 
                        task_targets[valid_mask]
                    )
                    total_loss += task_loss.mean()
                    valid_tasks += 1
            
            return total_loss / max(valid_tasks, 1)
        
        else:
            # 单任务情况
            if predictions.dim() > 1 and predictions.size(-1) > 1:
                predictions = predictions[:, 0]  # 取第一个任务
            
            loss = self.main_loss_fn(predictions.squeeze(), targets.squeeze())
            return loss.mean()
    
    def _compute_mechanism_loss(
        self, 
        outputs: Dict[str, torch.Tensor], 
        batch: Dict[str, Any]
    ) -> torch.Tensor:
        """计算机制分类损失"""
        mechanism_logits = outputs['mechanism']
        
        # 从批次数据中获取机制标签
        mechanism_labels = batch.get('mechanism_labels')
        
        if mechanism_labels is None:
            # 如果没有机制标签，返回零损失
            return torch.tensor(0.0, device=mechanism_logits.device)
        
        if isinstance(mechanism_labels, dict):
            # 多热编码标签
            mechanism_targets = mechanism_labels['labels']
            # 使用二元交叉熵损失处理多标签分类
            loss = F.binary_cross_entropy_with_logits(
                mechanism_logits, mechanism_targets, reduction='none'
            )
            return loss.mean()
        
        else:
            # 单标签分类
            loss = self.mechanism_loss_fn(mechanism_logits, mechanism_labels)
            return loss.mean()
    
    def _compute_uncertainty_loss(
        self, 
        outputs: Dict[str, torch.Tensor], 
        batch: Dict[str, Any]
    ) -> torch.Tensor:
        """计算不确定性损失"""
        predictions = outputs['activity']
        uncertainties = outputs['uncertainty']
        targets = batch['targets']
        
        if isinstance(targets, dict):
            # 多任务情况 - 简化处理
            target_values = list(targets.values())[0]  # 取第一个任务
        else:
            target_values = targets
        
        if predictions.dim() > 1 and predictions.size(-1) > 1:
            predictions = predictions[:, 0]  # 取第一个任务
        
        if uncertainties.dim() > 1 and uncertainties.size(-1) > 1:
            uncertainties = uncertainties[:, 0]  # 取第一个任务
        
        # 高斯负对数似然损失
        loss = self.uncertainty_loss_fn(
            predictions.squeeze(), 
            target_values.squeeze(), 
            uncertainties.squeeze()
        )
        
        return loss.mean()
    
    def _compute_attention_regularization(
        self, 
        outputs: Dict[str, torch.Tensor]
    ) -> torch.Tensor:
        """计算注意力正则化损失"""
        attention_weights = outputs['attention_weights']
        
        total_reg = torch.tensor(0.0, device=list(attention_weights.values())[0].device)
        num_components = 0
        
        # 遍历所有注意力权重
        for component_name, weights in attention_weights.items():
            if weights is None:
                continue
            
            if isinstance(weights, list):
                # 多头注意力权重
                for head_weights in weights:
                    if isinstance(head_weights, dict):
                        for weight_tensor in head_weights.values():
                            if weight_tensor is not None:
                                # 鼓励注意力权重的稀疏性
                                reg = self._sparsity_regularization(weight_tensor)
                                total_reg += reg
                                num_components += 1
                    else:
                        reg = self._sparsity_regularization(head_weights)
                        total_reg += reg
                        num_components += 1
            
            elif torch.is_tensor(weights):
                reg = self._sparsity_regularization(weights)
                total_reg += reg
                num_components += 1
        
        return total_reg / max(num_components, 1)
    
    def _sparsity_regularization(self, attention_weights: torch.Tensor) -> torch.Tensor:
        """注意力权重稀疏性正则化"""
        # L1正则化鼓励稀疏性
        l1_reg = torch.abs(attention_weights).mean()
        
        # 熵正则化鼓励集中注意力
        # 添加小的epsilon避免log(0)
        eps = 1e-8
        normalized_weights = F.softmax(attention_weights, dim=-1)
        entropy_reg = -(normalized_weights * torch.log(normalized_weights + eps)).sum(dim=-1).mean()
        
        # 组合正则化
        return 0.5 * l1_reg + 0.5 * entropy_reg


class MultiTaskLoss(nn.Module):
    """
    多任务学习损失函数
    
    支持不同任务类型的组合和动态权重调整。
    """
    
    def __init__(
        self,
        task_configs: Dict[str, Dict[str, Any]],
        adaptive_weights: bool = False
    ):
        """
        初始化多任务损失
        
        Args:
            task_configs: 任务配置字典
            adaptive_weights: 是否使用自适应权重
        """
        super().__init__()
        
        self.task_configs = task_configs
        self.adaptive_weights = adaptive_weights
        
        # 为每个任务创建损失函数
        self.task_losses = nn.ModuleDict()
        self.task_weights = nn.ParameterDict()
        
        for task_name, config in task_configs.items():
            task_type = config.get('type', 'regression')
            
            if task_type == 'regression':
                self.task_losses[task_name] = nn.MSELoss()
            elif task_type == 'classification':
                self.task_losses[task_name] = nn.CrossEntropyLoss()
            
            # 初始化任务权重
            initial_weight = config.get('weight', 1.0)
            if adaptive_weights:
                self.task_weights[task_name] = nn.Parameter(
                    torch.tensor(initial_weight, dtype=torch.float32)
                )
            else:
                self.register_buffer(
                    f'{task_name}_weight', 
                    torch.tensor(initial_weight, dtype=torch.float32)
                )
    
    def forward(
        self, 
        outputs: Dict[str, torch.Tensor], 
        targets: Dict[str, torch.Tensor]
    ) -> Dict[str, torch.Tensor]:
        """
        计算多任务损失
        
        Args:
            outputs: 模型输出
            targets: 目标值
            
        Returns:
            损失字典
        """
        losses = {}
        total_loss = torch.tensor(0.0, device=list(outputs.values())[0].device)
        
        for task_name in self.task_configs.keys():
            if task_name not in outputs or task_name not in targets:
                continue
            
            # 计算任务损失
            task_loss = self.task_losses[task_name](
                outputs[task_name], targets[task_name]
            )
            
            # 获取任务权重
            if self.adaptive_weights:
                weight = self.task_weights[task_name]
            else:
                weight = getattr(self, f'{task_name}_weight')
            
            # 加权损失
            weighted_loss = weight * task_loss
            
            losses[f'{task_name}_loss'] = task_loss
            losses[f'{task_name}_weighted_loss'] = weighted_loss
            
            total_loss += weighted_loss
        
        losses['total_loss'] = total_loss
        
        return losses


class UncertaintyLoss(nn.Module):
    """
    不确定性量化损失函数
    
    支持认知不确定性和偶然不确定性的建模。
    """
    
    def __init__(self, uncertainty_type: str = 'aleatoric'):
        """
        初始化不确定性损失
        
        Args:
            uncertainty_type: 不确定性类型 ('aleatoric', 'epistemic', 'both')
        """
        super().__init__()
        
        self.uncertainty_type = uncertainty_type
        
        if uncertainty_type in ['aleatoric', 'both']:
            self.aleatoric_loss = nn.GaussianNLLLoss()
        
        if uncertainty_type in ['epistemic', 'both']:
            self.epistemic_loss = nn.KLDivLoss()
    
    def forward(
        self, 
        predictions: torch.Tensor,
        targets: torch.Tensor,
        uncertainties: torch.Tensor
    ) -> torch.Tensor:
        """
        计算不确定性损失
        
        Args:
            predictions: 预测值
            targets: 目标值
            uncertainties: 不确定性估计
            
        Returns:
            不确定性损失
        """
        if self.uncertainty_type == 'aleatoric':
            return self.aleatoric_loss(predictions, targets, uncertainties)
        
        elif self.uncertainty_type == 'epistemic':
            # 认知不确定性通过模型集成或dropout采样估计
            # 这里简化为均方误差
            return F.mse_loss(predictions, targets)
        
        elif self.uncertainty_type == 'both':
            aleatoric = self.aleatoric_loss(predictions, targets, uncertainties)
            epistemic = F.mse_loss(predictions, targets)
            return aleatoric + epistemic
        
        else:
            raise ValueError(f"不支持的不确定性类型: {self.uncertainty_type}")


class AttentionRegularizationLoss(nn.Module):
    """
    注意力正则化损失
    
    鼓励注意力权重的生物学合理性。
    """
    
    def __init__(
        self,
        sparsity_weight: float = 0.1,
        diversity_weight: float = 0.1,
        biological_weight: float = 0.1
    ):
        """
        初始化注意力正则化损失
        
        Args:
            sparsity_weight: 稀疏性权重
            diversity_weight: 多样性权重
            biological_weight: 生物学合理性权重
        """
        super().__init__()
        
        self.sparsity_weight = sparsity_weight
        self.diversity_weight = diversity_weight
        self.biological_weight = biological_weight
    
    def forward(self, attention_weights: Dict[str, torch.Tensor]) -> torch.Tensor:
        """
        计算注意力正则化损失
        
        Args:
            attention_weights: 注意力权重字典
            
        Returns:
            正则化损失
        """
        total_loss = torch.tensor(0.0)
        
        for name, weights in attention_weights.items():
            if weights is None:
                continue
            
            # 稀疏性正则化
            sparsity_loss = self._sparsity_loss(weights)
            
            # 多样性正则化
            diversity_loss = self._diversity_loss(weights)
            
            # 生物学合理性正则化
            biological_loss = self._biological_loss(weights, name)
            
            total_loss += (
                self.sparsity_weight * sparsity_loss +
                self.diversity_weight * diversity_loss +
                self.biological_weight * biological_loss
            )
        
        return total_loss
    
    def _sparsity_loss(self, weights: torch.Tensor) -> torch.Tensor:
        """稀疏性损失"""
        return torch.abs(weights).mean()
    
    def _diversity_loss(self, weights: torch.Tensor) -> torch.Tensor:
        """多样性损失"""
        if weights.dim() < 2:
            return torch.tensor(0.0)
        
        # 鼓励不同头的注意力权重差异
        correlation_matrix = torch.corrcoef(weights)
        # 减去对角线元素（自相关）
        off_diagonal = correlation_matrix - torch.eye(correlation_matrix.size(0))
        return torch.abs(off_diagonal).mean()
    
    def _biological_loss(self, weights: torch.Tensor, component_name: str) -> torch.Tensor:
        """生物学合理性损失"""
        # 这里可以根据具体的生物学知识设计损失
        # 例如，药效团相关的注意力应该更集中
        if 'pharmacophore' in component_name:
            # 鼓励药效团注意力的集中性
            entropy = -(weights * torch.log(weights + 1e-8)).sum(dim=-1).mean()
            return -entropy  # 负熵鼓励集中
        
        return torch.tensor(0.0)
