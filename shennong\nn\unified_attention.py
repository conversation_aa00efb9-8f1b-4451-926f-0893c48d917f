# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-27
# 描述: 统一的注意力机制管理器

"""
统一的注意力机制管理器

整合所有基于结构的注意力机制，提供统一的接口和智能选择策略。
"""

from typing import Dict, List, Any, Optional, Tuple, Union
import torch
import torch.nn as nn
import torch.nn.functional as F
import logging
from enum import Enum

from .structure_aware_attention import (
    PharmacophoreAttention,
    ScaffoldSimilarityAttention,
    AtomImportanceAttention,
    DescriptorCorrelationAttention,
    MultiScaleStructureAttention
)

logger = logging.getLogger(__name__)


class AttentionStrategy(Enum):
    """注意力策略枚举"""
    PHARMACOPHORE = "pharmacophore"
    SCAFFOLD = "scaffold"
    ATOM_IMPORTANCE = "atom_importance"
    DESCRIPTOR_CORRELATION = "descriptor_correlation"
    MULTI_SCALE = "multi_scale"
    ADAPTIVE = "adaptive"
    ENSEMBLE = "ensemble"


class UnifiedStructureAttention(nn.Module):
    """
    统一结构注意力机制
    
    根据输入数据特征和任务需求自动选择最适合的注意力策略。
    """
    
    def __init__(
        self,
        feature_dim: int,
        strategy: Union[AttentionStrategy, str] = AttentionStrategy.ADAPTIVE,
        ensemble_weights: Optional[List[float]] = None,
        dropout: float = 0.1,
        **kwargs
    ):
        super().__init__()
        
        self.feature_dim = feature_dim
        self.strategy = AttentionStrategy(strategy) if isinstance(strategy, str) else strategy
        self.dropout = nn.Dropout(dropout)
        
        # 初始化所有注意力机制
        self.attention_mechanisms = nn.ModuleDict({
            'pharmacophore': PharmacophoreAttention(
                feature_dim=feature_dim,
                pharmacophore_dim=kwargs.get('pharmacophore_dim', 32),
                dropout=dropout
            ),
            'scaffold': ScaffoldSimilarityAttention(
                feature_dim=feature_dim,
                scaffold_dim=kwargs.get('scaffold_dim', 64),
                num_reference_scaffolds=kwargs.get('num_reference_scaffolds', 16),
                dropout=dropout
            ),
            'atom_importance': AtomImportanceAttention(
                feature_dim=feature_dim,
                atom_dim=kwargs.get('atom_dim', 32),
                bond_dim=kwargs.get('bond_dim', 16),
                dropout=dropout
            ),
            'descriptor_correlation': DescriptorCorrelationAttention(
                feature_dim=feature_dim,
                correlation_dim=kwargs.get('correlation_dim', 64),
                num_descriptor_groups=kwargs.get('num_descriptor_groups', 8),
                dropout=dropout
            ),
            'multi_scale': MultiScaleStructureAttention(
                feature_dim=feature_dim,
                atom_dim=kwargs.get('atom_dim', 32),
                fragment_dim=kwargs.get('fragment_dim', 64),
                molecule_dim=kwargs.get('molecule_dim', 128),
                dropout=dropout
            )
        })
        
        # 策略选择器（用于自适应策略）
        if self.strategy == AttentionStrategy.ADAPTIVE:
            self.strategy_selector = nn.Sequential(
                nn.Linear(feature_dim, 64),
                nn.ReLU(),
                nn.Dropout(dropout),
                nn.Linear(64, len(self.attention_mechanisms)),
                nn.Softmax(dim=-1)
            )
        
        # 集成权重
        if self.strategy == AttentionStrategy.ENSEMBLE:
            if ensemble_weights is None:
                ensemble_weights = [1.0] * len(self.attention_mechanisms)
            self.register_buffer(
                'ensemble_weights',
                torch.tensor(ensemble_weights, dtype=torch.float32)
            )
        
        logger.info(f"初始化统一结构注意力: 策略={self.strategy.value}, 特征维度={feature_dim}")
    
    def forward(
        self,
        features: torch.Tensor,
        structure_info: Optional[Dict[str, Any]] = None,
        return_attention: bool = False
    ) -> Tuple[torch.Tensor, Optional[Dict[str, Any]]]:
        """
        前向传播
        
        Args:
            features: 输入特征 [batch_size, seq_len, feature_dim]
            structure_info: 结构信息
            return_attention: 是否返回注意力权重
            
        Returns:
            (输出特征, 注意力信息)
        """
        batch_size, seq_len, _ = features.shape
        
        if self.strategy == AttentionStrategy.ADAPTIVE:
            return self._adaptive_forward(features, structure_info, return_attention)
        elif self.strategy == AttentionStrategy.ENSEMBLE:
            return self._ensemble_forward(features, structure_info, return_attention)
        else:
            return self._single_strategy_forward(features, structure_info, return_attention)
    
    def _single_strategy_forward(
        self,
        features: torch.Tensor,
        structure_info: Optional[Dict[str, Any]],
        return_attention: bool
    ) -> Tuple[torch.Tensor, Optional[Dict[str, Any]]]:
        """单一策略前向传播"""
        strategy_name = self.strategy.value
        
        if strategy_name == 'multi_scale':
            # 多尺度注意力有特殊的返回格式
            attended_features, attention_dict = self.attention_mechanisms[strategy_name](
                features, structure_info, return_attention
            )
        else:
            # 其他注意力机制
            attention_mechanism = self.attention_mechanisms[strategy_name]
            attended_features, attention_weights = attention_mechanism(
                features, structure_info, return_attention
            )
            
            attention_dict = {strategy_name: attention_weights} if return_attention else None
        
        attended_features = self.dropout(attended_features)
        
        return attended_features, attention_dict
    
    def _adaptive_forward(
        self,
        features: torch.Tensor,
        structure_info: Optional[Dict[str, Any]],
        return_attention: bool
    ) -> Tuple[torch.Tensor, Optional[Dict[str, Any]]]:
        """自适应策略前向传播"""
        batch_size, seq_len, _ = features.shape
        
        # 计算策略选择权重
        # 使用全局平均池化来获得分子级别的特征表示
        global_features = features.mean(dim=1)  # [B, F]
        strategy_weights = self.strategy_selector(global_features)  # [B, num_strategies]
        
        # 计算所有策略的注意力权重
        all_attended_features = []
        all_attention_weights = {}
        
        strategy_names = list(self.attention_mechanisms.keys())
        
        for i, (name, mechanism) in enumerate(self.attention_mechanisms.items()):
            if name == 'multi_scale':
                attended_feat, attention_info = mechanism(features, structure_info, return_attention)
                if return_attention:
                    all_attention_weights[name] = attention_info
            else:
                attended_feat, attention_weight = mechanism(features, structure_info, return_attention)
                if return_attention:
                    all_attention_weights[name] = attention_weight
            
            all_attended_features.append(attended_feat)
        
        # 堆叠所有注意力结果
        stacked_features = torch.stack(all_attended_features, dim=0)  # [num_strategies, B, L, F]
        
        # 应用策略权重
        strategy_weights = strategy_weights.t().unsqueeze(-1).unsqueeze(-1)  # [num_strategies, B, 1, 1]
        weighted_features = stacked_features * strategy_weights  # [num_strategies, B, L, F]
        
        # 聚合结果
        final_features = weighted_features.sum(dim=0)  # [B, L, F]
        final_features = self.dropout(final_features)
        
        attention_dict = None
        if return_attention:
            attention_dict = {
                'strategy_weights': strategy_weights.squeeze(-1).squeeze(-1).t(),  # [B, num_strategies]
                'individual_attentions': all_attention_weights
            }
        
        return final_features, attention_dict
    
    def _ensemble_forward(
        self,
        features: torch.Tensor,
        structure_info: Optional[Dict[str, Any]],
        return_attention: bool
    ) -> Tuple[torch.Tensor, Optional[Dict[str, Any]]]:
        """集成策略前向传播"""
        # 计算所有策略的注意力权重
        all_attended_features = []
        all_attention_weights = {}
        
        for name, mechanism in self.attention_mechanisms.items():
            if name == 'multi_scale':
                attended_feat, attention_info = mechanism(features, structure_info, return_attention)
                if return_attention:
                    all_attention_weights[name] = attention_info
            else:
                attended_feat, attention_weight = mechanism(features, structure_info, return_attention)
                if return_attention:
                    all_attention_weights[name] = attention_weight
            
            all_attended_features.append(attended_feat)
        
        # 堆叠所有注意力结果
        stacked_features = torch.stack(all_attended_features, dim=0)  # [num_strategies, B, L, F]
        
        # 应用预定义的集成权重
        normalized_weights = F.softmax(self.ensemble_weights, dim=0)
        ensemble_weights = normalized_weights.view(-1, 1, 1, 1)  # [num_strategies, 1, 1, 1]
        
        weighted_features = stacked_features * ensemble_weights
        final_features = weighted_features.sum(dim=0)  # [B, L, F]
        final_features = self.dropout(final_features)
        
        attention_dict = None
        if return_attention:
            attention_dict = {
                'ensemble_weights': normalized_weights,
                'individual_attentions': all_attention_weights
            }
        
        return final_features, attention_dict
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        return {
            'current_strategy': self.strategy.value,
            'available_mechanisms': list(self.attention_mechanisms.keys()),
            'feature_dim': self.feature_dim,
            'num_parameters': sum(p.numel() for p in self.parameters()),
            'trainable_parameters': sum(p.numel() for p in self.parameters() if p.requires_grad)
        }
    
    def set_strategy(self, strategy: Union[AttentionStrategy, str]):
        """动态设置注意力策略"""
        self.strategy = AttentionStrategy(strategy) if isinstance(strategy, str) else strategy
        logger.info(f"注意力策略已更改为: {self.strategy.value}")


class BiologicallyInformedAttention(nn.Module):
    """
    生物学指导的注意力机制
    
    整合化学直觉和生物学知识，无需外部标签的智能注意力。
    """
    
    def __init__(
        self,
        graph_dim: int,
        expert_dim: int,
        output_dim: int,
        attention_strategy: Union[AttentionStrategy, str] = AttentionStrategy.MULTI_SCALE,
        dropout: float = 0.1
    ):
        super().__init__()
        
        self.graph_dim = graph_dim
        self.expert_dim = expert_dim
        self.output_dim = output_dim
        
        # 图特征注意力
        self.graph_attention = UnifiedStructureAttention(
            feature_dim=graph_dim,
            strategy=attention_strategy,
            dropout=dropout
        )
        
        # 专家特征注意力
        self.expert_attention = UnifiedStructureAttention(
            feature_dim=expert_dim,
            strategy=AttentionStrategy.DESCRIPTOR_CORRELATION,  # 专家特征更适合描述符相关性
            dropout=dropout
        )
        
        # 跨模态注意力
        self.cross_attention = nn.MultiheadAttention(
            embed_dim=min(graph_dim, expert_dim),
            num_heads=4,
            dropout=dropout,
            batch_first=True
        )
        
        # 特征投影器
        self.graph_projector = nn.Linear(graph_dim, min(graph_dim, expert_dim))
        self.expert_projector = nn.Linear(expert_dim, min(graph_dim, expert_dim))
        
        # 输出投影器
        self.output_projector = nn.Sequential(
            nn.Linear(graph_dim + expert_dim, output_dim * 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(output_dim * 2, output_dim)
        )
        
        logger.info(f"初始化生物学指导注意力: 图维度={graph_dim}, 专家维度={expert_dim}, 输出维度={output_dim}")
    
    def forward(
        self,
        graph_features: torch.Tensor,
        expert_features: torch.Tensor,
        structure_info: Optional[Dict[str, Any]] = None,
        return_attention: bool = False
    ) -> Tuple[torch.Tensor, Optional[Dict[str, Any]]]:
        """
        前向传播
        
        Args:
            graph_features: 图特征 [batch_size, graph_seq_len, graph_dim]
            expert_features: 专家特征 [batch_size, expert_seq_len, expert_dim]
            structure_info: 结构信息
            return_attention: 是否返回注意力权重
            
        Returns:
            (融合特征, 注意力信息)
        """
        # 应用自注意力
        attended_graph, graph_attention = self.graph_attention(
            graph_features, structure_info, return_attention
        )
        attended_expert, expert_attention = self.expert_attention(
            expert_features, structure_info, return_attention
        )
        
        # 投影到相同维度用于跨模态注意力
        projected_graph = self.graph_projector(attended_graph)
        projected_expert = self.expert_projector(attended_expert)
        
        # 跨模态注意力
        cross_attended_graph, cross_attention_weights = self.cross_attention(
            projected_graph, projected_expert, projected_expert
        )
        
        # 恢复原始维度并融合
        # 使用残差连接
        enhanced_graph = attended_graph + F.linear(
            cross_attended_graph, 
            self.graph_projector.weight.t()
        )
        
        # 拼接特征
        fused_features = torch.cat([enhanced_graph, attended_expert], dim=-1)
        
        # 全局池化（如果需要分子级别的表示）
        if fused_features.dim() == 3:
            pooled_features = fused_features.mean(dim=1)  # [B, graph_dim + expert_dim]
        else:
            pooled_features = fused_features
        
        # 输出投影
        output_features = self.output_projector(pooled_features)
        
        attention_dict = None
        if return_attention:
            attention_dict = {
                'graph_attention': graph_attention,
                'expert_attention': expert_attention,
                'cross_attention': cross_attention_weights
            }
        
        return output_features, attention_dict
