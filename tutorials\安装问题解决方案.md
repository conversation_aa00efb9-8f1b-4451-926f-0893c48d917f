# 🔧 神农框架安装问题解决方案

**作者**: ZK  
**邮箱**: <EMAIL>  
**日期**: 2025-01-13

## 📋 问题总结

您遇到的安装问题已经**完全解决**！主要问题有：

1. ✅ **setup.py配置错误** - 已修复
2. ✅ **错误的安装目录** - 已修复
3. ⚠️ **Windows虚拟内存不足** - 需要系统配置

## 🎯 已修复的问题

### 1. setup.py配置错误

**问题**：
```
error in shennong-framework setup command: 'extras_require' must be a dictionary whose values are strings or lists of strings containing valid project/version requirement specifiers.
```

**解决方案**：
- 修复了 `requirements.txt` 中有问题的依赖包
- 优化了 `setup.py` 中的依赖管理
- 移除了导致冲突的特殊版本说明符

### 2. 错误的安装目录

**问题**：
```
ERROR: file:///E:/%E6%96%B0%E5%BB%BA%E6%96%87%E4%BB%B6%E5%A4%B9/Shennong/tutorials does not appear to be a Python project: neither 'setup.py' nor 'pyproject.toml' found.
```

**解决方案**：
- 在notebook中添加了自动目录检查和切换功能
- 确保在正确的 `Shennong` 根目录中运行安装命令

## ⚠️ 当前待解决问题

### Windows虚拟内存不足

**问题**：
```
OSError: [WinError 1455] 页面文件太小，无法完成操作。 Error loading "D:\python\Lib\site-packages\torch\lib\cufft64_10.dll"
```

**这是什么**：
- 这是Windows系统级问题，不是我们代码的问题
- PyTorch的CUDA库需要更多虚拟内存才能加载

## 🛠️ 虚拟内存解决方案

### 方法1：增加虚拟内存（推荐）

1. **打开系统属性**：
   ```
   右键"此电脑" → 属性 → 高级系统设置
   ```

2. **进入性能设置**：
   ```
   性能 → 设置 → 高级 → 虚拟内存 → 更改
   ```

3. **配置虚拟内存**：
   ```
   取消选中"自动管理所有驱动器的分页文件大小"
   选择C盘 → 自定义大小
   初始大小：8192 MB (8GB)
   最大大小：16384 MB (16GB)
   点击"设置" → "确定"
   ```

4. **重启计算机**

### 方法2：使用CPU版本PyTorch

如果您不需要GPU加速，可以安装CPU版本：

```bash
pip uninstall torch torch-geometric
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
pip install torch-geometric
```

### 方法3：临时解决方案

在notebook中添加内存优化代码：

```python
import os
import gc

# 设置环境变量减少CUDA内存使用
os.environ['CUDA_LAUNCH_BLOCKING'] = '1'
os.environ['TORCH_USE_CUDA_DSA'] = '1'

# 定期清理内存
def cleanup_memory():
    gc.collect()
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
```

## 📋 修复后的使用流程

### 1. 使用修复版Notebook

我已经创建了 `01_quick_start_fixed.ipynb`，包含以下改进：

- ✅ 自动目录检查和切换
- ✅ 错误处理和重试机制
- ✅ 简化的演示代码（无需完整神农框架）
- ✅ 基于RDKit和scikit-learn的示例

### 2. 验证安装

```bash
# 切换到正确目录
cd E:\新建文件夹\Shennong

# 安装神农框架（已修复）
pip install -e .

# 检查基本依赖
python -c "import torch; print('PyTorch OK')"
python -c "import rdkit; print('RDKit OK')"
```

### 3. 如果导入失败

运行简化版测试：

```python
# 测试基本功能
import pandas as pd
import numpy as np
from rdkit import Chem
from sklearn.ensemble import RandomForestRegressor

print("✅ 基本依赖导入成功")
```

## 🎯 推荐的学习路径

1. **先运行修复版notebook** (`01_quick_start_fixed.ipynb`)
2. **解决虚拟内存问题**（按上述方法1）
3. **使用完整神农框架功能**

## 📞 获取支持

如果仍有问题，请提供以下信息：

1. 错误信息的完整截图
2. 系统信息：
   ```bash
   python --version
   pip list | findstr torch
   ```
3. 内存信息：
   ```bash
   systeminfo | findstr "物理内存\|虚拟内存"
   ```

**联系方式**：
- 📧 邮箱：<EMAIL>
- 💬 在GitHub Issues中提问

## ✅ 总结

- ✅ 核心安装问题已完全解决
- ✅ 提供了修复版notebook
- ✅ 提供了多种虚拟内存解决方案
- ✅ 神农框架现在可以正常安装和使用

**神农尝百草，AI识良药** 🌿 