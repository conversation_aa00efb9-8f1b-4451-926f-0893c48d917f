# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-29
# 描述: 神农框架数据验证

"""
神农框架数据验证

提供数据质量检查、SMILES验证、异常值检测等功能。
"""

import logging
import numpy as np
import pandas as pd
from typing import List, Dict, Any, Tuple, Optional
from rdkit import Chem
from rdkit.Chem import Descriptors, Crippen
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import DBSCAN

from .datapoints import ShennongDatapoint

logger = logging.getLogger(__name__)


class DataValidator:
    """数据验证器"""
    
    def __init__(self):
        """初始化数据验证器"""
        self.validation_results = {}
    
    def validate_dataset(
        self,
        datapoints: List[ShennongDatapoint],
        check_duplicates: bool = True,
        check_outliers: bool = True,
        check_drug_likeness: bool = True
    ) -> Dict[str, Any]:
        """
        验证数据集
        
        Args:
            datapoints: 数据点列表
            check_duplicates: 是否检查重复
            check_outliers: 是否检查异常值
            check_drug_likeness: 是否检查药物相似性
            
        Returns:
            验证结果字典
        """
        logger.info(f"开始验证数据集，共 {len(datapoints)} 个数据点")
        
        results = {
            'total_samples': len(datapoints),
            'valid_samples': 0,
            'invalid_samples': 0,
            'issues': []
        }
        
        # 基本SMILES验证
        smiles_results = self._validate_smiles([dp.smiles for dp in datapoints])
        results.update(smiles_results)
        
        # 检查重复
        if check_duplicates:
            duplicate_results = self._check_duplicates(datapoints)
            results.update(duplicate_results)
        
        # 检查异常值
        if check_outliers:
            outlier_results = self._check_outliers(datapoints)
            results.update(outlier_results)
        
        # 检查药物相似性
        if check_drug_likeness:
            drug_like_results = self._check_drug_likeness(datapoints)
            results.update(drug_like_results)
        
        # 统计有效样本
        results['valid_samples'] = len(datapoints) - len(results.get('invalid_indices', []))
        results['invalid_samples'] = len(results.get('invalid_indices', []))
        
        logger.info(f"数据验证完成: {results['valid_samples']}/{results['total_samples']} 个有效样本")
        
        self.validation_results = results
        return results
    
    def _validate_smiles(self, smiles_list: List[str]) -> Dict[str, Any]:
        """验证SMILES字符串"""
        logger.info("验证SMILES字符串...")
        
        invalid_indices = []
        invalid_smiles = []
        
        for i, smiles in enumerate(smiles_list):
            try:
                mol = Chem.MolFromSmiles(smiles)
                if mol is None:
                    invalid_indices.append(i)
                    invalid_smiles.append(smiles)
            except Exception as e:
                logger.warning(f"SMILES验证失败 {smiles}: {e}")
                invalid_indices.append(i)
                invalid_smiles.append(smiles)
        
        results = {
            'invalid_smiles_count': len(invalid_indices),
            'invalid_smiles_indices': invalid_indices,
            'invalid_smiles': invalid_smiles
        }
        
        if invalid_indices:
            results['issues'].append(f"发现 {len(invalid_indices)} 个无效SMILES")
        
        return results
    
    def _check_duplicates(self, datapoints: List[ShennongDatapoint]) -> Dict[str, Any]:
        """检查重复数据"""
        logger.info("检查重复数据...")
        
        smiles_dict = {}
        duplicate_groups = []
        
        for i, dp in enumerate(datapoints):
            smiles = dp.smiles
            if smiles in smiles_dict:
                # 找到重复
                existing_indices = smiles_dict[smiles]
                if len(existing_indices) == 1:
                    # 第一次发现重复，创建新组
                    duplicate_groups.append(existing_indices + [i])
                else:
                    # 添加到现有组
                    for group in duplicate_groups:
                        if existing_indices[0] in group:
                            group.append(i)
                            break
                smiles_dict[smiles].append(i)
            else:
                smiles_dict[smiles] = [i]
        
        results = {
            'duplicate_groups': duplicate_groups,
            'duplicate_count': sum(len(group) - 1 for group in duplicate_groups)
        }
        
        if duplicate_groups:
            results['issues'] = results.get('issues', [])
            results['issues'].append(f"发现 {len(duplicate_groups)} 组重复数据")
        
        return results
    
    def _check_outliers(self, datapoints: List[ShennongDatapoint]) -> Dict[str, Any]:
        """检查异常值"""
        logger.info("检查异常值...")
        
        # 计算分子描述符用于异常值检测
        descriptors = []
        valid_indices = []
        
        for i, dp in enumerate(datapoints):
            try:
                mol = Chem.MolFromSmiles(dp.smiles)
                if mol is not None:
                    desc = [
                        Descriptors.MolWt(mol),
                        Descriptors.MolLogP(mol),
                        Descriptors.NumHDonors(mol),
                        Descriptors.NumHAcceptors(mol),
                        Descriptors.TPSA(mol),
                        Descriptors.NumRotatableBonds(mol)
                    ]
                    descriptors.append(desc)
                    valid_indices.append(i)
            except Exception as e:
                logger.warning(f"描述符计算失败 {dp.smiles}: {e}")
        
        outlier_indices = []
        
        if len(descriptors) > 10:  # 需要足够的样本进行聚类
            # 标准化描述符
            scaler = StandardScaler()
            descriptors_scaled = scaler.fit_transform(descriptors)
            
            # 使用DBSCAN检测异常值
            clustering = DBSCAN(eps=2.0, min_samples=5)
            labels = clustering.fit_predict(descriptors_scaled)
            
            # 标签为-1的是异常值
            for i, label in enumerate(labels):
                if label == -1:
                    outlier_indices.append(valid_indices[i])
        
        results = {
            'outlier_indices': outlier_indices,
            'outlier_count': len(outlier_indices)
        }
        
        if outlier_indices:
            results['issues'] = results.get('issues', [])
            results['issues'].append(f"发现 {len(outlier_indices)} 个异常值")
        
        return results
    
    def _check_drug_likeness(self, datapoints: List[ShennongDatapoint]) -> Dict[str, Any]:
        """检查药物相似性（Lipinski规则）"""
        logger.info("检查药物相似性...")
        
        non_drug_like_indices = []
        lipinski_violations = []
        
        for i, dp in enumerate(datapoints):
            try:
                mol = Chem.MolFromSmiles(dp.smiles)
                if mol is not None:
                    violations = self._check_lipinski_rule(mol)
                    if len(violations) > 1:  # 允许违反1个规则
                        non_drug_like_indices.append(i)
                        lipinski_violations.append(violations)
            except Exception as e:
                logger.warning(f"药物相似性检查失败 {dp.smiles}: {e}")
        
        results = {
            'non_drug_like_indices': non_drug_like_indices,
            'non_drug_like_count': len(non_drug_like_indices),
            'lipinski_violations': lipinski_violations
        }
        
        if non_drug_like_indices:
            results['issues'] = results.get('issues', [])
            results['issues'].append(f"发现 {len(non_drug_like_indices)} 个非药物相似化合物")
        
        return results
    
    def _check_lipinski_rule(self, mol) -> List[str]:
        """检查Lipinski五规则"""
        violations = []
        
        # 分子量 <= 500 Da
        mw = Descriptors.MolWt(mol)
        if mw > 500:
            violations.append(f"分子量过大: {mw:.1f} > 500")
        
        # LogP <= 5
        logp = Crippen.MolLogP(mol)
        if logp > 5:
            violations.append(f"LogP过大: {logp:.1f} > 5")
        
        # 氢键供体 <= 5
        hbd = Descriptors.NumHDonors(mol)
        if hbd > 5:
            violations.append(f"氢键供体过多: {hbd} > 5")
        
        # 氢键受体 <= 10
        hba = Descriptors.NumHAcceptors(mol)
        if hba > 10:
            violations.append(f"氢键受体过多: {hba} > 10")
        
        return violations
    
    def get_clean_dataset(
        self,
        datapoints: List[ShennongDatapoint],
        remove_invalid_smiles: bool = True,
        remove_duplicates: bool = True,
        remove_outliers: bool = False,
        remove_non_drug_like: bool = False
    ) -> List[ShennongDatapoint]:
        """
        获取清洗后的数据集
        
        Args:
            datapoints: 原始数据点列表
            remove_invalid_smiles: 是否移除无效SMILES
            remove_duplicates: 是否移除重复数据
            remove_outliers: 是否移除异常值
            remove_non_drug_like: 是否移除非药物相似化合物
            
        Returns:
            清洗后的数据点列表
        """
        if not hasattr(self, 'validation_results') or not self.validation_results:
            logger.warning("请先运行validate_dataset方法")
            return datapoints
        
        indices_to_remove = set()
        
        # 移除无效SMILES
        if remove_invalid_smiles:
            indices_to_remove.update(self.validation_results.get('invalid_smiles_indices', []))
        
        # 移除重复数据（保留每组的第一个）
        if remove_duplicates:
            for group in self.validation_results.get('duplicate_groups', []):
                indices_to_remove.update(group[1:])  # 保留第一个，移除其余
        
        # 移除异常值
        if remove_outliers:
            indices_to_remove.update(self.validation_results.get('outlier_indices', []))
        
        # 移除非药物相似化合物
        if remove_non_drug_like:
            indices_to_remove.update(self.validation_results.get('non_drug_like_indices', []))
        
        # 创建清洗后的数据集
        clean_datapoints = [
            dp for i, dp in enumerate(datapoints)
            if i not in indices_to_remove
        ]
        
        logger.info(f"数据清洗完成: {len(clean_datapoints)}/{len(datapoints)} 个数据点保留")
        return clean_datapoints


def validate_smiles(smiles: str) -> bool:
    """
    验证单个SMILES字符串
    
    Args:
        smiles: SMILES字符串
        
    Returns:
        是否有效
    """
    try:
        mol = Chem.MolFromSmiles(smiles)
        return mol is not None
    except:
        return False


def clean_smiles(smiles: str) -> Optional[str]:
    """
    清理SMILES字符串
    
    Args:
        smiles: 原始SMILES字符串
        
    Returns:
        清理后的SMILES字符串，如果无效则返回None
    """
    try:
        # 去除空白字符
        smiles = smiles.strip()
        
        # 尝试解析和重新生成
        mol = Chem.MolFromSmiles(smiles)
        if mol is not None:
            return Chem.MolToSmiles(mol)
        else:
            return None
    except:
        return None
