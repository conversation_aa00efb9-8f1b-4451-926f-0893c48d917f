# 神农框架核心依赖
# 作者: ZK
# 日期: 2025-06-27

# 深度学习框架
torch>=2.0.0
torch-geometric>=2.3.0
lightning>=2.0.0

# 化学信息学
rdkit-pypi>=2022.9.5
mordred>=1.2.0

# Chemprop集成 (需要从源码安装)
# git+https://github.com/chemprop/chemprop.git@v2.0.0

# 数据处理
numpy>=1.24.0
pandas>=1.5.0
scikit-learn>=1.3.0
scipy>=1.10.0

# 配置管理
pyyaml>=6.0
hydra-core>=1.3.0
omegaconf>=2.3.0
configargparse>=1.7
toml>=0.10.2

# 进度条和日志
tqdm>=4.64.0
loguru>=0.7.0

# 可视化
matplotlib>=3.6.0
seaborn>=0.12.0
plotly>=5.15.0

# 超参数优化
optuna>=3.2.0
ray[tune]>=2.5.0

# API服务
fastapi>=0.100.0
uvicorn>=0.22.0
pydantic>=2.0.0

# 数据验证
marshmallow>=3.19.0
cerberus>=1.3.4

# 工具库
click>=8.1.0
rich>=13.4.0
typer>=0.9.0

# 测试
pytest>=7.4.0
pytest-cov>=4.1.0
pytest-mock>=3.11.0

# 代码质量
black>=23.0.0
flake8>=6.0.0
mypy>=1.4.0
pre-commit>=3.3.0

# 文档
sphinx>=7.0.0
sphinx-rtd-theme>=1.3.0
myst-parser>=2.0.0

# 性能分析
memory-profiler>=0.60.0
line-profiler>=4.0.0

# 分子可视化
py3Dmol>=2.0.0
nglview>=3.0.0

# 并行计算
joblib>=1.3.0
multiprocessing-logging>=0.3.4

# 数据库
sqlalchemy>=2.0.0
alembic>=1.11.0

# 缓存
redis>=4.6.0
diskcache>=5.6.0

# 监控
wandb>=0.15.0
tensorboard>=2.13.0

# 安全
cryptography>=41.0.0
