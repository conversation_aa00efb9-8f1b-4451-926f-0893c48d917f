# 神农框架核心架构与创新点

## 概述

神农框架(Shennong Framework)是专门为抗菌化合物预测设计的双模态深度学习架构，融合了图神经网络和化学领域知识，实现了结构信息与化学特征的智能融合。本文档详细阐述框架的核心架构设计和关键创新点。

## 总体架构设计

### 架构概览

神农框架采用双分支融合架构：

```
输入: 分子SMILES
    ↓
┌─────────────────┐    ┌──────────────────┐
│  GNN分支         │    │  专家分支         │
│ (图结构信息)      │    │ (化学描述符特征)   │
└─────────────────┘    └──────────────────┘
    ↓                        ↓
┌─────────────────────────────────────────┐
│        化学导向注意力机制                │
└─────────────────────────────────────────┘
    ↓
┌─────────────────────────────────────────┐
│         自适应特征融合层                │
└─────────────────────────────────────────┘
    ↓
┌─────────────────────────────────────────┐
│         抗菌活性预测层                  │
└─────────────────────────────────────────┘
    ↓
输出: 抗菌活性概率 + 化学解释
```

### 核心组件

#### 1. GNN分支 (Graph Neural Network Branch)
负责提取分子图的结构信息：

```python
class GNNBranch(nn.Module):
    """
    图神经网络分支 - 处理分子图结构信息
    """
    def __init__(self, node_dim, edge_dim, hidden_dim, num_layers=3):
        super().__init__()
        self.num_layers = num_layers
        self.hidden_dim = hidden_dim
        
        # 原子嵌入层
        self.atom_embedding = nn.Linear(node_dim, hidden_dim)
        
        # 消息传递层
        self.mp_layers = nn.ModuleList([
            MessagePassingLayer(hidden_dim, edge_dim) 
            for _ in range(num_layers)
        ])
        
        # 图池化层
        self.graph_pooling = AttentionPooling(hidden_dim)
        
    def forward(self, mol_graph):
        # 原子特征嵌入
        h = self.atom_embedding(mol_graph.node_features)
        
        # 多层消息传递
        for layer in self.mp_layers:
            h = layer(h, mol_graph.edge_index, mol_graph.edge_features)
        
        # 图级表示
        graph_repr = self.graph_pooling(h, mol_graph.batch)
        
        return graph_repr
```

#### 2. 专家分支 (Expert Branch)
处理化学描述符特征：

```python
class ExpertBranch(nn.Module):
    """
    专家分支 - 处理化学领域知识特征
    """
    def __init__(self, input_dim, hidden_dim):
        super().__init__()
        
        # 特征分组处理
        self.constitutional_encoder = FeatureGroupEncoder(
            constitutional_dim, hidden_dim//4
        )
        self.topological_encoder = FeatureGroupEncoder(
            topological_dim, hidden_dim//4
        )
        self.geometric_encoder = FeatureGroupEncoder(
            geometric_dim, hidden_dim//4
        )
        self.electronic_encoder = FeatureGroupEncoder(
            electronic_dim, hidden_dim//4
        )
        
        # 特征融合
        self.feature_fusion = nn.Linear(hidden_dim, hidden_dim)
        
    def forward(self, chemical_features):
        # 按化学意义分组处理特征
        const_repr = self.constitutional_encoder(
            chemical_features[:, :constitutional_dim]
        )
        topo_repr = self.topological_encoder(
            chemical_features[:, constitutional_dim:constitutional_dim+topological_dim]
        )
        geom_repr = self.geometric_encoder(
            chemical_features[:, -geometric_dim-electronic_dim:-electronic_dim]
        )
        elec_repr = self.electronic_encoder(
            chemical_features[:, -electronic_dim:]
        )
        
        # 融合各组特征
        combined_repr = torch.cat([const_repr, topo_repr, geom_repr, elec_repr], dim=1)
        expert_repr = self.feature_fusion(combined_repr)
        
        return expert_repr
```

## 核心创新点

### 1. 化学导向注意力机制 (Chemistry-Guided Attention)

这是神农框架的第一个核心创新，使用分子的化学性质来指导特征融合的注意力权重：

```python
class ChemistryGuidedAttention(nn.Module):
    """
    化学导向注意力机制
    
    核心思想: 利用分子的化学性质(如官能团、极性、疏水性等)
    来动态调整图特征和描述符特征的融合权重
    """
    def __init__(self, graph_dim, expert_dim, chemistry_dim):
        super().__init__()
        
        # 化学性质编码器
        self.chemistry_encoder = nn.Sequential(
            nn.Linear(chemistry_dim, 64),
            nn.ReLU(),
            nn.Linear(64, 32)
        )
        
        # 注意力权重计算
        self.attention_graph = nn.Linear(32, graph_dim)
        self.attention_expert = nn.Linear(32, expert_dim)
        
        # 门控机制
        self.gate = nn.Linear(32, 1)
        
    def forward(self, graph_features, expert_features, chemistry_properties):
        # 编码化学性质
        chem_encoding = self.chemistry_encoder(chemistry_properties)
        
        # 计算注意力权重
        graph_attention = torch.sigmoid(self.attention_graph(chem_encoding))
        expert_attention = torch.sigmoid(self.attention_expert(chem_encoding))
        
        # 应用注意力
        attended_graph = graph_features * graph_attention
        attended_expert = expert_features * expert_attention
        
        # 门控融合
        gate_weight = torch.sigmoid(self.gate(chem_encoding))
        
        return attended_graph, attended_expert, gate_weight
```

#### 化学性质特征

注意力机制使用的化学性质包括：

```python
def extract_chemistry_properties(mol):
    """
    提取用于指导注意力的化学性质
    """
    properties = {}
    
    # 1. 官能团信息
    functional_groups = identify_functional_groups(mol)
    properties['functional_groups'] = encode_functional_groups(functional_groups)
    
    # 2. 分子极性
    properties['polarity'] = calculate_polarity(mol)
    
    # 3. 疏水性
    properties['hydrophobicity'] = calculate_logP(mol)
    
    # 4. 分子大小
    properties['molecular_weight'] = Descriptors.MolWt(mol)
    properties['heavy_atom_count'] = mol.GetNumHeavyAtoms()
    
    # 5. 环状结构
    properties['ring_count'] = calculate_ring_features(mol)
    
    # 6. 抗菌相关性质
    properties['antimicrobial_indicators'] = extract_antimicrobial_indicators(mol)
    
    return torch.tensor(list(properties.values()), dtype=torch.float32)
```

### 2. 自适应特征融合层 (Adaptive Feature Fusion)

第二个核心创新是自适应特征融合机制，相比Chemprop的简单拼接，实现了智能的特征交互：

```python
class AdaptiveFeatureFusion(nn.Module):
    """
    自适应特征融合层
    
    创新点: 不是简单拼接，而是学习图特征和描述符特征之间的
    复杂交互模式，实现1+1>2的效果
    """
    def __init__(self, graph_dim, expert_dim, output_dim):
        super().__init__()
        
        # 特征交互矩阵
        self.interaction_matrix = nn.Parameter(
            torch.randn(graph_dim, expert_dim) * 0.1
        )
        
        # 多头交互注意力
        self.multi_head_interaction = MultiHeadInteraction(
            graph_dim, expert_dim, num_heads=8
        )
        
        # 融合网络
        self.fusion_network = nn.Sequential(
            nn.Linear(graph_dim + expert_dim + graph_dim*expert_dim//64, output_dim*2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(output_dim*2, output_dim)
        )
        
    def forward(self, graph_features, expert_features, gate_weight):
        batch_size = graph_features.size(0)
        
        # 1. 基础特征
        basic_features = torch.cat([graph_features, expert_features], dim=1)
        
        # 2. 特征交互 (外积简化版)
        interaction = torch.bmm(
            graph_features.unsqueeze(2), 
            expert_features.unsqueeze(1)
        ).view(batch_size, -1)
        
        # 降维处理
        interaction_reduced = F.adaptive_avg_pool1d(
            interaction.unsqueeze(1), 
            graph_features.size(1)*expert_features.size(1)//64
        ).squeeze(1)
        
        # 3. 多头交互注意力
        attention_features = self.multi_head_interaction(
            graph_features, expert_features
        )
        
        # 4. 组合所有特征
        combined_features = torch.cat([
            basic_features, 
            interaction_reduced,
            attention_features
        ], dim=1)
        
        # 5. 融合输出
        fused_output = self.fusion_network(combined_features)
        
        # 6. 门控调节
        return fused_output * gate_weight + basic_features[:, :fused_output.size(1)] * (1 - gate_weight)

class MultiHeadInteraction(nn.Module):
    """
    多头特征交互注意力
    """
    def __init__(self, graph_dim, expert_dim, num_heads=8):
        super().__init__()
        self.num_heads = num_heads
        self.head_dim = min(graph_dim, expert_dim) // num_heads
        
        self.graph_projections = nn.ModuleList([
            nn.Linear(graph_dim, self.head_dim) for _ in range(num_heads)
        ])
        self.expert_projections = nn.ModuleList([
            nn.Linear(expert_dim, self.head_dim) for _ in range(num_heads)
        ])
        
        self.output_projection = nn.Linear(num_heads * self.head_dim, graph_dim)
        
    def forward(self, graph_features, expert_features):
        head_outputs = []
        
        for i in range(self.num_heads):
            # 投影到头空间
            graph_head = self.graph_projections[i](graph_features)
            expert_head = self.expert_projections[i](expert_features)
            
            # 计算交互注意力
            attention_scores = torch.bmm(
                graph_head.unsqueeze(1), 
                expert_head.unsqueeze(2)
            ).squeeze()
            attention_weights = F.softmax(attention_scores, dim=-1)
            
            # 加权特征
            weighted_features = graph_head * attention_weights.unsqueeze(-1)
            head_outputs.append(weighted_features)
        
        # 合并多头输出
        combined_output = torch.cat(head_outputs, dim=1)
        return self.output_projection(combined_output)
```

### 3. 抗菌活性专门化模块

第三个创新是针对抗菌活性预测的专门化设计：

```python
class AntimicrobialSpecializedPredictor(nn.Module):
    """
    抗菌活性专门化预测模块
    
    整合抗菌机制相关的化学知识
    """
    def __init__(self, input_dim, hidden_dim):
        super().__init__()
        
        # 抗菌机制分类器
        self.mechanism_classifier = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, 5)  # 5种主要抗菌机制
        )
        
        # 机制特定的预测器
        self.mechanism_predictors = nn.ModuleList([
            nn.Linear(hidden_dim, 1) for _ in range(5)
        ])
        
        # 最终融合预测器
        self.final_predictor = nn.Sequential(
            nn.Linear(input_dim + 5, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, 1),
            nn.Sigmoid()
        )
        
    def forward(self, fused_features):
        # 预测抗菌机制
        mechanism_logits = self.mechanism_classifier(fused_features)
        mechanism_probs = F.softmax(mechanism_logits, dim=1)
        
        # 机制特定预测
        mechanism_predictions = []
        for i, predictor in enumerate(self.mechanism_predictors):
            pred = predictor(fused_features)
            mechanism_predictions.append(pred)
        
        mechanism_predictions = torch.cat(mechanism_predictions, dim=1)
        
        # 加权预测
        weighted_prediction = torch.sum(
            mechanism_predictions * mechanism_probs, dim=1, keepdim=True
        )
        
        # 最终预测 (融合机制信息)
        final_input = torch.cat([fused_features, mechanism_probs], dim=1)
        final_prediction = self.final_predictor(final_input)
        
        return {
            'activity_prediction': final_prediction,
            'mechanism_prediction': mechanism_probs,
            'mechanism_specific_predictions': mechanism_predictions,
            'weighted_prediction': weighted_prediction
        }
```

### 4. 可解释性模块

第四个创新是丰富的可解释性设计：

```python
class InterpretabilityModule(nn.Module):
    """
    可解释性模块 - 提供化学层面的预测解释
    """
    def __init__(self, graph_dim, expert_dim):
        super().__init__()
        
        # 原子重要性计算
        self.atom_importance = nn.Linear(graph_dim, 1)
        
        # 特征重要性计算
        self.feature_importance = nn.Linear(expert_dim, 1)
        
        # 化学片段重要性
        self.fragment_analyzer = ChemicalFragmentAnalyzer()
        
    def forward(self, graph_features, expert_features, mol_graph):
        # 1. 原子级重要性
        atom_importance = self.atom_importance(graph_features)
        
        # 2. 特征重要性
        feature_importance = self.feature_importance(expert_features)
        
        # 3. 化学片段分析
        fragment_importance = self.fragment_analyzer(mol_graph)
        
        # 4. 生成解释
        explanation = {
            'atom_importance': atom_importance,
            'feature_importance': feature_importance,
            'fragment_importance': fragment_importance,
            'chemical_insights': self.generate_chemical_insights(
                atom_importance, feature_importance, fragment_importance
            )
        }
        
        return explanation
    
    def generate_chemical_insights(self, atom_imp, feature_imp, fragment_imp):
        """
        生成化学层面的洞察
        """
        insights = {
            'key_atoms': extract_important_atoms(atom_imp),
            'critical_features': extract_critical_descriptors(feature_imp),
            'active_fragments': extract_active_fragments(fragment_imp),
            'mechanism_hypothesis': generate_mechanism_hypothesis(
                atom_imp, feature_imp, fragment_imp
            )
        }
        return insights
```

## 完整的神农框架

将所有组件整合的完整框架：

```python
class ShennongFramework(nn.Module):
    """
    神农框架完整实现
    
    整合了所有核心创新：
    1. 化学导向注意力机制
    2. 自适应特征融合
    3. 抗菌活性专门化
    4. 可解释性设计
    """
    def __init__(self, config):
        super().__init__()
        
        # 双分支架构
        self.gnn_branch = GNNBranch(
            config.node_dim, config.edge_dim, 
            config.graph_hidden_dim, config.gnn_layers
        )
        
        self.expert_branch = ExpertBranch(
            config.expert_input_dim, config.expert_hidden_dim
        )
        
        # 核心创新模块
        self.chemistry_attention = ChemistryGuidedAttention(
            config.graph_hidden_dim, config.expert_hidden_dim, 
            config.chemistry_dim
        )
        
        self.adaptive_fusion = AdaptiveFeatureFusion(
            config.graph_hidden_dim, config.expert_hidden_dim,
            config.fusion_output_dim
        )
        
        self.antimicrobial_predictor = AntimicrobialSpecializedPredictor(
            config.fusion_output_dim, config.predictor_hidden_dim
        )
        
        self.interpretability = InterpretabilityModule(
            config.graph_hidden_dim, config.expert_hidden_dim
        )
        
    def forward(self, mol_graph, chemical_features, chemistry_properties):
        # 1. 双分支特征提取
        graph_features = self.gnn_branch(mol_graph)
        expert_features = self.expert_branch(chemical_features)
        
        # 2. 化学导向注意力
        attended_graph, attended_expert, gate_weight = self.chemistry_attention(
            graph_features, expert_features, chemistry_properties
        )
        
        # 3. 自适应特征融合
        fused_features = self.adaptive_fusion(
            attended_graph, attended_expert, gate_weight
        )
        
        # 4. 抗菌活性预测
        prediction_results = self.antimicrobial_predictor(fused_features)
        
        # 5. 可解释性分析
        explanations = self.interpretability(
            graph_features, expert_features, mol_graph
        )
        
        return {
            'predictions': prediction_results,
            'explanations': explanations,
            'intermediate_features': {
                'graph_features': graph_features,
                'expert_features': expert_features,
                'fused_features': fused_features,
                'attention_weights': gate_weight
            }
        }
```

## 与基线方法的比较

### 相比Chemprop的优势

| 特性 | Chemprop | 神农框架 |
|------|----------|----------|
| **特征融合** | 简单拼接 | 化学导向注意力 + 自适应融合 |
| **领域知识** | 无 | 深度集成抗菌机制知识 |
| **可解释性** | 基础特征重要性 | 多层次化学解释 |
| **专门化程度** | 通用GNN | 抗菌活性专门化 |

### 相比AutoGluon的优势

| 特性 | AutoGluon | 神农框架 |
|------|-----------|----------|
| **结构信息** | 描述符间接编码 | 直接图结构建模 |
| **化学理解** | 黑盒集成 | 白盒化学机制 |
| **创新性** | 通用AutoML | 领域特定创新 |
| **解释能力** | 统计重要性 | 化学机制解释 |

## 技术细节和实现要点

### 1. 训练策略
```python
def train_shennong_framework(model, train_loader, val_loader, config):
    """
    神农框架训练策略
    """
    optimizer = torch.optim.AdamW(
        model.parameters(), 
        lr=config.learning_rate,
        weight_decay=config.weight_decay
    )
    
    # 多任务损失
    activity_criterion = nn.BCELoss()
    mechanism_criterion = nn.CrossEntropyLoss()
    
    for epoch in range(config.epochs):
        for batch in train_loader:
            optimizer.zero_grad()
            
            results = model(
                batch.mol_graph, 
                batch.chemical_features,
                batch.chemistry_properties
            )
            
            # 组合损失
            activity_loss = activity_criterion(
                results['predictions']['activity_prediction'],
                batch.activity_labels
            )
            
            mechanism_loss = mechanism_criterion(
                results['predictions']['mechanism_prediction'],
                batch.mechanism_labels
            )
            
            total_loss = activity_loss + 0.1 * mechanism_loss
            
            total_loss.backward()
            optimizer.step()
```

### 2. 超参数配置
```yaml
# 神农框架配置
model_config:
  # GNN分支
  node_dim: 78
  edge_dim: 12
  graph_hidden_dim: 256
  gnn_layers: 3
  
  # 专家分支
  expert_input_dim: 1826  # Mordred特征维数
  expert_hidden_dim: 256
  
  # 融合模块
  chemistry_dim: 32
  fusion_output_dim: 512
  
  # 预测器
  predictor_hidden_dim: 256
  
# 训练配置
training_config:
  learning_rate: 1e-3
  weight_decay: 1e-4
  batch_size: 64
  epochs: 100
  early_stopping_patience: 10
```

## 相关文件

- [[10_研究项目/神农框架 vs Chemprop vs AutoGluon 对比研究]]
- [[30_神农框架/30.02_架构重构计划 (Shennong v2.0)]]
- [[20_核心概念/特征工程/内部集成 vs 外部注入策略对比]]

---

*创建时间: 2024-01-XX*
*最后更新: 2024-01-XX*
*状态: 架构设计完成* 