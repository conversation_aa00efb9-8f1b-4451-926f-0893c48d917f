# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-29
# 描述: 神农框架抗菌机制解释器

"""
神农框架抗菌机制解释器

基于分子结构特征解释预测的抗菌机制，提供结构-机制关系的化学解释。
"""

import logging
from typing import Dict, List, Any, Optional
from rdkit import Chem
from rdkit.Chem import rdMolDescriptors, Fragments

from ..antibacterial.mechanisms import ANTIBACTERIAL_MECHANISMS

logger = logging.getLogger(__name__)


class MechanismExplainer:
    """
    抗菌机制解释器
    
    基于分子结构特征解释预测的抗菌机制。
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化机制解释器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        
        # 机制-结构关联规则
        self.mechanism_structure_rules = self._initialize_mechanism_rules()
        
        logger.info("抗菌机制解释器初始化完成")
    
    def explain_mechanism(
        self,
        smiles: str,
        predicted_mechanism: str,
        predicted_activity: float
    ) -> Dict[str, Any]:
        """
        解释预测的抗菌机制
        
        Args:
            smiles: 分子SMILES
            predicted_mechanism: 预测机制
            predicted_activity: 预测活性
            
        Returns:
            机制解释结果
        """
        mol = Chem.MolFromSmiles(smiles)
        if mol is None:
            return {'error': '无效的SMILES结构'}
        
        analysis = {
            'mechanism_info': self._get_mechanism_info(predicted_mechanism),
            'structural_basis': self._analyze_structural_basis(mol, predicted_mechanism),
            'mechanism_confidence': self._calculate_mechanism_confidence(
                mol, predicted_mechanism, predicted_activity
            ),
            'alternative_mechanisms': self._suggest_alternative_mechanisms(mol),
            'clinical_relevance': self._assess_clinical_relevance(
                predicted_mechanism, predicted_activity
            )
        }
        
        return analysis
    
    def _initialize_mechanism_rules(self) -> Dict[str, Dict[str, Any]]:
        """初始化机制-结构关联规则"""
        return {
            'cell_wall_synthesis': {
                'key_features': [
                    {'pattern': '[C;R]1[C;R][N;R][C;R](=O)[C;R]1', 'name': 'β-内酰胺环', 'weight': 0.9},
                    {'pattern': '[C;R]1[C;R][C;R][N;R][C;R]1', 'name': '五元环', 'weight': 0.3},
                    {'pattern': '[NH2][C](=O)', 'name': '酰胺基', 'weight': 0.4},
                    {'pattern': '[OH][C]', 'name': '羟基', 'weight': 0.2}
                ],
                'molecular_properties': {
                    'mw_range': (150, 800),
                    'logp_range': (-2, 4),
                    'hbd_min': 1,
                    'hba_min': 2
                },
                'examples': ['青霉素', '头孢菌素', '万古霉素'],
                'description': '通过抑制细胞壁合成酶（如转肽酶）破坏细菌细胞壁完整性'
            },
            'protein_synthesis': {
                'key_features': [
                    {'pattern': '[NH3+]', 'name': '季铵基', 'weight': 0.7},
                    {'pattern': '[NH2][C][C][OH]', 'name': '氨基醇', 'weight': 0.6},
                    {'pattern': 'c1ccc(Cl)cc1', 'name': '氯苯基', 'weight': 0.5},
                    {'pattern': '[C][C](=O)[NH]', 'name': '酰胺键', 'weight': 0.4}
                ],
                'molecular_properties': {
                    'mw_range': (200, 1000),
                    'logp_range': (-3, 2),
                    'hbd_min': 2,
                    'hba_min': 3
                },
                'examples': ['链霉素', '氯霉素', '红霉素'],
                'description': '通过结合核糖体亚基干扰蛋白质合成过程'
            },
            'dna_replication': {
                'key_features': [
                    {'pattern': 'c1cc2c(cc1)c(=O)c(cn2)', 'name': '喹诺酮核心', 'weight': 0.9},
                    {'pattern': '[F]c1cc2c(cc1)', 'name': '氟喹诺酮', 'weight': 0.8},
                    {'pattern': '[C](=O)[OH]', 'name': '羧基', 'weight': 0.6},
                    {'pattern': 'N1CCN(CC1)', 'name': '哌嗪环', 'weight': 0.5}
                ],
                'molecular_properties': {
                    'mw_range': (250, 500),
                    'logp_range': (-1, 3),
                    'hbd_min': 1,
                    'hba_min': 3
                },
                'examples': ['环丙沙星', '左氧氟沙星', '诺氟沙星'],
                'description': '通过抑制DNA回旋酶和拓扑异构酶IV阻断DNA复制'
            },
            'cell_membrane': {
                'key_features': [
                    {'pattern': '[NH3+][C][C][C]', 'name': '长链阳离子', 'weight': 0.8},
                    {'pattern': '[C][C][C][C][C][C][C][C]', 'name': '长链烷基', 'weight': 0.6},
                    {'pattern': '[NH2][C](=O)[C]', 'name': '肽键', 'weight': 0.5},
                    {'pattern': '[OH][C][C][NH]', 'name': '氨基醇链', 'weight': 0.4}
                ],
                'molecular_properties': {
                    'mw_range': (800, 2000),
                    'logp_range': (-5, 1),
                    'hbd_min': 5,
                    'hba_min': 8
                },
                'examples': ['多粘菌素', '达托霉素', '短杆菌肽'],
                'description': '通过破坏细胞膜完整性导致细胞内容物泄漏'
            },
            'metabolic_pathway': {
                'key_features': [
                    {'pattern': '[NH2]c1nc(N)nc(OC)c1', 'name': '嘧啶环', 'weight': 0.7},
                    {'pattern': '[S](=O)(=O)[NH]', 'name': '磺酰胺基', 'weight': 0.8},
                    {'pattern': 'c1cc(N)ccc1', 'name': '苯胺基', 'weight': 0.5},
                    {'pattern': '[C](=O)[OH]', 'name': '羧基', 'weight': 0.3}
                ],
                'molecular_properties': {
                    'mw_range': (150, 400),
                    'logp_range': (-2, 3),
                    'hbd_min': 1,
                    'hba_min': 2
                },
                'examples': ['磺胺甲恶唑', '甲氧苄啶', '异烟肼'],
                'description': '通过抑制关键代谢酶干扰细菌的代谢途径'
            }
        }
    
    def _get_mechanism_info(self, mechanism: str) -> Dict[str, Any]:
        """获取机制基本信息"""
        if mechanism in ANTIBACTERIAL_MECHANISMS:
            mech_obj = ANTIBACTERIAL_MECHANISMS[mechanism]
            return {
                'name': mech_obj.name,
                'type': mech_obj.mechanism_type.value,
                'description': mech_obj.description,
                'targets': mech_obj.targets,
                'examples': mech_obj.examples,
                'gram_specificity': mech_obj.gram_specificity,
                'resistance_mechanisms': mech_obj.resistance_mechanisms
            }
        else:
            return {
                'name': mechanism,
                'description': '未知抗菌机制',
                'targets': [],
                'examples': [],
                'gram_specificity': 'unknown',
                'resistance_mechanisms': []
            }
    
    def _analyze_structural_basis(self, mol: Chem.Mol, mechanism: str) -> Dict[str, Any]:
        """分析结构基础"""
        if mechanism not in self.mechanism_structure_rules:
            return {'error': f'未知机制: {mechanism}'}
        
        rules = self.mechanism_structure_rules[mechanism]
        
        # 检查关键结构特征
        matched_features = []
        total_weight = 0
        
        for feature in rules['key_features']:
            pattern = feature['pattern']
            try:
                matches = mol.GetSubstructMatches(Chem.MolFromSmarts(pattern))
                if matches:
                    matched_features.append({
                        'name': feature['name'],
                        'pattern': pattern,
                        'matches': len(matches),
                        'weight': feature['weight'],
                        'contribution': feature['weight'] * len(matches)
                    })
                    total_weight += feature['weight'] * len(matches)
            except:
                continue
        
        # 检查分子性质
        properties = self._calculate_molecular_properties(mol)
        property_match = self._check_property_match(properties, rules['molecular_properties'])
        
        # 计算结构匹配度
        max_possible_weight = sum(f['weight'] for f in rules['key_features'])
        structure_match_score = min(1.0, total_weight / max_possible_weight) if max_possible_weight > 0 else 0
        
        return {
            'matched_features': matched_features,
            'structure_match_score': float(structure_match_score),
            'property_match': property_match,
            'molecular_properties': properties,
            'mechanism_support': structure_match_score > 0.3 and property_match['overall_match']
        }
    
    def _calculate_mechanism_confidence(
        self,
        mol: Chem.Mol,
        mechanism: str,
        activity: float
    ) -> Dict[str, Any]:
        """计算机制预测置信度"""
        
        # 结构匹配度
        structural_basis = self._analyze_structural_basis(mol, mechanism)
        structure_confidence = structural_basis.get('structure_match_score', 0)
        
        # 活性水平置信度
        activity_confidence = self._assess_activity_confidence(activity, mechanism)
        
        # 分子性质置信度
        properties = structural_basis.get('molecular_properties', {})
        property_confidence = self._assess_property_confidence(properties, mechanism)
        
        # 综合置信度
        overall_confidence = (
            structure_confidence * 0.5 +
            activity_confidence * 0.3 +
            property_confidence * 0.2
        )
        
        confidence_level = 'high' if overall_confidence > 0.7 else 'medium' if overall_confidence > 0.4 else 'low'
        
        return {
            'overall_confidence': float(overall_confidence),
            'confidence_level': confidence_level,
            'structure_confidence': float(structure_confidence),
            'activity_confidence': float(activity_confidence),
            'property_confidence': float(property_confidence),
            'confidence_factors': self._identify_confidence_factors(
                structure_confidence, activity_confidence, property_confidence
            )
        }
    
    def _suggest_alternative_mechanisms(self, mol: Chem.Mol) -> List[Dict[str, Any]]:
        """建议可能的替代机制"""
        alternatives = []
        
        for mechanism, rules in self.mechanism_structure_rules.items():
            structural_basis = self._analyze_structural_basis(mol, mechanism)
            match_score = structural_basis.get('structure_match_score', 0)
            
            if match_score > 0.2:  # 有一定匹配度
                alternatives.append({
                    'mechanism': mechanism,
                    'match_score': match_score,
                    'matched_features': len(structural_basis.get('matched_features', [])),
                    'description': rules.get('description', ''),
                    'likelihood': 'high' if match_score > 0.6 else 'medium' if match_score > 0.4 else 'low'
                })
        
        # 按匹配度排序
        alternatives.sort(key=lambda x: x['match_score'], reverse=True)
        
        return alternatives[:3]  # 返回前3个最可能的机制
    
    def _assess_clinical_relevance(self, mechanism: str, activity: float) -> Dict[str, Any]:
        """评估临床相关性"""
        
        # 机制的临床重要性
        clinical_importance = {
            'cell_wall_synthesis': 'high',
            'protein_synthesis': 'high', 
            'dna_replication': 'high',
            'cell_membrane': 'medium',
            'metabolic_pathway': 'medium'
        }
        
        importance = clinical_importance.get(mechanism, 'low')
        
        # 活性水平的临床意义
        if activity <= 1.0:
            activity_relevance = 'high'
            activity_description = '具有临床治疗潜力'
        elif activity <= 4.0:
            activity_relevance = 'medium'
            activity_description = '可能具有临床价值'
        elif activity <= 16.0:
            activity_relevance = 'low'
            activity_description = '临床价值有限'
        else:
            activity_relevance = 'very_low'
            activity_description = '临床价值很低'
        
        # 耐药性考虑
        resistance_risk = self._assess_resistance_risk(mechanism)
        
        return {
            'mechanism_importance': importance,
            'activity_relevance': activity_relevance,
            'activity_description': activity_description,
            'resistance_risk': resistance_risk,
            'overall_clinical_potential': self._calculate_clinical_potential(
                importance, activity_relevance, resistance_risk
            )
        }
    
    def _calculate_molecular_properties(self, mol: Chem.Mol) -> Dict[str, float]:
        """计算分子性质"""
        return {
            'mw': rdMolDescriptors.CalcExactMolWt(mol),
            'logp': rdMolDescriptors.CalcCrippenDescriptors(mol)[0],
            'hbd': rdMolDescriptors.CalcNumHBD(mol),
            'hba': rdMolDescriptors.CalcNumHBA(mol),
            'tpsa': rdMolDescriptors.CalcTPSA(mol),
            'rotatable_bonds': rdMolDescriptors.CalcNumRotatableBonds(mol),
            'aromatic_rings': rdMolDescriptors.CalcNumAromaticRings(mol)
        }
    
    def _check_property_match(self, properties: Dict[str, float], requirements: Dict[str, Any]) -> Dict[str, Any]:
        """检查分子性质匹配"""
        matches = {}
        
        # 检查分子量范围
        if 'mw_range' in requirements:
            mw_min, mw_max = requirements['mw_range']
            matches['mw'] = mw_min <= properties['mw'] <= mw_max
        
        # 检查LogP范围
        if 'logp_range' in requirements:
            logp_min, logp_max = requirements['logp_range']
            matches['logp'] = logp_min <= properties['logp'] <= logp_max
        
        # 检查氢键供体最小值
        if 'hbd_min' in requirements:
            matches['hbd'] = properties['hbd'] >= requirements['hbd_min']
        
        # 检查氢键受体最小值
        if 'hba_min' in requirements:
            matches['hba'] = properties['hba'] >= requirements['hba_min']
        
        overall_match = sum(matches.values()) >= len(matches) * 0.6  # 至少60%匹配
        
        return {
            'individual_matches': matches,
            'overall_match': overall_match,
            'match_percentage': sum(matches.values()) / len(matches) if matches else 0
        }
    
    def _assess_activity_confidence(self, activity: float, mechanism: str) -> float:
        """评估活性置信度"""
        # 不同机制的典型活性范围
        typical_ranges = {
            'cell_wall_synthesis': (0.1, 10),
            'protein_synthesis': (0.5, 20),
            'dna_replication': (0.2, 8),
            'cell_membrane': (1, 50),
            'metabolic_pathway': (2, 100)
        }
        
        if mechanism in typical_ranges:
            min_val, max_val = typical_ranges[mechanism]
            if min_val <= activity <= max_val:
                return 0.8
            else:
                # 计算偏离程度
                if activity < min_val:
                    deviation = (min_val - activity) / min_val
                else:
                    deviation = (activity - max_val) / max_val
                return max(0.2, 0.8 - deviation)
        
        return 0.5  # 默认中等置信度
    
    def _assess_property_confidence(self, properties: Dict[str, float], mechanism: str) -> float:
        """评估分子性质置信度"""
        if mechanism not in self.mechanism_structure_rules:
            return 0.5
        
        requirements = self.mechanism_structure_rules[mechanism]['molecular_properties']
        property_match = self._check_property_match(properties, requirements)
        
        return property_match['match_percentage']
    
    def _identify_confidence_factors(
        self,
        structure_conf: float,
        activity_conf: float,
        property_conf: float
    ) -> Dict[str, List[str]]:
        """识别置信度影响因素"""
        positive_factors = []
        negative_factors = []
        
        if structure_conf > 0.7:
            positive_factors.append('结构特征高度匹配')
        elif structure_conf < 0.3:
            negative_factors.append('结构特征匹配度低')
        
        if activity_conf > 0.7:
            positive_factors.append('活性水平符合预期')
        elif activity_conf < 0.3:
            negative_factors.append('活性水平异常')
        
        if property_conf > 0.7:
            positive_factors.append('分子性质符合要求')
        elif property_conf < 0.3:
            negative_factors.append('分子性质不匹配')
        
        return {
            'positive_factors': positive_factors,
            'negative_factors': negative_factors
        }
    
    def _assess_resistance_risk(self, mechanism: str) -> Dict[str, Any]:
        """评估耐药风险"""
        resistance_risks = {
            'cell_wall_synthesis': {
                'risk_level': 'high',
                'main_mechanisms': ['β-内酰胺酶', 'PBP改变', '外排泵'],
                'description': 'β-内酰胺类抗生素耐药性普遍存在'
            },
            'protein_synthesis': {
                'risk_level': 'medium',
                'main_mechanisms': ['核糖体甲基化', '外排泵', '酶修饰'],
                'description': '蛋白质合成抑制剂耐药性逐渐增加'
            },
            'dna_replication': {
                'risk_level': 'medium',
                'main_mechanisms': ['靶点突变', '外排泵', 'DNA保护蛋白'],
                'description': '喹诺酮类耐药性在某些菌株中较高'
            },
            'cell_membrane': {
                'risk_level': 'low',
                'main_mechanisms': ['膜组成改变', '外排泵'],
                'description': '膜破坏剂耐药性相对较少'
            },
            'metabolic_pathway': {
                'risk_level': 'medium',
                'main_mechanisms': ['酶过表达', '旁路途径', '靶点修饰'],
                'description': '代谢抑制剂可能出现旁路耐药'
            }
        }
        
        return resistance_risks.get(mechanism, {
            'risk_level': 'unknown',
            'main_mechanisms': [],
            'description': '耐药风险未知'
        })
    
    def _calculate_clinical_potential(
        self,
        importance: str,
        activity_relevance: str,
        resistance_risk: Dict[str, Any]
    ) -> Dict[str, Any]:
        """计算临床潜力"""
        
        # 重要性评分
        importance_scores = {'high': 0.8, 'medium': 0.6, 'low': 0.4}
        importance_score = importance_scores.get(importance, 0.4)
        
        # 活性相关性评分
        activity_scores = {'high': 0.8, 'medium': 0.6, 'low': 0.4, 'very_low': 0.2}
        activity_score = activity_scores.get(activity_relevance, 0.4)
        
        # 耐药风险评分（风险越低分数越高）
        resistance_scores = {'low': 0.8, 'medium': 0.6, 'high': 0.4}
        resistance_score = resistance_scores.get(resistance_risk.get('risk_level', 'medium'), 0.6)
        
        # 综合评分
        overall_score = (importance_score * 0.4 + activity_score * 0.4 + resistance_score * 0.2)
        
        if overall_score > 0.7:
            potential_level = 'high'
        elif overall_score > 0.5:
            potential_level = 'medium'
        else:
            potential_level = 'low'
        
        return {
            'overall_score': float(overall_score),
            'potential_level': potential_level,
            'importance_score': float(importance_score),
            'activity_score': float(activity_score),
            'resistance_score': float(resistance_score)
        }
