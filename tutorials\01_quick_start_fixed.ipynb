{"cells": [{"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["# 🧬 神农框架快速开始教程（修复版）\n", "\n", "**作者**: ZK  \n", "**邮箱**: <EMAIL>  \n", "**日期**: 2025-01-13\n", "\n", "欢迎使用神农框架！这是一个基于深度学习的抗菌化合物预测系统。\n", "\n", "## 📋 教程概述\n", "\n", "在这个快速开始教程中，您将学会：\n", "1. 安装和配置神农框架\n", "2. 准备示例数据\n", "3. 训练您的第一个模型\n", "4. 进行抗菌活性预测\n", "5. 分析结果和注意力权重\n", "\n", "预计完成时间：**15-20分钟**\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 🔧 环境检查和安装\n", "\n", "首先，让我们检查环境并安装必要的依赖。\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Python版本: 3.11.8 (tags/v3.11.8:db85d51, Feb  6 2024, 22:03:32) [MSC v.1937 64 bit (AMD64)]\n", "PyTorch版本: 2.7.1+cu118\n", "CUDA可用: True\n", "RDKit版本: 2025.03.3\n"]}], "source": ["# 检查Python版本\n", "import sys\n", "print(f\"Python版本: {sys.version}\")\n", "\n", "# 检查关键依赖\n", "try:\n", "    import torch\n", "    print(f\"PyTorch版本: {torch.__version__}\")\n", "    print(f\"CUDA可用: {torch.cuda.is_available()}\")\n", "except ImportError:\n", "    print(\"❌ PyTorch未安装\")\n", "\n", "try:\n", "    import rdkit\n", "    print(f\"RDKit版本: {rdkit.__version__}\")\n", "except ImportError:\n", "    print(\"❌ RDKit未安装\")\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["当前目录: E:\\新建文件夹\\Shennong\n", "✅ 已在正确目录中\n", "✅ 找到setup.py文件，准备安装...\n"]}], "source": ["# 检查当前目录并切换到正确的安装目录\n", "import os\n", "import sys\n", "\n", "current_dir = os.getcwd()\n", "print(f\"当前目录: {current_dir}\")\n", "\n", "# 确保在正确的目录中\n", "target_dir = r\"E:\\新建文件夹\\Shennong\"\n", "if not current_dir.endswith(\"Shennong\"):\n", "    try:\n", "        os.chdir(target_dir)\n", "        print(f\"✅ 切换到目录: {os.getcwd()}\")\n", "    except Exception as e:\n", "        print(f\"❌ 无法切换目录: {e}\")\n", "        print(\"请手动切换到 E:\\\\新建文件夹\\\\Shennong 目录\")\n", "else:\n", "    print(\"✅ 已在正确目录中\")\n", "\n", "# 验证setup.py文件是否存在\n", "if os.path.exists(\"setup.py\"):\n", "    print(\"✅ 找到setup.py文件，准备安装...\")\n", "else:\n", "    print(\"❌ 未找到setup.py文件，请确保在正确的目录中\")\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 开始安装神农框架...\n", "Obtaining file:///E:/%E6%96%B0%E5%BB%BA%E6%96%87%E4%BB%B6%E5%A4%B9/Shennong\n", "  Installing build dependencies: started\n", "  Installing build dependencies: finished with status 'done'\n", "  Checking if build backend supports build_editable: started\n", "  Checking if build backend supports build_editable: finished with status 'done'\n", "  Getting requirements to build editable: started\n", "  Getting requirements to build editable: finished with status 'done'\n", "  Preparing editable metadata (pyproject.toml): started\n", "  Preparing editable metadata (pyproject.toml): finished with status 'done'\n", "Requirement already satisfied: torch>=2.0.0 in d:\\python\\lib\\site-packages (from shennong-framework==1.0.0) (2.7.1+cu118)\n", "Requirement already satisfied: torch-geometric>=2.3.0 in d:\\python\\lib\\site-packages (from shennong-framework==1.0.0) (2.6.1)\n", "Requirement already satisfied: lightning>=2.0.0 in d:\\python\\lib\\site-packages (from shennong-framework==1.0.0) (2.5.2)\n", "Requirement already satisfied: rdkit-pypi>=2022.9.5 in d:\\python\\lib\\site-packages (from shennong-framework==1.0.0) (2022.9.5)\n", "Requirement already satisfied: numpy>=1.24.0 in d:\\python\\lib\\site-packages (from shennong-framework==1.0.0) (1.26.4)\n", "Requirement already satisfied: pandas>=1.5.0 in d:\\python\\lib\\site-packages (from shennong-framework==1.0.0) (2.3.0)\n", "Requirement already satisfied: scikit-learn>=1.3.0 in d:\\python\\lib\\site-packages (from shennong-framework==1.0.0) (1.7.0)\n", "Requirement already satisfied: scipy>=1.10.0 in d:\\python\\lib\\site-packages (from shennong-framework==1.0.0) (1.16.0)\n", "Requirement already satisfied: pyyaml>=6.0 in d:\\python\\lib\\site-packages (from shennong-framework==1.0.0) (6.0.2)\n", "Requirement already satisfied: tqdm>=4.64.0 in d:\\python\\lib\\site-packages (from shennong-framework==1.0.0) (4.67.1)\n", "Requirement already satisfied: matplotlib>=3.6.0 in d:\\python\\lib\\site-packages (from shennong-framework==1.0.0) (3.10.3)\n", "Requirement already satisfied: seaborn>=0.12.0 in d:\\python\\lib\\site-packages (from shennong-framework==1.0.0) (0.13.2)\n", "Requirement already satisfied: click>=8.1.0 in d:\\python\\lib\\site-packages (from shennong-framework==1.0.0) (8.2.1)\n", "Requirement already satisfied: colorama in d:\\python\\lib\\site-packages (from click>=8.1.0->shennong-framework==1.0.0) (0.4.6)\n", "Requirement already satisfied: fsspec<2027.0,>=2022.5.0 in d:\\python\\lib\\site-packages (from fsspec[http]<2027.0,>=2022.5.0->lightning>=2.0.0->shennong-framework==1.0.0) (2024.6.1)\n", "Requirement already satisfied: lightning-utilities<2.0,>=0.10.0 in d:\\python\\lib\\site-packages (from lightning>=2.0.0->shennong-framework==1.0.0) (0.14.3)\n", "Requirement already satisfied: packaging<27.0,>=20.0 in d:\\python\\lib\\site-packages (from lightning>=2.0.0->shennong-framework==1.0.0) (25.0)\n", "Requirement already satisfied: torchmetrics<3.0,>=0.7.0 in d:\\python\\lib\\site-packages (from lightning>=2.0.0->shennong-framework==1.0.0) (1.7.3)\n", "Requirement already satisfied: typing-extensions<6.0,>=4.4.0 in d:\\python\\lib\\site-packages (from lightning>=2.0.0->shennong-framework==1.0.0) (4.14.0)\n", "Requirement already satisfied: pytorch-lightning in d:\\python\\lib\\site-packages (from lightning>=2.0.0->shennong-framework==1.0.0) (2.5.2)\n", "Requirement already satisfied: aiohttp!=4.0.0a0,!=4.0.0a1 in d:\\python\\lib\\site-packages (from fsspec[http]<2027.0,>=2022.5.0->lightning>=2.0.0->shennong-framework==1.0.0) (3.12.13)\n", "Requirement already satisfied: setuptools in d:\\python\\lib\\site-packages (from lightning-utilities<2.0,>=0.10.0->lightning>=2.0.0->shennong-framework==1.0.0) (65.5.0)\n", "Requirement already satisfied: filelock in d:\\python\\lib\\site-packages (from torch>=2.0.0->shennong-framework==1.0.0) (3.18.0)\n", "Requirement already satisfied: sympy>=1.13.3 in d:\\python\\lib\\site-packages (from torch>=2.0.0->shennong-framework==1.0.0) (1.13.3)\n", "Requirement already satisfied: networkx in d:\\python\\lib\\site-packages (from torch>=2.0.0->shennong-framework==1.0.0) (3.3)\n", "Requirement already satisfied: jinja2 in d:\\python\\lib\\site-packages (from torch>=2.0.0->shennong-framework==1.0.0) (3.1.6)\n", "Requirement already satisfied: aiohappyeyeballs>=2.5.0 in d:\\python\\lib\\site-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<2027.0,>=2022.5.0->lightning>=2.0.0->shennong-framework==1.0.0) (2.6.1)\n", "Requirement already satisfied: aiosignal>=1.1.2 in d:\\python\\lib\\site-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<2027.0,>=2022.5.0->lightning>=2.0.0->shennong-framework==1.0.0) (1.3.2)\n", "Requirement already satisfied: attrs>=17.3.0 in d:\\python\\lib\\site-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<2027.0,>=2022.5.0->lightning>=2.0.0->shennong-framework==1.0.0) (25.3.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in d:\\python\\lib\\site-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<2027.0,>=2022.5.0->lightning>=2.0.0->shennong-framework==1.0.0) (1.7.0)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in d:\\python\\lib\\site-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<2027.0,>=2022.5.0->lightning>=2.0.0->shennong-framework==1.0.0) (6.5.0)\n", "Requirement already satisfied: propcache>=0.2.0 in d:\\python\\lib\\site-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<2027.0,>=2022.5.0->lightning>=2.0.0->shennong-framework==1.0.0) (0.3.2)\n", "Requirement already satisfied: yarl<2.0,>=1.17.0 in d:\\python\\lib\\site-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<2027.0,>=2022.5.0->lightning>=2.0.0->shennong-framework==1.0.0) (1.20.1)\n", "Requirement already satisfied: idna>=2.0 in d:\\python\\lib\\site-packages (from yarl<2.0,>=1.17.0->aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<2027.0,>=2022.5.0->lightning>=2.0.0->shennong-framework==1.0.0) (3.10)\n", "Requirement already satisfied: contourpy>=1.0.1 in d:\\python\\lib\\site-packages (from matplotlib>=3.6.0->shennong-framework==1.0.0) (1.3.2)\n", "Requirement already satisfied: cycler>=0.10 in d:\\python\\lib\\site-packages (from matplotlib>=3.6.0->shennong-framework==1.0.0) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in d:\\python\\lib\\site-packages (from matplotlib>=3.6.0->shennong-framework==1.0.0) (4.58.4)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in d:\\python\\lib\\site-packages (from matplotlib>=3.6.0->shennong-framework==1.0.0) (1.4.8)\n", "Requirement already satisfied: pillow>=8 in d:\\python\\lib\\site-packages (from matplotlib>=3.6.0->shennong-framework==1.0.0) (11.2.1)\n", "Requirement already satisfied: pyparsing>=2.3.1 in d:\\python\\lib\\site-packages (from matplotlib>=3.6.0->shennong-framework==1.0.0) (3.2.3)\n", "Requirement already satisfied: python-dateutil>=2.7 in d:\\python\\lib\\site-packages (from matplotlib>=3.6.0->shennong-framework==1.0.0) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in d:\\python\\lib\\site-packages (from pandas>=1.5.0->shennong-framework==1.0.0) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in d:\\python\\lib\\site-packages (from pandas>=1.5.0->shennong-framework==1.0.0) (2025.2)\n", "Requirement already satisfied: six>=1.5 in d:\\python\\lib\\site-packages (from python-dateutil>=2.7->matplotlib>=3.6.0->shennong-framework==1.0.0) (1.17.0)\n", "Requirement already satisfied: joblib>=1.2.0 in d:\\python\\lib\\site-packages (from scikit-learn>=1.3.0->shennong-framework==1.0.0) (1.5.1)\n", "Requirement already satisfied: threadpoolctl>=3.1.0 in d:\\python\\lib\\site-packages (from scikit-learn>=1.3.0->shennong-framework==1.0.0) (3.6.0)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in d:\\python\\lib\\site-packages (from sympy>=1.13.3->torch>=2.0.0->shennong-framework==1.0.0) (1.3.0)\n", "Requirement already satisfied: psutil>=5.8.0 in d:\\python\\lib\\site-packages (from torch-geometric>=2.3.0->shennong-framework==1.0.0) (7.0.0)\n", "Requirement already satisfied: requests in d:\\python\\lib\\site-packages (from torch-geometric>=2.3.0->shennong-framework==1.0.0) (2.32.4)\n", "Requirement already satisfied: MarkupSafe>=2.0 in d:\\python\\lib\\site-packages (from jinja2->torch>=2.0.0->shennong-framework==1.0.0) (3.0.2)\n", "Requirement already satisfied: charset_normalizer<4,>=2 in d:\\python\\lib\\site-packages (from requests->torch-geometric>=2.3.0->shennong-framework==1.0.0) (3.4.2)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in d:\\python\\lib\\site-packages (from requests->torch-geometric>=2.3.0->shennong-framework==1.0.0) (2.5.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in d:\\python\\lib\\site-packages (from requests->torch-geometric>=2.3.0->shennong-framework==1.0.0) (2025.6.15)\n", "Building wheels for collected packages: shennong-framework\n", "  Building editable for shennong-framework (pyproject.toml): started\n", "  Building editable for shennong-framework (pyproject.toml): finished with status 'done'\n", "  Created wheel for shennong-framework: filename=shennong_framework-1.0.0-0.editable-py3-none-any.whl size=7157 sha256=2181a711bbe27c3446434b338aae209a8b6d867ee656661fa229b0d9b35b84af\n", "  Stored in directory: C:\\Users\\<USER>\\AppData\\Local\\Temp\\pip-ephem-wheel-cache-c9nwjuwq\\wheels\\54\\3e\\b4\\da4c6fff0f1f8e4745cb2533d3b57b715f40ac895349b5affb\n", "Successfully built shennong-framework\n", "Installing collected packages: shennong-framework\n", "  Attempting uninstall: shennong-framework\n", "    Found existing installation: shennong-framework 1.0.0\n", "    Uninstalling shennong-framework-1.0.0:\n", "      Successfully uninstalled shennong-framework-1.0.0\n", "Successfully installed shennong-framework-1.0.0\n", "Note: you may need to restart the kernel to use updated packages.\n", "✅ 安装完成！\n"]}], "source": ["# 安装神农框架（现在setup.py已经修复）\n", "print(\"🚀 开始安装神农框架...\")\n", "%pip install -e .\n", "print(\"✅ 安装完成！\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 验证神农框架安装\n", "try:\n", "    import shennong\n", "    print(f\"✅ 神农框架导入成功！\")\n", "    print(f\"框架位置: {shennong.__file__}\")\n", "    \n", "    # 检查主要模块\n", "    from shennong.models import base\n", "    from shennong.data import datasets\n", "    from shennong.utils import config\n", "    print(\"✅ 主要模块导入成功\")\n", "    \n", "except ImportError as e:\n", "    print(f\"⚠️ 神农框架导入失败: {e}\")\n", "    print(\"将使用简化版演示，基于基础机器学习库\")\n", "    \n", "    # 导入基础依赖进行演示\n", "    import pandas as pd\n", "    import numpy as np\n", "    from rdkit import Chem\n", "    from sklearn.ensemble import RandomForestRegressor\n", "    print(\"✅ 基础依赖导入成功，准备简化版演示\")\n", "    \n", "except Exception as e:\n", "    print(f\"⚠️ 部分模块导入失败: {e}\")\n", "    print(\"这可能是因为缺少可选依赖，基本功能应该可用\")\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 📊 创建示例数据\n", "\n", "让我们创建一些示例抗菌化合物数据用于演示。这些数据包含真实的化学结构（SMILES）和模拟的抗菌活性值。\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 创建了包含 20 个抗菌化合物样本的数据集\n", "📁 数据文件: data\\antibacterial_sample.csv\n", "\\n📋 数据集概览:\n", "   - 化合物数量: 20\n", "   - 机制类型: 7 种\n", "   - MIC范围: 0.200 - 156.285 μg/mL\n", "\\n🔬 前5个样本:\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[16:13:26] SMILES Parse Error: unclosed ring for input: 'CCN(CC)CCCC(C)NC1=C2C=CC(=CC2=NC=C1)CF3'\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>smiles</th>\n", "      <th>compound_name</th>\n", "      <th>mechanism</th>\n", "      <th>activity_mic</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>CN1CCN(CC1)C2=C(C=C3C(=C2F)N(C=C(C3=O)C(=O)O)C...</td>\n", "      <td>环丙沙星</td>\n", "      <td>DNA复制抑制</td>\n", "      <td>0.529</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>CC1=C(C=C2C(=C1)N(C=C(C2=O)C(=O)O)C3CC3)N4CCN(...</td>\n", "      <td>诺氟沙星</td>\n", "      <td>DNA复制抑制</td>\n", "      <td>0.385</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>CCN(C)C1=NC2=C(C=C(C=C2)F)C(=C1C(=O)O)C(=O)C3=...</td>\n", "      <td>氟喹酸</td>\n", "      <td>DNA复制抑制</td>\n", "      <td>0.743</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>CC1([C@@H](N2[C@H](S1)[C@@H](C2=O)NC(=O)CC3=CC...</td>\n", "      <td>青霉素G</td>\n", "      <td>细胞壁抑制</td>\n", "      <td>0.933</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>CC1=C(N2[C@@H]([C@@H](C2=O)NC(=O)[C@@H](C3=CC=...</td>\n", "      <td>头孢菌素</td>\n", "      <td>细胞壁抑制</td>\n", "      <td>0.200</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                              smiles compound_name mechanism  \\\n", "0  CN1CCN(CC1)C2=C(C=C3C(=C2F)N(C=C(C3=O)C(=O)O)C...          环丙沙星   DNA复制抑制   \n", "1  CC1=C(C=C2C(=C1)N(C=C(C2=O)C(=O)O)C3CC3)N4CCN(...          诺氟沙星   DNA复制抑制   \n", "2  CCN(C)C1=NC2=C(C=C(C=C2)F)C(=C1C(=O)O)C(=O)C3=...           氟喹酸   DNA复制抑制   \n", "3  CC1([C@@H](N2[C@H](S1)[C@@H](C2=O)NC(=O)CC3=CC...          青霉素G     细胞壁抑制   \n", "4  CC1=C(N2[C@@H]([C@@H](C2=O)NC(=O)[C@@H](C3=CC=...          头孢菌素     细胞壁抑制   \n", "\n", "   activity_mic  \n", "0         0.529  \n", "1         0.385  \n", "2         0.743  \n", "3         0.933  \n", "4         0.200  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "import numpy as np\n", "from pathlib import Path\n", "from rdkit import Chem\n", "from rdkit.Chem import rdMolDescriptors, Descriptors\n", "\n", "# 创建示例数据目录\n", "data_dir = Path(\"data\")\n", "data_dir.mkdir(exist_ok=True)\n", "\n", "# 示例抗菌化合物SMILES（真实的抗生素和抗菌化合物）\n", "sample_data = [\n", "    # 氟喹诺酮类\n", "    (\"CN1CCN(CC1)C2=C(C=C3C(=C2F)N(C=C(C3=O)C(=O)O)C4CC4)F\", \"环丙沙星\", \"DNA复制抑制\"),\n", "    (\"CC1=C(C=C2C(=C1)N(C=C(C2=O)C(=O)O)C3CC3)N4CCN(CC4)C\", \"诺氟沙星\", \"DNA复制抑制\"),\n", "    (\"CCN(C)C1=NC2=C(C=C(C=C2)F)C(=C1C(=O)O)C(=O)C3=CC=C(C=C3)F\", \"氟喹酸\", \"DNA复制抑制\"),\n", "    \n", "    # β-内酰胺类\n", "    (\"CC1([C@@H](N2[C@H](S1)[C@@H](C2=O)NC(=O)CC3=CC=CC=C3)C(=O)O)C\", \"青霉素G\", \"细胞壁抑制\"),\n", "    (\"CC1=C(N2[C@@H]([C@@H](C2=O)NC(=O)[C@@H](C3=CC=C(C=C3)O)N)SC1)C(=O)O\", \"头孢菌素\", \"细胞壁抑制\"),\n", "    (\"CNC(=O)[C@@H]1CC[C@@H](CC1)OC2=C(C=C(C=C2)NC(=O)C3=C(C=CC=C3Cl)Cl)F\", \"安必仙\", \"细胞壁抑制\"),\n", "    \n", "    # 氨基糖苷类\n", "    (\"CN[C@@H]1[C@@H]([C@H]([C@@H]([C@H](O1)O[C@@H]2[C@H](O[C@H]([C@@H]([C@H]2O)N)O[C@@H]3[C@H]([C@@H]([C@H]([C@@H](O3)CN)O)O)N)CO)N)O)O\", \"链霉素\", \"蛋白质合成抑制\"),\n", "    (\"C[C@@H]1[C@H]([C@@H]([C@@H]([C@@H](O1)O[C@@H]2[C@H](O[C@H]([C@@H]([C@H]2O)N)O[C@@H]3[C@H]([C@@H]([C@H]([C@@H](O3)CN)O)O)N)CO)N)O)NC\", \"庆大霉素\", \"蛋白质合成抑制\"),\n", "    \n", "    # 大环内酯类\n", "    (\"CC[C@H]1OC(=O)[C@H](C)[C@@H](O[C@H]2C[C@@](C)(OC)[C@@H](O)[C@H](C)O2)[C@H](C)[C@@H](O[C@@H]3O[C@H](C)C[C@H]([C@H]3O)N(C)C)[C@](C)(O)C[C@@H](C)C(=O)[C@H](C)[C@@H](O)[C@]1(C)O\", \"红霉素\", \"蛋白质合成抑制\"),\n", "    (\"CC[C@@H]1[C@@]([C@@H]([C@H](C(=O)[C@@H](C[C@@]([C@@H]([C@H]([C@@H]([C@H](C(=O)O1)C)O[C@H]2C[C@@]([C@H]([C@@H](O2)C)O)(C)OC)C)O[C@H]3[C@@H]([C@H](C[C@H](O3)C)N(C)C)O)(C)O)C)C)O)(C)O\", \"阿奇霉素\", \"蛋白质合成抑制\"),\n", "    \n", "    # 四环素类\n", "    (\"CN(C)[C@H]1[C@@H]2[C@@H]([C@@H]3[C@H](C(=C(C(=O)[C@]3(C(=O)C2=C(C4=C1C=CC=C4O)O)O)O)C(=O)N)O)O\", \"四环素\", \"蛋白质合成抑制\"),\n", "    (\"CN(C)[C@H]1[C@@H]2[C@@H]([C@@H]3[C@H](C(=C(C(=O)[C@]3(C(=O)C2=C(C4=C1C=CC=C4O)O)O)O)C(=O)N)O)O\", \"强力霉素\", \"蛋白质合成抑制\"),\n", "    \n", "    # 磺胺类\n", "    (\"CC1=CC=C(C=C1)S(=O)(=O)NC2=NC=CC=N2\", \"磺胺嘧啶\", \"代谢途径抑制\"),\n", "    (\"CC1=CC=C(C=C1)S(=O)(=O)NC2=NC(=CC=N2)OC\", \"磺胺甲恶唑\", \"代谢途径抑制\"),\n", "    \n", "    # 抗真菌药物\n", "    (\"CCN(CC)CCCC(C1=CC=CC=C1)(C2=CC=C(C=C2)Cl)O\", \"克霉唑\", \"细胞膜破坏\"),\n", "    (\"C[C@H]([C@H]1CC[C@@]2([C@@]1(CC[C@H]3[C@H]2CC[C@@H]4[C@@]3(CC[C@H](C4)O)C)C)C)C(C)C\", \"麦角甾醇\", \"细胞膜破坏\"),\n", "    \n", "    # 抗病毒药物\n", "    (\"CCN(CC)CCCC(C)NC1=C2C=CC(=CC2=NC=C1)CF3\", \"氯喹\", \"病毒复制抑制\"),\n", "    (\"NC1=NC=NC2=C1N=CN2[C@@H]3O[C@H](CO)[C@@H](O)[C@H]3O\", \"阿昔洛韦\", \"病毒复制抑制\"),\n", "    \n", "    # 对照化合物（非抗菌）\n", "    (\"CC(C)CC1=CC=C(C=C1)C(C)C(=O)O\", \"布洛芬\", \"非抗菌\"),\n", "    (\"CC(=O)NC1=CC=C(C=C1)O\", \"对乙酰氨基酚\", \"非抗菌\"),\n", "    (\"CC(C)(C)OOC(C)(C)C\", \"过氧化物\", \"非抗菌\"),\n", "]\n", "\n", "# 生成数据框\n", "smiles_list = []\n", "names_list = []\n", "mechanisms_list = []\n", "activities_list = []\n", "\n", "np.random.seed(42)  # 确保结果可重现\n", "\n", "for smiles, name, mechanism in sample_data:\n", "    mol = Chem.Mo<PERSON>rom<PERSON>(smiles)\n", "    if mol is not None:\n", "        # 基于分子特征生成模拟的MIC值（最小抑菌浓度，μg/mL）\n", "        mw = Descriptors.MolWt(mol)\n", "        logp = Descriptors.MolLogP(mol)\n", "        hbd = Descriptors.NumHDonors(mol)\n", "        tpsa = Descriptors.TPSA(mol)\n", "        \n", "        # 根据机制和分子特征计算基础活性\n", "        if mechanism == \"非抗菌\":\n", "            base_activity = 100 + np.random.normal(50, 20)  # 高MIC值（低活性）\n", "        elif mechanism == \"细胞壁抑制\":\n", "            base_activity = 0.5 + (mw - 400) / 200 + np.random.normal(0, 0.5)\n", "        elif mechanism == \"DNA复制抑制\":\n", "            base_activity = 0.1 + (400 - mw) / 500 + logp * 0.1 + np.random.normal(0, 0.3)\n", "        elif mechanism == \"蛋白质合成抑制\":\n", "            base_activity = 1.0 + (tpsa - 150) / 200 + np.random.normal(0, 0.8)\n", "        elif mechanism == \"代谢途径抑制\":\n", "            base_activity = 2.0 + hbd * 0.5 + np.random.normal(0, 1.0)\n", "        elif mechanism == \"细胞膜破坏\":\n", "            base_activity = 0.8 + abs(logp - 3) * 0.5 + np.random.normal(0, 0.6)\n", "        else:\n", "            base_activity = 5.0 + np.random.normal(0, 3.0)\n", "        \n", "        # 确保MIC值为正数且在合理范围内\n", "        activity = max(0.01, base_activity)\n", "        if activity > 200:\n", "            activity = 200\n", "            \n", "        smiles_list.append(smiles)\n", "        names_list.append(name)\n", "        mechanisms_list.append(mechanism)\n", "        activities_list.append(round(activity, 3))\n", "\n", "# 创建DataFrame\n", "df = pd.DataFrame({\n", "    'smiles': smiles_list,\n", "    'compound_name': names_list,\n", "    'mechanism': mechanisms_list,\n", "    'activity_mic': activities_list  # MIC值，μg/mL，值越小活性越强\n", "})\n", "\n", "# 保存数据\n", "sample_file = data_dir / \"antibacterial_sample.csv\"\n", "df.to_csv(sample_file, index=False)\n", "\n", "print(f\"✅ 创建了包含 {len(df)} 个抗菌化合物样本的数据集\")\n", "print(f\"📁 数据文件: {sample_file}\")\n", "print(\"\\\\n📋 数据集概览:\")\n", "print(f\"   - 化合物数量: {len(df)}\")\n", "print(f\"   - 机制类型: {df['mechanism'].nunique()} 种\")\n", "print(f\"   - MIC范围: {df['activity_mic'].min():.3f} - {df['activity_mic'].max():.3f} μg/mL\")\n", "\n", "print(\"\\\\n🔬 前5个样本:\")\n", "df.head()\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 详细数据统计:\n", "==================================================\n", "总化合物数: 20\n", "MIC值统计:\n", "  - 平均值: 21.542 μg/mL\n", "  - 中位数: 0.911 μg/mL\n", "  - 标准差: 49.941 μg/mL\n", "  - 最小值: 0.200 μg/mL (最强)\n", "  - 最大值: 156.285 μg/mL (最弱)\n", "\\n🔬 按机制分类的统计:\n", "  DNA复制抑制: 3.0个化合物, 平均MIC=0.552±0.180\n", "  代谢途径抑制: 2.0个化合物, 平均MIC=1.664±1.524\n", "  病毒复制抑制: 1.0个化合物, 平均MIC=1.962±nan\n", "  细胞壁抑制: 3.0个化合物, 平均MIC=0.571±0.367\n", "  细胞膜破坏: 2.0个化合物, 平均MIC=1.460±1.197\n", "  蛋白质合成抑制: 6.0个化合物, 平均MIC=1.564±0.859\n", "  非抗菌: 3.0个化合物, 平均MIC=136.626±17.756\n", "\\n🎯 活性分级:\n", "  强活性(<1): 11个 (55.0%)\n", "  中等活性(1-10): 6个 (30.0%)\n", "  无活性(>50): 3个 (15.0%)\n", "  弱活性(10-50): 0个 (0.0%)\n"]}], "source": ["# 数据统计分析\n", "print(\"📊 详细数据统计:\")\n", "print(\"=\" * 50)\n", "print(f\"总化合物数: {len(df)}\")\n", "print(f\"MIC值统计:\")\n", "print(f\"  - 平均值: {df['activity_mic'].mean():.3f} μg/mL\")\n", "print(f\"  - 中位数: {df['activity_mic'].median():.3f} μg/mL\")\n", "print(f\"  - 标准差: {df['activity_mic'].std():.3f} μg/mL\")\n", "print(f\"  - 最小值: {df['activity_mic'].min():.3f} μg/mL (最强)\")\n", "print(f\"  - 最大值: {df['activity_mic'].max():.3f} μg/mL (最弱)\")\n", "\n", "print(\"\\\\n🔬 按机制分类的统计:\")\n", "mechanism_stats = df.groupby('mechanism')['activity_mic'].agg(['count', 'mean', 'std']).round(3)\n", "for mechanism, stats in mechanism_stats.iterrows():\n", "    print(f\"  {mechanism}: {stats['count']}个化合物, 平均MIC={stats['mean']:.3f}±{stats['std']:.3f}\")\n", "\n", "print(\"\\\\n🎯 活性分级:\")\n", "df['activity_class'] = pd.cut(df['activity_mic'], \n", "                              bins=[0, 1, 10, 50, float('inf')], \n", "                              labels=['强活性(<1)', '中等活性(1-10)', '弱活性(10-50)', '无活性(>50)'])\n", "\n", "activity_distribution = df['activity_class'].value_counts()\n", "for class_name, count in activity_distribution.items():\n", "    percentage = (count / len(df)) * 100\n", "    print(f\"  {class_name}: {count}个 ({percentage:.1f}%)\")\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 🔬 简化模型演示\n", "\n", "由于神农框架的完整功能需要更多的数据和训练时间（可能还需要解决虚拟内存问题），我们先演示一个基于传统机器学习的抗菌活性预测流程。这将展示：\n", "\n", "1. **分子描述符计算** - 从SMILES提取化学特征\n", "2. **模型训练** - 使用随机森林预测MIC值\n", "3. **性能评估** - 评估模型预测准确性\n", "4. **特征重要性分析** - 理解哪些分子特征最重要\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🧮 计算分子描述符...\n"]}, {"ename": "AttributeError", "evalue": "module 'rdkit.Chem.Descriptors' has no attribute 'FractionCsp3'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mAttributeError\u001b[39m                            <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[11]\u001b[39m\u001b[32m, line 36\u001b[39m\n\u001b[32m     34\u001b[39m \u001b[38;5;66;03m# 计算分子特征\u001b[39;00m\n\u001b[32m     35\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33m🧮 计算分子描述符...\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m---> \u001b[39m\u001b[32m36\u001b[39m X, feature_names = \u001b[43mcalculate_molecular_descriptors\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdf\u001b[49m\u001b[43m[\u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43msmiles\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m.\u001b[49m\u001b[43mtolist\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     37\u001b[39m y = df[\u001b[33m'\u001b[39m\u001b[33mactivity_mic\u001b[39m\u001b[33m'\u001b[39m].values\n\u001b[32m     39\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33m✅ 特征计算完成\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[11]\u001b[39m\u001b[32m, line 26\u001b[39m, in \u001b[36mcalculate_molecular_descriptors\u001b[39m\u001b[34m(smiles_list)\u001b[39m\n\u001b[32m     16\u001b[39m mol = Chem.MolFromSmiles(smiles)\n\u001b[32m     17\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m mol \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m     18\u001b[39m     desc = [\n\u001b[32m     19\u001b[39m         Descriptors.MolWt(mol),              \u001b[38;5;66;03m# 分子量\u001b[39;00m\n\u001b[32m     20\u001b[39m         Descriptors.MolLogP(mol),            \u001b[38;5;66;03m# 脂水分配系数\u001b[39;00m\n\u001b[32m     21\u001b[39m         Descriptors.NumHDonors(mol),         \u001b[38;5;66;03m# 氢键供体数\u001b[39;00m\n\u001b[32m     22\u001b[39m         Descriptors.NumHAcceptors(mol),      \u001b[38;5;66;03m# 氢键受体数\u001b[39;00m\n\u001b[32m     23\u001b[39m         Descriptors.TPSA(mol),               \u001b[38;5;66;03m# 拓扑极性表面积\u001b[39;00m\n\u001b[32m     24\u001b[39m         Descriptors.NumRotatableBonds(mol),  \u001b[38;5;66;03m# 可旋转键数\u001b[39;00m\n\u001b[32m     25\u001b[39m         Descriptors.NumAromaticRings(mol),   \u001b[38;5;66;03m# 芳环数\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m26\u001b[39m         \u001b[43mDescriptors\u001b[49m\u001b[43m.\u001b[49m\u001b[43mFractionCsp3\u001b[49m(mol)        \u001b[38;5;66;03m# sp3碳原子比例\u001b[39;00m\n\u001b[32m     27\u001b[39m     ]\n\u001b[32m     28\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m     29\u001b[39m     desc = [\u001b[32m0\u001b[39m] * \u001b[38;5;28mlen\u001b[39m(descriptor_names)\n", "\u001b[31mAttributeError\u001b[39m: module 'rdkit.Chem.Descriptors' has no attribute 'FractionCsp3'"]}], "source": ["# 使用简单的机器学习方法进行演示\n", "from sklearn.ensemble import RandomForestRegressor\n", "from sklearn.model_selection import train_test_split, cross_val_score\n", "from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error\n", "from rdkit.Chem import Descriptors\n", "\n", "def calculate_molecular_descriptors(smiles_list):\n", "    \"\"\"计算分子描述符\"\"\"\n", "    descriptors = []\n", "    descriptor_names = [\n", "        'Mol<PERSON>t', 'MolLogP', 'NumHDonors', 'NumHAcceptors', \n", "        'TPSA', 'NumRotatableBonds', 'NumAromaticRings', 'FractionCsp3'\n", "    ]\n", "    \n", "    for smiles in smiles_list:\n", "        mol = Chem.Mo<PERSON>rom<PERSON>(smiles)\n", "        if mol is not None:\n", "            desc = [\n", "                Descriptors.Mol<PERSON><PERSON>(mol),              # 分子量\n", "                Descriptors.MolLogP(mol),            # 脂水分配系数\n", "                Descriptors.NumHDonors(mol),         # 氢键供体数\n", "                Descriptors.NumHAcceptors(mol),      # 氢键受体数\n", "                Descriptors.TPSA(mol),               # 拓扑极性表面积\n", "                Descriptors.NumRotatableBonds(mol),  # 可旋转键数\n", "                Descriptors.NumAromaticRings(mol),   # 芳环数\n", "                Descriptors.FractionCsp3(mol)        # sp3碳原子比例\n", "            ]\n", "        else:\n", "            desc = [0] * len(descriptor_names)\n", "        descriptors.append(desc)\n", "    \n", "    return np.array(descriptors), descriptor_names\n", "\n", "# 计算分子特征\n", "print(\"🧮 计算分子描述符...\")\n", "X, feature_names = calculate_molecular_descriptors(df['smiles'].tolist())\n", "y = df['activity_mic'].values\n", "\n", "print(f\"✅ 特征计算完成\")\n", "print(f\"   - 特征矩阵维度: {X.shape}\")\n", "print(f\"   - 标签维度: {y.shape}\")\n", "\n", "# 显示特征统计\n", "print(\"\\\\n📊 分子描述符统计:\")\n", "for i, name in enumerate(feature_names):\n", "    mean_val = X[:, i].mean()\n", "    std_val = X[:, i].std()\n", "    min_val = X[:, i].min()\n", "    max_val = X[:, i].max()\n", "    print(f\"  {name}: {mean_val:.2f}±{std_val:.2f} (范围: {min_val:.2f}-{max_val:.2f})\")\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 开始模型训练...\n"]}, {"ename": "NameError", "evalue": "name 'X' is not defined", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[13]\u001b[39m\u001b[32m, line 6\u001b[39m\n\u001b[32m      2\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33m🚀 开始模型训练...\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m      4\u001b[39m \u001b[38;5;66;03m# 分割数据集\u001b[39;00m\n\u001b[32m      5\u001b[39m X_train, X_test, y_train, y_test = train_test_split(\n\u001b[32m----> \u001b[39m\u001b[32m6\u001b[39m     \u001b[43mX\u001b[49m, y, test_size=\u001b[32m0.3\u001b[39m, random_state=\u001b[32m42\u001b[39m, stratify=df[\u001b[33m'\u001b[39m\u001b[33mactivity_class\u001b[39m\u001b[33m'\u001b[39m]\n\u001b[32m      7\u001b[39m )\n\u001b[32m      9\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33m数据分割:\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m     10\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33m  - 训练集: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mX_train.shape[\u001b[32m0\u001b[39m]\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m 个样本\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[31mNameError\u001b[39m: name 'X' is not defined"]}], "source": ["# 数据分割和模型训练\n", "print(\"🚀 开始模型训练...\")\n", "\n", "# 分割数据集\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X, y, test_size=0.3, random_state=42, stratify=df['activity_class']\n", ")\n", "\n", "print(f\"数据分割:\")\n", "print(f\"  - 训练集: {X_train.shape[0]} 个样本\")\n", "print(f\"  - 测试集: {X_test.shape[0]} 个样本\")\n", "\n", "# 训练随机森林模型\n", "rf_model = RandomForestRegressor(\n", "    n_estimators=100,\n", "    max_depth=10,\n", "    min_samples_split=2,\n", "    min_samples_leaf=1,\n", "    random_state=42,\n", "    n_jobs=-1\n", ")\n", "\n", "# 训练模型\n", "rf_model.fit(X_train, y_train)\n", "\n", "# 进行预测\n", "y_train_pred = rf_model.predict(X_train)\n", "y_test_pred = rf_model.predict(X_test)\n", "\n", "print(\"\\\\n✅ 模型训练完成！\")\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 模型性能评估:\n", "========================================\n"]}, {"ename": "NameError", "evalue": "name 'y_train' is not defined", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[15]\u001b[39m\u001b[32m, line 6\u001b[39m\n\u001b[32m      3\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33m=\u001b[39m\u001b[33m\"\u001b[39m * \u001b[32m40\u001b[39m)\n\u001b[32m      5\u001b[39m \u001b[38;5;66;03m# 训练集性能\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m6\u001b[39m train_rmse = np.sqrt(mean_squared_error(\u001b[43my_train\u001b[49m, y_train_pred))\n\u001b[32m      7\u001b[39m train_mae = mean_absolute_error(y_train, y_train_pred)\n\u001b[32m      8\u001b[39m train_r2 = r2_score(y_train, y_train_pred)\n", "\u001b[31mNameError\u001b[39m: name 'y_train' is not defined"]}], "source": ["# 模型性能评估\n", "print(\"📊 模型性能评估:\")\n", "print(\"=\" * 40)\n", "\n", "# 训练集性能\n", "train_rmse = np.sqrt(mean_squared_error(y_train, y_train_pred))\n", "train_mae = mean_absolute_error(y_train, y_train_pred)\n", "train_r2 = r2_score(y_train, y_train_pred)\n", "\n", "print(\"🎯 训练集性能:\")\n", "print(f\"  - RMSE: {train_rmse:.3f}\")\n", "print(f\"  - MAE:  {train_mae:.3f}\")\n", "print(f\"  - R²:   {train_r2:.3f}\")\n", "\n", "# 测试集性能\n", "test_rmse = np.sqrt(mean_squared_error(y_test, y_test_pred))\n", "test_mae = mean_absolute_error(y_test, y_test_pred)\n", "test_r2 = r2_score(y_test, y_test_pred)\n", "\n", "print(\"\\\\n🎯 测试集性能:\")\n", "print(f\"  - RMSE: {test_rmse:.3f}\")\n", "print(f\"  - MAE:  {test_mae:.3f}\")\n", "print(f\"  - R²:   {test_r2:.3f}\")\n", "\n", "# 交叉验证\n", "print(\"\\\\n🔄 5折交叉验证:\")\n", "cv_scores = cross_val_score(rf_model, X_train, y_train, cv=5, scoring='r2')\n", "print(f\"  - 平均R²: {cv_scores.mean():.3f} ± {cv_scores.std():.3f}\")\n", "print(f\"  - 各折得分: {[f'{score:.3f}' for score in cv_scores]}\")\n", "\n", "# 特征重要性分析\n", "print(\"\\\\n🔍 特征重要性排序:\")\n", "feature_importance = list(zip(feature_names, rf_model.feature_importances_))\n", "feature_importance.sort(key=lambda x: x[1], reverse=True)\n", "\n", "for i, (name, importance) in enumerate(feature_importance, 1):\n", "    print(f\"  {i}. {name}: {importance:.3f}\")\n", "\n", "# 模型解释\n", "if test_r2 > 0.7:\n", "    performance_level = \"优秀\"\n", "elif test_r2 > 0.5:\n", "    performance_level = \"良好\"\n", "elif test_r2 > 0.3:\n", "    performance_level = \"中等\"\n", "else:\n", "    performance_level = \"待改进\"\n", "\n", "print(f\"\\\\n🎉 模型性能评级: {performance_level} (R² = {test_r2:.3f})\")\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 📈 数据可视化\n", "\n", "让我们创建一些图表来可视化数据和模型结果。\n"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'y_test' is not defined", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[17]\u001b[39m\u001b[32m, line 30\u001b[39m\n\u001b[32m     27\u001b[39m axes[\u001b[32m0\u001b[39m, \u001b[32m1\u001b[39m].set_title(\u001b[33m'\u001b[39m\u001b[33m抗菌机制分布\u001b[39m\u001b[33m'\u001b[39m)\n\u001b[32m     29\u001b[39m \u001b[38;5;66;03m# 3. 预测vs实际散点图\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m30\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(\u001b[43my_test\u001b[49m) > \u001b[32m0\u001b[39m:\n\u001b[32m     31\u001b[39m     axes[\u001b[32m1\u001b[39m, \u001b[32m0\u001b[39m].scatter(y_test, y_test_pred, alpha=\u001b[32m0.7\u001b[39m, color=\u001b[33m'\u001b[39m\u001b[33m#D55E00\u001b[39m\u001b[33m'\u001b[39m, s=\u001b[32m60\u001b[39m)\n\u001b[32m     32\u001b[39m     min_val = \u001b[38;5;28mmin\u001b[39m(y_test.min(), y_test_pred.min())\n", "\u001b[31mNameError\u001b[39m: name 'y_test' is not defined"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\events.py:82: UserWarning: Glyph 39057 (\\N{CJK UNIFIED IDEOGRAPH-9891}) missing from font(s) Arial.\n", "  func(*args, **kwargs)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\events.py:82: UserWarning: Glyph 27425 (\\N{CJK UNIFIED IDEOGRAPH-6B21}) missing from font(s) Arial.\n", "  func(*args, **kwargs)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\events.py:82: UserWarning: Glyph 25239 (\\N{CJK UNIFIED IDEOGRAPH-6297}) missing from font(s) Arial.\n", "  func(*args, **kwargs)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\events.py:82: UserWarning: Glyph 33740 (\\N{CJK UNIFIED IDEOGRAPH-83CC}) missing from font(s) Arial.\n", "  func(*args, **kwargs)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\events.py:82: UserWarning: Glyph 27963 (\\N{CJK UNIFIED IDEOGRAPH-6D3B}) missing from font(s) Arial.\n", "  func(*args, **kwargs)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\events.py:82: UserWarning: Glyph 24615 (\\N{CJK UNIFIED IDEOGRAPH-6027}) missing from font(s) Arial.\n", "  func(*args, **kwargs)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\events.py:82: UserWarning: Glyph 20998 (\\N{CJK UNIFIED IDEOGRAPH-5206}) missing from font(s) Arial.\n", "  func(*args, **kwargs)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\events.py:82: UserWarning: Glyph 24067 (\\N{CJK UNIFIED IDEOGRAPH-5E03}) missing from font(s) Arial.\n", "  func(*args, **kwargs)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\events.py:82: UserWarning: Glyph 24179 (\\N{CJK UNIFIED IDEOGRAPH-5E73}) missing from font(s) Arial.\n", "  func(*args, **kwargs)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\events.py:82: UserWarning: Glyph 22343 (\\N{CJK UNIFIED IDEOGRAPH-5747}) missing from font(s) Arial.\n", "  func(*args, **kwargs)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\events.py:82: UserWarning: Glyph 20540 (\\N{CJK UNIFIED IDEOGRAPH-503C}) missing from font(s) Arial.\n", "  func(*args, **kwargs)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\events.py:82: UserWarning: Glyph 26426 (\\N{CJK UNIFIED IDEOGRAPH-673A}) missing from font(s) Arial.\n", "  func(*args, **kwargs)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\events.py:82: UserWarning: Glyph 21046 (\\N{CJK UNIFIED IDEOGRAPH-5236}) missing from font(s) Arial.\n", "  func(*args, **kwargs)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\events.py:82: UserWarning: Glyph 34507 (\\N{CJK UNIFIED IDEOGRAPH-86CB}) missing from font(s) Arial.\n", "  func(*args, **kwargs)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\events.py:82: UserWarning: Glyph 30333 (\\N{CJK UNIFIED IDEOGRAPH-767D}) missing from font(s) Arial.\n", "  func(*args, **kwargs)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\events.py:82: UserWarning: Glyph 36136 (\\N{CJK UNIFIED IDEOGRAPH-8D28}) missing from font(s) Arial.\n", "  func(*args, **kwargs)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\events.py:82: UserWarning: Glyph 21512 (\\N{CJK UNIFIED IDEOGRAPH-5408}) missing from font(s) Arial.\n", "  func(*args, **kwargs)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\events.py:82: UserWarning: Glyph 25104 (\\N{CJK UNIFIED IDEOGRAPH-6210}) missing from font(s) Arial.\n", "  func(*args, **kwargs)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\events.py:82: UserWarning: Glyph 25233 (\\N{CJK UNIFIED IDEOGRAPH-6291}) missing from font(s) Arial.\n", "  func(*args, **kwargs)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\events.py:82: UserWarning: Glyph 22797 (\\N{CJK UNIFIED IDEOGRAPH-590D}) missing from font(s) Arial.\n", "  func(*args, **kwargs)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\events.py:82: UserWarning: Glyph 32454 (\\N{CJK UNIFIED IDEOGRAPH-7EC6}) missing from font(s) Arial.\n", "  func(*args, **kwargs)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\events.py:82: UserWarning: Glyph 32990 (\\N{CJK UNIFIED IDEOGRAPH-80DE}) missing from font(s) Arial.\n", "  func(*args, **kwargs)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\events.py:82: UserWarning: Glyph 22721 (\\N{CJK UNIFIED IDEOGRAPH-58C1}) missing from font(s) Arial.\n", "  func(*args, **kwargs)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\events.py:82: UserWarning: Glyph 38750 (\\N{CJK UNIFIED IDEOGRAPH-975E}) missing from font(s) Arial.\n", "  func(*args, **kwargs)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\events.py:82: UserWarning: Glyph 20195 (\\N{CJK UNIFIED IDEOGRAPH-4EE3}) missing from font(s) Arial.\n", "  func(*args, **kwargs)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\events.py:82: UserWarning: Glyph 35874 (\\N{CJK UNIFIED IDEOGRAPH-8C22}) missing from font(s) Arial.\n", "  func(*args, **kwargs)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\events.py:82: UserWarning: Glyph 36884 (\\N{CJK UNIFIED IDEOGRAPH-9014}) missing from font(s) Arial.\n", "  func(*args, **kwargs)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\events.py:82: UserWarning: Glyph 24452 (\\N{CJK UNIFIED IDEOGRAPH-5F84}) missing from font(s) Arial.\n", "  func(*args, **kwargs)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\events.py:82: UserWarning: Glyph 33180 (\\N{CJK UNIFIED IDEOGRAPH-819C}) missing from font(s) Arial.\n", "  func(*args, **kwargs)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\events.py:82: UserWarning: Glyph 30772 (\\N{CJK UNIFIED IDEOGRAPH-7834}) missing from font(s) Arial.\n", "  func(*args, **kwargs)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\events.py:82: UserWarning: Glyph 22351 (\\N{CJK UNIFIED IDEOGRAPH-574F}) missing from font(s) Arial.\n", "  func(*args, **kwargs)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\events.py:82: UserWarning: Glyph 30149 (\\N{CJK UNIFIED IDEOGRAPH-75C5}) missing from font(s) Arial.\n", "  func(*args, **kwargs)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\events.py:82: UserWarning: Glyph 27602 (\\N{CJK UNIFIED IDEOGRAPH-6BD2}) missing from font(s) Arial.\n", "  func(*args, **kwargs)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\events.py:82: UserWarning: Glyph 31070 (\\N{CJK UNIFIED IDEOGRAPH-795E}) missing from font(s) Arial.\n", "  func(*args, **kwargs)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\events.py:82: UserWarning: Glyph 20892 (\\N{CJK UNIFIED IDEOGRAPH-519C}) missing from font(s) Arial.\n", "  func(*args, **kwargs)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\events.py:82: UserWarning: Glyph 26694 (\\N{CJK UNIFIED IDEOGRAPH-6846}) missing from font(s) Arial.\n", "  func(*args, **kwargs)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\events.py:82: UserWarning: Glyph 26550 (\\N{CJK UNIFIED IDEOGRAPH-67B6}) missing from font(s) Arial.\n", "  func(*args, **kwargs)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\events.py:82: UserWarning: Glyph 31034 (\\N{CJK UNIFIED IDEOGRAPH-793A}) missing from font(s) Arial.\n", "  func(*args, **kwargs)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\events.py:82: UserWarning: Glyph 20363 (\\N{CJK UNIFIED IDEOGRAPH-4F8B}) missing from font(s) Arial.\n", "  func(*args, **kwargs)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\events.py:82: UserWarning: Glyph 25968 (\\N{CJK UNIFIED IDEOGRAPH-6570}) missing from font(s) Arial.\n", "  func(*args, **kwargs)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\events.py:82: UserWarning: Glyph 25454 (\\N{CJK UNIFIED IDEOGRAPH-636E}) missing from font(s) Arial.\n", "  func(*args, **kwargs)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\events.py:82: UserWarning: Glyph 26512 (\\N{CJK UNIFIED IDEOGRAPH-6790}) missing from font(s) Arial.\n", "  func(*args, **kwargs)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 39057 (\\N{CJK UNIFIED IDEOGRAPH-9891}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 27425 (\\N{CJK UNIFIED IDEOGRAPH-6B21}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 25239 (\\N{CJK UNIFIED IDEOGRAPH-6297}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 33740 (\\N{CJK UNIFIED IDEOGRAPH-83CC}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 27963 (\\N{CJK UNIFIED IDEOGRAPH-6D3B}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 24615 (\\N{CJK UNIFIED IDEOGRAPH-6027}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 20998 (\\N{CJK UNIFIED IDEOGRAPH-5206}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 24067 (\\N{CJK UNIFIED IDEOGRAPH-5E03}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 24179 (\\N{CJK UNIFIED IDEOGRAPH-5E73}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 22343 (\\N{CJK UNIFIED IDEOGRAPH-5747}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 20540 (\\N{CJK UNIFIED IDEOGRAPH-503C}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 26426 (\\N{CJK UNIFIED IDEOGRAPH-673A}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 21046 (\\N{CJK UNIFIED IDEOGRAPH-5236}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 34507 (\\N{CJK UNIFIED IDEOGRAPH-86CB}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 30333 (\\N{CJK UNIFIED IDEOGRAPH-767D}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 36136 (\\N{CJK UNIFIED IDEOGRAPH-8D28}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 21512 (\\N{CJK UNIFIED IDEOGRAPH-5408}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 25104 (\\N{CJK UNIFIED IDEOGRAPH-6210}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 25233 (\\N{CJK UNIFIED IDEOGRAPH-6291}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 22797 (\\N{CJK UNIFIED IDEOGRAPH-590D}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 32454 (\\N{CJK UNIFIED IDEOGRAPH-7EC6}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 32990 (\\N{CJK UNIFIED IDEOGRAPH-80DE}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 22721 (\\N{CJK UNIFIED IDEOGRAPH-58C1}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 38750 (\\N{CJK UNIFIED IDEOGRAPH-975E}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 20195 (\\N{CJK UNIFIED IDEOGRAPH-4EE3}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 35874 (\\N{CJK UNIFIED IDEOGRAPH-8C22}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 36884 (\\N{CJK UNIFIED IDEOGRAPH-9014}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 24452 (\\N{CJK UNIFIED IDEOGRAPH-5F84}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 33180 (\\N{CJK UNIFIED IDEOGRAPH-819C}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 30772 (\\N{CJK UNIFIED IDEOGRAPH-7834}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 22351 (\\N{CJK UNIFIED IDEOGRAPH-574F}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 30149 (\\N{CJK UNIFIED IDEOGRAPH-75C5}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 27602 (\\N{CJK UNIFIED IDEOGRAPH-6BD2}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 31070 (\\N{CJK UNIFIED IDEOGRAPH-795E}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 20892 (\\N{CJK UNIFIED IDEOGRAPH-519C}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 26694 (\\N{CJK UNIFIED IDEOGRAPH-6846}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 26550 (\\N{CJK UNIFIED IDEOGRAPH-67B6}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 31034 (\\N{CJK UNIFIED IDEOGRAPH-793A}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 20363 (\\N{CJK UNIFIED IDEOGRAPH-4F8B}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 25968 (\\N{CJK UNIFIED IDEOGRAPH-6570}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 25454 (\\N{CJK UNIFIED IDEOGRAPH-636E}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 26512 (\\N{CJK UNIFIED IDEOGRAPH-6790}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABIcAAAORCAYAAABoWcKTAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjMsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvZiW1igAAAAlwSFlzAAAPYQAAD2EBqD+naQAAtltJREFUeJzs3Qd4FHX+x/HPbnoPhCSEXqWIglJsiHjYxVOwY0M9C3bBgnKevdxfvdOzn93zOLmzYFdsoCCISC9K7wQChPSy7f/MrAlJCCSBJLM78349zzzMzs7OfH8Bkt1PfsUVCAQCAgAAAAAAgCO5rS4AAAAAAAAA1iEcAgAAAAAAcDDCIQAAAAAAAAcjHAIAAAAAAHAwwiEAAAAAAAAHIxwCAAAAAABwMMIhAAAAAAAAByMcAgAAAAAAcDDCIQAAAAAAAAcjHAIAAAAAAHAwwiEAAAAAAAAHi7S6AAAAcGDmzJmjZ555RnFxcXWeW15ertdee63asVdffVU///xzve7VqVMnjR8/fo/jd955p/Ly8up1jZNOOkkjR47c4/iIESOUmZlZ5+t37dqlJ554Qu3atWvUr4OBr0Xjfi0AAEB4IBwCACDMlZaW6v777zc/oNfl7bff3uNYTEyMXnzxxXrdq7bXGw455BBdfPHFB3SNs88+u17XmD59urxeb6N/HQx8LRr3awEAAMIDw8oAAAAAAAAcjHAIAAAAAADAwQiHAAAAAAAAHIxwCAAAAAAAwMEIhwAAAAAAAByMcAgAAAAAAMDBCIcAAAAAAAAcjHAIAAAAAADAwQiHAAAAAAAAHIxwCAAAAAAAwMEIhwAAAAAAABws0uoCAADAgZswYYKSkpLqPG/nzp26+OKLqx3bsWOHrr322nrdp1WrVrUenzlzpqZPn16vaxx99NG1Hn/zzTfrdY3s7Gw99dRTjf51MPC1aNyvBQAACA+uQCAQsLoIAAAAAAAAWINhZQAAAAAAAA5GOAQAAAAAAOBghEMAAAAAAAAORjgEAAAAAADgYIRDAAAAAAAADkY4BAAAAAAA4GCEQwAAAAAAAA5GOAQAAAAAAOBghEMAAAAAAAAORjgEAAAAAADgYIRDAAAAAAAADkY4BAAAAAAA4GCEQwAAAAAAAA5GOAQAAAAAAOBghEMAAAAAAAAORjgEAAAAAADgYIRDAAAAAAAADkY4BAAAAAAA4GCEQwAAAAAAAA5GOAQAAAAAAOBghEMAAAAAAAAORjgEAAAAAADgYIRDAAAAAAAADkY4BAAAAAAA4GCEQwAAAAAAAA5GOAQAAAAAAOBghEMAAAAAAAAORjgEAAAAAADgYIRDAAAAAAAADkY4BAAAAAAA4GCEQwAAAAAAAA5GOAQAAAAAAOBghEMAAAAAAAAORjgEAAAAAADgYIRDAAAAAAAADkY4BAAAAAAA4GCEQwAAAAAAAA5GOAQAAAAAAOBghEMAAAAAAAAORjgEAAAAAADgYIRDAAAAAAAADkY4BAAAAAAA4GCEQwAAAAAAAA5GOAQAAAAAAOBghEMAAAAAAAAORjgEAAAAAADgYIRDAAAAAAAADkY4BAAAAAAA4GCEQwAAAAAAAA5GOAQAAAAAAOBghEMAAAAAAAAORjgEAAAAAADgYIRDAAAAAAAADkY4BAAAAAAA4GCEQwAAAAAAAA5GOAQAAAAAAOBghEMAAAAAAAAORjgEAAAAAADgYIRDAAAAAAAADkY4BAAAAAAA4GCEQwAAAAAAAA5GOAQAAAAAAOBghEMAAAAAAAAORjgEAAAAAADgYJFWFwAg9N1+++0KBAJ7ff7kk0/WiSeeaO5/9dVX+vLLL/d6rsvl0uOPP77f1w/lewAAAFTg/VP97gEgNBAOAaiT8YP/hBNO2OvzX3/9dbUf+k888US9zt2f64fyPQAAACrw/ql+9wAQGhhWBgAAAAAA4GCEQwAAAAAAAA5GOAQAAAAAAOBghEMAAAAAAAAORjgEAAAAAADgYIRDAAAAAAAADkY4BAAAAAAA4GCEQwAAAAAAAA4WaXUBAELfpEmT9MUXX+z1+SOOOKJyPzc3V7fddttez83Ly9MJJ5yw39cP5XsAAABU4P1T/e4BIDS4AoFAwOoiAAAAAAAAYA2GlQEAAAAAADgY4RAAAAAAAICDEQ4BAAAAAAA4GOEQAAAAAACAgxEOAQAAAAAAOJhtl7L3+/3avHmzkpKS5HK5rC4HAADshbFwakFBgdq0aSO3m99bAQAANDfbhkNGMNS+fXurywAAAPW0YcMGtWvXzuoyAAAAHMe24ZDRY6jijWZycnKj9kjKyclRenq6bX+7uUcbi4qkNm2CT27eLCUkyA4c+XdpQ05oo1Pa6YQ2OqWdDW1jfn6++Qudip/dAAAAaF62DYcqhpIZwVBjh0OlpaXmNe38pr5aGyMidj9pfC1tFA457u/ShpzQRqe00wltdEo797eNDAMHAACwhj3flQIAAAAAAKBeCIcAAAAAAAAcjHAIAAAAAADAwWw75xAAwD58Pp88Ho/sMh+P0RZjTh47zzlUtY1RUVGKqDp/HQAAAEIK4RDqZryhP+203fsA0EwCgYCys7O1a9cu2alNRnhSUFBg2wmYa2tjamqqWrdubds2AwAAhDPCIdQtNlb69FOrqwDgQBXBUEZGhuLj420RLBjBidfrVWRkpC3aU1cbDcXFxdq2bZu5n5WVZXF1AAAAqIlwCAAQskPJKoKhtLQ02YXTwiGjjXFxceZxIyAy/j4ZYgYAABBa7DnZAQAg7FXMMWT0GEL4q/h7tMvcUQAAAHZCOIS6FRVJCQnBzdgHgGZk1941TsPfIwAAQOhiWBnqp7jY6goAAAAAAEAToOcQAAAAAACAgxEOAQDgEMYE33/605+UmZmp9PR0jR492jxW086dO81z1q5du8/r9e3b1xwuVnVbvHhxE7YAAAAATYFwCAAAh7j22mu1YMECffbZZ/ryyy+1bNkyXXXVVdXOyc3N1RlnnFG59Py+VpNbvny5pk2bpi1btlRuPXv2bOJWAAAAoLEx59B+yMvLU2FhYbNPrpmcnGz+phcAENpmzZqlyZMn1/pcUlKS7rjjDnN/3LhxioqK2uMc42fMhAkTlJWVZT6eOHGiFi5cWOv1+vXrpwsuuKDOmoqKivTuu+9qxowZ6t+/v3nsqaee0rHHHqvS0lLFxsZq+vTpuvTSS80a67JmzRqVl5dr0KBB5mtrLmUPAACA8EE41EDbt2/XU888r0XLVzX7m9+U+Fi9+cqLBEQAnG1fqyZGREhVg4p9net2S3FxdZ9rrNTYQF6vV4899litz/3www+V+yNGjNDgwYP3OMcYzmX0zKnQoUMHjRo1qtbrGYFOxWs6d+5c6zmvv/66zj//fH3yySdmmFSVcR8jjDICHqM30RVXXGGGTd27d99nG5cuXar27dvvEQwBAAAg/BAONVB+fr6Kysp18MhrlJLZrtnuu2vrJi157wXz/s0eDhkfoI47bvc+AFgpMXHvz512mvTpp7sfZ2TsfbVF4/va1Km7H3fqZPwGYM/zwqQXjBHUGMO6apOSkqK4uDidcsop1Y4//fTTOvTQQ9WqVSvz8YMPPmj+WddcQwZjSFp0dLSGDx+uOXPmqEePHnr88cfNnkQAAAAIL4RD+ykls63S2tX+G1rbMX6zXvUDFAAg5ERERKh169b1Pv/ZZ5/Vf//7X33xxRf7db9ff/3VnJ/ImOD6gQce0Msvv6xhw4aZPYratWu+X54AAADgwBEOAQDCS2HhvoeVVbWvSZVr9oSsR2+ZULZ+/Xr17t271udeeuklXXTRRZWPn3/+ed100036+9//rpNOOmm/7meEQcXFxeZ8eBXXNOYz+te//qW77rprP1sBAAAAKxAOAQDCS0PmAGqqc0NQmzZtNH/+/FqfM5alr/DEE0/o9ttvN4eA3Xzzzft9v8jIyMpgyGAs0mCsVLZp06b9viYAAACsQTiEuhmTtBpzcVT8Zj3MP0ABgB0ZYU23bt32ec6bb75pBkNGj6FbbrnlgO53/PHHa+jQobr33nvNx36/31xR7frrrz+g6wIAAKD5EQ6hfmqbpBUAEDZ27typG264QZdddpm5Gll2dnblc8ZCB8acRftirGqWk5Ojli1bmhNRn3HGGeZcQ4cddpg5GbUxufWuXbs0evToZmgNAAAAGhPhEAAAjczoRTN+/Phan4uJidGRRx5p7k+aNMlcXr6mvLw8TZgwofLxypUraz3PYMwzNHjw4DprmjJlirlkvdF7yNiqWrNmjTpV9BDdiw0bNqhz58767rvvzB5Dt956q0pLS3XjjTdq69atOuKII/T1118rKSlJgTBZ4Q0AAABBhEMAADSyIUOGmFttjODE6/Wa+88880y9rtcYvXGM3kLGVh9GUFQz4Kl5zJhj6O677zY3AAAAhLcaS7UAAAAAAADASQiHAAAAAAAAHIxwCAAAAAAAwMGYcwh1c7ulAQN27wMAAAAAANsgHELd4uKkn3+2ugoADl75C+GPv0cAAIDQRTgEAAhJ0dHRcrvd2rx5s9LT083HxgpZ4a5itbLIyEhbtKeuNhrKy8uVk5Nj/n0af48AAAAILSERDpWVlal///569tlnNXToUPPY+vXrde2112rq1Klq06aNHnnkEZ133nlWlwoAaCZGkNC5c2dt2bLFDIjsFJwYvWiM9tk5HKrZxvj4eHXo0ME8BgAAgNBieThUWlqqUaNGacmSJZXHjN82nn766erSpYvmzZtnBkQXX3yxevfurT59+lharyMVF0u9ewf3ly413uFbXREAhzB6mRiBgvFzwefzyQ6M0GTHjh1KS0uzbVBSs40RERG27ikFAAAQ7iwNh5YuXWoGQ8ZvGKv67LPPtGHDBs2YMUPJycnq0aOHPv/8c/3444+EQ1Yw/n7Wrdu9DwDNyAgUoqKizM0uwYnRltjYWFuHQ3ZvIwAAgJ1YGg5NmzZNxx9/vB5++GElJCRUHjd6Cg0bNswMhipMnjzZoioBAAAAwHoBb4n8pTnyl22Xv3R79f3yXZK/XAG/R/J7gn8GPLsfB3xyuWPkiozfvUVU2Y9MkDumldzxWYqIbyN3XJbcUYlWNxmAE8KhMWPG1Hp89erV6tSpk8aPH69//etfatWqle6//36dddZZ+5y3yNgq5OfnV/72sjFXSDF6OQW7xQeauRdN8L4V8zg0JeP61e5jzBtR5TnjsR3s0U4boo324YR2OqGNTmlnQ9to568FADREwFcuX+EaefNXylewSt6CVcE/81fKX7xBAW9xs9bjikwMhkVGUBTfRhEJHRWZcpAikw9SRHJ3RcRlNms9AGw851BtCgsL9cYbb+j888/Xxx9/rO+++07nnHOOZs2apQEDBtT6mkcffdQMkGoyVkcx5jVqzNpap7dSqkqVWLZDzcWvEnXp2F4FBQXatm1b097L71deXp75xt6cTLS4WJlVvp6BoiLZQc122hFttA8ntNMJbXRKOxvaRuNnGwA4ja9wvTw7fpFnx1zzT++uJfIVb5QCoROYB7yF8uWvMLfauKJbKDL1YEWlHqzIFgcrqmU/RaX1N3siAQgvIRkOGZNWGpNYvvDCC+abysMPP1w//PCD/vnPf+41HLrrrrs0duzYaj2H2rdvby5/XHV42oEy3sBm52xXQLFyx6SpueQqX6vXbVBSUpIyMjKa/E290UvJ+NqZb+qrhEHGMVUZAhjO9minDdFG+3BCO53QRqe0s6FtNOYmAgA78xVvUXnOzGAItP0XeXbONYeEhbtAea4826abWyVXpCJbHqro9CMVlX6kotOPUmRyNyvLBBCu4VBWVpb5prLqG0pjUuqFCxfu9TUxMTHmVpNxjcZ8810xtEtyGQ8a7br1uHPlkLbm+DBRcR/zXlXuV/NxuKvWTpuijfbhhHY6oY1OaWdD2mjnrwMAZ/KX5ao8e6rKtnxjbr68X+UYAa+8O+aam3593jxkzGUUlX6EojOHKKbtyYpq2dfqKgGEQzh05JFH6qGHHjKXLTaWvzUsW7bMnIcIFjBCsIql7FmGGAAAAKgm4C1V+dbvzSCofMs38uycF1LDw6xmTJpdtvFTcyv45U5z/qKYNicrpu0pimlzotwxLawuEXC8kAyHLrzwQj3wwAO67rrrdPvtt2vKlCnmUvY//fST1aU5U3y8tGSJ1VUAAAAAIdU7qHTDxypdP1nlm79s9smiw5m/eLNKVr5ubnJFKKrVIDMoiu1wJr2KAIuEZDhkzBH01VdfmauZ9enTRx07dtSkSZPMuYcAAAAAwAoF5X7Nzi7TT1tKdeGagYotXWV1SeEv4JPHmI8pZ6YK59+ryJReiu18geK6XKjI5O5WVwc4RsiEQ8F5fHbr3bu3pk2bZlk9AAAAAFDuC+in7DL9sKlUi7eXy/f7x5bD0m9Svw03W12e7XjzlpkhkbEZK5/Fdr5QcZ3PV0RCO6tLA2wtZMIhhLDiYmngwOD+zz8Hh5kBAAAANrYu36tv1pdo+uZSFXmq/yLbsCDqJPWzpDLnMFZ3M7aCObcrOvNYxXW/QnGdzpcrklUugcZGOIS6Gb26li7dvQ8AAADYUInXrxmby/Tt+hKtyvPu89zFRS1VHpmhaO+2ZqvPuQLmhN/Glv/zOMV3u1zxPccoMqmL1YUBtkE4BAAAAMDR1hd49dmaYv24uUxlFePG6uDxS7+1HqdDNt7Z5PVht0DZDhUteUJFS55UTNuTFd/zOsW0O10ul1uhYtasWZo8efJe59e9++67zf1x48YpKipqj3MKCws1YcIEZWVlmY8nTpyohQsX1nq9fv366YILLjD3N2zYoMcff1zxtYz0MFYCN56rYKwObtynNiNHjtSgQYPMfWNhqL1N92LMDWzME1zhiiuuUEZGxh7nbd++Xa+88kqt10DoIBwCAAAA4EhLd5Tro1XFmp9Trv3pH78g5nQdIsIhawRUtukLc4tI6Gj2JIrvMUbu6GSrC5PX69Vjjz1W63PTp0+v3B8xYoQGDx68xzlr1641w5wKHTp00KhRo+q8nvGaO+64Q+3atdvneYahQ4fWeu+a5yYlJdWrLRXhUG3XrHkeQhPhEAAAAADH8AcC+jm7TB+tLtbKXfseOlaXRUXpKo9IVbRvV6PVh4bzFa1TwS/jVbjoMSX0ulEJvW+RO6al1WUBYSV0+t4BAAAAQBPx+AL6al2Jxk7bqb/NzT/gYMhQ5pNWtb61UerDgQuU71Lhgge17X8dlT/nDvlKtlpdEhA2CIcAAAAA2Lqn0HcbSnTz1B16ZXGBthTtHq7TGBbGntmo18OBC3gLVbT4cW17t7PyfrpZvqJNVpcEhDzCIdTN5TJmGwtuxj4AAAAQBozhY7d/v1MvLizQjlJ/k9xjfnGWvO6EJrk2DpCvRMXL/qFt73VR/uxb5S/LtboiIGQRDqFuxmz3a9cGt1pmvgcAAABCbaLpe2bs1BO/5GljYeP2FKqpxCutzrypSe+BA+QvV9HSp7Tt/W4qWvKUAn6P1RUBIYdwCAAAAIBtlqR/bPYu3T9rl5Y3wpxC9bUw/uxmuxf2X6Bsp/J/vlU5kw9W6boPrC4HCCmsVgYAAAAgrJV4/frf8iJ9vrZE/v1Zk/4AzS9prxGuGEUEypr/5mgwX/4K5X43UtGZQ5Q88G+KatW/Ua/v9/s1fvz4Wp+LjY2tXO590qRJ+uSTT/Y4Jy8vTxMmTKh8vHLlylrPM/Tu3bva8vGPPvqoufx8TSUlJdXOmzJlyl6vOXz48Mr9HTt27LUtWVlZ1a754osv1nrNzZs317rEPUIL4RDqVlIiDRkS3P/+eykuzuqKAAAAANOsLaV6c2mhdjbRnEL1UeiR1mVcqy5bn7asBjRc+dbvtf2TgYrrfqWSBzwud0xqo1x3yJAh5laXZ555pl7XGz16dL3O69Spk5577rl6nfvAAw/U67wzzzzT3Orj7bffrtd5CE0MK0Pd/H5pzpzgZuwDAAAAFssu8urR2bv097n5lgZDFRYmXmB1CdgvAZWseEU5k3upZO27VhcDWIZwCAAAAEDY8PoDem9FkW77fqfm55QrVMwr7Sy/K8LqMrCf/CXZ2jX1XO385iz5ijZZXQ7Q7AiHAAAAAISF1Xke3fHDTv13eZE81ncWqiav3KUN6X+yugwcoLINHypncm8V/fq8AgELJrACLEI4BAAAACCk+QMBvb+iSH+ekatNTbw0/YFYlHSR1SWgEQQ8+cqfdb12fH6svHnLrS4HaBaEQwAAAMA+Jnh1uVzm5na7lZiYqGOOOUZffvlltXOMlXhq9jKYOnWq+bqaKo7fc889zdIGO8wtdO/MXZq0vEi+EO/IMbfsIAW05985wpNn2wxt//hwFS9/2epSgCZHOAQAAADsw1NPPaUtW7Zo48aNmjVrlhkOnX766fr6668rz5kxY4Zef/31el3vP//5j7p27Wqu7MOwlX37Zn2J7vwhV8tzPQoHO8tc2tTqYqvLQCMKeIuU9+PVyv3ubPnLdlpdDtBkWMoe9dOqldUVAAAAWCIlJUWtW7c299u0aaP/+7//M8OiW2+9VYsWLarsPXTnnXfqrLPOUsuWLfd6LY/Ho3fffVdPPvmkrrzySk2bNk1Dhw6tds64ceMUFRW1x2sLCws1YcIEZWVlmY8nTpyohQsX1nqffv366YILgqtnbdiwQY8//rji4+P3OM/n85nPVXjooYfM+9Rm5MiRGjRokJpDfplfLy3K15ytoTPhdH0tShmtdtv/ZXUZaGSl695Xec5stThuoqIzj7W6HKDREQ6hbgkJUk6O1VUAAACEjKuvvlpDhgzRypUrzce33Xab/vrXv5oB0csv730IypQpU5SXl6czzzzTPO/NN9/cIxwaMWKEOUytprVr15phToUOHTpo1KhRtd5n+vTplfvGa+644w61a9dun+cZjFpqu3dt5zaVlbs8+tsvedoRAsvT74+55b11qtVFoEn4izdqxxfHK7HffUo89G65XAzEgX3wrxkAAABooN69e5t/Ll261PwzISFBTz/9tF599VXNnDlzr6975513zGFpLVq0MAMioxdRUVFRs9UdDsPI7puZG7bBkCGn1K0tLc+xugw0lYBPhfPu0c4pJ8tfusPqaoBGQzgEAAAA7MdQM0NBQUG1Hj+nnXaaxowZU62HT4WSkhJ9+OGH5tCzimFaxhCu999/X07n8QX00sJ8/XNRQcgtUb8/FqeypL3dlW/5Wts/GShP7mKrSwEaBeEQ6lZSYvQxDm7GPgAAgMPl5+ebfyYnJ1c7/swzz2j58uXmnzV98sknZphUEQ5169ZNhxxyiDm0zMm2l/h078xcfbuhVHYxz3uo1SWgGfgK12jHp0epdP2HVpcCHDDCIdTN75emTQtuxj4AAIDDVUwE3adPn2rHO3furLvvvlt/+ctftHnz5j1WKTN0795dkZGR5rZ48WJ999135qTRTrR4e7numr5Tq/K8spPNJRHKSWXmIScIeAuV++0IFSx4yOpSgANCOAQAAAA00Guvvab+/fubYVBNxuTPxqpmxspiVXsaff755xo/frzmz59fuRnBkOFf/3Le6lbTNpbokdm7lF8ekB0tbjnG6hLQbALmPES5Uy9QwMtIC4QnVisDAAAA9sFYXSw7O1uBQEDbt283J502Jpb+6quvaj0/Ojpazz33nE444YTKYx988IG8Xq9uvvlmtW7dutr5p5xyijm0zOhx5BTvrSjSf5fbeyLueb7DdbzVRaBZla6dpO0FK9TyDx8pIqGt1eUADUI4BAAAAOzDLbfcYm4ul0vp6ek6/PDD9e233+51yXfDsGHDdOGFF1YOJTP+NCarrhkMGYwJrM844wzNmjVLRx55pCZNmmTOT1RbSFW1N9LKlStrPa9iNbWq9T366KNKSkqqdZLsqudNmTJlr9ccPny4DpQ/ENCriwv09Xr7zC+0N+uLo7Qz+Ti1zJ9mdSloRt4dc7Xjs2PU8qQpikw5yOpygPAKh8rKysxuuc8++6yGGpMe1/ghaPxwe/jhhzV69GjLagQAAIDzrF27dr/PmThxorkZvvjii72+3ghdjF5JFWqbzLo29X1v3KlTJ7MnU3088MADaiplvoCenpunX7aVyykWp92oIYRDjuMrWqcdnx+rlid+oai0w6wuBwiPOYdKS0vN36osWbKk1ufvvPPOPSbzAwAAABA+8sv9enBWrqOCIcP8wCCrS4BF/KXbtOOLoSrL/t7qUoDQD4eWLl1qdp1dtWpVrc9Pnz5d33zzTa3db9HM4uODGwAAANDAper/8mOuVuyy14pk9bG6KEZ5iQOtLgMWCXjytfOrk1W64WOrSwFCOxyaNm2ajj/+eM2cObPWoWZXXXWV2QU2JibGkvrwu4QEqagouBn7AAAAQD3kFPt0/8xcbSnyyamWtLrF6hJgJV+pcr8dqeJVzluREOHF0jmHjMn39uaRRx7RYYcdppNOOqle1zLCJGOrulyowe/3m1tjMcaDG5MRGssVqsrY8KYXvK9x/8ZsT22M6zfHfazmhHbSRvtwQjud0EantLOhbbTz1wJwsq3FPj0wK1fbS5z9f3y+6xgdbXURsFbAq7wfLlPAU6iEnnv/DAzI6RNS1zbc7MUXX9TChQvr/RpjBYb7779/j+M5OTnmvEaNpbCwUK3TWylVpUos26Hm4leJunRsr4KCAm3btq1p7+X3mxOBG2/s3W7Lp6VqMk5oJ220Dye00wltdEo7G9pG42cbAPsFQ0aPoR2lzg6GDCsK41QQf4iSihdZXQosFVD+rOvliohTfHcWWkLoCblwyHgjaQwnM1ZKyMzMrPfr7rrrLo0dO7Zaz6H27duby40mJyc3Wn3GG9jsnO0KKFbumDQ1l1zla/W6DeYSpBkZGU3+pr5iqVbzTX1pqVznnGM+F3j3XSk2VnawRzttiDbahxPa6YQ2OqWdDW1jrE1+rgAI2vZ7jyGCoSBjrMGyjFs1aO0VVpcCywWU9+OVckXEKq7LBVYXA4R2OLR+/Xr9+OOPWrBggcaNG2ceKy4u1rXXXqtJkybp888/r/V1xrxEtc1NZLwpbcw33xVDuySX8aDRrluPO1cOaWuODxMV9zHvZbT396+7y9i30YeZau20KdpoH05opxPa6JR2NqSNdv46AE6cfNpYlczpQ8lqmu8+TqxbBlPAr10/XGIGRLEdz7K6GiB0w6G2bdtqxYoV1Y4NHTpUN910ky666CLL6gIAAACwd/llfj300y5tIxjaw6+FiSqO7ab40pVWl4JQEPAqd9r5ajnsI8W0PdnqagBTyP2qLjIyUt26dau2GceMoVRGcAQAAAAgtJT5AvrrnF2OXpVsX/wBY2jZbVaXgVDiL9fOb0eobMtUqysBQjMcAgAAABA+/IGAnpqbp5W7vFaXEtLmR/7B6hIQanwlyv3mDHl2zLW6EiB0hpUF5/Gp3dq1a5u1FgAAAAD188qiAs3dVm51GSFvaWGKSqLbK658g9WlIIQEvIXa+c0ZanX6bEUkMFIG1qHnEAAAAID98u6KIn2zodTqMsKCLyAtz9y9ujJQwV+8WTu/GS6/p9DqUuBghEMAAAAAGuy7DSX63/Iiq8sIKwuiT7G6BIQo78752jXtQgX8zNsFaxAOoW4JCcHl7I3N2AcAAICjLdxerpcXFVhdRthZVNhS5ZEZVpeBEFW28RPl/0zvMliDcAgAAABAveUU+/SPuXnmMCk0jMcvLW/Nh3/sXfGyf6ho2bNWlwEHIhwCAAAAUC/lvoCe/CVPBR6Sof21IOZ0q0tAiMuffYvKNn1pdRlwGMIh1K20VDr33OBm7AMAAMCRXllcoDX5LFl/IBYWZag8ItXqMhDKAj7lfn+RfEUbra4EDkI4hLr5fNK77wY3Yx8AAACO8+XaYk3byC8KD1SZT1rV+mary0CIC5TtUO7U8xTwe6wuBQ5BOAQAAABgn5bnevTmUpbZbiwLY8+yugSEAU/OTBX8cpfVZcAhCIcAAAAA7NWuMr/+9gsTUDemBcVt5HWzCjDqVrTkSZWu/8jqMuAAhEMAAAAA9ur5BfnKLfNbXYatFHulNZk3Wl0GwsSu6ZfJW7DG6jJgc4RDAAAAAPY6z9CCnHKry7ClhfFnW10CwkSgfJd2GfMP+fi/iKZDOAQAAABgD5sLvXp7GfMMNZV5JR3kc8VYXQbChGfHHBXM+7PVZcDGCIcAAAAAVOPzB/TM/HyVM5qsyRR6pHUZ11hdBsJs/qHybbOsLgM2RTiEusXHS4WFwc3YBwAAgK29t6JIq/O8VpdhewsTL7S6BISTgF+7po9WwFtqdSWwIcIh1M3lkhISgpuxDwAAAFsvW//BqmKry3CEeaWd5XdFWF0Gwogv/zcVzLvH6jJgQ4RDAAAAAExlvoCem58vP8vWN4u8cpc2pl9pdRkIM0VL/6bybTOtLgM2QziEupWVSaNHBzdjHwAAALYdTpZd7LO6DEdZmHSx1SUgLIeXXc7wMjQqwiHUzeuV3nwzuBn7AAAAsJ2NBV59sprhZM1tXtlBCoipG7A/w8tYvQyNh3AIAAAAgF5dXCAfw8ma3Y4ylza3GiW7WrNVuvAJqdu10oBx0vOf735ufY503uNS12uk4yZIUxfv+1ofzJKOukPqco10xTPSjoLdz302R+p3i9R/rDRlfvXXnf6gtGidbKdo6VPy7FxgdRmwCcIhAAAAwOG+31iipTs9VpfhWIuSL5cd+f3SJU9JaUnSlPukv14qPf2x9P5MKRAIBjwZydIX90rnHCVd+Yy0cUft15q3Whr3ujT2TOmTP0t5RdKtrwaf8/ml29+U/nK+dNc5wePG9Q3fLJAyUqRDOsp+Aj7l/3ST1VXAJgiHAAAAAAcr9Pj19rJCq8twtLmeg2VHOfnSwe2lxy6VurSWhvWVBveSfl4hzVgmrd0m/d9oqXsb6cbhUv9u0js/1H6t17+R/jhQOvcYqXd76R9XSd8sDPY+2lkg5RZKwwdKZwwM7lf0Kvr7R8FAya7Kt36vktXvWF0GbIBwCAAAAHCwd34tUl4548mstK3UreyWI2U3manSS9dJiXHBnjyzV0izlktH9ZTmrg725omP2X3+oO7SLytrv9bcVdIRB+1+3DZNattS+mWV1DJJiosODh1buDZ4zRaJ0neLpFbJNu01VEX+nNsV8DJfGA4M4RAAAADgUCt3efT1+hKry4CkxalXyc4G3S6d9YjUv6t0+gBp665geFRVerK0Jbf212/Nk1q3qHF+SvD8CLc04VxpxKPSuf8n3XdB8NjfPpTGnSXb8xdvVOHCR6wuA2Eu0uoCAAAAAFjj38sKRZ+h0DDXe6hOkH29cr20LU8a/5Z073+kknIppsan0ehIqXwviyOXlAWf3+P836fKuuIE6bxjJJdLSogNTm5tzHXUtbV0zfPBHkbDB0j3XhA8x24KFz+huO5XKDKpi9WlIEzRcwh1i4+Xtm0LbsY+AAAAwt68bWVMQh1CNpdEKiflFNlV387Sif2k+y+U3p4aDHbKagRBRjBkDA+rTUzUnsGReX6VYWnG8DUjGKo619BrX0ten/TDo9JPy6XPfpE9+cuUP3us1VUgjBEOoW5GtJ6eHtzsGLMDAAA4TCAQ0Du/FVldBmpYnDZGdpKTJ30+t/qxg9oEQx1jBTHj+aqMnkXG8dpktQg+X5/zv18ipSZIh3aSfl4pDTk4GDod0ys475FdlW34UGXZ06wuA2GKcAgAAABwmBmby7Q2fy/jd2CZeb7+spP126U/PVt9HqGF64LDvYzJp40JpI3hZRWMVcwO71r7tYzjs5fvfrxph7R5Z3AOo5qMXkPjfl+hzO2S/IHdS95XLHFvVwVzJ1hdAsJUSIRDZWVl6tOnj6ZOnVp5bNasWTr66KOVmJioHj166JVXXrG0RkcrK5Ouvz64GfsAAAAIW15/QP9dHv5L1/s8ZfriuiHatnBG5bF5L03Qf4dnVttWfPzqXq+x/MOX9PGlffX+uV3089O3yFu6e8WnFZ+8psmjeunza47Wjl/nVLvvZ1cdqZKdWxu9TeuLo7QzeYjsol9n6dCO0thXpeWbpG8WSA9Okm4eHlyxrE1L6dZXpd82Sc98Ks1bI134e/ON3kVGzyAj0DFcerz03kxp4vfS0g3Sza9IJ/SVOqRXv+f0pVJSXLDXUEUNn/wcvMdX82sPk+zEs22GSjd+ZnUZCEOWh0OlpaW68MILtWTJkspj2dnZOvXUUzV06FDNmzdP999/v2688UZ9+umnltbqWF6v9Pzzwc3YBwAAQNgyVifbWvz7J+4w5Ssv1az/u1b563+rdjx/w3IdctkEnfGvRZVb5xMvrPUaG2d8oiUTn1D/Gx7X0Iff047f5mrh6w+az5XmbdfC1+7X0eNfVqdh5+uXF8ZXvm7NlInKGniC4lpmNknblqTdKLswVgx7/abg0vJnPCzd9oZ05YnBreI5IwA65T7p/ZnSqzdI7dKCr52zUup3S7B3kGFAN+mvlwVXIPvjw1JKvPT3K/fda6hiomrj/sZrjEDqjIGyvYK5fzaHjgJhs1rZ0qVLNWrUqD3+4U6ePFmtW7fWI48El+Pr3r27vvvuO02cOFGnn366RdUCAAAA4a3U69f7K8J7rqG89b/pp8fH1PrhN3/DCvUYeb3iWmTUeZ3lH72s7n+8Wm0GnWQ+7n/94/r+L+fr0MvvUVH2OkUlpijj0MGKbdlaSyf93TzH5ynX8g//qeMfm6ymMi9whI6VfRjLz7+6l7yrc6b0/u7crZqje0qbX69+7PzBwW1f3qtxPaMX0b9ulaN4d85T2foPFdvxLKtLQRixtOfQtGnTdPzxx2vmzJnVjp9yyil6/fXX9/xBkFdjBjIAAAAA9fbVulLllYd3j4KcxTOVfugxGvZE9VEFnuIClezYoqS2dS/l7ff5lLtivtL7HFl5LK1nf/k95dq1ZoniW7VVecEuFW3bqNyVCxSf3tY8Z81X/1FW/z80Wa8hw+qiGOUlOqB7C5pUwYIHrC4BYcbSnkNjxtQ+G3+nTp3MrcK2bdv0zjvv6L777tvnvEXGViE/P9/80+/3m1tjMX5D4TJX7Ao082xmwfsa92/M9tTGuH61+/j9lSmieayJ799c9minDdFG+3BCO53QRqe0s6FttPPXAgi1uYY+W7t7Tp1w1e200bUeN4aUGSvrLp30lLJ/+VbRSS3UY8S15rCwmjxFeebQtLiWrSuPuSMiFZ3cwgyYWvUaqO5/vEqf/WmQIqJjdOQdL8nv9WjFR//U0EfeV1Nb2uoWHVV4UZPfB/buPVS6/iPFdvij1aUgTFgaDtVHSUmJzj77bHOY2TXXXLPX8x599FFzbqKacnJyzHmNGkthYaFap7dSqkqVWLZDzcWvEnXp2F4FBQVmWNak9/L7zV5axht7t9stV3GxMqt8PQNF4d0VeW/ttCPaaB9OaKcT2uiUdja0jcbPNgBNb/qmUu0stW8YW7BxpfnL1OR23dX9jCvNHkZznrlNkXFJanf0adXO9ZWVmH+6o6KrHY+IijF7Dxn6Xn6Pep17oyKiY81t1RdvqfVhQ+VyuzXtz+eqYPMaM6jqec4Njd6Wea6jdVSjXxVOU7jgQcIh2CMcMoKYM888U8uXL9f06dMVHx+/13PvuusujR07tlrPofbt2ys9PV3JycmNVpPxBjY7Z7sCipU75vfZ0ppBrvK1et0GJSUlKSOj7jHUB/qm3vjBanztzDf1VcIg45gSEmQHe7TThmijfTihnU5oo1Pa2dA2xsbGNktdgJMZYe3Hq8O/19C+dPzDecoadJJiklqYj1M7H6yCTau06vM39giH3NEx5p8VQVDVlcgiYuIqH0cnpgbP83q0fPI/NfThd7Xk348ruUMPHTX+FU258Xhl9DtWLbv1bdS2rCiMV2H8wUos3r1oD9BQnh1zVL71B0Vn2mkWKzguHDLCHWPFspUrV+rbb781J6Xel5iYGHOryXhT2phvviuGdkkus9tq8wne17h/c3yYqLiPea8q96v5ONxVa6dN0Ub7cEI7ndBGp7SzIW2089cBCBVzt5VrY6FPdmZ836kIhioktz+o2lL3FWKSWpq9gUpztym5ffBzht/nVXl+rmJrmcx67TeTlHnYEMWltdb2ZbN16Oh7FJ2YYs5TtH3JT40eDhmfNpamj9WgdbUsxwU0QNHSpwiHUC/uUP2N48iRI7V69Wpz0uqDDz7Y6pKcLS5OWrMmuBn7AAAACCsf2bzXkGHx23/V1AnnVDu2a/ViJbXrtse5xtCwFt37afvSnyqP7Vg2R+7IKLPHUVVGaLR88kvqdc5Nwde63AoE/JUTWwejnMY33z20Sa4LZyld/6G8heusLgNhICTDoVdffdVcuv6VV15RamqqsrOzzW3nzp1Wl+ZMxm90jQnCjY3f7gIAAISV5bke/brTI7szlqQ35hn69f3nVbhlrVZ+9obWfvs/9RgRXATHW1aiktzdc3ca8wX99v7z2jTzM+1cPk+/PH+HOp98kSJjq09lsfab/yqj72Cz15DBCJXWT31PuasWKWfRj0rr0b9J2vNbUaKKY/cMtoAGCfhUvOwZq6tAGAjJT/rvvfee2Xto+PDhysrKqtyM3kQAAAAA6u/TNfbvNWRoedBhOvquV7Tu2//py+uP04qPXtGRt79grjxm2PDDh/r4kkMqz+9w3Aj1PPcmzXnudk275zyl9ThcfS//Sy29hl5Uz997DRkOHnWbirZu0NS7z1a34VcoreeAJmmPLyAty9g9pyqwv4pXvCq/p9DqMhDiQmbOoeA8PkFffPGFpbWghvJyacKE4P7DD0vR1Vd1AAAAQGjKK/Pr5+wy2dV5n2yt9rjtkaeaW206n3CBuVXV69ybzG1vjOXtT3n++2rH4lu10bAnPlVzWBB5gpqmXxKcJFC+SyUr31BCr8ZfWQ/2EZI9hxBiPB7piSeCm7EPAACAsDBtY4nZAwXhaWlhikqj2lpdBmygaNk/qnXIAGoiHAIAAABs6rsNpVaXgAPgDUi/tR5ndRmwAV/+CpVv+dbqMhDCCIcAAAAAG1q6o1ybi+y9fL0TLIg6xeoSYBMlq96yugSEMMIhAAAAwIa+pdeQLSwqSlN5ZIbVZcAGSte9J7+nyOoyEKIIhwAAAACbKfb49dMWwiE78Pil5a1vtboM2EDAW2QGREBtCIcAAAAAm/lhU6nK/VZXgcayMGa41SXAJkpWvWl1CQhRhEMAAACAzXy/iV5DdrKwKEOeiBSry4ANlGdPla9og9VlIAQRDqFucXHS4sXBzdgHAABAyMop9mnlLq/VZaARlfqkVZk3W10G7CDgV8mqt62uAiGIcAh1c7ulgw8ObsY+AAAAQtasLWVWl4AmsCBuhNUlwCaKWbUMteCTPgAAAGAjM5mI2pYWFLeR151gdRmwAV/er/LsWmZ1GQgxhEOoW3m5dN99wc3YBwAAQEhaX5ir97a+Il/MLMXF7LC6HDSiYq+0JvN6q8uATZRt+MjqEhBiCIdQN49Huv/+4GbsAwAAICRNXr9Yy/O36b0N3+vNDa9qdvGr8kTPUHzMNqtLQyNYGH+O1SXAJkoJh1BDZM0DAAAAAMLT5HWLqz1eW7hDawtnSJqhdvGp6t+yl1q4uqu4NFNyuSyrE/tnfklHjXBFyR3gF7Y4MJ6cWfKV5igiNt3qUhAi6DkEAAAA2MDOsmJ9v3XNXp/fWLxLH26cqTc2vKVpBS+qOOo7xcVulORv1jqx/wo80rqMa60uA3YQ8Ktsw6dWV4EQQjgEAAAA2MCUTb/JF6hf0LO1tECfbPpZb66fqG/yXlBB5DeKi10vl8vX5HXiwCxMvNDqEmATDC1DVQwrAwAAAGzgmy0r9+t1O8qK9PnmXyT9opSoWB3Rqoeyog5SWVl7+QN8XAg180q76AxXhNwBgjwcmPLNUxTwlckVEWN1KQgBfLcHAAAAbODb/QyHqsrzlGrKlgXGwulKjIzWEa0OUtuYg+Qp7ShfIKpR6sSB2VXu0oZWl6tjzitWl4IwF/AWqWzLd4ptd4rVpSAEEA4BAAAAYW5twU6tLmjcpesLveX6JtuY4HqxYiMiNSituzrGHSRfWWd5/dGNei80zOLkSwiH0CjKs6cSDsFEOIS6xcZKs2fv3gcAAEBI+WbLiia9fqnPq++3LZO0TNHuCA1M66rO8T0UKOssj5/3h81tXlkPnW51EbCF8m0/WF0CQgThEOoWESENHGh1FQAAAGjCIWX1Ve73aUbOcs3QckW43Oqf1knd43vK5e2icm98s9XhZNvLXNrU6iK13f5vq0tBmPNsn6OAt0SuyDirS4HFCIcAAACAMNec4VBVxupos7ev1mytltvl0mEtOuigxJ6K9HZVmTfRkpqcYlHy5YRDOHD+cpXn/KSYrKFWVwKLEQ6hbuXl0tNPB/dvvlmKZow5AABAqFi6K1vZJQVWlyF/IKBfdq4zN8OhLdqpd1JPRfu6qdSTbHV5tjPPc7CYKQaNoXzr94RDIBxCPXg80h13BPevu45wCAAAIITM2LpWoWhh7kZzk75Wr5QsHZLcU7GBbiotb2F1abawtdSt7BYj1Dr3A6tLQZgr38q8QyAcAgAAAMLaLzuMACa0LcvbYm7Sd+qelKFDU3sqUd1VUpZmdWlhbXGLqwiHcMA8OTMV8HvlchMPOBl/+wAAAEAYm7tjk8LJioJt5iZ9r06JaTostaeSXd1UUpohuVxWlxdW5nn76gSri0DYC3iL5Nk5T9GtWITIyQiHAAAAgDDl8fu0MNfokROe1hbu0NrCGcbgOLWNT9GAFr2UGtFdJSWtCYrqYVNJpLannKxWeV9aXQrCnHfnfMIhhyMcAgAAAMLUktxslfm8soNNxXnaVDxL0ixlxiZpYFpPpUV0V2lpGwXktrq8kLW45RgNJRzCAfLsXGh1CbBYSHyXLSsrU58+fTR16tTKY2vWrNEJJ5yghIQE9e7dW1OmTLG0RgAAACDUhMN8Q/tja2mBPtn0s95cP1Ff5T2vgsivFRe7Xi6Xz+rSQs48/wCrS4ANeHMJh5zO8p5DpaWlGjVqlJYsWVJ5LBAI6KyzztIhhxyiOXPmaPLkyRoxYoSWLVumDh06WFovAAAAECrsGg5VtbOsWJ9vnmvMrqSUqFgd0aqHsqIOUllZO/kDUXK6dcVRyk0erBb5060uBWHMk7vI6hLg5HBo6dKlZjBkhEFVfffdd1q1apV+/PFHs+dQr1699M033+i1117TfffdZ1m9jhUba/yl7N4HAABASAi3yagPVJ6nVFO2LJC0QAmR0Tqi1UFqF32QPGUd5XNwULQk7SYNJhzCAQiU58pXtEERCe2tLgVODIemTZum448/Xg8//LAZAlWYNWuWDj/88GrHBg8erJkzZ1pUqcNFREhDh1pdBQAAAGpYkrtVTlXkLde32YuNWXcUGxGpQWnd1TGuh3xlHeX1x8hJ5gaO0GCri4Ateg8RDjmXpeHQmDFjaj2+ZcsWtWnTptqxzMxMbdy4cZ/zFhlbhfz8fPNPv99vbo3F6OXkMldOCBgPGu269bizeV/j/o3ZntoY12+O+1jNCe2kjfbhhHY6oY1OaWdD22jnrwXQlLKL81Xo3f3+18lKfV59v22ZpGWKckdoYFoXdYnvoUB5Z3l8cbK7NUWxykscoJTCOVaXgjDmNSalbnea1WXAqXMO1aa4uFgxMdXTfuNx1fCnpkcffVT333//HsdzcnLMeY0aS2FhoVqnt1KqSpVYtkPNxa8SdenYXgUFBdq2bVvT3svvV15envnG3u12Sx6P4t9+23yu+OKLpSh7dNndo502RBvtwwntdEIbndLOhrbR+NkGoOFWFTTfe9Fw4vH79GPOCv2oFYpwuTUgrZO6JfSUy9NF5d542ZHxK+ulrW7RUYUXW10KwphnF/MOOVlIhkOxsbHasaP6DzsjGIqP3/s387vuuktjx46t1nOoffv2Sk9PV3JycqPVZryBzc7ZroBi5Y5JU3PJVb5Wr9ugpKQkZWRkNPmbeqOXkvG1M9/UFxXJfffd5nOJN9wgVRnuF872aKcN0Ub7cEI7ndBGp7SzoW00fu4DaLiV+dutLiHk+QJ+/bR9tbm5XS71a9FBPRJ7KNLXVWWeJNnJfNcxOsrqIhDWfAWrrS4BFgrJcKht27bVVi8zZGdnKysra6+vMXoW1extZDDelDbmm++KoV2Sy3jQaNetx50rh7Q1x4eJivuY96pyv5qPw121dtoUbbQPJ7TTCW10Sjsb0kY7fx2ApkTPoYbxBwKau3OduRkObdFOvZN6KtrXVaWeFIW75YXxKozrpcQSY3gd0HC+wuD/DThTSL4bO/LIIzV37lyVlJRUHps+fbp5HAAAAIC0knDogCzM3ah31n+ttza9pIVlbykQM1ux0TsVroxfXy/LGGd1GQhj/pJsBXzlVpcBi4Rkz6HjjjvOHBJ2+eWX65577tHHH3+s2bNn6/XXX7e6NAAAACAkrGJYWaP5NS/b3KSp6paUrr6pvZSobiopa6VwMs89VAOtLgJhLGAuZx+Z3NXqQmCBkAyHIiIi9OGHH+rKK69U//791a1bN33wwQfq0KGD1aUBAAAAIYGeQ01jZUGOuUnfq2NCSx3WoqdSXN1VUprRzNNKNNxvRUkqju2i+FLmjsH+8RWtJxxyqJAJh4Lz+OxmBELTpk2zrB4AAAAgVBV5yrSzrNjqMmxvXdFOrSv6UdKPahOXogEte6llRHcVl7QOyaDIF5B+zRinw9dfb3UpCONwCM4UMuEQAAAAgPrJKS2yugTH2VySp482zZI0SxmxiRqU1kutIoweRW0UCKGpXBdEnqDDrS4CYYtJqZ2LcAh1M1aB++ST3fsAAACw1PYywiErbSst1Cebfpb0s1rGxGtQy55qHW0ERe0UCERYWtuSwlSVRrVVrGeTpXUgPNFzyLkIh1C3yEjp9NOtrgIAAAC/207PoZBhDO/7YstcSXOVEhWrQa16KCvqIJWXtZM/ENXs9XgD0m+tx6rvBlYuQ8P5S435tuBEhEMAAABAmKHnUGjK85Tqqy0LjMFdSoiM1qC07mofe5C8ZR3l9Uc3Wx0Lok5VXxEOoeEC5XlWlwCLEA6hbh6P9O9/B/cvukiKav7fgAAAAGA3eg6FviJvub7busQY6KXYiEgNSuumjrE95CvvJK+/aadqWFyUpvLIVor2bm/S+8B+/B7CIaciHELdysulyy8P7p97LuEQAACAxQiHwkupz6vvt/0q6VdFuSM0MK2LusT1UMDTWR5fXKPfr9wvrWg9VgdvvLvRrw17o+eQcxEOAQAAAGGGYWXhy+P36cecFfpRKxThcqt/y07qntBDLm8XlXsTGu0+C2KG62ARDqFh6DnkXIRDAAAAQJjJLSuxugQ0Al/Ar9k7VpubSy71a9lBPRN7KNLXVWWepAO69sKiTJ0bkaIoHx/2UX+B8nyrS0A4hUNbt26Vz+fb5zlt2rTZ35oAAAAA7EOZ32t1CWhkAQU0b+c6czMcktpWvZN7KsbfTaXlKQ2+XqlPWpV5k3pufrAJqoVtBbzye4rkjmq8XmwID+79edHDDz8sr9crj8dTbas4ZjwPAAAAoOmGJsHeFu3apEnrv9FbG1/SwtK3FIiZrdjonQ26xsK4EU1WH+wrwNAyR9qvnkMJCQnq0KHDXp9PTk4+kJoAAAAA7IPH77e6BDSjX/OzzU2aqq5J6eqX2lOJge4qKW+1z9ctKG6rs91xivAzDBH1F/Ayp5kT7Vc45HK5Duh5AAAAAPuPnkPOtaogx9ykH9QhoaX6t+ilFFc3FZdmGB/Eqp1b5JXWZN6gblset6xehCG+vzgSE1KjbjEx0n//u3sfAAAAliIcgmF90U6tL5ohaYbaxKVoQMueahnRXcUlWZVB0cL4c9VNhEOov4DomehEhEOoW2SkdO65VlcBAACA3xEOoabNJXn6aNNPkn5SRmyiBrbspfTIbppf0l5nuaLkDnisLhHhIsD3FyciHAIAAADCDHMOYV+2lRbq080/S/pZYw9K14aYP6t1aarKy7IU8DMFCPYt1ZOhKKuLQHiEQ+Xl5frxxx8VCAQq5xeq2Df+LCwsbOw6YSWvV/rgg+D+iBHBnkQAAACwjDdAOIR9i3K59NHRsToq4WudO+dGPb32ZqmkWJHdL5Mvpr9Kctzye+ghgloEYq2uABbYr0/5TzzxxD6fP+aYY/a3HoSisjLpvPOC+0bwRzgEAABgqWh3hNUlIIQNaBGryQO3K1FzzcfbPLF6MH28/rzyBnkWBucfiolOVVT3S+VPOFLFOVHyl3strhqhggWmnMltdQEAAAAAGiY+kkEfqN1DByfrq0GLlKgV1Y7/Z0cbzet9w+4D5bvkWfIP+WaPUszG0UpOnqaktj5FxPKLYMeLIBxyov36n//YY49pyJAh5hCyqiqGlX3zzTf6y1/+0lg1AgAAAKgiPjLa6hIQYlpERWjKMS51j55mLDdVq8u2DNXPnX5RzNofqj/hKZJn2YuSXlRURKwSul2gQMrxKslNkLeYiaydhp5DzrRf4VBeXp6OPvrovT7/ySefHEhNAAAAAPYhgXAIVZyRlaDXDl2rqMDGfZ7nk1uX+Mfof4nLFCjcXus5Ll+pPL+9IekNRbojFd/lXKnliSrOS5G3sLyJWoBQ4nITDjmRuymSRJJGAAAAoOkwrAwV3hyQpH8dMrvOYKjCkuJEvdrxrnqd6/J75Vn5H3lmX6HI5ecoKfq/Sm5bqKjkmAOsGqEsIpbw2YkYUAoAAACEmfiIMPjw5vFKD7wtXTRM6tk+eGzit9LX86qfd9EfpGGH1X6NKb9IX8yRSsqkgT2C58b8Hox9M0/68EcpIU760ylS1za773vPm9L486XURNlV54QoTTmqROnu7xv82ie3dtcJvS9Th6Vv1vs1rkBA3jUfSGs+MD9ExnY8Ta7MM1RalKnyXWUKN9/99rNuf/9v1Y79occg/d/IW/c496c1i/Tk129p065tOqRtN/351KvVrkWm+dz8jb/p3o+fV1FZia4fer5G9BtW+bo7P3hKpx58jIYeNFDhwhXhVkQs4bMTEQ4BAAAAYSbkew4ZAc1Ln0qbdlQ/vnmHdPZgaXCf3cf21kthznLpw5nSVadKKQnSq19I//teuniYlF8s/fd76ZYR0uot0r++ke67JPi6HxZLh3axdTA0pkuSHu6+RO5Aja9vA5y74yzNzJor95ZF+/V677rPpHWfyVg3L6nt8XK1Gamy0rYq2xkeQdHq7Rt1bLfDNeHUqyqPxdTy/yo7b7tue+9JXXPsOTqqS1+9MuN98/F/rvyrOWLm/6a8bgZCvbO66Nb//Z+OP2igUuOTtTJngxkmHdd9gMJJZHwYBM9oEqxWhrpFR0uvvx7cjH0AAABYKqTnHDICoYcmStvy9nxuy06pY2Yw7KnYKnoC1fT1XOnEw6V+XaXOraVLT5CmL5bKPFJOnhQfI/XqIB3eXcreGXyN1yd9NVc6LXx6ajRElMulL4+J06Pdph9QMGQo9EXoprhxcsUkHHBd3k3fyfPzjXIvGqlE3z+UnLVFsa1Ce+jZ2h2b1C29vVolplZuSbF7fi0mL/hOvbK66OIjhqtrenvde/q12pK3Xb+sX2Y+v27HZjMQGtSpjxJjErRx1zbz+Ksz3tefjhkRdlOuRCaE9t8bQiwcqrlKWUOfR5iJipJGjw5uxj4AAAAslVbLh9iQsXxDcBjZhAurHzeGhuUWSq1b1H0Nv19as1Xq0W73MWPYmBH+bMiRWiZJRaXSjnxp3dbg44peQ4d0smWvoUEtYrX2xHwdkfCj8QVqlGt+l99SXxw0To3Jlz1Tnjlj5VowUollf1Vy6w2Ky4iRQiwjWb19kzq0zKrzvEWbVujw9r0qH8dGxahH605atGm5+TgzuZV+3bpGW/JyVFBapMyklmavpA25W8Ou15Ah0ghd4Uj7NazsrLPO0o8/Gt+Uag+GTjvttAOtCwAAAMBeZMX9HoaEouP71X58885gQPDJT9KiNcG5gk7uLx1z8J7nFpcFh6alVgnBItxSYpyUWyB1axPsVXTnK1JUpHTN6b/3GvpFuuM82c2jfZJ1bds5cgWKGv3aY7cM0IyD/qjU5R81+rV92+ebmyGhZS9FdrpIHvVScY5H8lvXocD4zLpu5xbNXL1Ar/84Wb6AXyf0PFLXDjlXURHVPyJvL9pl9iqqKi0hRdsKgr3Vbhh6ge756Dl5fV5dfvRZSk9qqb9/87auPDr8eg0ZCIeca7/CoSOOOKLxK0Ho8nqlL78M7p98shTJVFUAAABWyopPVtgxh365pNYtgxNQ/7ZBevOr4JxD/btXP7fcE/yz5vvOyIhgCGQ4d4h02iApOjIYEE1dKPXpJLnd0hP/k7bukv7QVzp1kMJVi6gIfT1Y6ho1TWrCLOXcwkv0dcsFCuxc12T38O9cpvKdfzb341O6KKrLJfK4+6h4m6/Zg6Ls/O0q9ZQpOjJKj464WZt35eiJr95Qmbdct514WbVzK86rKioiSuU+b+Uk1kff0k8en8cclmYMVzOCp2O7H65Hv3hV01fOVf+OB2vCqX9STCgPB/0dw8qcizmHULeyMmn48OBm7AMAAMBSbeLCMBw6urf09BjplAFS+3TphMOl4w6Rpi7Y89yKUMj4JWVVRjAUXeWDekJsMBiq6DVkBEGTf5TatJLuv0T6Zr60dqvC0VltErT8D1vUNWpOk99rc3mMHk4fL9XoNdNUAnmrVT7vfgV+OVfxuWOV0mqhErIi5Ipsno+nWSnp+uaWl835g3pkdtLxPQZq7AmX6oP538hnDGmswpikutz7e1j5OyMIiq0S9MRGRVfOV/TK9A905TEj9O2vs/Vr9hq9d83fzZXM/vfLFIUDwiHnCulwaMOGDRo+fLiSk5PVqVMnPfXUU1aXBAAAAFguLHsOGUNsjGFhVWWlBechqsk4zwh98op3H/P5pcKS4CTWNf24ROrdUWqRKK3cFOxBFB8rdc2SVmxSuPnXgES90We2ogLNV/u/d7TV/N7XqbkFCjerfMGj8s85T3Fbr1dyy1+U2MYld5SxDlrTSYlLrDbsq3OrtirzepRfWv3fY3piS+0oqj65+o7CPYeaGdbu2Ky1O4MTVC/Y+Jv6d+xtBkdHdTnUXPI+HDCszLlCOhw677zzlJiYqF9++UVPP/20JkyYoA8++MDqsgAAAABLJUbFKDEyzD7EfTBDevx/1Y+t3yZltdzzXLdL6pxZPdhZtVmKiAj2OqrKCI2+/CU4xMxgfOCvWCDHGK4URovldE2I0soTPTqj5Q/G2Lpmv/+lW/6g8k6DZZVA6XZ5Fj0h388XKGbz1UpO/VGJbQNyG0MHG5Ex19Cwv19lDhmrsHzrOjMwalEjeD2kbXcz6KlgvOa3revM4zW9OuMDXXH0WWbo5Ha5Khdq8vp9YfPPkJ5DzhWy4VBubq5mzZqlP//5z+revbvOPPNMnXLKKfrmm2+sLg0AAACwXFZ8CE9KXRtjSfrlG6Uvfpa27ZK+my/9uFQ6ecDueYbyiqpPbG2cO3eFtCZb+tfX0pBDpJioWnoNdQj2GjIYy97PWhZcxezXDcHeQ2Hghq5J+nnwSrVyLbasBp/cutR/vVwJabJc+S55ljwj3+xRitk4WsnJU5XU1quI2AMPig5te5BioqL14Gf/NHv7zFg1X09/+29deuQZ5rCy7YW75Pl9TqE/HjrUDIfemPmhVuVs0P2fvqg2qenq36F3tWuu37nFXKXM6DVk6J3VVT+snGse+3rZrFrDpFBEOORcITuzcFxcnOLj4/X666/rscce0+rVqzVjxgw9/PDDtZ5fVlZmbhXy8/PNP/1+v7k1FiP9DXY/bO7fQgTva9y/MdtTG+P61e7j91emiOaxJr5/c9mjnTZEG+3DCe10Qhud0s6GttHOXwugKWXFJWtF/naFDSO0ue4M6YMfg1ur5OAqY8bKY4bZv0mvfSm99vvS6kf0lLbnSW99bUzyEpy0+rwhtfcaGnf27mN/PEp64eNgL6UTDpO6/n79EBXjdumTo2I1MGG6FLD+++Gi4gS90ekuXbbkNoUMT5E8y16S9JKiIqKV0PVCBVL/oJLcBHmLq88HVB8JMXF65vzxevLrt3TpGxMUHx2rkf2G6dIjztCWvO364ws36cVR92hAx95mEPR/I8ea5748/X31bXeQnjh73B4rkVXtNWQ4sfdRmrlmoS5/6y8a1OkQnd//JIWD6Ba1DNuEI7gCFX3dQtAbb7yhG264QaWlpfL5fBo9erQZFtXmvvvu0/3337/H8eXLlyspqfF+q7Jlyxa98+4HSh1wspJbZaq55G/fqmWfva1br7taWVlN+9sP4016Xl6eUlJS5Ha75SouVmbXruZzW1etUiA+XnZQs512RBvtwwntdEIbndLOhraxoKBABx10kPkaY55BAPVz6ff/0b9W/WJ1GTgAR6bF6v3+OYrXyia9z3H/uVMLtjRsYZkvMt5V+6VvKZQFXBGK7nqu1PJEleSlylPY/EPx7MQV4dZh951j/gnnCdmeQ4Zly5bpjDPO0Lhx47R48WLdeOONOuGEE3TRRRftce5dd92lsWPHVus51L59e6WnpzfqG03jDWx2znYFFCt3TPN1t8xVvlav22AGXRkZGU3+pt5IvI2vnfmmvmh3917jmBLskSbv0U4boo324YR2OqGNTmlnQ9sYGxvbLHUBdtM9uZXVJeAA/LVPsq5pO0cKVBlKF0LO2TFCs1rPlSvbumFudXEFfPKsfEfSO4pwuRTb6Uy50k9TSUErefJZZXl/eg0RDDlXyIZDxtxCr7zyijZu3GgOMRswYIA2bdqkhx56qNZwKCYmxtxqMt6UNuab74qhXZIrONldswne15zcrBk+TFTcx7yX8ab92WfN425j30YfZqq106Zoo304oZ1OaKNT2tmQNtr564CmZ8xROXny5FqfM35BePfdd5v7xi8bo6JqzFUjqbCw0Fz0pKJn9sSJE7Vw4cJar9evXz9dcMEFlavqPv744+Y0CDUZPd6N5yoY71+N+9Rm5MiRGjTo94mUG+ig5BoTMyMspEW79dUxLnWJmmbOVBGqCn0RuiV1nP4Rfb0C5VVWjQtRrkBA3jWTpTWTzQ+5sR1Okav1H1Va1FrluwiK6iM27cBG3Njp+/Hnn3+uadOm1Xpex44dNWbMGNlNyIZDxgplxkTURjBU4bDDDtvrnENoQsZ/3Ouvt7oKAACAPXi9XnN+ytpMnz69cn/EiBEaPHjPVZjWrl1rfnio0KFDB40aNarO6xmvueOOO9SuXbt9nmcYOnRorfeu7dyGOCiFcCjcjGybqH/2WaXIwGaFg693pWlKj3E6cdGDCjfe9V9I679QhKSktkPlajNSZaXtVLaToGhvYlr9Pqn7frLT9+OkpKR6tcVOQjYcatOmjVauXKny8nJFR0ebx3799Vd17tzZ6tIAAAAAy/VISZfL6F0eyt1PUOnfA5N0eouZUqDhEyhb6ZYtA/XjQcOVsvwThSvvpqnSpqnmIjuJmUfI3f58lXs6qXQ7QVFVscYk8XCskO3Hbcw1ZHQ1+9Of/mROKv3xxx/rkUce0U033WR1ac5jpLdTpwa3KkkuAAAArBMfGa1OiS2sLgN16J4QrVUnenR6i++NZbcUjs4rukyuFh1kB76tP8kzZ6xcC0YqseyvSm69XnEZ0easIU4Xl5lidQmwUMj2HDJWODHmHbr55ps1cOBAc1LLP//5z7r66qutLs15Skul448P7hvjM20yITUAAEC4O7hFptYU7rS6DOzFTd2SdV/XRXIHwvvvaGNZjB7NHK/xeTdKfvv8sti3fb65GRJa9FRk54vlUU8V53glv/N65MUSDjlayIZDht69e+urr76yugwAAAAgJB2c2lqfbFhmdRmoIcbt0mdHx6p//A9SwC87+Nf2djqt93U6dPEzsiN/7q8qz/2zuZ+Q3FmRXS6VJ6KPSnJ8CvjsHxRFJccpMi44nQucKaTDIQAAAAB717dlG6tLQA1Hp8Xq3QHbFB+YK7u5OHuY5nT8RdHrfpSd+fPXqHz+/eZ+XGIbRXW9TN6ow1ScE1DAa4+wryaGlIFwCAAAAAhTR6TbYx4Yu3jikGT9qc0cKVAkO/LJrct0g95J+E2Boh1ygkDhZpUveNTcj4ttpajul8kXO1DFOW75PfYZYhfXOtXqEmAxwiEAAACELb/fr/Hjx9f6XGxsbOWSxZMmTdInn+y52lJeXp4mTJhQ+dhYLbe28yqmPKi6BPKjjz5qLndcU0lJSbXzpkyZstdrDh8+XAeiS1KaMmITta208ICugwPTKjpCXx8TUKeoabL74nELixL1Vqc7dcmSO+Q0gdLtKl/0pLkfE52sqO6Xyp9wtIq3R8lf5lU4S+zY6oCvYafvxzt27NhrW7Kysqpd0y5cgUDAlt++8vPzzUmtjX9gycmNtySf8Q/0gb8+qdanXaW0dl3UXHZsXKMZz/9Zb734D3Xt2rVJ72X8p962bZsyMjLkdruloiIpMdF2E1Lv0U4boo324YR2OqGNTmlnQ9vYVD+zAac48+vX9dGGJVaX4VjntE3Qi31WKzKwWaHquP/cqQVbGnfZ9i8z/qt2S99u1GuGragERXW7SIGkY1W8M06+kjBblc4l9Z0wQpHxMVZXAgvZ810pAAAA4BBHZjC0zCr/GZSoVw7+KaSDoaZy7o6RCrQ+2OoyQoOnSJ5l/5R39iWKWjNKyQmfK6ltmSITwmOC59j0ZIIhMKwM9RAVJf3f/+3eBwAAQMg4Mr2j1SU4To+kaH1+ZKFaun6QU+X7IjU2ZZyeir5egfISq8sJGS5fuTzL35L0liJdEYrveo4CLU9SaX6qPAXlCkWJnTKsLgEhgHAIdYuOlm6/3eoqAAAAUItBrdorwuWWzyZLpoe6W7sn654uC+UO5MrppuS10tc9xmnYooesLiUkuQI+eVZOMmbZUYTLpbhOf5TST1dJQSt58ht3mN+BSOp04PMNIfwxrAwAAAAIYwlRMerTorXVZdhejNul746N1b2dvycYquKmLYOU3/00q8sIea5AQJ41H8oz+2pFLhuppIg3lNw2VzEtrB/OldiZnkMgHEJ9+HzSzz8HN2MfAAAAIeXYzM5Wl2Brx7aK09oTd+mwuJnGmlVWlxNyzi8eLVeL9laXEVa867+UZ/Z1ci8eqaTAi0puk6OYtOYPiqJbJCg6Jb7Z74vQQziEupWWSoMGBTdjHwAAACHlpDYHWV2Cbf3t0GR93H++4gKrrS4lZK0vi9VfM8dL7girSwlL3s3T5Pn5JrkXjlSi9+9Kztqs2FbNExQldkpvlvsg9DHnEAAAABDm/pDVTdHuCJX76eXdWDJiIvX1MT51iJxGZ6F6eHN7e53We4z6LH7W6lLCmm/rbHNzGcFNq76K6Hihyv3dVZJT3iT/DpMIh/A7eg4BAAAANph36NjMLlaXYRvnt0vU0qEb1SFyrtWlhJWLt54gT4ejrC7DNnzbF6j8l/HSvLOVUHSvUjJXKj4zSnIb0VHjoOcQKhAOAQAAADZwStseVpcQ9oyP3P89IlEv9Z6lyMAWq8sJO56AW5e7rpcroaXVpdiOP3e5yufeo8Dcc5SQd5dS0pcpIStSroj9D4qikuMUm57cqHUifBEOAQAAADZwSjvCoQPRMylaq08s00kpPxgxh9XlhK15Rcl6u/OdVpdha/78NSqf/4D8c85V3I5blZK2QAlZEXJFNuzjfWqvtk1WI8IP4RAAAABgA31aZKldfIrVZYSlcd2TNfPo5WrhWmp1KbbwSHYvbe59kdVlOEKgaIvKFz4m/5zzFLf1OqW0/FmJbVxyR9U9OXgK4RCqIBwCAAAAbOKUdj2tLiGsxEW4NPXYWN3T+Xu5ArlWl2MrZ+84W4HM3laX4SiB0h0qX/Q3+X6+QDGb/6SUlOlKbOuXO2bPdajcMVFK6pJhSZ0ITYRDqFtUlHTvvcHN2AcAAEBIOqM9H8br67hWcVpzQq76xc00PlZbXY7t5PsidVvSbXJFx1ldijOV56t86XPyzb5IMesvVXLyN0pq61VEXPDzXMpBWXJH1t27CM5BOIS6RUdL990X3Ix9AAAAhOyk1Kl8GK/TU4cm68P+8xQbWGN1Kbb2xa5W+rbH2Ea/brlPOvMjaXb27mMbC6Qrp0gDJkpnfCjN2Lzva3y6Rjrlfan/v6WbvpNyS3c/99U66bj/ScPelb7bUP11F3wmLduh8OItkWfZK/LOvkRRa0YpOeEzteyTaHVVCDGEQwAAAIBNREdEamTHQ6wuI2RlxERq0R9cGt16mhQosbocR7hhyxEq6H5qo12vzCfd/oO0ctfuY4GAdONUqVWcNOk06Y9dpJunSpsLa7/Gwu3SX36UxvSVJp4m5ZdLE2YEn/P5pftmSbf3l24+TPrzj8HrG77fKKXHSb3SFLZcvnJ5176r5IO6Wl0KQgzhEOrm90tLlgQ3Yx8AAAAh68Iu/awuISRd0D5RS4duUPvIuVaX4jjnFV8uV2q7A76OEQhd+Jm0vqD68Z+ypQ0F0r1HSl1TpasOkfq2kj5YWft1/vOrdHIn6cyuUo8W0qODpe83BXsf5ZZJu8qkkzpKp3QK7u/8vVfRCwul6w5V2Evse7rcMQlWl4EQQziEupWUSH36BDdjHwAAACHr+NbdlBmXZHUZIcMl6X9HJOrFXjMVGagyDgnNZn1ZrJ5oPV5yH9gcN3O2SoNaSxNP3bMnUO+WUnyV6VEPz5Dm59R+nQU50oAqczFnJQS3BdulFjFSXKS0bKe0ZEdwPzVG+mGTlBYb3r2GKqQccb7VJSAEEQ4BAAAANhLhduvcTjbo3tAIeifHaM1JZTox5Qdj4hWry3G017Z30JLe1xzQNS7oIY0fGAxsqsopljLiqx9Li5O2Ftd+nZwSKb2284uM/z/SrYdLl34hXT5FumNA8NiLC6Xr+yrsuWMTzZ5DQE2EQwAAAIDNXNj5MDndHQcla8ZRvypVS60uBb+7aOtJ8nQ4stGvW+qTomp8so12S+X+vZ8fHbH38y/qKf14gTTjfOm8g4KTWxs9ijomS2OnBSeq/uvPu+ciCieJ/c6Qm0nrUQvCIQAAAMBmjsroqI6JLeRECREufT8kRnd3+l6uQJVZi2E5T8CtK903yhXfuP82jaDHUyMIMoKeuL2MYouJCK54VvP82Co9khKigpvhhQXSdcbk1b9JvoD06VnSL9ukr9Yr7LQYepXVJSBEEQ4BAAAANuNyuTS62wA5zR8y4rTqhJ06NHaWsYaV1eWgFr8UJuk/XcY36jUz46XtNaZGNR4bq5fVJiOu9vONlchqmrlZSomReqdJ87ZJR2UFQ6QjWktztymsRLfppYRex1tdBkIU4RAAAABgQ1cddKQiXc55u/9M32S9f9g8xQbWWl0K6vBgdi9t6XVho13v0FbS0p1SaZVppYzgpm967ecbx42gp8KWIim7KLjCWU3mCmW/zzXkdkn+3zNHowdRuMWPLf8wxuoSEMKc89MCAAAAcJC2CSn6Y4eDZXetYyO1eJh0SeY0KcDKuuHinNxzpIyejXKtgZlS63jpzz8Gl7t/eZG0aLs0slvweWMImTEJte/3oWfn95A+Wi29t0L6LVe6a7p0XDupXY1F/mZtkRKjpYN/X6GsT5o0ZV3wHlM3SP1qCZNClSsmQSmDL7O6DISwkA6HysrKdP3116tFixbKzMzU3XffrUA4zvoV7qKipNtuC27GPgAAAMLCdT2Plp1d1CFRS45br3YR86wuBQ20yxul21Nulysq9oCvZawm9szxwQDo3E+kT9ZI/xgqtUkMPm8saT/0f1L276uX9UuX7j1Sen6BdNHnwWFjD9fyX8VYoey6Kgv/GRNVGyulGa8Z2Fo6uZPCRspRFykiLtnqMhDCaiwCGFpuvvlmffvtt/ryyy9VUFCgCy64QB07dtQ11xzYEohooOho6fHHra4CAAAADTSsTXf1TMnQr3lhNjlKHVyS3j0yQcOSZ0oBlqgPV5/lpmt4z7E6btEjDX7tkkurPzZWEnvz5NrPHdR6z/NHdAtu+/JGjesZvYheGKaw1HLYdVaXgBAXsj2Hdu7cqVdffVUvv/yyBg0apGHDhmncuHH66aefrC4NAAAACBtjeh4lO+mTHKO1J5VqWPJ0SQRD4e66LUeqsNteUh00irjuxyi2w+8TJwHhFg5Nnz5dKSkpOu644yqPjR8/Xq+99pqldTmS3y+tXRvcjH0AAACEjcu6DVBCZLTsYHyPZP1w1DKlaJnVpaARnVdypVypba0uw7boNYSwHla2evVqderUSW+99ZYeeeQRlZeX6/LLL9eECRPkdrtrnZ/I2Crk5+ebf/r9fnNrLMacR8bSoObc9M06/1Hwvsb9G7M9tTGuX+0+RUVyd+4cfM74uiYkyA72aKcN0Ub7cEI7ndBGp7SzoW2089cCCAUp0XG6uOvheuk3Y3n38JQQ4dKXx0SpT+z3zfweHM1hXVms/pZ1l27Nu0kK8DOhMUUkZyh54DlWl4EwELLhUGFhoVasWKGXXnpJr7/+urZs2WLONRQfH28OL6vp0Ucf1f3337/H8ZycHJWWljZqXa3TWylVpUos26Hm4leJunRsb869tG1b044ZN96k5+XlmW/sjSDOVVyszCpfz0BRkeygZjvtiDbahxPa6YQ2OqWdDW2j8bMNQNO6rc9QvbJ8tnxh+MH7hIx4/fuwjYoJrLO6FDShV3I66JSDr1GvxS9YXYqtpA65Ui6b9ByEQ8OhyMhIs/fPxIkTzUmoDevXr9fzzz9fazh01113aezYsZWPjde2b99e6enpSk5uvFnZjTew2TnbFVCs3DG/r2nYDHKVr9XrNigpKUkZGRlN/qbe6KVkfO3MN/VVwiDjmJ16DlVrpw3RRvtwQjud0EantLOhbYyNPfCVagDsW7fkVjq/c19NXB1eq3o93y9ZozJ/kgKN98tehK4Lt56sOe1/UeSG2VaXYguu6HilnXSz1WUgTIRsOJSVlWW+WawIhgw9evTQhg0baj0/JibG3Goy3pQ25pvviqFd5hoJ5vCy5hK8r3H/5vgwUXEf815V7lfzcbir1k6boo324YR2OqGNTmlnQ9po568DEEom9B2m/6yer4AxPUKIaxMbqa+O8aptxDRzNgc4gyfg1p8ibtKb8TcoULzL6nJsMddQZErFGBBg30L23diRRx5pDgdbvnx55bFly5aZ8xABAAAAaJjeqa01omMfhbpLOyZq0XHr1TYivHo5oXH8XJisd7qMt7qMsOeOTVTa6XdaXQbCSMiGQ0YvodNPP12jR4/WggUL9OWXX+qxxx7TmDFjrC4NAAAACEt/7nuCQpXRJ3/ykQn6R48fFRHYanU5sNAD2b2V3esCq8sIay1OuFGRSa2sLgNhJGTDIcO///1vdevWTYMHD9all16qG264QTfeeKPVZQEAAABh6bC0tjq9XS+FmkOTY7TupBINTZ4uyWd1OQgB5+SeK2X0sLqMsOSOS1arU2+zugyEmZCdc8iQkpJiLmUPi0VGStddt3sfAAAAYd176NONyxQqJvRM1m0d58kVyLe6FISQXG+U7ky9Q/+38zoFvGVWlxNWWp50syISW1pdBsJMSPccQogwJvp+7rngVsuk3wAAAAgfR2Z01PD21vceSohw6cfjonV7h2kEQ6jVJ7vS9X2vW60uI6y441OVdvLuVbyB+iIcAgAAABzm8QFnKNJl3UeBEzPjtfqE7eod85NlNSA8XLvlaBV1PcnqMsJG2iljFZGQanUZCEOEQ6hbICDl5AQ3Yx8AAABhrWdqhq7pcaQl937psGT9r98vigmst+T+CD/nl14pV0qW1WWEvIiElmp50i1Wl4EwRTiEuhUXSxkZwc3YBwAAQNi777CTlRId22z3axcXqaXDAjo/fZoUKG22+yL8rSmL01Nt7pIs7O0WDtJOv0MRcUlWl4Ewxf8uAAAAwIFaxSbo7kOHNcu9Lu+YpAVD1qpNxHyFsrIyv44a9pN++DG38tidf1mu1HbfVtv++frGvV7j+Vc2qFf/6WrXY5puGLdMxSW7V197+Y2N6nLIDxowZJZ+/iWv2n0PHzxT2VuZeHlv/pnTSb8dfLXVZYSs6NYHKe1k5mfC/iMcAgAAABzq5t7HqlNiiya7foRL+uioBP29xwxFBHIUykpLfbry+iVa9ltRteO/rSjSveO76Le5x1RuF19Q+xCnDz/dpsf+tkZ//2tPfTTpMM2Zl697H1ppPrd9R7nueXCl3nixj0ad11rjJvxW+bp/vbNZJw1LU+tMFn/Zlwu3niJvuwFWlxGSWl/6nFyR0VaXgTBGOAQAAAA4VExEpB7rf3qTXLtvaozWnFisIUnTJe3uPROKfl1epBP++IvWrCvZ47nlK4rV95AkZWbEVG7xcRG1XufF1zZozJXtdMoJrXR4v2T9/bEeenvSFrP30Np1JUpNjdSQY1rojFPTtWJlcLqG8nK/Xnhlg265rmOTtzPclQXcuirqZrniUqwuJaQkH3GBEg8+weoyEOYIhwAAAAAHO79LPw3J7NKo1/xzr2RNPWKpkrW7d0womzErV8ce3UJffdS/2vH8Aq82Z5epa5f4Oq/h8wU0b36Bjj5y90pRAw9PVrknoMVLC9W2Taxyd3m1YVOp5i8sULu2wfmejPDohOPpNVRfswtS9F7XO6wuI2S445KVOepvVpcBG4i0ugAAAAAA1nr5mHN16IdPqsznPaDrJEW69dUxEeoZY0w6rbBx5aXtaj2+fEWRXC7pyX+s1dff7VSLFlG6/ur2GnXunsPK8vK9Ki3zK6tKyBMZ6VbLFpHavKVMg/qn6Nor2qnf0TMVG+PWq88fLI8n2Gvo4/8e1qTts5t7sg/R4J7nKuPX/8npMs5+SFGprOSGA0fPIQAAAMDhDkpJ1z19D2xYysmZ8Vo1LEc9Y2bLLpavKjbDoe7dEvTft/rq0guzdMudv+rjz/ecP6li4uno6OofsWKi3eaE04b7J3TTqgWDtWrhYHPo2b8nbdEfjmupiAiXzrpwng458kc9/fw6Wcbrkd68QdqwaPexvGzp3Xukf5wrvXG9tHbevq/x6zTp1aulf5wjffiIVJK/+7kVP0ovXSb98wppVY1/JxNvk7atqnep5+y6QErvLieL7Xi4Wgy7zuoyYBOEQ6hbZKR02WXBzdgHAACA7dxxyPE6pMX+9UD45+HJmtR3jqID62UnF57TWqsWHKsbr+mgPr0Tdc0V7XXZqLZ67V97rlZm9AaqmEOoqrJyv+Lidn/sSk2NUmxshNlr6Pnf5xp69Mk16nlQgn74cqC5otn8hVUClebiLZc+e1zaUeXvMBAIBjzxqdJFf5N6D5U+ekTK38vk4luWS1OekY66QLrwcamsUPriqeBzfp/01XPSkMulwZdIXz4dvL5h9RwpoYWU0bXe5e7wRunu1DvkinTocDyXW1mjX5TLXfv8V0BDEQ6hbjEx0htvBDdjHwAAALYT5Y7QK8ecK7fRVaaeOsRH6ddhfp3XapoRg8huXC6XOZSsqh7d47Ulu3yPc1u2iDIDoq3bdj/n9fq1M9db63xC//lftoYe21JZrWM06+ddGnZcmlJTojSwf4pmzt69zH2zMAKh/9wu7cqufnzDwmDPoROvl9LaS4POldr0lBZ/Vft15n8qHTRY6v0HKb2zdOqt0ppfgtcwehCVFkgHHRM8x9gv+b2ds94JBkoN9GFupmb0ullO1OL4axTXZaDVZcBGCIcAAAAAmAald9CNvQbX69wrOyVp/rFr1Dpigezq4cdX68wLqg+jWrSkUN277jlBtdvt0mH9ksygp8LsX/IVFekyex1VZYRGz728Xrde37Hytf7fe9F4vYHmn65p42Kp/SHB3j5VbflNyugiRQUnzza16RU8XhvjeLuDdz9OSpeS04PH45Ilo5fP1lXStpXBa8YmBcOjhNQG9Rqq6qotg1Xc1VkrdUUkZyjjnEesLgM2wxgh1M34QVUcXGpT8fHGr1CsrggAAABN5OHDT9Hk9Yu1rjC31ucjXNKHR8VrcOJ0KVB9CJXdnHpiK/39uXV65sX1Gn5KK337/U6981525QTSJSU+c0UzY3l7w58ubadbx/+qXj0S1aZ1jMbd/ZsuHdVG8XHVh/78591sDTkm2GvIcHjfJP3v/a1mD6PpM3N107UdmrehfU+r/XhRrpSYVv2YEeQUbt/7+Qktqx8zhqQV7JCM4U/HXib9967g54njrw4emzVJGnbNAZV/Yfmf9FHyYgXya/R8sqmsy15QhPH3ADQieg6hbkYwlJgY3CpCIgAAANhSQlSMXj3mPLm05y8ED0uN1doTizQ4cYYxiYzs7vB+yXrzpT5mIHTUsNl66bWNevmZ3ubKY4b3P96mHocbX4ugs8/MNHsDGQHRWaPmq3+/ZD0woeuevYb+uaGy15Dhzls7a/3GEv3x/Hm6anQ7c2hZSPCUSRE1+hNERAUnrq6N1zi/+jA88/W+388/bLh03b+lMf+WDj0lOLl1fLLUoq30yV+ll6+Qpr66ey6ielpZEq9n2t1tzsNjd6lDrlDygJFWlwEboucQAAAAgGqGtemusQcP0ZNLjLmEgu7rnayb28+VK1AgO9u18Q/VHp9+crq51eai87LMrapbb+hkbntjLG8/69sjqh1r2yZWUz4coJATGR2cG6gqI+iJ2ss8pEYwVBEEVZ7vrX5+dJUhecZcQ0YPonmfBiesHv1CsGfRipnSQUc3qNQXtnXSyb2vVPclL8uuojO7qfVFT1tdBmzK/tEqAAAAgAZ7pP+p6teyjVKi3PppaJRuaTfN9sEQakhsGRwqVlXRrj2HjlWenyYV76plqFmLPc9dNz8451BmV2nzMqljv2CI1P5QafPS/Sr3/G2ny9euv2wpIlJtrnlb7tjq81cBjYVwCAAAAMAeoiMi9c5xF2n5H/LUI3q21eXAClk9pG2rgsPLKmxaGjy+t/ON5ysU5EgF22s/v+oKZcYcRBVDyQK+Bg8rq1AWcOuaqJvlik2W3aSf+RfFd63e4wxoTIRDAAAAAGrVIzVTqSlnWV0GrNKuj5TUSpryD2n7emn2u1L2CqnPicHnjSFkRs8gY0iYoe+p0rLvpEVTpJw10udPScZy6ymtq193/QIpJkHK7BZ83Lq7tHxG8B6rfpba9NzvkmcWpOr9bnfKTuJ7/0GtzphgdRmwOcIhAAAAAHsVnXCqouKGWV0GrGCsJvbHCVLhTunft0rLpkp/vCu4PL1h86/SS5cFewcZjFDnhOuDvYLeuVOKTZBOvmnP6xorlB35e68hQ7/hwaXt37lDat9HOuiYAyr7z9mHKKfnObKDiJRMtbvm33K5+eiOpsWE1AAAAAD2KS71Vvk8v8nv3Wh1KWhqYz+q/rhFG+n8R2s/t/0he55/8LDgti/nPVL9cUy8NOIvakzn5l2gqenzpZyVClsut9pe87YiU2v0vAKaAPEj6hYRIZ1zTnAz9gEAAOAoLnec4lveL7mqrDQFhLAcT7T+3OLO4IprYarVGXcr8eATrC4DDkHPIdQtNlb63/+srgIAAAAWiojqpPgWd6t4p9HDw291OUCdPtiZqeG9btaRix5XuEnsN1zpI+4/4OvMmjVLkydPrvW55ORk3X333eb+uHHjFBUVtcc5hYWFmjBhgrKysszHEydO1MKFC2u9Xr9+/XTBBcHhghs2bNDjjz+u+Pg9A2Wfz2c+V+Ghhx4y71ObkSNHatCgQfVqKw4M4RAAAACAeomKO0oxyZerLP9Vq0sB6uVPWwbr5y6/KG71twoXsR36qd2Y/zTKPENer1ePPfZYrc9Nnz69cn/EiBEaPHjwHuesXbvWDHMqdOjQQaNGjarzesZr7rjjDrVr126f5xmGDh1a671rOxdNh2FlAAAAAOotNmkUE1QjbATk0ijP1XIlh8e8PZEt2qr9rZ/IHZtodSlwGMIh1K2oSHK5gpuxDwAAAEeLa3GbIqJ6WF0GUC/LS+L1XLvxwc8zIcwVk6D2t36sqJZtrS4FDkQ4BAAAAKBBXK5oxafdL5c7zepSgHp5blsXrep9pUKWy612105UXMfDrK4EDhU24dDpp5+u0aNHW10GAAAAAOODRES64tMelFxxVpcC1Mv5OafL1/ZwhaLMC59U0uF/tLoMOFhYhEPvvPOOPvvsM6vLAAAAAFBFZHQPJbR8wJiq2upSgDqV+CM0JvoWuWKTFEpaDLtOaSffYnUZcLiQD4d27typ22+/XQMHDrS6FAAAAAA1RMYerviWd4fDRwtAMwpSNbn7HQoViYeeqtYX/8PqMoDQX8r+tttu0yWXXKLNmzdbXQoAAACAWkTFDVFc6i0q2fU3q0sB6nT3lr46psdItfrtfUvriO08UG2vmySXO6LJ7uH3+zV+/Pja7x8bW7mE/KRJk/TJJ5/scU5eXp4mTJhQ+XjlypW1nmfo3bt3tSXpH330USUl7dlLq6SkpNp5U6ZM2es1hw8fvs/2wSHh0Lfffqvvv/9eixYt0pgxY/Z5bllZmblVyM/Pr/zPYGyNJRAIyGXOch8wHjTadetxZ/O+xv0bsz21Ma5f7T5+f+XvgcxjTXz/5rJHO22INtqHE9rphDY6pZ0NbaOdvxaAk0QnnK6AP1+l+a9YXQpQp3PyR2lqqwXS9lWWBUMd7/hKEXFNO8RtyJAh5laXZ555pl7Xq+88wJ06ddJzzz1Xr3MfeMAYmgqrhWw4VFpaqmuuucb8BxUXV/ckd0Yqef/99+9xPCcnx7xWYyksLFTr9FZKVakSy3aoufhVoi4d26ugoEDbtm1r2nv5/WZCbLyxd7vdxl+GUocNM5/btWOHbZaz36OdNkQb7cMJ7XRCG53Szoa20fjZBsAeYpIulN+fr/LC/1pdCrBPOZ5o/aXlnXog93rJ57EmGIpPadb7AmEZDhlBz4ABA3TyySfX6/y77rpLY8eOrdZzqH379kpPT1dycnKj1WW8gc3O2a6AYuWOab6lO3OVr9XrNpjd8jIyMpr8Tb3RS8n42lW+qZ8yxfyjae/cvGptp83QRvtwQjud0EantLOhbTS6tQOwj7iUa6RAqcqLPrK6FGCf3tvZWsN736RBi55stnsSDCFURYbyCmXZ2dlKTEw0H1cMGXv33XfN3js1xcTEmFtNxpvSxnzzXTG0S3IZDxrtuvW4c+WQtub4MFFxH7t+cHFSO2mjfTihnU5oo1Pa2ZA22vnrADhVXOrNkita5YXvWl0KsE9XbBminzvPVdya75r8XgRDCGUhGw5NnTpVHs/u7n133nmn+edf//pXC6sCAAAAUB9xKWPkcsWqrOBtq0sB9iogly72XaP3k5YqULC1ye5DMIRQF7LhUMeOHas9rpjlvFu3bhZV5GDGHEMVQ9mM+Y4SEqyuCAAAAGEgNvlyyRWjsvxXrS4F2Ktfi+P1YvvxumbZ2CZZdIhgCOGAftyon+Li4AYAAAA0QGzSKMWmXBeclgEIUf/Y1lWre1/R6NclGEK4CNmeQzW98cYbVpcAAAAAYD/EJJ5tDjEr2fWUuQ4vEIrOyxmun9rOVcSmeY1yvYQ+J6ndDf9TRFzjLZAENBV6DgEAAABoctEJpyu+5T3mMDMgFJX4I3RdzK1yxQQXRToQLY6/Rh3GfkowhLBBOAQAAACgWUTFDVFCq7/J5W5hdSlArabnp+rj7nfs/wVcbmVe+KSyRr8oV0TYDNQBCIcAAAAANJ/I6J5KTH9O7sjOVpcC1OrO7H7a0WNkg1/nio5X+5veV9opY5ukLqApEQ4BAAAAaFbuyEwlpj+tyJiBVpcC1Orc/FFSWpd6nx+ZmqVOd3+vpMPPbNK6gKZCOIS6ud3ScccFN2MfAAAAOEAud4Li0x5WdMIZVpcC7GGrJ1r3p90pRUTVeW5M+0PV+S8/Ka5z/2apDWgKfNJH3eLipKlTg5uxDwAAADQClytCcam3KDblhnBaSBkO8d+dWZrT+8Z9npPY9zR1mjBdUWntm60uoCkQDgEAAACwVEziCCWkPyVXRIbVpQDVjN5ynEo7H7fnEy6X0k6/U+1v+UgRcUlWlAY0KsIhAAAAAJaLjO6lxIyXFBlzhNWlAJUCculi77VyJe0OLiOSWqn9rZ8q87zH5HJHWFof0FgIh1C3oiIpPT24GfsAAABAE3C7k815iGKT/2R8BLe6HMC0rCRBL7cfb/YWij/oWHV5YL6S+p5qdVlAoyIcQv1s3x7cAAAAgCbkcrkUk3ShElo9IZc7zepyANPTOd1UMOoddbzrO0W1bGt1OUCjIxwCAAAAEHIiYw5VYsY/FRl7tNWlwOE6tojT1OuO1hEnnccwMtgWSwIAAAAACEnuiFQlpD2o8uKvVbrrWQUCBVaXBIe56PC2em7kIUqJq3tJeyCcEQ4BAAAACGnR8ScoMuYwlez6u7ylM60uBw7QMj5Kz444RBcezhAyOAPhEAAAAICQ545IU0LaQyov/kqlu56jFxGahMslXTagvf5veC+lJ8ZYXQ7QbAiHAAAAAISN6PgTq/QimmV1ObCRg1sn6YWzD9GxXZgIHc5DOIS6ud3SgAG79wEAAAALuSNaKSHtYXlKpqsk70UFfFusLglhLCE6QveedJBuHdJFkRF83oEzEQ6hbnFx0s8/W10FAAAAUE1U3GBFxg5SWcEklRW+IwVKrS4JYeasPq319FkHq0OLeKtLASxFOAQAAAAgbLlc0YpNvkTR8SerNP8leUqmWl0SwkCnlnF6ZsQhGt470+pSgJBAOAQAAAAg7LkjMxTf8h55y/6okl3Pyu9dbXVJCEFJMZHm8LHxw7opLirC6nKAkEE4hLoVF0u9ewf3ly6V4ulyCQAAgNAUGdNXiRkvylM8RaUFbyng22Z1SQiReYVuOKazbj++q9ISoq0uBwg5hEOoWyAgrVu3ex8AAAAIYS5XhKITTlVU/AkqL/pYZQUTFfDnWl0WLBAX5daYozvpzuO7KSOJpemBvSEcAgAAAGBLLleUYhJHKjr+NJUXfaSywv8SEjlETKRbVx/ZUXcN66as5FirywFCHuEQAAAAAFtzuWMVk3SeohP+GOxJZIZEO60uC00gOsKtKwa114QTuqtdapzV5QBhg3AIAAAAgINConMVnXiWPMXfqqzoffk9K60uC40gOTZSl/Rvp9uHdlXHlsyRCjQU4RAAAAAAxw03i0442dy8ZQtUVvievKUzJfmtLg0NdEhWkq47upMu7t9OiTF8vAX2F/97AAAAADh6dTNj83u3qKzwA5UXfyEFiqwuC3UMHTv70Cxdd3RHDe6SZnU5gC0QDqFuLtfupeyNfQAAAMBm3JFZiku9TrHJo+Up+U7lxV/JV77YWK7X6tLwuw4t4nT1kR30pyM6KpOVxwDnhEObNm3SzTffrG+//VZxcXE6//zz9cgjjyg2ltnmm1V8vLRkidVVAAAAAE3O5Y5XdMLp5ub3blZ58dfyFH8lv2+z1aU5UlSESycdlK6rjuyo4b0zFeHml9WAo8KhQCCgc845Ry1atNAPP/ygnTt36oorrlBERIQef/xxq8sDAAAAYHPuyDaKTb7U3Lxli+UpnqLykmlSoNDq0mzNCID+0C1N5/drqxGHtFbL+GirSwJsL2TDod9++02zZs1Sdna2MjMzzWMPPPCAbrvtNsIhAAAAIMQZ7+UnT55c63PJycm6++67zf1x48YpKipqj3MKCws1YcIEZWVlmY8nTpyohQsX1nq9fv366YILLlBTiozpY26xqTfIWzbfnMDaUzpLAd+2Jr2vk+YROr5bmhkGjTwkS+mJDBsDmlPIhkOtW7fWF198URkMVcjLy7OsJscqLpYGDgzu//xzcJgZAAAAsA9er1ePPfZYrc9Nnz69cn/EiBEaPHjwHuesXbtWPp+v8nGHDh00atSoOq/X1FyuaEXFDjK3ON0sX/kKeUpnmmGRz7OCOYoaICU2Uqf1ytSZB2fq1F4ZSo7dMyQE4PBwKDU1VSeffHLlY7/fr2effVbDhg2r9fyysjJzq5Cfn1/5OmNrzOFuLnNS5oDxoNGuW487m/c17t+Y7amNcf1q9/H55F66NPic8QO6ie/fXPZopw3RRvtwQjud0EantLOhbbTz1wKA/UVEdzc3JV8qvy9H3tKf5C1bKG/5QgV8OVaXF1JS46J0TKcWGtIlTcd2aakB7VMVFeG2uiwAoRwO1XTHHXdo7ty5+tnouVKLRx99VPfff/8ex3NyclRaWtpodRjdW1unt1KqSpVYtkPNxa8SdenYXgUFBdq2rWm7rhpv0o0eWsYbe7fbLVdxsTKrfD0DRfZY2rNmO+2INtqHE9rphDY6pZ0NbaPxsw0A7MAdka7ohOHmZvB7s82QyFu2SL7yRfJ7N8hJspJjdGznYBBkBEJ9WifJzYTSQEgKi3Dozjvv1FNPPaVJkyapT58+tZ5z1113aezYsdV6DrVv317p6enmmObGYryBzc7ZroBi5Y5JU3PJVb5Wr9ugpKQkZWRkNPmbeqOXkvG1M9/UVwmDjGNKSJAd7NFOG6KN9uGEdjqhjU5pZ0PbyCqkAOzKHdla0cYWf5L52O/Lla98sXyeVfJ5VstvbL5sWwxFM4aI9cxI1MGtk3RMp5Ya0jVN3VrZ43MD4AQhHw7deOONeuGFF/T222/r7LPP3ut5MTEx5laT8aa0Md98VwztklzGg0a7bj3uXDmkrTk+TFTcx7xXlfvVfBzuqrXTpmijfTihnU5oo1Pa2ZA22vnrAABVuSNayB13rKLijq08FvCXyOddYwZFPs8ac/P7tijg226OHwg1bVNi1Ssj0QyCemUmqVdmcD8rmaAfCGchHQ4Zw8RefPFFvfPOO+ay9gAAAABgJy53nCKje0vGVkUg4DMDIr9vm/y+reafAe/vf/qNobtFCviDm1R+QDVEul1KS4hWekK0WtWyZSTGmL2AjBAoKTakP0IC2E8h+z972bJlevDBB83hYsbqBcaS9lVXMgMAAAAAu3K5IuSKzJQ70pj985B9nhsIeIJBUaAwGBYFyn7vdRQwQ6YXzm6rEm+UjLmfI91uRbhdZiBkTBBthD/GnwCcLWTDoQ8//NBcuvKhhx4yt6qCw7rQbIzhcx077t4HAAAA6jH/2Pjx4/c611jF8vXGvKKffPLJHucYE9tPmDCh8vHKlStrPc/Qu3fvyus5kcsVJVdEqrEeWK3PH9W52UsCEGZCNhwyfpDs7YcJmll8vLR2rdVVAAAAIIwMGTLE3OryzDPP1Ot6o0ePboSqEM5mzZqlyZMn1/qcsQjR3Xffbe6PGzdOUVFRta48bQSOWVlZ5uOJEydq4cKFtV6vX79+uuCCC8z9DRs26PHHH1e88bmoBqNDg/FcBaNjg3Gf2owcOVKDBg0y9z///HNNmzat1vM6duyoMWPG1Poc4LhwCAAAAACACl6vV4899litz02fPr1yf8SIEbX2JFu7dq0Z5lTo0KGDRo0aVef1jNfccccdateu3T7PMwwdOnSvvdiqnmusQl2ftgDNheVBAAAAAAAAHIxwCHUrKZEGDgxuxj4AAAAAALANhpWhbn6/NGfO7n0AAAAAAGAb9BwCAAAAAABwMMIhAAAAAAAAByMcAgAAAAAAcDDmHAIAAAAAhDy/36/x48fX+lxsbGzlEvKTJk3SJ598ssc5eXl5mjBhQuXjlStX1nqeoXfv3tWWpH/00UfN5edrKikpqXbelClT9nrN4cOHV+7v2LFjr23Jysqqdk2gORAOAQAAAABC3pAhQ8ytLs8880y9rjd69Oh6ndepUyc999xz9Tr3gQceqNd5Z555prkBoYJwCPXTqpXVFQAAAAAAgCZAOIS6JSRIOTlWVwEAAAAAAJoAE1IDAAAAAAA4GD2HAACNKicnR/n5+Q1+XSAQUEFBgQoLC+VyuRr8+vLyckVHR6u5JScnKz09PWy+zuHaXgAAADQdwiHUraREOvXU4P7nn0txcVZXBCBEGYHFZX+6VnnFpQ1+rREIdenYXqvXbTCDoobwlJdpw7q16tC5iyIjo9ScUuJj9eYrLzZrYHIgX+dwbC8AAACaFuEQ6ub3S9Om7d4HgL0werIYgcXBZ49RambbBr46oBYqUWsZAXTDeg6tXzxHa157Qj3PvFqZHbuquezauklL3nvBbHdzhiUH9nUOv/YCAACgaREOAQAanRFYpLXr3LAXBQJKLNshd0ya0Y2oQS/Nzd5g/pmc3qbh93Xa1xkAAACogQmpAQAAAAAAHIxwCAAAAAAAwMEIhwAAAAAAAByMcAgAAAAAAMDBmJAa9RMfb3UFAAAAAACgCRAOoW4JCVJRkdVVAAAAAACAJsCwMgAAAAAAAAcjHAIAAAAAAHAwwiHUrbRUOv304GbsAwAAAAAA22DOIdTN55M++2z3PgAAAAAAsA16DgEAAAAAADgY4RAAAAAAAICDhXQ4VFpaqiuvvFKpqanKysrSk08+aXVJAAAAAAAAthLScw7dfvvtmjNnjr799lutW7dOl112mTp27KhzzjnH6tIAAAAAAABsIWTDoaKiIr3yyiv6/PPPdfjhh5vbkiVL9OyzzxIOAQAAAAAA2D0cWrBggTwej44++ujKY4MHD9bDDz8sv98vt7v6iLiysjJzq5CXl2f+uWvXLvP8xlJQUGDWtX3tcpUVF6q55G/borLSEjMgy8/Pb/L7Ge3csmWLue8qKVGX34+vXrBAgbg42UXVdtoVbbSPcGjnhg0bzO/FOeb3yIIGvjqgVJVqlzYZ33ka9MrcjWvN1+/YsFJueRXq35sP9O/ywL7OB9Zen89rttX4+bo3xs9d45zo6Og9fl7Xet3fv3aBQKBR6wUAAED9uAIh+k7svffe0/XXX6/s7OzKY8uWLVPv3r21bds2paenVzv/vvvu0/33329BpQAAoDEYoVe7du2sLgMAAMBxQrbnUHFxsWJiYqodq3hctYdQhbvuuktjx46t9lvLnTt3Ki0tTS5Xw34DXddvN9u3b2++gU1OTpYdOaGNTmknbbQPJ7TTCW10Sjsb2kbj91RGb6o2bdo0S30AAAAIk3AoNjZ2jxCo4nF8fPwe5xvBUc0wyVjlrKkYb3bt+qbeSW10Sjtpo304oZ1OaKNT2tmQNqakpDR5PQAAAAizpezbtm2r7du3y+vdPXeEMcQsLi6uSUMfAAAAAAAAJwnZcKhfv36KiorSrFmzKo9Nnz5dAwcOrNfklgAAAAAAAAjjYWXG0LHLLrtM1157rV5//XVt2rRJTzzxhLlvJWPo2r333rvHEDY7cUIbndJO2mgfTminE9rolHY6oY0AAAB2ErKrlVVMSj1mzBhz5TJjLoLbb79dt9xyi9VlAQAAAAAA2EZIh0MAAAAAAABoWkzeAwAAAAAA4GCEQwAAAAAAAA5GOAQAAAAAAOBghEP1VFpaqiuvvFKpqanKysrSk08+KTswVoE755xz1LJlS7Vt21Zjx44122pYs2aNTjjhBCUkJKh3796aMmWKwt3pp5+u0aNHVz6eN2+ejjjiCHN1vIEDB+qXX35RuCorK9P111+vFi1aKDMzU3fffbcqphSzSzs3bNig4cOHKzk5WZ06ddJTTz1V+Zwd2mj8Hfbp00dTp06tPFbX/8Ovv/7afI3R7j/84Q9avXq1wq2Ns2bN0tFHH63ExET16NFDr7zySli3cW/trJCXl2d+v33jjTeqHf/Pf/6jrl27mu0cMWKEtm/frnBr4/r163XaaaeZbejWrZv++9//hnUbAQAAnIJwqJ6MldLmzJmjb7/9Vs8//7zuv/9+vfvuuwpnRnBgBEPGqnA//PCD3nnnHX388ce65557zOfOOusstW7d2mz3JZdcYr6RN974hyujfZ999lnl46KiIvNDzLHHHmsGCcaHUyM8Mo6Ho5tvvllfffWVvvzyS02cOFEvv/yy/vnPf9qqneedd54ZIBjtePrppzVhwgR98MEHtmijEcpeeOGFWrJkSeWxuv4fGn8az19++eX6+eeflZ6ebj4O1XUGamtjdna2Tj31VA0dOtQM+IzvrTfeeKM+/fTTsGzj3tpZ1Z133qnNmzdXOzZ79mzzFxDG8u9GWJabm1styA6HNnq9XvP/XVRUlPl3afzcvPjii7V48eKwbCMAAICjGKuVYd8KCwsDsbGxge+++67y2IMPPhg47rjjAuFs2bJlxqerQHZ2duWxiRMnBtq0aRP45ptvAgkJCWbbKwwbNixw7733BsLRjh07Au3atQsMHDgwcNlll5nHXn311UDnzp0Dfr/ffGz82a1bt8Drr78eCMf2RUZGBqZOnVp57NFHHw1cfvnltmnnzp07zX+vixYtqjw2cuTIwPXXXx/2bVyyZEmgb9++gUMPPdRsY8X3mrr+H95zzz3Vvg8VFRUFkpKSqn2vCvU2vvDCC4GePXtWO/fqq68OjBo1KuzauK92Vvjhhx/Mf5utW7eu9u/zkksuqfzeZFi/fn3A5XIFVq9eHQiXNn744YeBlJSUQF5eXuW5Z555ZuCll14KuzYCAAA4DT2H6mHBggXyeDxmb4QKgwcP1k8//SS/369wZfRG+OKLL8whSDWHPBi/1T388MPNoSxV2zxz5kyFo9tuu83sdWEMy6lgtNFok8vlMh8bfx5zzDFh2cbp06crJSVFxx13XOWx8ePH67XXXrNNO+Pi4syhKK+//rr5//G3337TjBkzdNhhh4V9G6dNm6bjjz9+j3rr+n9oPD9kyJDK54yvj3F+KLZ7b2085ZRTzL/TmozvQ+HWxn21s2IY1lVXXaXnnntOMTEx1Z6r2c727durQ4cO5vFwaaMxvGzYsGHmsM8KkydP1tVXXx12bQQAAHAawqF62LJli1q1aqXo6OjKY0agYnSr37Fjh8KVMX/SySefXPnYCLqeffZZ88290eY2bdpUO99o88aNGxVujKGA33//vTlcrio7tdGYg8WYg+ett95Sz5491aVLFz344IPm36ld2hkbG2t+qH7ppZfMoMhopzEcyRimEu5tHDNmjP7+97+bwUdVdbUrnNq9tzYa/26PPPLIysfbtm0zh4Aa34fCrY37aqfhkUceMcPMk046aY/nwqmde2uj8X3ICHyMYNqYU6lv375mOBSObQQAAHCaSKsLCAfGnDw1f8tb8dj4TbBd3HHHHZo7d645r4fxxr+2Nodbe40A75prrjFDBSNQqM/fa7i10VBYWKgVK1aYwYnRC8P4EGa02/jwZqd2Llu2TGeccYbGjRtnzmNizE1jTNZspzZWVVe77NbukpISnX322WavRuPfr53auHTpUr344otauHBhrc/boZ3G9yFjku3zzz/fnL/uu+++M+e1M3oGDRgwwBZtBAAAsCvCoXr2WKj55rXicW2/HQ5HxgSpxspPkyZNMlefMdpcs1eU0eZwa68xua3xoaRqD6m6/l7DrY2GyMhI5efnmxNRd+zYsXIiX2Py9O7du9uind988425ipXRy8AI+oy/V2O1vYceesjsKWWHNtZU1//Dvf0bNnoFhhsjWDjzzDO1fPlyc5ikndpoTJ5tDCd74IEH9hjGa6fvR8b3obS0NL3wwgtyu93m8D9jsQNjYnzj/+v/t3c3QFJVd/64D+8v4kAiLw4sQVdjRHwBEVREkTW+shtXNLqbSoEupUJwNWWCBneNIVHJloC6RYxa7LKusWpZMVpGQkwUYRcQ5UVRIVoYI6IFupCF0eFFyNx/nfv7z2RmYBBWpqe7z/NUtT339u3ucz1M93c+99x7ymEfAQDKldPKDkAcHh+n240zsdSfYSf+gVpKf6A0JY6+mD59evjZz36WH7Wv3ee4j/XF5crKylBK4ukp8bSGOMNVvD322GP5Lf5cLvsYxTbHP7xqg6EoTgkep34vl/2Ms5DFoKv+CLB4is769evLZh8b+6z9Kpf9jsFmDHDjaLB4Gmjs51rlsI8xqF26dGk+4q32syiuGz9+fH5qZLnsZ2zrcccdlwdDjT+HymUfAQDKlXDoAAwcODCfmrf+RTPjke0hQ4Y0KIJLURxZE091iCHK3/zN39Stj9cAiaeYxdM86u9z/WuDlIJ4gdTXX389vPrqq/nta1/7Wn6LP8d9iX+w1U6JHe/jBY5LbR+j2OZ4Cl0cdVH/FKza67mUw37Ga5W8/fbb4dNPP61b9+abb4ajjz66bPaxsc/6PYz3cblWPG0nTiFeSvsdr4s1evTo/Ho18ULHAwYMaPB4OexjDEXiaZ+1n0PxFv89x5FEcTTcvvYzBirxVkr7GdsaA74//vGPe30Olcs+AgCUrZaeLq1UXH/99dmAAQOyl19+OXvyySezioqK7IknnshK2dq1a7M2bdpk//iP/5ht3LixwW3Pnj3ZCSeckF111VXZG2+8kU+L3qVLl2z9+vVZKYvTKNdOpRynW+7Ro0d244035lMzx/s4vXT9acNLyahRo7Izzzwze/XVV7Nf/epX+b7df//9ZbOfW7duzdsdp8N+6623sqeffjo74ogjsgcffLBs9jGqPzX4Z/0e/v73v886duyYr4+PX3nllfn04jU1NVmp7OPDDz+ctW7dOnvmmWcafAZt2bKlpPcx2tdU9rX69evXYCr7pUuXZu3bt89mzZqVrV69Ojv33HOzv/qrv8pKaR/j72Hv3r2z6667Llu3bl32k5/8JGvbtm22cuXKkt5HAIAUCIcOUHV1dTZmzJjssMMOy4vfe++9Nyt18Y+tWNjv6xbF4v6cc87JOnTokAdjv/nNb7JSVz8cil566aVs0KBB+R+fQ4cOzVatWpWVqhiexOAkhgc9e/bMpkyZUvcHdLnsZwx+vvrVr+bh7DHHHJP/HpbbPjYOFD7r9/CXv/xldtxxx2WdOnXKzjvvvOydd97JSmkfL7zwwn1+Bo0YMaKk9/Fgw6EoLvft2zf/nrnsssuyzZs3Z6W2j/F3tPbfa+yzxgdRSnEfAQBS0Cr+p6VHLwEAAADQMkr7gjkAAAAAfC7CIQAAAICECYcAAAAAEiYcAgAAAEiYcAgAAAAgYcIhAAAAgIQJhwAAAAASJhwCmtSqVav89t577+312IMPPpg/9oMf/CBfXrhwYb5cX01NTbj//vvDKaecEjp37hz69esXbrzxxvCHP/xhv++bZVk499xzw5tvvnnI9uWOO+4Id99990E/76ijjgr/9m//ts/HYvtiO2N7AQAASpVwCNivdu3ahaeffnqv9U8++eReYVBjX//618O9994bbrvttvDGG2+ERx55JCxdujRcdNFFYefOnU0+L24Xg6Tjjz8+HCrPP/98OO+888KhFNsX2xnbCwAAUKqEQ8B+nXPOOXuFQ1VVVeHFF18MgwYNavJ5jz32WHjmmWfyUOaqq64Kf/7nf56Pspk3b15Ys2ZNePTRR/f5vDgK58477wwTJkw4ZPvwySefhLfeeiucdtpp4VD71re+Fe666y6jhwAAgJIlHAL269JLLw2LFi3KA6FaMeA5++yzw+GHH97k8+KpWJdddlk45phjGqzv1atXWLBgQbj88sv3+bxf//rXYfv27eH000+vWxdHKMXT1uq/djzdq9Y777wTvvrVr+anrp100klh2rRpDR7/r//6rzBs2LDQpk2b/DS4MWPGhL//+78PXbp0ybeL7zlz5sy8bT169Aj//M//fMD/f4YOHZqHT7/5zW8O+DkAAADFRDgE7FcMW/r06RN+9atfNTil7K//+q/3+7zVq1eHIUOG7POxGPx88Ytf3Odj8X3i6V+fdcparT179oS//Mu/DN26dQsrVqwIkydPDlOmTNnvKWVz5szJt49tjG258sorw7PPPpsHUPGaSN/5znfC//zP/xzQ+8d2xteu//8HAACglAiHgAMaPVR7atmuXbvykTZx3f5s3bo1dO3a9aDfa9WqVaF///4HvH0chbRhw4bwr//6r+GEE04I3/jGN8INN9yw33Coe/fu4Yc//GE+qunqq68O27Ztyy+cHd/3u9/9bh44vf322wfchvi+sd0AAAClSDgEfKYYBM2fPz8PTWLQEkcT9ezZc7/POeKII8L//u//HvR7xRE7Mbw5UK+99lo47rjjQkVFRd26M888s+7nzZs3h48++igMGDCgbt3RRx9dNzKpU6dO+X3taWi1yzEEO1BxX+N7AAAAlCLhEPCZhg8fnt8vXrw4PPXUU/m1hD7L4MGDw8qVK/f5WJy9LI7U2ZcY2vzxj3/ca/2nn366z5/btm2718Wg6y/HkUUjR45s8Hh8TmOtW//fPw5ramo+1/MBAABakr9mgM8Uw5RRo0blp5b94he/OKBw6Jvf/GYeJMWLRdf3wQcfhJ/85CehXbt2+3xevCj0li1b9lr/3nvv1f0cTyOrFUcErVu3Lnz88cd16+qHUs0xhX1jcXTSkUce2azvAQAA0FyEQ8ABn1o2a9asPLyJp2V9ljh9fZy6PgYzjz/+ePj973+fn5p24YUX5tf2+bu/+7t9Pm/QoEH5qWKN/ehHPwqvvPJKHvb89Kc/rVsfX79v377h2muvDb/97W/D3Llz81FJtaeNHYpw6PXXX88vOF3/Vj/Aiu099dRTP9d7AAAAtBThEHBAYqize/fuz5ylrFYMZ+LIobFjx4Z/+Id/yC/aPGHChHD++efnIVHHjh33+byLLrooLFmyZK9TxUaMGJGPXoojkuJFp2vF07l+/vOf5yOSBg4cmIdI11xzTWjfvn1Yv359vk2/fv0+177PmDEjXHzxxQ1uMaiKYjuXLl2arwMAAChFrbLGf4EBtKB4vaF4genZs2eHc845py5oeuGFF/KRSI3FC0HHoCaGV7XuueeeMG/evHxq+ua2aNGifNTSm2++6bpDAABASfKXDFBU2rRpE773ve+Fhx566ICf87WvfS0/1SyOFHruuefCfffdF77+9a+HQojtvOWWWwRDAABAyfLXDFB0xo0blwc98RpCn6Vnz57hP//zP/Nw6Ctf+Ur+3BtuuCF861vfavZ2xvbFC2XH9wQAAChVTisDAAAASJiRQwAAAAAJEw4BAAAAJEw4BAAAAJAw4RAAAABAwoRDAAAAAAkTDgEAAAAkTDgEAAAAkDDhEAAAAEDChEMAAAAACRMOAQAAACRMOAQAAACQMOEQAAAAQMKEQwAAAAAJEw4BAAAAJEw4BAAAAJAw4RAAAABAwoRDAAAAAAkTDgEAAAAkTDgEAAAAkDDhEAAAAEDChEMAAAAACWuRcGjXrl3hxBNPDAsXLmxym1deeSWcfvrpoXPnzmHIkCFh5cqVBW0jAEAxUT8BAGUTDu3cuTP87d/+bVizZk2T21RXV4dLLrkknH322XlRM2zYsDBq1Kh8PQBAatRPAEDZhENr164NZ5xxRvjd73633+3mzJkTOnXqFO65557Qv3//cN9994XDDz88PP744wVrKwBAMVA/AQDNrW0ooEWLFoWRI0eGu+66Kxx22GFNbrds2bIwfPjw0KpVq3w53p911lnhxRdfDFdffXWTQ63jrVZNTU34wx/+EI444oi61wEAik+WZeHjjz8OvXv3Dq1buxxiY+onAKC566eChkMTJkw4oO02btwYBgwY0GBdr169whtvvNHkc6ZOnRqmTJnyudsIALSMDRs2hD/7sz9r6WYUHfUTANDc9VNBw6EDtX379tChQ4cG6+Jy/SNbjU2ePDncfPPNdcvbtm0LX/rSl/L/URUVFc3aXgDg/66qqir07ds3PwWK/zv1EwCko+oQ109FGQ517Nhxr0ImLseZN5oSi5/GBVEUCxvFDQAUP6cxfT7qJwBIT6tDVD8V5Yn9ffr0CZs2bWqwLi5XVla2WJsAAIqZ+gkAKKtwKM7IsXTp0vwCS1G8X7JkSb4eAIC9qZ8AgJIPh+KRrR07duQ/X3HFFWHr1q3h29/+dj59a7yvrq4OV155ZUs3EwCgaKifAICyCofikOc5c+bkP8dz3J955pnw3//932Hw4MH51Ky//OUv9zt9KwBAatRPAEBJX5C6dshzU8tDhw4Nq1atKnCrAACKl/oJACjrkUMAAAAAFJ5wCAAAACBhwiEAAACAhAmHAAAAABImHAIAAABImHAIAAAAIGHCIQAAAICECYcAAAAAEiYcAgAAAEiYcAgAAAAgYcIhAAAAgIQJhwAAAAASJhwCAAAASJhwCAAAACBhwiEAAACAhAmHAAAAABImHAIAAABImHAIAAAAIGHCIQAAAICECYcAAAAAEiYcAgAAAEiYcAgAAAAgYcIhAAAAgIQJhwAAAAASJhwCAAAASJhwCAAAACBhwiEAAACAhAmHAAAAABImHAIAAABImHAIAAAAIGHCIQAAAICECYcAAAAAEiYcAgAAAEiYcAgAAAAgYcIhAAAAgIQJhwAAAAASJhwCAAAASJhwCAAAACBhwiEAAACAhAmHAAAAABImHAIAAABImHAIAAAAIGHCIQAAAICECYcAAAAAEiYcAgAAAEiYcAgAAAAgYcIhAAAAgIQJhwAAAAASJhwCAAAASJhwCAAAACBhwiEAAACAhAmHAAAAABImHAIAAABImHAIAAAAIGHCIQAAAICECYcAAAAAEiYcAgAAAEiYcAgAAAAgYQUNh3bu3BnGjRsXunXrFiorK8P06dOb3PbJJ58M/fv3D126dAnDhw8Pq1atKmRTAQCKgvoJACircGjSpElhxYoVYcGCBeGBBx4IU6ZMCXPnzt1ruzVr1oRvfOMbYfLkyWH16tVh4MCBYdSoUWH79u2FbC4AQItTPwEAza1VlmVZs79LCKG6ujp07949zJ8/P5x77rn5ujvvvDM899xzYeHChQ22vffee8Njjz2WF0LRxx9/HCoqKsLy5cvDaaeddkDvV1VVFbp27Rq2bduWPxcAKE6+s5umfgIACvGdXbCRQ/EI1u7du8OwYcPq1sXhzi+99FKoqalpsO0RRxyRH/1asmRJ/tjs2bPznT3mmGOafP1du3bl/3Pq3wAASpn6CQAohLYFeZcQwsaNG/MjX+3bt69b16tXr/w8+i1btoQePXrUrb/qqqvC008/nRc/bdq0Ca1btw7z5s0LX/jCF5p8/alTp+bDrAEAyoX6CQAohIKNHIrnu3fo0KHButrleNSqvljsbNq0KcycOTM/MjZmzJhwzTXXhI8++qjJ14/n18fhVLW3DRs2NNOeAAAUhvoJACircKhjx457FTG1y507d26w/tZbbw0nnXRSmDhxYhg8eHB4+OGHw2GHHZYPj25KLJTi0On6NwCAUqZ+AgDKKhzq06dP2Lx5c9izZ0/dunh0q1OnTvnUrPWtXLkynHLKKX9qZOvW+fL69esL1VwAgBanfgIAyiocitOptmvXLixbtqxu3eLFi8OQIUPy4qW+3r17h7Vr1zZY99Zbb4Wjjz66UM0FAGhx6icAoKwuSB2HPo8dOzaMHz8+H978wQcfhGnTptUNdY5HweI0bPFI2LXXXhuuvvrqvPA588wzw6xZs/KjXvH5AACpUD8BAGUVDkUzZswIEyZMCCNHjswLmTg7xujRo/PHKisr80InFjVxto1PPvkk3H333eH999/Pj5otWLAg9OzZs5DNBQBoceonAKC5tcqyLAtlqKqqKi+g4swbLq4IAMXLd3bx0BcAkOZ3dsGuOQQAAABA8REOAQAAACRMOAQAAACQMOEQAAAAQMKEQwAAAAAJEw4BAAAAJEw4BAAAAJAw4RAAAABAwoRDAAAAAAkTDgEAAAAkTDgEAAAAkDDhEAAAAEDChEMAAAAACRMOAQAAACRMOAQAAACQMOEQAAAAQMKEQwAAAAAJEw4BAAAAJEw4BAAAAJAw4RAAAABAwoRDAAAAAAkTDgEAAAAkTDgEAAAAkDDhEAAAAEDChEMAAAAACRMOAQAAACRMOAQAAACQMOEQAAAAQMKEQwAAAAAJEw4BAAAAJEw4BAAAAJAw4RAAAABAwoRDAAAAAAkTDgEAAAAkTDgEAAAAkDDhEAAAAEDChEMAAAAACRMOAQAAACRMOAQAAACQMOEQAAAAQMKEQwAAAAAJEw4BAAAAJEw4BAAAAJAw4RAAAABAwoRDAAAAAAkTDgEAAAAkTDgEAAAAkDDhEAAAAEDChEMAAAAACRMOAQAAACRMOAQAAACQMOEQAAAAQMKEQwAAAAAJEw4BAAAAJEw4BAAAAJAw4RAAAABAwoRDAAAAAAkTDgEAAAAkrKDh0M6dO8O4ceNCt27dQmVlZZg+fXqT277++uth+PDhoVOnTuGkk04KL7zwQiGbCgBQFNRPAEBZhUOTJk0KK1asCAsWLAgPPPBAmDJlSpg7d+5e223bti2cf/754YQTTsiLnNGjR4fLLrssfPTRR4VsLgBAi1M/AQBlEw5VV1eHWbNmhfvvvz+ceuqpebFyyy23hJkzZ+617SOPPBK6dOkSfvrTn4Zjjz02L4K+/OUv54URAEAq1E8AQCG0Lci7hBBWr14ddu/eHYYNG1a3Lg57vuuuu0JNTU1o3fpPOdXChQvDpZdeGtq0aVO3bvny5ft9/V27duW3WlVVVYd8HwAACkn9BACU1cihjRs3hu7du4f27dvXrevVq1d+Hv2WLVsabPvOO++EHj16hOuuuy4ceeSR4YwzzghLlizZ7+tPnTo1dO3ate7Wt2/fZtsXAIBCUD8BAGUVDm3fvj106NChwbra5fpHrKJPPvkk/PjHP84vujh//vwwYsSIcMEFF4QNGzY0+fqTJ0/Oz7Wvve1vWwCAUqB+AgDK6rSyjh077lXE1C537ty5YaPatg2DBg3Kz5WP4s+//vWvw6OPPhpuu+22fb5+LJQaF08AAKVM/QQAlNXIoT59+oTNmzeHPXv21K3btGlTPtVqnJq1vnjE6/jjj2+w7rjjjnM0CwBIivoJACircGjgwIGhXbt2YdmyZXXrFi9eHIYMGdLgYopRPEc+XoCxvjfffDMcddRRhWouAECLUz8BAGUVDsWhz2PHjg3jx4/PZ8546qmnwrRp08JNN91UdxRsx44d+c9xm9deey384Ac/CG+//Xb4/ve/n19k8Zvf/GahmgsA0OLUTwBAWYVD0YwZM8LgwYPDyJEjw8SJE/Nz4kePHl03FHrOnDn5z/369QvPPvts+MUvfhFOPPHE/H7evHn50GoAgJSonwCA5tYqy7IslKGqqqp8StY480ZFRUVLNwcAaILv7OKhLwAgze/sgo4cAgAAAKC4CIcAAAAAEiYcAgAAAEiYcAgAAAAgYcIhAAAAgIQJhwAAAAASJhwCAAAASJhwCAAAACBhwiEAAACAhAmHAAAAABImHAIAAABImHAIAAAAIGHCIQAAAICECYcAAAAAEiYcAgAAAEiYcAgAAAAgYcIhAAAAgIQJhwAAAAASJhwCAAAASJhwCAAAACBhwiEAAACAhAmHAAAAABImHAIAAABImHAIAAAAIGHCIQAAAICECYcAAAAAEiYcAgAAAEiYcAgAAAAgYcIhAAAAgIQJhwAAAAASJhwCAAAASJhwCAAAACBhwiEAAACAhAmHAAAAABImHAIAAABImHAIAAAAIGHCIQAAAICECYcAAAAAEiYcAgAAAEiYcAgAAAAgYcIhAAAAgIQJhwAAAAASJhwCAAAASJhwCAAAACBhwiEAAACAhAmHAAAAABImHAIAAABImHAIAAAAIGHCIQAAAICECYcAAAAAEiYcAgAAAEiYcAgAAAAgYcIhAAAAgIQJhwAAAAASJhwCAAAASJhwCAAAACBhwiEAAACAhAmHAAAAABJW0HBo586dYdy4caFbt26hsrIyTJ8+/TOf8+6774YuXbqEhQsXFqSNAADFRP0EADS3tqGAJk2aFFasWBEWLFgQ1q9fH8aOHRv69esXrrjiiiafM2HChFBdXV3IZgIAFA31EwBQNuFQLFBmzZoV5s+fH0499dT8tmbNmjBz5swmi5vHHnssfPzxx4VqIgBAUVE/AQBldVrZ6tWrw+7du8OwYcPq1g0fPjy89NJLoaamZq/tt2zZEm655Zbw0EMPHdDr79q1K1RVVTW4AQCUMvUTAFBW4dDGjRtD9+7dQ/v27evW9erVKz+PPhYyjd188835sOkBAwYc0OtPnTo1dO3ate7Wt2/fQ9p+AIBCUz8BAGUVDm3fvj106NChwbra5XjUqr7nnnsuLF68ONx+++0H/PqTJ08O27Ztq7tt2LDhELUcAKBlqJ8AgLK65lDHjh33KmJqlzt37ly3bseOHeH6668PDzzwQOjUqdMBv34slBoXTwAApUz9BACU1cihPn36hM2bN4c9e/bUrdu0aVNewMSpWWu9/PLL4Z133gmXX355PgVrvEUXX3xxGD9+fKGaCwDQ4tRPAEBZjRwaOHBgaNeuXVi2bFl+IcUoDn0eMmRIaN36TxnV0KFDw7p16xo898tf/nI+U8f5559fqOYCALQ49RMAUFbhUBz6HC+QGI9ezZ49O3zwwQdh2rRp+c+1R8HihRDjkbBjjz12n0fOevbsWajmAgC0OPUTAFBWp5VFM2bMCIMHDw4jR44MEydODFOmTAmjR4/OH6usrAxz5swpZHMAAIqe+gkAaG6tsizLQhmqqqrKj6TFmTcqKipaujkAQBN8ZxcPfQEAaX5nF3TkEAAAAADFRTgEAAAAkDDhEAAAAEDChEMAAAAACRMOAQAAACRMOAQAAACQMOEQAAAAQMKEQwAAAAAJEw4BAAAAJEw4BAAAAJAw4RAAAABAwoRDAAAAAAkTDgEAAAAkTDgEAAAAkDDhEAAAAEDChEMAAAAACRMOAQAAACRMOAQAAACQMOEQAAAAQMKEQwAAAAAJEw4BAAAAJEw4BAAAAJAw4RAAAABAwoRDAAAAAAkTDgEAAAAkTDgEAAAAkDDhEAAAAEDChEMAAAAACRMOAQAAACRMOAQAAACQMOEQAAAAQMKEQwAAAAAJEw4BAAAAJEw4BAAAAJAw4RAAAABAwoRDAAAAAAkTDgEAAAAkTDgEAAAAkDDhEAAAAEDChEMAAAAACRMOAQAAACRMOAQAAACQMOEQAAAAQMKEQwAAAAAJEw4BAAAAJEw4BAAAAJAw4RAAAABAwoRDAAAAAAkTDgEAAAAkTDgEAAAAkDDhEAAAAEDChEMAAAAACRMOAQAAACRMOAQAAACQMOEQAAAAQMKEQwAAAAAJEw4BAAAAJEw4BAAAAJCwgoZDO3fuDOPGjQvdunULlZWVYfr06U1uO2/evDBw4MDQpUuXcPLJJ4enn366kE0FACgK6icAoLm1DQU0adKksGLFirBgwYKwfv36MHbs2NCvX79wxRVXNNjutddeC6NHjw733HNPuOSSS8Kzzz6bb7N8+fJwyimnFLLJAAAtSv0EADS3VlmWZc3+LiGE6urq0L179zB//vxw7rnn5uvuvPPO8Nxzz4WFCxc22PZ73/teWL16db5trQsvvDCcdtpp4a677jqg96uqqgpdu3YN27ZtCxUVFYd4bwCAQ8V3dtPUTwBAIb6zCzZyKBYru3fvDsOGDatbN3z48LxYqampCa1b/+kMt3hE7NNPP93rNeJON2XXrl35rf7/KACAUqZ+AgDK6ppDGzduzI98tW/fvm5dr1698vPot2zZ0mDb/v37Nxj+vGbNmvD888+H8847r8nXnzp1ap6a1d769u3bTHsCAFAY6icAoKzCoe3bt4cOHTo0WFe7XP+IVWObN28Ol19+eTjrrLPCpZde2uR2kydPzo+M1d42bNhwCFsPAFB46icAoBAKdlpZx44d9ypiapc7d+68z+d8+OGH4fzzz8+HTc+dO7fB0OnGYqHUuHgCAChl6icAoKxGDvXp0yc/irVnz566dZs2bQqdOnXKp2Zt7IMPPgjnnHNOXgDFCy726NGjUE0FACgK6icAoKzCoYEDB4Z27dqFZcuW1a1bvHhxGDJkyF5HtOLMHBdddFG+ftGiRaF3796FaiYAQNFQPwEAZXVaWRz6HGfRGD9+fJg9e3Z+ZGvatGn5z7VHweKFEOORsLvvvjv87ne/q5uiNT4WxcfiNgAAKVA/AQBlNXIomjFjRhg8eHAYOXJkmDhxYpgyZUoYPXp0/lhlZWWYM2dO/vMTTzwRduzYEU4//fR8fe3tpptuKmRzAQBanPoJAGhurbIsy0IZqqqqyo+SxZk3KioqWro5AEATfGcXD30BAGl+Zxd05BAAAAAAxUU4BAAAAJAw4RAAAABAwoRDAAAAAAkTDgEAAAAkTDgEAAAAkDDhEAAAAEDChEMAAAAACRMOAQAAACRMOAQAAACQMOEQAAAAQMKEQwAAAAAJEw4BAAAAJEw4BAAAAJAw4RAAAABAwoRDAAAAAAkTDgEAAAAkTDgEAAAAkDDhEAAAAEDChEMAAAAACRMOAQAAACRMOAQAAACQMOEQAAAAQMKEQwAAAAAJEw4BAAAAJEw4BAAAAJAw4RAAAABAwoRDAAAAAAkTDgEAAAAkTDgEAAAAkDDhEAAAAEDChEMAAAAACRMOAQAAACRMOAQAAACQMOEQAAAAQMKEQwAAAAAJEw4BAAAAJEw4BAAAAJAw4RAAAABAwoRDAAAAAAkTDgEAAAAkTDgEAAAAkDDhEAAAAEDChEMAAAAACRMOAQAAACRMOAQAAACQMOEQAAAAQMKEQwAAAAAJEw4BAAAAJEw4BAAAAJAw4RAAAABAwoRDAAAAAAkTDgEAAAAkTDgEAAAAkDDhEAAAAEDChEMAAAAACRMOAQAAACRMOAQAAACQsIKGQzt37gzjxo0L3bp1C5WVlWH69OlNbvvKK6+E008/PXTu3DkMGTIkrFy5spBNBQAoCuonAKCswqFJkyaFFStWhAULFoQHHnggTJkyJcydO3ev7aqrq8Mll1wSzj777LyoGTZsWBg1alS+HgAgJeonAKBswqFYmMyaNSvcf//94dRTTw2XXXZZuOWWW8LMmTP32nbOnDmhU6dO4Z577gn9+/cP9913Xzj88MPD448/XqjmAgC0OPUTAFAIbQvyLiGE1atXh927d+dHsWoNHz483HXXXaGmpia0bv2nnGrZsmX5Y61atcqX4/1ZZ50VXnzxxXD11Vfv8/V37dqV32pt27Ytv6+qqmrGvQIAPq/a7+osy1q6KUVH/QQAFKJ+Klg4tHHjxtC9e/fQvn37unW9evXKz6PfsmVL6NGjR4NtBwwY0OD5cds33nijydefOnVqPsy6sb59+x6yfQAAmk+sB7p27drSzSgq6icAoBD1U8HCoe3bt4cOHTo0WFe7XP+I1f62bbxdfZMnTw4333xz3fLWrVtDv379wnvvvafQbOE0MxaYGzZsCBUVFS3dnKTpi+KhL4qDfigecbTKl770pfDFL36xpZtSdNRPafL5VDz0RfHQF8VBP5Rv/VSwcKhjx457FSe1y3FGjQPZtvF2jYufxgVRFAsb/2hbXuwD/VAc9EXx0BfFQT8Uj/qnSPH/qJ/S5vOpeOiL4qEvioN+KL/6qWBVWJ8+fcLmzZvDnj176tZt2rQpv3BinJq18bbxsfricpy+FQAgFeonAKAQChYODRw4MLRr1y6/WGKtxYsXhyFDhuyVdJ1xxhlh6dKldRdWivdLlizJ1wMApEL9BACUVTgUhzSPHTs2jB8/Pixfvjw89dRTYdq0aeGmm26qO7K1Y8eO/OcrrrgiP+f929/+dli7dm1+H6dyvfLKKw/4/eIQ6TvuuGOfQ6UpHP1QPPRF8dAXxUE/FA990TT1U5r0Q/HQF8VDXxQH/VC+fdEqK+C8sfFCiRMmTAhPPPFEfi77pEmT8sIlb0irVmH27Nl1U62+/PLLeSH029/+Npx88snhwQcfDIMGDSpUUwEAioL6CQBobgUNhwAAAAAoLqYFAQAAAEiYcAgAAAAgYcIhAAAAgISVdDi0c+fOMG7cuNCtW7dQWVkZpk+f3uS2r7zySjj99NPzWT/i9K8rV64saFvL2cH0w7x58/Jpebt06ZJfKPPpp58uaFvL3cH0Ra13330374+FCxcWpI2pOJi+eP3118Pw4cNDp06dwkknnRReeOGFgra1nB1MPzz55JOhf//++e9D7I9Vq1YVtK2p2LVrVzjxxBP3+5njO7t5qZ+Kg/qpeKifiof6qTionxKtn7ISdsMNN2Qnn3xytnLlyuznP/95dvjhh2ePP/74Xtt98skn2ZFHHpl95zvfydauXZvdeOONWa9evfL1FK4fVq9enbVv3z67//77s3Xr1mUzZ87M2rVrl7366qst0u6U+6K+iy66KF6UPnvhhRcK1s4UHGhfbN26Nf88uvbaa/Pfi+9///tZ165dsw8//LBF2p1qP7zxxhtZx44ds0ceeSR7++23s4kTJ+bfG9XV1S3S7nK1Y8eO7LLLLtvvZ47v7OanfioO6qfioX4qHuqn4qB+SrN+KtlwKO5k/IdY/3/Oj370o2zEiBF7bfsv//Iv2dFHH53V1NTky/H+2GOPzWbPnl3QNpejg+mHW2+9Nf8ire+CCy7IbrvttoK0tdwdTF/U+tnPfpadddZZipsW7ItY7B9zzDHZnj176taddtpp2bx58wrW3nJ1MP0wY8aMbPDgwXXLVVVV+e/F8uXLC9becrdmzZrslFNOyYvN/X3m+M5uXuqn4qB+Kh7qp+KhfioO6qd066eSPa1s9erVYffu3WHYsGF16+IwtpdeeinU1NQ02HbZsmX5Y61atcqX4/1ZZ50VXnzxxYK3u9wcTD+MHTs2/PjHP97rNbZt21aQtpa7g+mLaMuWLeGWW24JDz30UIFbWv4Opi/i0NBLL700tGnTpm7d8uXLwyWXXFLQNqfeD0cccURYs2ZNWLJkSf7Y7NmzQ0VFRTjmmGNaoOXladGiRWHkyJGf+d3rO7t5qZ+Kg/qpeKifiof6qTion9Ktn0o2HNq4cWPo3r17aN++fd26Xr165edHxg/txtv27t27wbq47fvvv1+w9parg+mHeC7qKaecUrccP0ief/75cN555xW0zeXqYPoiuvnmm/OCc8CAAQVuafk7mL545513Qo8ePcJ1110XjjzyyHDGGWfkX7AUth+uuuqqMGrUqPxLNW7/3e9+N8ydOzd84QtfaIGWl6cJEyaEe++9Nz8Pfn98Zzcv9VNxUD8VD/VT8VA/FQf1U7r1U8mGQ9u3bw8dOnRosK52OV6s6UC2bbwdzdsP9W3evDlcfvnleZoZU38K2xfPPfdcWLx4cbj99tsL2sZUHExffPLJJ/kR4Xixv/nz54cRI0aECy64IGzYsKGgbU69H2Kxs2nTpjBz5sz8yNiYMWPCNddcEz766KOCthnf2c1N/VQc1E/FQ/1UPNRPxUH9VJoOxXd2yYZDHTt23GtHa5cbp2pNbftZ6RuHth9qffjhh+Ev/uIv8qGHMVlu3bpk/xmWZF/s2LEjXH/99eGBBx7IZ3egZX8v2rZtGwYNGhSmTJmS3//TP/1TOO6448Kjjz5a0Dan3g+33nprPtPJxIkTw+DBg8PDDz8cDjvssHx4NIXlO7t5qZ+Kg/qpeKifiof6qTion0rTofjOLtlvlT59+uRHT/bs2VO3LqaW8cM6TrnXeNv4WH1xOSbNFK4fog8++CCcc845+T/UeK5wHA5KYfvi5ZdfzofixiOPccrJeIsuvvjiMH78+BZpe8q/F/Fz6Pjjj2+wLhY3jnwVth/iVJ/1T9uIf3TF5fXr1xe0zfjObm7qp+Kgfioe6qfioX4qDuqn0nQovrNLNhwaOHBgaNeuXX7hpVpxmOeQIUP2OpISz0FdunRpnJktX4738ZzUuJ7C9UN1dXW46KKL8vXxwlqNz4mkMH0xdOjQsG7duvDqq6/W3aJZs2aFH/7why3S9nJzsJ9P8cJ/9b355pvhqKOOKlh7y9XB9EP8PFq7dm2DdW+99VY4+uijC9Ze/h/f2c1L/VQc1E/FQ/1UPNRPxUH9VJoOyXd2VsKuv/76bMCAAdnLL7+cPfnkk1lFRUX2xBNP5I9t3Lgx2759e/7ztm3bsh49emQ33nhjPhVcvD/yyCPzafooXD/EKVc7deqUvfTSS/n62tvWrVtbeA/S64vGTMXacn3x7rvvZocddlh2xx13ZOvWrctuv/32rEuXLtn777/fwnuQVj/8x3/8Rz5t67//+7/n/RCnju7atWv24YcftvAelKfGnzm+swtL/VQc1E/FQ/1UPNRPxUH9lGb9VNLhUHV1dTZmzJj8g6F3797Zvffe2+B/3OzZs+uW4xfqoEGD8n+8Q4cOzVatWtVCrS4/B9oPX/nKV/LlxrexY8e2YOvLy8H8TtSnuGnZvli8eHF26qmnZh06dMgGDhyYLVq0qIVanXY/zJo1Kzv++OPz4nL48OHZypUrW6jV5a/xZ47v7MJSPxUH9VPxUD8VD/VTcVA/pVk/tfr/XxQAAACABJXsNYcAAAAA+PyEQwAAAAAJEw4BAAAAJEw4BAAAAJAw4RAAAABAwoRDAAAAAAkTDgEAAAAkTDgEAAAAkDDhEAAAAEDChEMAAAAACRMOAQAAAIR0/X+6bHiqxfUw6wAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 1400x1000 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# 设置绘图风格（Nature期刊标准）\n", "plt.style.use('default')\n", "plt.rcParams['font.family'] = 'Arial'\n", "plt.rcParams['font.size'] = 10\n", "\n", "# 创建图表\n", "fig, axes = plt.subplots(2, 2, figsize=(14, 10))\n", "fig.suptitle('神农框架示例数据分析', fontsize=16, fontweight='bold')\n", "\n", "# 1. MIC活性分布直方图\n", "axes[0, 0].hist(df['activity_mic'], bins=15, alpha=0.7, color='#56B4E9', edgecolor='black')\n", "axes[0, 0].set_xlabel('MIC (μg/mL)')\n", "axes[0, 0].set_ylabel('频次')\n", "axes[0, 0].set_title('抗菌活性分布')\n", "axes[0, 0].grid(True, alpha=0.3)\n", "axes[0, 0].axvline(df['activity_mic'].mean(), color='red', linestyle='--', label=f'平均值={df[\"activity_mic\"].mean():.1f}')\n", "axes[0, 0].legend()\n", "\n", "# 2. 机制分布饼图\n", "mechanism_counts = df['mechanism'].value_counts()\n", "colors = ['#E69F00', '#56B4E9', '#009E73', '#F0E442', '#0072B2', '#D55E00', '#CC79A7']\n", "axes[0, 1].pie(mechanism_counts.values, labels=mechanism_counts.index, autopct='%1.1f%%', \n", "               colors=colors[:len(mechanism_counts)])\n", "axes[0, 1].set_title('抗菌机制分布')\n", "\n", "# 3. 预测vs实际散点图\n", "if len(y_test) > 0:\n", "    axes[1, 0].scatter(y_test, y_test_pred, alpha=0.7, color='#D55E00', s=60)\n", "    min_val = min(y_test.min(), y_test_pred.min())\n", "    max_val = max(y_test.max(), y_test_pred.max())\n", "    axes[1, 0].plot([min_val, max_val], [min_val, max_val], 'k--', lw=2, label='完美预测线')\n", "    axes[1, 0].set_xlabel('实际MIC (μg/mL)')\n", "    axes[1, 0].set_ylabel('预测MIC (μg/mL)')\n", "    axes[1, 0].set_title(f'预测效果 (R² = {test_r2:.3f})')\n", "    axes[1, 0].grid(True, alpha=0.3)\n", "    axes[1, 0].legend()\n", "\n", "# 4. 特征重要性条形图\n", "feature_importance_sorted = sorted(zip(feature_names, rf_model.feature_importances_), \n", "                                   key=lambda x: x[1], reverse=True)\n", "features, importances = zip(*feature_importance_sorted)\n", "\n", "y_pos = np.arange(len(features))\n", "bars = axes[1, 1].barh(y_pos, importances, color='#009E73', alpha=0.7)\n", "axes[1, 1].set_yticks(y_pos)\n", "axes[1, 1].set_yticklabels(features)\n", "axes[1, 1].set_xlabel('重要性得分')\n", "axes[1, 1].set_title('分子描述符重要性')\n", "axes[1, 1].grid(True, alpha=0.3, axis='x')\n", "\n", "# 添加数值标签\n", "for i, bar in enumerate(bars):\n", "    width = bar.get_width()\n", "    axes[1, 1].text(width + 0.001, bar.get_y() + bar.get_height()/2, \n", "                    f'{width:.3f}', ha='left', va='center', fontsize=8)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"📊 可视化完成！\")\n", "print(\"\\\\n🎨 图表说明:\")\n", "print(\"  1. 左上：MIC活性分布 - 展示化合物抗菌强度分布\")\n", "print(\"  2. 右上：机制分布 - 显示不同抗菌机制的比例\")\n", "print(\"  3. 左下：预测效果 - 模型预测值vs实际值的对比\")\n", "print(\"  4. 右下：特征重要性 - 影响抗菌活性的关键分子特征\")\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 🧪 分子特征分析\n", "\n", "让我们深入分析几个重要的抗菌化合物，了解它们的分子特征和药物性质。\n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🏆 最佳抗菌化合物分析:\n", "============================================================\n", "\\n#1 头孢菌素\n", "   MIC: 0.200 μg/mL\n", "   机制: 细胞壁抑制\n", "   分子量: 363.4 Da\n", "   LogP: 0.15\n", "   氢键供体: 4\n", "   氢键受体: 6\n", "   TPSA: 133.0 Ų\n", "   可旋转键: 4\n", "   ✅ Lipinski五原则: 完全符合\n"]}, {"ename": "AttributeError", "evalue": "module 'rdkit.Chem.Descriptors' has no attribute 'FractionCsp3'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mAttributeError\u001b[39m                            <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[18]\u001b[39m\u001b[32m, line 59\u001b[39m\n\u001b[32m     54\u001b[39m     \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33m   ⚠️ <PERSON>违反: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mlipinski_violations\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m项 (\u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[33m'\u001b[39m\u001b[33m, \u001b[39m\u001b[33m'\u001b[39m.join(violations)\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m)\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m     56\u001b[39m \u001b[38;5;66;03m# 预测其MIC值\u001b[39;00m\n\u001b[32m     57\u001b[39m mol_descriptors = np.array([[mw, logp, hbd, hba, tpsa, rotbonds, \n\u001b[32m     58\u001b[39m                              Descriptors.NumAromaticRings(mol), \n\u001b[32m---> \u001b[39m\u001b[32m59\u001b[39m                              \u001b[43mDescriptors\u001b[49m\u001b[43m.\u001b[49m\u001b[43mFractionCsp3\u001b[49m(mol)]])\n\u001b[32m     60\u001b[39m predicted_mic = rf_model.predict(mol_descriptors)[\u001b[32m0\u001b[39m]\n\u001b[32m     62\u001b[39m prediction_error = \u001b[38;5;28mabs\u001b[39m(predicted_mic - mic)\n", "\u001b[31mAttributeError\u001b[39m: module 'rdkit.Chem.Descriptors' has no attribute 'FractionCsp3'"]}], "source": ["# 分析具有最佳活性的化合物\n", "print(\"🏆 最佳抗菌化合物分析:\")\n", "print(\"=\" * 60)\n", "\n", "# 找出活性最强的5个化合物（MIC值最小）\n", "top_compounds = df.nsmallest(5, 'activity_mic')\n", "\n", "for i, (idx, compound) in enumerate(top_compounds.iterrows(), 1):\n", "    smiles = compound['smiles']\n", "    name = compound['compound_name']\n", "    mic = compound['activity_mic']\n", "    mechanism = compound['mechanism']\n", "    \n", "    mol = Chem.Mo<PERSON>rom<PERSON>(smiles)\n", "    if mol is not None:\n", "        # 计算关键药物性质\n", "        mw = Descriptors.MolWt(mol)\n", "        logp = Descriptors.MolLogP(mol)\n", "        hbd = Descriptors.NumHDonors(mol)\n", "        hba = Descriptors.NumHAcceptors(mol)\n", "        tpsa = Descriptors.TPSA(mol)\n", "        rotbonds = Descriptors.NumRotatableBonds(mol)\n", "        \n", "        print(f\"\\\\n#{i} {name}\")\n", "        print(f\"   MIC: {mic:.3f} μg/mL\")\n", "        print(f\"   机制: {mechanism}\")\n", "        print(f\"   分子量: {mw:.1f} Da\")\n", "        print(f\"   LogP: {logp:.2f}\")\n", "        print(f\"   氢键供体: {hbd}\")\n", "        print(f\"   氢键受体: {hba}\")\n", "        print(f\"   TPSA: {tpsa:.1f} Ų\")\n", "        print(f\"   可旋转键: {rotbonds}\")\n", "        \n", "        # Lipinski五原则检查\n", "        lipinski_violations = 0\n", "        violations = []\n", "        \n", "        if mw > 500:\n", "            lipinski_violations += 1\n", "            violations.append(\"分子量>500\")\n", "        if logp > 5:\n", "            lipinski_violations += 1\n", "            violations.append(\"LogP>5\")\n", "        if hbd > 5:\n", "            lipinski_violations += 1\n", "            violations.append(\"氢键供体>5\")\n", "        if hba > 10:\n", "            lipinski_violations += 1\n", "            violations.append(\"氢键受体>10\")\n", "        \n", "        if lipinski_violations == 0:\n", "            print(f\"   ✅ <PERSON><PERSON>ski五原则: 完全符合\")\n", "        else:\n", "            print(f\"   ⚠️ <PERSON><PERSON><PERSON>违反: {lipinski_violations}项 ({', '.join(violations)})\")\n", "        \n", "        # 预测其MIC值\n", "        mol_descriptors = np.array([[mw, logp, hbd, hba, tpsa, rotbonds, \n", "                                     Descriptors.NumAromaticRings(mol), \n", "                                     Descriptors.FractionCsp3(mol)]])\n", "        predicted_mic = rf_model.predict(mol_descriptors)[0]\n", "        \n", "        prediction_error = abs(predicted_mic - mic)\n", "        print(f\"   🔮 模型预测MIC: {predicted_mic:.3f} μg/mL (误差: {prediction_error:.3f})\")\n", "\n", "print(\"\\\\n\\\\n📊 活性vs分子特征相关性分析:\")\n", "print(\"-\" * 40)\n", "\n", "# 计算相关性\n", "correlations = []\n", "for i, feature in enumerate(feature_names):\n", "    correlation = np.corrcoef(X[:, i], y)[0, 1]\n", "    correlations.append((feature, correlation))\n", "\n", "# 按相关性绝对值排序\n", "correlations.sort(key=lambda x: abs(x[1]), reverse=True)\n", "\n", "print(\"与抗菌活性(MIC)的相关性:\")\n", "for feature, corr in correlations:\n", "    direction = \"正相关\" if corr > 0 else \"负相关\"\n", "    strength = \"强\" if abs(corr) > 0.5 else \"中等\" if abs(corr) > 0.3 else \"弱\"\n", "    print(f\"  {feature}: {corr:+.3f} ({strength}{direction})\")\n", "\n", "print(\"\\\\n💡 解释:\")\n", "print(\"  - 正相关：该特征值越大，MIC越大（活性越弱）\")\n", "print(\"  - 负相关：该特征值越大，MIC越小（活性越强）\")\n"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["## 🎓 教程总结\n", "\n", "恭喜！您已经完成了神农框架的快速开始教程。让我们回顾一下学到的内容和下一步的建议。\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 教程总结\n", "print(\"🎉 神农框架快速开始教程完成！\")\n", "print(\"=\" * 50)\n", "\n", "print(\"\\\\n📚 今天您学会了:\")\n", "print(\"  ✅ 安装和配置神农框架\")\n", "print(\"  ✅ 创建抗菌化合物数据集\")\n", "print(\"  ✅ 使用RDKit计算分子描述符\")\n", "print(\"  ✅ 训练机器学习预测模型\")\n", "print(\"  ✅ 评估模型性能和解释结果\")\n", "print(\"  ✅ 分析分子特征和药物性质\")\n", "print(\"  ✅ 创建专业的数据可视化\")\n", "\n", "print(\"\\\\n📊 本次实验成果:\")\n", "print(f\"  - 处理了 {len(df)} 个抗菌化合物\")\n", "print(f\"  - 训练模型R² = {test_r2:.3f}\")\n", "print(f\"  - 识别了 {len(set(df['mechanism']))} 种抗菌机制\")\n", "print(f\"  - 分析了 {len(feature_names)} 个分子特征\")\n", "print(f\"  - 数据文件已保存至: {sample_file}\")\n", "\n", "print(\"\\\\n🚀 下一步学习建议:\")\n", "print(\"  1. 📖 阅读完整的神农框架文档\")\n", "print(\"  2. 🧪 尝试使用您自己的化合物数据\")\n", "print(\"  3. 🔬 探索更复杂的神农深度学习模型\")\n", "print(\"  4. 📊 学习更多的化学信息学分析方法\")\n", "print(\"  5. 🤖 尝试集成其他机器学习算法\")\n", "\n", "print(\"\\\\n🔧 解决虚拟内存问题的建议:\")\n", "print(\"  - 增加Windows虚拟内存至8-16GB\")\n", "print(\"  - 使用CPU版本的PyTorch避免CUDA内存限制\")\n", "print(\"  - 减少批次大小和模型复杂度\")\n", "print(\"  - 考虑使用云计算平台进行大规模训练\")\n", "\n", "print(\"\\\\n📞 获取帮助:\")\n", "print(\"  - 💬 神农框架GitHub Issues\")\n", "print(\"  - 📧 邮箱：<EMAIL>\")\n", "print(\"  - 📝 查看memory-bank中的详细文档\")\n", "\n", "print(\"\\\\n🎯 记住：这只是开始！\")\n", "print(\"    神农框架的完整功能包括更先进的图神经网络、\")\n", "print(\"    注意力机制、多任务学习等。随着您对框架的\")\n", "print(\"    深入了解，您将能够构建更强大的抗菌药物\")\n", "print(\"    发现系统。\")\n", "\n", "print(\"\\\\n🧬 继续您的抗菌药物发现之旅吧！ 💪\")\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 2}