# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-27
# 描述: 神农框架数据处理模块初始化

"""
神农框架数据处理模块

提供抗菌化合物数据的加载、预处理、特征化和批处理功能。
支持双模态输入：分子图 + 专家特征描述符。

主要组件:
- datapoints: 数据点定义
- datasets: 数据集类
- molgraph: 分子图表示
- collate: 批处理数据整理
- dataloader: 数据加载器
- splitting: 数据划分
"""

from .datapoints import ShennongDatapoint
from .datasets import AntibacterialDataset, ShennongDataset
from .molgraph import ShennongMolGraph
from .collate import shennong_collate_fn
from .dataloader import ShennongDataLoader
from .splitting import scaffold_split, random_split, stratified_split

__all__ = [
    # 数据点和数据集
    'ShennongDatapoint',
    'AntibacterialDataset',
    'ShennongDataset',
    
    # 分子图表示
    'ShennongMolGraph',
    
    # 数据加载
    'shennong_collate_fn',
    'ShennongDataLoader',
    
    # 数据划分
    'scaffold_split',
    'random_split', 
    'stratified_split',
]
