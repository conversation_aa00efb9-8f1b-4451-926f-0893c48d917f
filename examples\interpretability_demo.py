#!/usr/bin/env python3
# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-29
# 描述: 神农框架可解释性功能演示

"""
神农框架可解释性功能演示

展示如何使用神农框架的可解释性模块分析AI预测结果，
为化学家提供有意义的化学解释。
"""

import sys
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from shennong.interpretation import (
    ChemicalExplainer,
    AttentionInterpreter,
    DescriptorAnalyzer,
    MechanismExplainer,
    PharmacophoreAnalyzer
)
from shennong.featurizers.molecule import MordredFeaturizer
from rdkit import Chem
from rdkit.Chem import Descriptors


def simulate_ai_prediction(smiles: str, true_mechanism: str, true_activity: float):
    """
    模拟AI预测结果
    
    Args:
        smiles: 分子SMILES
        true_mechanism: 真实机制
        true_activity: 真实活性
        
    Returns:
        模拟的预测结果
    """
    mol = Chem.MolFromSmiles(smiles)
    if mol is None:
        return None
    
    num_atoms = mol.GetNumAtoms()
    
    # 模拟注意力权重
    attention_weights = {
        'graph_attention': np.random.beta(2, 5, num_atoms),
        'expert_attention': np.random.beta(2, 5, num_atoms),
        'fusion_attention': np.random.beta(2, 5, num_atoms)
    }
    
    # 为重要原子增加注意力权重
    for i, atom in enumerate(mol.GetAtoms()):
        if atom.GetSymbol() in ['N', 'O', 'F', 'S', 'Cl']:
            for key in attention_weights:
                attention_weights[key][i] *= 2.0
        if atom.GetIsAromatic():
            for key in attention_weights:
                attention_weights[key][i] *= 1.5
    
    # 归一化
    for key in attention_weights:
        attention_weights[key] = attention_weights[key] / np.sum(attention_weights[key])
    
    # 模拟描述符
    try:
        featurizer = MordredFeaturizer()
        descriptors = featurizer.featurize(smiles)
        descriptor_names = featurizer.get_descriptor_names()
    except:
        # 使用基本描述符
        descriptors = np.array([
            Descriptors.MolWt(mol),
            Descriptors.MolLogP(mol),
            Descriptors.NumHDonors(mol),
            Descriptors.NumHAcceptors(mol),
            Descriptors.TPSA(mol),
            Descriptors.NumRotatableBonds(mol)
        ])
        descriptor_names = ['MW', 'LogP', 'HBD', 'HBA', 'TPSA', 'RotBonds']
    
    # 添加噪声到活性预测
    predicted_activity = true_activity * (1 + np.random.normal(0, 0.1))
    predicted_activity = max(0.01, predicted_activity)
    
    return {
        'predicted_activity': predicted_activity,
        'predicted_mechanism': true_mechanism,
        'confidence': 0.85 + np.random.normal(0, 0.05),
        'attention_weights': attention_weights,
        'descriptors': descriptors,
        'descriptor_names': descriptor_names
    }


def demo_attention_interpretation():
    """演示注意力权重解释"""
    print("🔍 注意力权重解释演示")
    print("=" * 50)
    
    # 示例分子：环丙沙星
    smiles = 'O=C(O)C1=CN(C2CC2)C3=C(C=C(F)C(N4CCNCC4)=C3F)C1=O'
    mechanism = 'dna_replication'
    
    # 模拟预测结果
    prediction = simulate_ai_prediction(smiles, mechanism, 0.25)
    
    # 初始化注意力解释器
    interpreter = AttentionInterpreter()
    
    # 执行解释
    analysis = interpreter.interpret_attention(
        smiles=smiles,
        attention_weights=prediction['attention_weights'],
        predicted_mechanism=mechanism
    )
    
    print(f"分子: {smiles}")
    print(f"机制: {mechanism}")
    
    print("\n📊 注意力权重统计:")
    for layer, stats in analysis['attention_summary'].items():
        print(f"  {layer}: 平均={stats['mean']:.4f}, 最大={stats['max']:.4f}")
    
    print("\n🎯 关键区域:")
    for i, region in enumerate(analysis['key_regions'][:3]):
        print(f"  {i+1}. {region['description']} (权重: {region['attention_weight']:.4f})")
    
    print(f"\n🧬 机制相关性: {analysis['mechanism_relevance']['relevance_score']:.4f}")
    print(f"机制支持: {'是' if analysis['mechanism_relevance']['mechanism_support'] else '否'}")
    
    return analysis


def demo_descriptor_analysis():
    """演示描述符分析"""
    print("\n📊 Mordred描述符分析演示")
    print("=" * 50)
    
    # 示例分子：青霉素G
    smiles = 'CC1([C@@H](N2[C@H](S1)[C@@H](C2=O)NC(=O)CC3=CC=CC=C3)C(=O)O)C'
    activity = 0.1
    
    # 模拟预测结果
    prediction = simulate_ai_prediction(smiles, 'cell_wall_synthesis', activity)
    
    # 初始化描述符分析器
    analyzer = DescriptorAnalyzer()
    
    # 执行分析
    analysis = analyzer.analyze_descriptors(
        smiles=smiles,
        descriptor_values=prediction['descriptors'],
        descriptor_names=prediction['descriptor_names'],
        predicted_activity=activity
    )
    
    print(f"分子: {smiles}")
    print(f"活性: {activity} μg/mL")
    
    print("\n✅ 描述符质量:")
    quality = analysis['descriptor_quality']
    print(f"  成功率: {quality['success_rate']:.2%}")
    print(f"  有效描述符: {quality['valid_descriptors']}/{quality['total_descriptors']}")
    
    print("\n🎯 重要描述符 (前5个):")
    for i, desc in enumerate(analysis['important_descriptors'][:5]):
        print(f"  {i+1}. {desc['name']}: {desc['value']:.4f} (重要性: {desc['importance_score']:.4f})")
        print(f"     {desc['chemical_meaning']}")
    
    print("\n🧪 化学性质解释:")
    for prop in analysis['chemical_interpretation']['key_properties']:
        print(f"  • {prop['interpretation']}")
    
    return analysis


def demo_mechanism_explanation():
    """演示机制解释"""
    print("\n🧬 抗菌机制解释演示")
    print("=" * 50)
    
    # 示例分子：氯霉素
    smiles = 'O=C(NC(C(O)C1=CC=C([N+]([O-])=O)C=C1)CO)C(Cl)Cl'
    mechanism = 'protein_synthesis'
    activity = 2.0
    
    # 初始化机制解释器
    explainer = MechanismExplainer()
    
    # 执行解释
    analysis = explainer.explain_mechanism(
        smiles=smiles,
        predicted_mechanism=mechanism,
        predicted_activity=activity
    )
    
    print(f"分子: {smiles}")
    print(f"预测机制: {mechanism}")
    
    print("\n📋 机制信息:")
    mech_info = analysis['mechanism_info']
    print(f"  名称: {mech_info['name']}")
    print(f"  描述: {mech_info['description']}")
    
    print("\n🔍 结构基础:")
    struct_basis = analysis['structural_basis']
    if 'matched_patterns' in struct_basis:
        print(f"  匹配模式: {len(struct_basis['matched_patterns'])} 个")
        print(f"  结构评分: {struct_basis['structure_score']:.4f}")
        print(f"  机制支持: {'是' if struct_basis['mechanism_support'] else '否'}")
    
    print(f"\n🎯 机制置信度: {analysis['mechanism_confidence']:.4f}")
    
    print("\n💡 化学解释:")
    print(f"  {analysis['chemical_rationale']}")
    
    return analysis


def demo_pharmacophore_analysis():
    """演示药效团分析"""
    print("\n💊 药效团分析演示")
    print("=" * 50)
    
    # 示例分子：环丙沙星
    smiles = 'O=C(O)C1=CN(C2CC2)C3=C(C=C(F)C(N4CCNCC4)=C3F)C1=O'
    mechanism = 'dna_replication'
    
    # 模拟预测结果
    prediction = simulate_ai_prediction(smiles, mechanism, 0.25)
    
    # 初始化药效团分析器
    analyzer = PharmacophoreAnalyzer()
    
    # 执行分析
    analysis = analyzer.analyze_pharmacophores(
        smiles=smiles,
        predicted_mechanism=mechanism,
        attention_weights=prediction['attention_weights']
    )
    
    print(f"分子: {smiles}")
    print(f"机制: {mechanism}")
    
    print("\n🔍 识别的药效团:")
    for i, pharm in enumerate(analysis['identified_pharmacophores'][:5]):
        print(f"  {i+1}. {pharm['description']} (数量: {pharm['count']})")
        print(f"     重要性: {pharm['importance']}")
        print(f"     化学意义: {pharm['chemical_meaning']}")
    
    print("\n🧬 机制特异性特征:")
    mech_features = analysis['mechanism_specific_features']
    print(f"  机制支持: {'是' if mech_features['mechanism_support'] else '否'}")
    print(f"  支持评分: {mech_features['support_score']:.4f}")
    
    print("\n🧪 化学性质:")
    chem_interp = analysis['chemical_interpretation']
    props = chem_interp['basic_properties']
    print(f"  分子量: {props['molecular_weight']:.1f} Da")
    print(f"  LogP: {props['logp']:.2f}")
    print(f"  氢键供体: {props['hbd']}")
    print(f"  氢键受体: {props['hba']}")
    
    return analysis


def demo_comprehensive_explanation():
    """演示综合化学解释"""
    print("\n🎯 综合化学解释演示")
    print("=" * 50)
    
    # 示例分子：环丙沙星
    smiles = 'O=C(O)C1=CN(C2CC2)C3=C(C=C(F)C(N4CCNCC4)=C3F)C1=O'
    mechanism = 'dna_replication'
    activity = 0.25
    
    # 模拟预测结果
    prediction = simulate_ai_prediction(smiles, mechanism, activity)
    
    # 初始化综合解释器
    explainer = ChemicalExplainer()
    
    # 生成完整解释
    explanation = explainer.explain_prediction(
        smiles=smiles,
        predicted_activity=prediction['predicted_activity'],
        predicted_mechanism=prediction['predicted_mechanism'],
        attention_weights=prediction['attention_weights'],
        descriptor_values=prediction['descriptors'],
        descriptor_names=prediction['descriptor_names'],
        confidence=prediction['confidence']
    )
    
    # 生成报告
    report = explainer.generate_report(explanation)
    print(report)
    
    return explanation


def main():
    """主函数"""
    print("🧬 神农框架可解释性功能演示")
    print("=" * 60)
    print("本演示将展示神农框架如何将AI预测结果转化为化学家能理解的信息")
    print()
    
    try:
        # 1. 注意力权重解释
        demo_attention_interpretation()
        
        # 2. 描述符分析
        demo_descriptor_analysis()
        
        # 3. 机制解释
        demo_mechanism_explanation()
        
        # 4. 药效团分析
        demo_pharmacophore_analysis()
        
        # 5. 综合解释
        demo_comprehensive_explanation()
        
        print("\n" + "=" * 60)
        print("✅ 可解释性功能演示完成!")
        print("🧬 神农尝百草，AI识良药 - 让AI预测更加透明可信！")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
