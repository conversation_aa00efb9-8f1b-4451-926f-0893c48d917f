# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-27
# 描述: 神农框架专家特征分支

"""
神农框架专家特征分支

处理分子描述符等专家特征，支持自适应架构设计。
"""

from typing import Dict, Any, Optional, List
import torch
import torch.nn as nn
import numpy as np
import logging

logger = logging.getLogger(__name__)


class ExpertFeatureBranch(nn.Module):
    """
    专家特征分支基类
    
    处理分子描述符等专家知识特征。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化专家特征分支
        
        Args:
            config: 专家分支配置
        """
        super().__init__()
        
        self.config = config
        self.input_dim = config.get('descriptor_dim', 1613)
        self.output_dim = config.get('output_dim', 128)
        self.dropout = config.get('dropout', 0.3)
        self.batch_norm = config.get('batch_norm', True)
        
        # 构建网络
        self._build_network()
        
        logger.info(f"初始化专家特征分支: 输入维度={self.input_dim}, "
                   f"输出维度={self.output_dim}")
    
    def _build_network(self):
        """构建网络结构"""
        hidden_dims = self.config.get('hidden_dims', [512, 256])
        
        layers = []
        prev_dim = self.input_dim
        
        for hidden_dim in hidden_dims:
            layers.append(nn.Linear(prev_dim, hidden_dim))
            
            if self.batch_norm:
                layers.append(nn.BatchNorm1d(hidden_dim))
            
            layers.append(nn.ReLU())
            layers.append(nn.Dropout(self.dropout))
            
            prev_dim = hidden_dim
        
        # 输出层
        layers.append(nn.Linear(prev_dim, self.output_dim))
        
        self.encoder = nn.Sequential(*layers)
    
    def forward(self, descriptors: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            descriptors: 描述符张量 [batch_size, input_dim]
            
        Returns:
            编码后的特征 [batch_size, output_dim]
        """
        if descriptors is None:
            # 如果没有描述符，返回零向量
            batch_size = 1
            device = next(self.parameters()).device
            return torch.zeros(batch_size, self.output_dim, device=device)
        
        return self.encoder(descriptors)


class AdaptiveExpertBranch(ExpertFeatureBranch):
    """
    自适应专家特征分支
    
    根据输入描述符维度自动调整网络架构。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化自适应专家分支
        
        Args:
            config: 专家分支配置
        """
        self.adaptive_architecture = config.get('adaptive_architecture', True)
        
        # 如果启用自适应架构，重新设计网络结构
        if self.adaptive_architecture:
            config = self._design_adaptive_config(config)
        
        super().__init__(config)
        
        logger.info(f"初始化自适应专家分支: 自适应架构={self.adaptive_architecture}")
    
    def _design_adaptive_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        根据输入维度设计自适应架构
        
        Args:
            config: 原始配置
            
        Returns:
            调整后的配置
        """
        input_dim = config.get('descriptor_dim', 1613)
        output_dim = config.get('output_dim', 128)
        
        # 智能设计隐藏层维度
        hidden_dims = self._calculate_hidden_dims(input_dim, output_dim)
        config['hidden_dims'] = hidden_dims
        
        # 根据网络深度调整dropout
        num_layers = len(hidden_dims)
        if num_layers <= 2:
            config['dropout'] = 0.2
        elif num_layers <= 4:
            config['dropout'] = 0.3
        else:
            config['dropout'] = 0.4
        
        logger.info(f"自适应架构设计: {input_dim} → {hidden_dims} → {output_dim}")
        
        return config
    
    def _calculate_hidden_dims(self, input_dim: int, output_dim: int) -> List[int]:
        """
        计算隐藏层维度
        
        Args:
            input_dim: 输入维度
            output_dim: 输出维度
            
        Returns:
            隐藏层维度列表
        """
        hidden_dims = []
        current_dim = input_dim
        
        # 根据输入维度范围设计架构
        if input_dim >= 1500:  # Mordred全描述符
            # 大维度：需要更多层进行降维
            while current_dim > output_dim * 4:
                if current_dim > 2000:
                    next_dim = current_dim // 3
                elif current_dim > 500:
                    next_dim = current_dim // 2
                else:
                    next_dim = max(output_dim * 2, current_dim // 2)
                
                hidden_dims.append(next_dim)
                current_dim = next_dim
            
            # 最后一层
            if current_dim > output_dim * 2:
                hidden_dims.append(output_dim * 2)
        
        elif input_dim >= 500:  # 中等维度
            # 中等维度：适中的降维
            hidden_dims.append(input_dim // 2)
            if input_dim // 2 > output_dim * 2:
                hidden_dims.append(output_dim * 2)
        
        elif input_dim >= 200:  # 小维度
            # 小维度：简单的降维
            if input_dim > output_dim * 2:
                hidden_dims.append(output_dim * 2)
        
        else:  # 很小的维度
            # 直接连接或简单的隐藏层
            if input_dim > output_dim:
                hidden_dims.append((input_dim + output_dim) // 2)
        
        # 确保至少有一个隐藏层
        if not hidden_dims:
            hidden_dims = [max(output_dim * 2, input_dim // 2)]
        
        return hidden_dims
    
    def get_architecture_info(self) -> Dict[str, Any]:
        """
        获取架构信息
        
        Returns:
            架构信息字典
        """
        info = {
            'input_dim': self.input_dim,
            'output_dim': self.output_dim,
            'hidden_dims': self.config.get('hidden_dims', []),
            'num_parameters': sum(p.numel() for p in self.parameters()),
            'adaptive_architecture': self.adaptive_architecture,
            'dropout': self.dropout,
            'batch_norm': self.batch_norm
        }
        
        return info


class MultiModalExpertBranch(AdaptiveExpertBranch):
    """
    多模态专家特征分支
    
    支持多种类型的专家特征融合。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化多模态专家分支
        
        Args:
            config: 专家分支配置
        """
        super().__init__(config)
        
        # 多模态配置
        self.feature_types = config.get('feature_types', ['descriptors'])
        self.fusion_method = config.get('fusion_method', 'concatenation')
        
        # 为不同特征类型创建编码器
        self._build_multimodal_encoders()
        
        logger.info(f"初始化多模态专家分支: 特征类型={self.feature_types}, "
                   f"融合方法={self.fusion_method}")
    
    def _build_multimodal_encoders(self):
        """构建多模态编码器"""
        self.feature_encoders = nn.ModuleDict()
        
        for feature_type in self.feature_types:
            if feature_type == 'descriptors':
                # 已经在父类中构建
                continue
            elif feature_type == 'fingerprints':
                # 分子指纹编码器
                self.feature_encoders['fingerprints'] = nn.Sequential(
                    nn.Linear(2048, 512),  # 假设2048位指纹
                    nn.ReLU(),
                    nn.Dropout(0.2),
                    nn.Linear(512, 128)
                )
            elif feature_type == 'pharmacophores':
                # 药效团特征编码器
                self.feature_encoders['pharmacophores'] = nn.Sequential(
                    nn.Linear(100, 64),  # 假设100维药效团特征
                    nn.ReLU(),
                    nn.Linear(64, 32)
                )
        
        # 融合层
        if self.fusion_method == 'attention':
            total_dim = self.output_dim + sum(
                encoder[-1].out_features 
                for encoder in self.feature_encoders.values()
            )
            self.fusion_attention = nn.MultiheadAttention(
                embed_dim=total_dim,
                num_heads=4,
                batch_first=True
            )
    
    def forward(
        self, 
        descriptors: torch.Tensor,
        additional_features: Optional[Dict[str, torch.Tensor]] = None
    ) -> torch.Tensor:
        """
        多模态前向传播
        
        Args:
            descriptors: 主要描述符
            additional_features: 额外特征字典
            
        Returns:
            融合后的特征
        """
        # 主要描述符编码
        main_features = super().forward(descriptors)
        
        if additional_features is None or not self.feature_encoders:
            return main_features
        
        # 编码额外特征
        encoded_features = [main_features]
        
        for feature_type, encoder in self.feature_encoders.items():
            if feature_type in additional_features:
                feature_data = additional_features[feature_type]
                encoded_feature = encoder(feature_data)
                encoded_features.append(encoded_feature)
        
        # 特征融合
        if self.fusion_method == 'concatenation':
            fused_features = torch.cat(encoded_features, dim=-1)
        elif self.fusion_method == 'addition':
            # 需要确保维度一致
            min_dim = min(f.size(-1) for f in encoded_features)
            aligned_features = [f[:, :min_dim] for f in encoded_features]
            fused_features = torch.stack(aligned_features, dim=0).sum(dim=0)
        elif self.fusion_method == 'attention':
            # 注意力融合
            stacked_features = torch.stack(encoded_features, dim=1)
            fused_features, _ = self.fusion_attention(
                stacked_features, stacked_features, stacked_features
            )
            fused_features = fused_features.mean(dim=1)
        else:
            fused_features = main_features
        
        return fused_features
