# 神农框架 vs Chemprop vs AutoGluon 对比研究

## 项目概述

本研究旨在通过系统性的三模型对比，验证神农框架在抗菌化合物预测任务中的有效性。我们选择了三个代表不同技术路线的模型进行对比：

- **Chemprop**: 经典的GNN基线模型，代表图神经网络在分子预测中的标准方法
- **神农框架**: 领域专家模型，整合化学知识的双模态架构
- **AutoGluon**: 自动化通用模型，代表非GNN方法的性能上限

## 研究假设

### 核心假设
神农框架通过化学导向的注意力机制和自适应特征融合，能够在抗菌活性预测任务中显著超越传统的图神经网络和自动机器学习方法。

### 具体假设
1. **准确性假设**: 神农框架 > Chemprop > AutoGluon (在预测准确性上)
2. **可解释性假设**: 神农框架 >> Chemprop > AutoGluon (在化学可解释性上)
3. **数据效率假设**: 神农框架在小数据集(8k化合物)上表现更稳定

## 三模型技术特点对比

### Chemprop (经典基线)
- **架构**: 标准消息传递神经网络(MPNN)
- **特征处理**: 简单特征拼接 (`--features_path`参数)
- **优势**: 成熟稳定，广泛验证
- **局限**: 缺乏化学知识指导，特征融合方式简单

### 神农框架 (领域专家)
- **架构**: 双模态GNN + 专家分支
- **特征处理**: 化学导向注意力机制 + 自适应融合
- **创新点**:
  - 官能团感知的注意力权重
  - 分子性质导向的特征融合
  - 抗菌活性相关的知识集成
- **优势**: 融合领域知识，提供化学解释

### AutoGluon (自动化通用)
- **架构**: 集成多种传统ML算法
- **特征处理**: 基于Mordred描述符的表格数据
- **优势**: 自动化调优，无需人工干预
- **代表意义**: 非GNN方法的性能天花板

## 实验设计框架

### 数据集规划
- **训练集**: 6400化合物 (80%)
- **验证集**: 800化合物 (10%)
- **测试集**: 800化合物 (10%)
- **交叉验证**: 5折分层抽样

### 评估指标体系
1. **预测性能**:
   - RMSE (主要指标)
   - MAE, R²
   - Pearson/Spearman相关系数

2. **模型鲁棒性**:
   - 跨验证集性能稳定性
   - 不同数据子集的性能方差

3. **化学可解释性**:
   - 原子/基团重要性分析
   - 关键描述符识别
   - 化学机制洞察

### 特征工程策略

#### 统一特征集
为确保公平对比，所有模型使用相同的Mordred描述符集：
- **2D描述符**: 拓扑指数、电子性质、几何描述符
- **3D描述符**: 构象相关性质、表面积、体积
- **筛选后**: ~800-1000个有效描述符

#### 模型特定适配
- **Chemprop**: 通过`--features_path`注入外部特征
- **神农框架**: 特征通过专家分支处理
- **AutoGluon**: 直接使用表格化特征

## 技术实现计划

### 阶段一: 基础设施搭建
1. 统一数据预处理管道
2. 特征计算与存储系统
3. 模型训练框架搭建

### 阶段二: 模型实现与调优
1. Chemprop基线模型配置
2. AutoGluon自动化训练
3. 神农框架架构优化

### 阶段三: 系统性评估
1. 性能基准测试
2. 消融实验设计
3. 可解释性分析

## 预期贡献

### 学术贡献
1. **方法学**: 验证化学知识集成在GNN中的有效性
2. **基准**: 建立抗菌化合物预测的标准评估框架
3. **洞察**: 揭示不同技术路线的适用场景

### 实用价值
1. **药物发现**: 提供更准确的抗菌活性预测工具
2. **化学解释**: 帮助理解分子-活性关系
3. **方法指导**: 为分子预测任务提供技术选择依据

## 成功标准

### 定量指标
- 神农框架在测试集RMSE上至少比Chemprop提升10%
- 在小样本(2k, 4k)子集上表现更稳定
- 可解释性评分显著优于基线模型

### 定性标准
- 生成的化学解释符合已知的抗菌机制
- 识别的关键基团与文献报道一致
- 模型预测的活性趋势合理可信

## 相关文件

- [[20_核心概念/GNNs/Chemprop模型架构分析]]
- [[20_核心概念/AutoML/AutoGluon在分子预测中的应用]]
- [[30_神农框架/30.01_神农框架核心架构与创新点]]
- [[90_论文写作/论文结构与实验设计]]

---

*创建时间: 2024-01-XX*
*最后更新: 2024-01-XX*
*状态: 规划中* 