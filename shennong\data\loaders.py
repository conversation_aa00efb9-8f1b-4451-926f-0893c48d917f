# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-29
# 描述: 神农框架数据加载器

"""
神农框架数据加载器

提供从CSV文件加载数据并创建数据集的功能。
"""

import logging
import pandas as pd
from pathlib import Path
from typing import List, Optional, Dict, Any, Union
from rdkit import Chem

from .datapoints import ShennongDatapoint
from .datasets import AntibacterialDataset, ShennongDataset
from ..featurizers.molecule import MordredFeaturizer

logger = logging.getLogger(__name__)


class CSVDataLoader:
    """CSV数据加载器"""

    def __init__(
        self,
        smiles_column: str = "smiles",
        target_columns: Optional[List[str]] = None,
        mechanism_column: Optional[str] = None,
        id_column: Optional[str] = None,
        descriptor_columns: Optional[List[str]] = None
    ):
        """
        初始化CSV数据加载器

        Args:
            smiles_column: SMILES列名
            target_columns: 目标列名列表
            mechanism_column: 机制列名
            id_column: ID列名
            descriptor_columns: 描述符列名列表
        """
        self.smiles_column = smiles_column
        self.target_columns = target_columns or []
        self.mechanism_column = mechanism_column
        self.id_column = id_column
        self.descriptor_columns = descriptor_columns or []

        # 特征化器
        self.featurizer = MordredFeaturizer()

    def load_from_csv(
        self,
        csv_path: Union[str, Path],
        validate_smiles: bool = True,
        compute_descriptors: bool = True
    ) -> List[ShennongDatapoint]:
        """
        从CSV文件加载数据

        Args:
            csv_path: CSV文件路径
            validate_smiles: 是否验证SMILES
            compute_descriptors: 是否计算描述符

        Returns:
            数据点列表
        """
        logger.info(f"从CSV文件加载数据: {csv_path}")

        # 读取CSV文件
        df = pd.read_csv(csv_path)
        logger.info(f"读取了 {len(df)} 行数据")

        # 验证必需的列
        self._validate_columns(df)

        # 创建数据点
        datapoints = []
        invalid_count = 0

        for idx, row in df.iterrows():
            try:
                datapoint = self._create_datapoint(
                    row,
                    validate_smiles=validate_smiles,
                    compute_descriptors=compute_descriptors
                )
                if datapoint is not None:
                    datapoints.append(datapoint)
                else:
                    invalid_count += 1
            except Exception as e:
                logger.warning(f"第 {idx} 行数据处理失败: {e}")
                invalid_count += 1

        logger.info(f"成功加载 {len(datapoints)} 个数据点，跳过 {invalid_count} 个无效数据")
        return datapoints

    def _validate_columns(self, df: pd.DataFrame):
        """验证CSV文件的列"""
        missing_columns = []

        # 检查SMILES列
        if self.smiles_column not in df.columns:
            missing_columns.append(self.smiles_column)

        # 检查目标列
        for col in self.target_columns:
            if col not in df.columns:
                missing_columns.append(col)

        # 检查机制列
        if self.mechanism_column and self.mechanism_column not in df.columns:
            missing_columns.append(self.mechanism_column)

        if missing_columns:
            raise ValueError(f"CSV文件缺少必需的列: {missing_columns}")

    def _create_datapoint(
        self,
        row: pd.Series,
        validate_smiles: bool = True,
        compute_descriptors: bool = True
    ) -> Optional[ShennongDatapoint]:
        """
        从数据行创建数据点

        Args:
            row: 数据行
            validate_smiles: 是否验证SMILES
            compute_descriptors: 是否计算描述符

        Returns:
            数据点对象，如果无效则返回None
        """
        # 获取SMILES
        smiles = str(row[self.smiles_column]).strip()

        # 验证SMILES
        if validate_smiles:
            mol = Chem.MolFromSmiles(smiles)
            if mol is None:
                logger.warning(f"无效的SMILES: {smiles}")
                return None

        # 获取目标值
        targets = {}
        for col in self.target_columns:
            value = row.get(col)
            if pd.notna(value):
                targets[col] = float(value)

        # 获取机制标签
        mechanism_labels = []
        if self.mechanism_column and pd.notna(row.get(self.mechanism_column)):
            mechanism = str(row[self.mechanism_column]).strip()
            if mechanism:
                mechanism_labels = [mechanism]

        # 获取描述符
        descriptors = None
        if self.descriptor_columns:
            desc_values = []
            for col in self.descriptor_columns:
                value = row.get(col, 0.0)
                desc_values.append(float(value) if pd.notna(value) else 0.0)
            descriptors = desc_values
        elif compute_descriptors:
            # 计算Mordred描述符
            try:
                descriptors = self.featurizer.featurize(smiles)
            except Exception as e:
                logger.warning(f"描述符计算失败 {smiles}: {e}")
                descriptors = None

        # 获取元数据
        metadata = {}
        if self.id_column and pd.notna(row.get(self.id_column)):
            metadata['id'] = str(row[self.id_column])

        # 添加其他列作为元数据
        for col in row.index:
            if col not in [self.smiles_column] + self.target_columns + [self.mechanism_column, self.id_column] + self.descriptor_columns:
                if pd.notna(row[col]):
                    metadata[col] = row[col]

        # 创建数据点
        return ShennongDatapoint(
            smiles=smiles,
            targets=targets,
            descriptors=descriptors,
            mechanism_labels=mechanism_labels,
            metadata=metadata
        )

    def create_dataset(
        self,
        datapoints: List[ShennongDatapoint],
        dataset_type: str = "antibacterial",
        features_path: Optional[str] = None
    ) -> Union[AntibacterialDataset, ShennongDataset]:
        """
        创建数据集

        Args:
            datapoints: 数据点列表
            dataset_type: 数据集类型
            features_path: 预计算特征文件路径

        Returns:
            数据集对象
        """
        if dataset_type == "antibacterial":
            return AntibacterialDataset(datapoints, features_path=features_path)
        else:
            return ShennongDataset(datapoints, features_path=features_path)


def load_csv_data(
    csv_path: Union[str, Path],
    smiles_column: str = "smiles",
    target_columns: Optional[List[str]] = None,
    mechanism_column: Optional[str] = None,
    dataset_type: str = "antibacterial",
    validate_smiles: bool = True,
    compute_descriptors: bool = True,
    features_path: Optional[str] = None
) -> Union[AntibacterialDataset, ShennongDataset]:
    """
    从CSV文件加载数据并创建数据集

    Args:
        csv_path: CSV文件路径
        smiles_column: SMILES列名
        target_columns: 目标列名列表
        mechanism_column: 机制列名
        dataset_type: 数据集类型
        validate_smiles: 是否验证SMILES
        compute_descriptors: 是否计算描述符
        features_path: 预计算特征文件路径

    Returns:
        数据集对象
    """
    loader = CSVDataLoader(
        smiles_column=smiles_column,
        target_columns=target_columns,
        mechanism_column=mechanism_column
    )

    datapoints = loader.load_from_csv(
        csv_path,
        validate_smiles=validate_smiles,
        compute_descriptors=compute_descriptors
    )

    return loader.create_dataset(datapoints, dataset_type, features_path)


def create_sample_data(
    output_path: Union[str, Path],
    num_samples: int = 100
) -> Path:
    """
    创建示例数据文件

    Args:
        output_path: 输出文件路径
        num_samples: 样本数量

    Returns:
        创建的文件路径
    """
    # 示例SMILES和活性数据
    sample_data = [
        ("CCN(CC)CCCC(C)NC1=C2C=CC(=CC2=NC=C1)CF3", 0.5, "protein_synthesis"),
        ("CC(C)CC1=CC=C(C=C1)C(C)C(=O)O", 2.1, "cell_membrane"),
        ("CN1CCN(CC1)C2=C(C=C3C(=C2F)N(C=C(C3=O)C(=O)O)C4CC4)F", 0.8, "dna_replication"),
        ("CC1=C(C=C(C=C1)NC(=O)C2=CC=C(C=C2)CN3CCN(CC3)C)C", 1.2, "cell_wall_synthesis"),
        ("CN(C)CCCC1(C2=CC=CC=C2C3=CC=CC=C31)O", 3.5, "metabolic_pathway"),
    ]

    # 扩展到指定数量
    import random
    import numpy as np

    data_rows = []
    for i in range(num_samples):
        base_smiles, base_activity, mechanism = random.choice(sample_data)

        # 添加一些随机变化
        activity = base_activity + np.random.normal(0, 0.3)
        activity = max(0.1, activity)  # 确保活性值为正

        data_rows.append({
            'smiles': base_smiles,
            'activity': round(activity, 3),
            'mechanism': mechanism,
            'compound_id': f'COMP_{i+1:04d}'
        })

    # 创建DataFrame并保存
    df = pd.DataFrame(data_rows)
    output_path = Path(output_path)
    output_path.parent.mkdir(parents=True, exist_ok=True)

    df.to_csv(output_path, index=False)
    logger.info(f"创建了包含 {num_samples} 个样本的示例数据文件: {output_path}")

    return output_path
