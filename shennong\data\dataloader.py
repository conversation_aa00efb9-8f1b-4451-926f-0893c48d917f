# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-27
# 描述: 神农框架数据加载器

"""
神农框架数据加载器

提供高效的数据加载功能，支持多进程、GPU预加载和内存优化。
兼容Chemprop数据流，同时支持神农框架的双模态输入。
"""

from typing import Optional, Iterator, Dict, Any, Union
import torch
from torch.utils.data import DataLoader, Sampler
import logging

from .datasets import ShennongDataset, AntibacterialDataset
from .collate import shennong_collate_fn, ShennongBatchSampler

logger = logging.getLogger(__name__)


class ShennongDataLoader(DataLoader):
    """
    神农框架专用数据加载器
    
    扩展PyTorch DataLoader，优化分子图和描述符的批处理。
    支持自适应批次大小和GPU内存管理。
    """
    
    def __init__(
        self,
        dataset: Union[ShennongDataset, AntibacterialDataset],
        batch_size: int = 64,
        shuffle: bool = True,
        num_workers: int = 0,
        pin_memory: bool = True,
        drop_last: bool = False,
        collate_fn: Optional[callable] = None,
        sampler: Optional[Sampler] = None,
        batch_sampler: Optional[Sampler] = None,
        adaptive_batch_size: bool = False,
        max_atoms_per_batch: Optional[int] = None,
        **kwargs
    ):
        """
        初始化神农数据加载器
        
        Args:
            dataset: 神农数据集
            batch_size: 批次大小
            shuffle: 是否打乱数据
            num_workers: 工作进程数
            pin_memory: 是否固定内存
            drop_last: 是否丢弃最后一个不完整批次
            collate_fn: 自定义批处理函数
            sampler: 自定义采样器
            batch_sampler: 自定义批次采样器
            adaptive_batch_size: 是否使用自适应批次大小
            max_atoms_per_batch: 每批次最大原子数（用于内存控制）
            **kwargs: 其他DataLoader参数
        """
        self.dataset = dataset
        self.adaptive_batch_size = adaptive_batch_size
        self.max_atoms_per_batch = max_atoms_per_batch
        
        # 使用默认的批处理函数
        if collate_fn is None:
            collate_fn = shennong_collate_fn
        
        # 自适应批次采样
        if adaptive_batch_size and batch_sampler is None:
            batch_sampler = self._create_adaptive_batch_sampler(
                dataset, batch_size, shuffle, drop_last
            )
            # 使用batch_sampler时，需要设置batch_size=1, shuffle=False
            batch_size = 1
            shuffle = False
        
        # 调用父类构造函数
        super().__init__(
            dataset=dataset,
            batch_size=batch_size,
            shuffle=shuffle,
            sampler=sampler,
            batch_sampler=batch_sampler,
            num_workers=num_workers,
            collate_fn=collate_fn,
            pin_memory=pin_memory,
            drop_last=drop_last,
            **kwargs
        )
        
        logger.info(f"初始化神农数据加载器: "
                   f"数据集大小={len(dataset)}, "
                   f"批次大小={batch_size}, "
                   f"工作进程={num_workers}")
    
    def _create_adaptive_batch_sampler(
        self,
        dataset: ShennongDataset,
        batch_size: int,
        shuffle: bool,
        drop_last: bool
    ) -> ShennongBatchSampler:
        """
        创建自适应批次采样器
        
        Args:
            dataset: 数据集
            batch_size: 基础批次大小
            shuffle: 是否打乱
            drop_last: 是否丢弃最后批次
            
        Returns:
            自适应批次采样器
        """
        # 计算每个分子的原子数（用于内存估算）
        molecule_sizes = []
        for i in range(len(dataset)):
            item = dataset[i]
            mol_graph = item['mol_graph']
            if hasattr(mol_graph, 'n_atoms'):
                molecule_sizes.append(mol_graph.n_atoms)
            else:
                molecule_sizes.append(20)  # 默认原子数
        
        return ShennongBatchSampler(
            dataset_size=len(dataset),
            batch_size=batch_size,
            molecule_sizes=molecule_sizes,
            sort_by_size=True,  # 按分子大小排序以优化内存
            drop_last=drop_last
        )
    
    def get_data_statistics(self) -> Dict[str, Any]:
        """获取数据加载器统计信息"""
        stats = {
            'dataset_size': len(self.dataset),
            'batch_size': self.batch_size,
            'num_batches': len(self),
            'num_workers': self.num_workers,
            'pin_memory': self.pin_memory,
            'adaptive_batch_size': self.adaptive_batch_size
        }
        
        if hasattr(self.dataset, 'statistics'):
            stats['dataset_statistics'] = self.dataset.statistics
        
        return stats


class MultiTaskDataLoader:
    """
    多任务数据加载器
    
    支持同时处理多个任务的数据加载，每个任务可以有不同的采样策略。
    """
    
    def __init__(
        self,
        datasets: Dict[str, ShennongDataset],
        batch_sizes: Dict[str, int],
        task_weights: Optional[Dict[str, float]] = None,
        **dataloader_kwargs
    ):
        """
        初始化多任务数据加载器
        
        Args:
            datasets: 任务名称到数据集的映射
            batch_sizes: 任务名称到批次大小的映射
            task_weights: 任务权重（用于采样）
            **dataloader_kwargs: 数据加载器参数
        """
        self.task_names = list(datasets.keys())
        self.task_weights = task_weights or {task: 1.0 for task in self.task_names}
        
        # 创建每个任务的数据加载器
        self.dataloaders = {}
        for task_name in self.task_names:
            self.dataloaders[task_name] = ShennongDataLoader(
                dataset=datasets[task_name],
                batch_size=batch_sizes[task_name],
                **dataloader_kwargs
            )
        
        # 计算采样概率
        total_weight = sum(self.task_weights.values())
        self.task_probs = {
            task: weight / total_weight 
            for task, weight in self.task_weights.items()
        }
        
        logger.info(f"初始化多任务数据加载器: {len(self.task_names)}个任务")
    
    def __iter__(self) -> Iterator[Dict[str, Any]]:
        """迭代多任务批次"""
        # 创建每个任务的迭代器
        task_iterators = {
            task: iter(dataloader) 
            for task, dataloader in self.dataloaders.items()
        }
        
        # 计算总批次数（取最大值）
        total_batches = max(len(dataloader) for dataloader in self.dataloaders.values())
        
        for _ in range(total_batches):
            # 根据权重随机选择任务
            import random
            task_name = random.choices(
                self.task_names, 
                weights=list(self.task_probs.values())
            )[0]
            
            try:
                batch = next(task_iterators[task_name])
                batch['task_name'] = task_name
                yield batch
            except StopIteration:
                # 如果某个任务的数据用完，重新创建迭代器
                task_iterators[task_name] = iter(self.dataloaders[task_name])
                batch = next(task_iterators[task_name])
                batch['task_name'] = task_name
                yield batch
    
    def __len__(self) -> int:
        """总批次数"""
        return max(len(dataloader) for dataloader in self.dataloaders.values())


class MemoryEfficientDataLoader(ShennongDataLoader):
    """
    内存高效数据加载器
    
    针对大型数据集和有限GPU内存优化的数据加载器。
    支持动态批次大小调整和内存监控。
    """
    
    def __init__(
        self,
        dataset: ShennongDataset,
        initial_batch_size: int = 64,
        max_memory_gb: float = 8.0,
        memory_check_interval: int = 10,
        **kwargs
    ):
        """
        初始化内存高效数据加载器
        
        Args:
            dataset: 数据集
            initial_batch_size: 初始批次大小
            max_memory_gb: 最大GPU内存使用量(GB)
            memory_check_interval: 内存检查间隔（批次数）
            **kwargs: 其他参数
        """
        self.initial_batch_size = initial_batch_size
        self.current_batch_size = initial_batch_size
        self.max_memory_gb = max_memory_gb
        self.memory_check_interval = memory_check_interval
        self.batch_count = 0
        
        super().__init__(dataset=dataset, batch_size=initial_batch_size, **kwargs)
        
        logger.info(f"初始化内存高效数据加载器: "
                   f"初始批次大小={initial_batch_size}, "
                   f"最大内存={max_memory_gb}GB")
    
    def __iter__(self) -> Iterator[Dict[str, Any]]:
        """迭代批次，动态调整批次大小"""
        for batch in super().__iter__():
            self.batch_count += 1
            
            # 定期检查GPU内存使用
            if self.batch_count % self.memory_check_interval == 0:
                self._adjust_batch_size_if_needed()
            
            yield batch
    
    def _adjust_batch_size_if_needed(self):
        """根据内存使用情况调整批次大小"""
        if not torch.cuda.is_available():
            return
        
        # 获取GPU内存使用情况
        memory_allocated = torch.cuda.memory_allocated() / (1024**3)  # GB
        memory_reserved = torch.cuda.memory_reserved() / (1024**3)   # GB
        
        # 如果内存使用过高，减小批次大小
        if memory_reserved > self.max_memory_gb * 0.9:
            new_batch_size = max(self.current_batch_size // 2, 1)
            if new_batch_size != self.current_batch_size:
                self.current_batch_size = new_batch_size
                logger.warning(f"GPU内存使用过高({memory_reserved:.1f}GB), "
                             f"调整批次大小为{new_batch_size}")
        
        # 如果内存使用较低，可以尝试增大批次大小
        elif memory_reserved < self.max_memory_gb * 0.6:
            new_batch_size = min(self.current_batch_size * 2, self.initial_batch_size * 2)
            if new_batch_size != self.current_batch_size:
                self.current_batch_size = new_batch_size
                logger.info(f"GPU内存充足({memory_reserved:.1f}GB), "
                           f"调整批次大小为{new_batch_size}")


def create_train_val_dataloaders(
    train_dataset: ShennongDataset,
    val_dataset: ShennongDataset,
    batch_size: int = 64,
    num_workers: int = 0,
    **kwargs
) -> tuple[ShennongDataLoader, ShennongDataLoader]:
    """
    创建训练和验证数据加载器
    
    Args:
        train_dataset: 训练数据集
        val_dataset: 验证数据集
        batch_size: 批次大小
        num_workers: 工作进程数
        **kwargs: 其他参数
        
    Returns:
        (训练数据加载器, 验证数据加载器)
    """
    train_loader = ShennongDataLoader(
        dataset=train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        **kwargs
    )
    
    val_loader = ShennongDataLoader(
        dataset=val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        **kwargs
    )
    
    return train_loader, val_loader
