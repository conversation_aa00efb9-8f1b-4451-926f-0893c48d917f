# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-29
# 描述: 神农框架预测子命令

"""
神农框架预测子命令

提供模型预测的命令行接口，支持批量预测和结果输出。
"""

import logging
import pandas as pd
from pathlib import Path
from typing import List, Optional

try:
    from configargparse import ArgumentParser, Namespace
except ImportError:
    from argparse import ArgumentParser, Namespace

from .utils import validate_file_path, validate_dir_path, print_banner
from ..models.shennong_core import ShennongFramework

logger = logging.getLogger(__name__)


class PredictSubcommand:
    """预测子命令类"""
    
    COMMAND = "predict"
    HELP = "使用训练好的模型进行预测"
    
    @classmethod
    def add(cls, subparsers, parents):
        """添加预测子命令到解析器"""
        parser = subparsers.add_parser(
            cls.COMMAND,
            help=cls.HELP,
            parents=parents,
            description="使用训练好的神农框架模型进行抗菌活性预测"
        )
        
        # 模型相关参数
        model_group = parser.add_argument_group("模型参数")
        model_group.add_argument(
            "--model-path",
            type=str,
            required=True,
            help="训练好的模型文件路径"
        )
        model_group.add_argument(
            "--model-type",
            type=str,
            choices=["checkpoint", "saved_model"],
            default="checkpoint",
            help="模型文件类型 (默认: checkpoint)"
        )
        
        # 输入数据参数
        input_group = parser.add_argument_group("输入数据参数")
        input_group.add_argument(
            "--input-path",
            type=str,
            help="输入数据CSV文件路径"
        )
        input_group.add_argument(
            "--smiles",
            type=str,
            nargs="+",
            help="直接指定SMILES字符串列表"
        )
        input_group.add_argument(
            "--smiles-column",
            type=str,
            default="smiles",
            help="SMILES列名 (默认: smiles)"
        )
        
        # 预测参数
        predict_group = parser.add_argument_group("预测参数")
        predict_group.add_argument(
            "--batch-size",
            type=int,
            default=32,
            help="预测批次大小 (默认: 32)"
        )
        predict_group.add_argument(
            "--return-attention",
            action="store_true",
            help="返回注意力权重"
        )
        predict_group.add_argument(
            "--return-uncertainty",
            action="store_true",
            help="返回不确定性估计"
        )
        predict_group.add_argument(
            "--return-mechanism",
            action="store_true",
            help="返回抗菌机制预测"
        )
        
        # 输出参数
        output_group = parser.add_argument_group("输出参数")
        output_group.add_argument(
            "--output-path",
            type=str,
            required=True,
            help="预测结果输出文件路径"
        )
        output_group.add_argument(
            "--output-format",
            type=str,
            choices=["csv", "json", "xlsx"],
            default="csv",
            help="输出文件格式 (默认: csv)"
        )
        output_group.add_argument(
            "--include-input",
            action="store_true",
            help="在输出中包含输入数据"
        )
        
        # 硬件参数
        hardware_group = parser.add_argument_group("硬件参数")
        hardware_group.add_argument(
            "--device",
            type=str,
            choices=["auto", "cpu", "cuda", "mps"],
            default="auto",
            help="计算设备 (默认: auto)"
        )
        hardware_group.add_argument(
            "--num-workers",
            type=int,
            default=4,
            help="数据加载器工作进程数 (默认: 4)"
        )
        
        parser.set_defaults(func=cls.func)
        return parser
    
    @classmethod
    def func(cls, args: Namespace):
        """执行预测命令"""
        print_banner("🔮 神农框架模型预测")
        
        # 验证参数
        cls._validate_args(args)
        
        # 加载模型
        model = cls._load_model(args)
        
        # 加载输入数据
        smiles_list = cls._load_input_data(args)
        
        # 执行预测
        logger.info("开始执行预测...")
        predictions = cls._predict(model, smiles_list, args)
        
        # 保存结果
        cls._save_results(predictions, smiles_list, args)
        
        logger.info("预测完成!")
        print("✅ 预测成功完成!")
    
    @classmethod
    def _validate_args(cls, args: Namespace):
        """验证命令行参数"""
        # 验证模型文件
        validate_file_path(args.model_path, must_exist=True)
        
        # 验证输入数据
        if not args.input_path and not args.smiles:
            raise ValueError("必须指定 --input-path 或 --smiles")
        
        if args.input_path and args.smiles:
            raise ValueError("--input-path 和 --smiles 不能同时指定")
        
        if args.input_path:
            validate_file_path(args.input_path, must_exist=True)
        
        # 验证输出路径
        output_path = Path(args.output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 验证数值参数
        if args.batch_size <= 0:
            raise ValueError("batch_size必须大于0")
    
    @classmethod
    def _load_model(cls, args: Namespace) -> ShennongFramework:
        """加载模型"""
        logger.info(f"加载模型: {args.model_path}")
        
        try:
            if args.model_type == "checkpoint":
                model = ShennongFramework.load_model(args.model_path)
            else:
                # TODO: 实现其他模型加载方式
                raise NotImplementedError(f"暂不支持模型类型: {args.model_type}")
            
            # 设置设备
            if args.device != "auto":
                model = model.to(args.device)
            
            model.eval()
            logger.info("模型加载完成")
            return model
            
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            raise
    
    @classmethod
    def _load_input_data(cls, args: Namespace) -> List[str]:
        """加载输入数据"""
        if args.smiles:
            logger.info(f"使用命令行指定的 {len(args.smiles)} 个SMILES")
            return args.smiles
        
        elif args.input_path:
            logger.info(f"从文件加载数据: {args.input_path}")
            
            try:
                df = pd.read_csv(args.input_path)
                
                if args.smiles_column not in df.columns:
                    raise ValueError(f"未找到SMILES列: {args.smiles_column}")
                
                smiles_list = df[args.smiles_column].tolist()
                logger.info(f"加载了 {len(smiles_list)} 个SMILES")
                return smiles_list
                
            except Exception as e:
                logger.error(f"数据加载失败: {e}")
                raise
    
    @classmethod
    def _predict(cls, model: ShennongFramework, smiles_list: List[str], args: Namespace) -> dict:
        """执行预测"""
        try:
            predictions = model.predict(
                smiles_list,
                batch_size=args.batch_size,
                return_attention=args.return_attention,
                return_uncertainty=args.return_uncertainty,
                return_mechanism=args.return_mechanism
            )
            
            logger.info(f"预测完成，处理了 {len(smiles_list)} 个分子")
            return predictions
            
        except Exception as e:
            logger.error(f"预测失败: {e}")
            raise
    
    @classmethod
    def _save_results(cls, predictions: dict, smiles_list: List[str], args: Namespace):
        """保存预测结果"""
        logger.info(f"保存结果到: {args.output_path}")
        
        try:
            # 构建结果数据框
            result_data = {}
            
            if args.include_input:
                result_data['smiles'] = smiles_list
            
            # 添加预测结果
            if 'activity' in predictions:
                result_data['predicted_activity'] = predictions['activity']
            
            if 'mechanism' in predictions and args.return_mechanism:
                result_data['predicted_mechanism'] = predictions['mechanism']
            
            if 'uncertainty' in predictions and args.return_uncertainty:
                result_data['uncertainty'] = predictions['uncertainty']
            
            # 创建数据框
            df = pd.DataFrame(result_data)
            
            # 保存文件
            if args.output_format == "csv":
                df.to_csv(args.output_path, index=False)
            elif args.output_format == "json":
                df.to_json(args.output_path, orient='records', indent=2)
            elif args.output_format == "xlsx":
                df.to_excel(args.output_path, index=False)
            
            logger.info("结果保存完成")
            
        except Exception as e:
            logger.error(f"结果保存失败: {e}")
            raise
