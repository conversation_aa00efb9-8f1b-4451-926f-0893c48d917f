# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-27
# 描述: 神农框架抗菌机制定义模块

"""
神农框架抗菌机制定义

定义各种抗菌机制的分类、特征和相关信息。
为机制感知的模型提供生物学知识基础。
"""

from typing import Dict, List, Optional, Any, Tuple
import torch
import torch.nn as nn
from dataclasses import dataclass
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class AntibacterialMechanismType(Enum):
    """抗菌机制类型枚举"""
    CELL_WALL_SYNTHESIS = "cell_wall_synthesis"
    PROTEIN_SYNTHESIS = "protein_synthesis"
    DNA_REPLICATION = "dna_replication"
    CELL_MEMBRANE = "cell_membrane"
    METABOLIC_PATHWAY = "metabolic_pathway"


@dataclass
class AntibacterialMechanism:
    """抗菌机制数据类"""
    name: str
    mechanism_type: AntibacterialMechanismType
    description: str
    targets: List[str]
    key_features: List[str]
    examples: List[str]
    gram_specificity: Optional[str] = None  # 'positive', 'negative', 'both'
    resistance_mechanisms: Optional[List[str]] = None
    
    def __post_init__(self):
        if self.resistance_mechanisms is None:
            self.resistance_mechanisms = []


# 抗菌机制定义
ANTIBACTERIAL_MECHANISMS = {
    'cell_wall_synthesis': AntibacterialMechanism(
        name='细胞壁合成抑制',
        mechanism_type=AntibacterialMechanismType.CELL_WALL_SYNTHESIS,
        description='抑制细菌细胞壁的合成，导致细胞壁缺陷和细胞裂解',
        targets=[
            'penicillin_binding_proteins',
            'peptidoglycan_synthesis',
            'transpeptidase',
            'transglycosylase'
        ],
        key_features=[
            'beta_lactam_ring',
            'glycopeptide_structure',
            'peptide_backbone',
            'cyclic_structure'
        ],
        examples=[
            'penicillin', 'ampicillin', 'cephalexin', 'vancomycin',
            'teicoplanin', 'bacitracin'
        ],
        gram_specificity='both',
        resistance_mechanisms=[
            'beta_lactamase_production',
            'pbp_modification',
            'efflux_pumps',
            'van_genes'
        ]
    ),
    
    'protein_synthesis': AntibacterialMechanism(
        name='蛋白质合成抑制',
        mechanism_type=AntibacterialMechanismType.PROTEIN_SYNTHESIS,
        description='干扰细菌核糖体功能，阻止蛋白质合成',
        targets=[
            '30S_ribosome',
            '50S_ribosome',
            '16S_rRNA',
            '23S_rRNA',
            'elongation_factors'
        ],
        key_features=[
            'aminoglycoside_structure',
            'macrolide_ring',
            'chloramphenicol_structure',
            'tetracycline_core'
        ],
        examples=[
            'streptomycin', 'gentamicin', 'erythromycin', 'azithromycin',
            'chloramphenicol', 'tetracycline', 'doxycycline'
        ],
        gram_specificity='both',
        resistance_mechanisms=[
            'ribosome_modification',
            'efflux_pumps',
            'enzymatic_inactivation',
            'target_protection'
        ]
    ),
    
    'dna_replication': AntibacterialMechanism(
        name='DNA复制抑制',
        mechanism_type=AntibacterialMechanismType.DNA_REPLICATION,
        description='干扰DNA复制、转录或修复过程',
        targets=[
            'dna_gyrase',
            'topoisomerase_iv',
            'dna_polymerase',
            'primase',
            'helicase'
        ],
        key_features=[
            'quinolone_core',
            'fluoroquinolone_structure',
            'nitroimidazole_group',
            'intercalating_agent'
        ],
        examples=[
            'ciprofloxacin', 'levofloxacin', 'nalidixic_acid',
            'metronidazole', 'tinidazole'
        ],
        gram_specificity='both',
        resistance_mechanisms=[
            'gyrase_mutation',
            'efflux_pumps',
            'qnr_genes',
            'target_modification'
        ]
    ),
    
    'cell_membrane': AntibacterialMechanism(
        name='细胞膜破坏',
        mechanism_type=AntibacterialMechanismType.CELL_MEMBRANE,
        description='破坏细胞膜完整性，导致细胞内容物泄漏',
        targets=[
            'phospholipid_bilayer',
            'membrane_proteins',
            'lipopolysaccharide',
            'peptidoglycan_layer'
        ],
        key_features=[
            'cationic_peptide',
            'lipopeptide_structure',
            'polymyxin_structure',
            'amphiphilic_molecule'
        ],
        examples=[
            'polymyxin_b', 'colistin', 'daptomycin',
            'nisin', 'gramicidin'
        ],
        gram_specificity='negative',  # 主要对革兰氏阴性菌有效
        resistance_mechanisms=[
            'lps_modification',
            'efflux_pumps',
            'membrane_composition_change'
        ]
    ),
    
    'metabolic_pathway': AntibacterialMechanism(
        name='代谢途径抑制',
        mechanism_type=AntibacterialMechanismType.METABOLIC_PATHWAY,
        description='抑制细菌必需的代谢途径，如叶酸合成',
        targets=[
            'dihydrofolate_reductase',
            'dihydropteroate_synthase',
            'thymidylate_synthase',
            'folate_pathway_enzymes'
        ],
        key_features=[
            'sulfonamide_group',
            'trimethoprim_structure',
            'folate_analog',
            'antimetabolite'
        ],
        examples=[
            'sulfamethoxazole', 'trimethoprim', 'sulfadiazine',
            'co-trimoxazole'
        ],
        gram_specificity='both',
        resistance_mechanisms=[
            'enzyme_overproduction',
            'alternative_pathways',
            'efflux_pumps',
            'target_modification'
        ]
    )
}


class MechanismClassifier(nn.Module):
    """
    抗菌机制分类器
    
    基于分子特征预测抗菌机制类型。
    """
    
    def __init__(
        self,
        input_dim: int,
        hidden_dim: int = 128,
        dropout: float = 0.3,
        num_mechanisms: Optional[int] = None
    ):
        """
        初始化机制分类器
        
        Args:
            input_dim: 输入特征维度
            hidden_dim: 隐藏层维度
            dropout: Dropout概率
            num_mechanisms: 机制类型数量
        """
        super().__init__()
        
        if num_mechanisms is None:
            num_mechanisms = len(ANTIBACTERIAL_MECHANISMS)
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.num_mechanisms = num_mechanisms
        
        # 分类网络
        self.classifier = nn.Sequential(
            nn.Linear(input_dim, hidden_dim * 2),
            nn.ReLU(),
            nn.BatchNorm1d(hidden_dim * 2),
            nn.Dropout(dropout),
            
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.BatchNorm1d(hidden_dim),
            nn.Dropout(dropout),
            
            nn.Linear(hidden_dim, num_mechanisms)
        )
        
        # 机制嵌入（用于解释）
        self.mechanism_embeddings = nn.Embedding(num_mechanisms, hidden_dim)
        
        # 初始化权重
        self._initialize_weights()
    
    def _initialize_weights(self):
        """初始化权重"""
        for module in self.classifier:
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
        
        nn.init.normal_(self.mechanism_embeddings.weight, std=0.1)
    
    def forward(
        self, 
        features: torch.Tensor,
        return_embeddings: bool = False
    ) -> Dict[str, torch.Tensor]:
        """
        前向传播
        
        Args:
            features: 输入特征 [batch_size, input_dim]
            return_embeddings: 是否返回机制嵌入
            
        Returns:
            输出字典
        """
        # 机制分类
        logits = self.classifier(features)
        probabilities = torch.softmax(logits, dim=-1)
        
        outputs = {
            'logits': logits,
            'probabilities': probabilities,
            'predictions': torch.argmax(logits, dim=-1)
        }
        
        if return_embeddings:
            # 获取所有机制的嵌入
            mechanism_ids = torch.arange(self.num_mechanisms, device=features.device)
            embeddings = self.mechanism_embeddings(mechanism_ids)
            outputs['mechanism_embeddings'] = embeddings
        
        return outputs
    
    def predict_mechanism(
        self, 
        features: torch.Tensor,
        threshold: float = 0.5
    ) -> List[Dict[str, Any]]:
        """
        预测机制类型
        
        Args:
            features: 输入特征
            threshold: 概率阈值
            
        Returns:
            预测结果列表
        """
        self.eval()
        with torch.no_grad():
            outputs = self(features)
            probabilities = outputs['probabilities']
            
            results = []
            mechanism_names = list(ANTIBACTERIAL_MECHANISMS.keys())
            
            for i in range(probabilities.size(0)):
                probs = probabilities[i]
                
                # 找到超过阈值的机制
                predicted_mechanisms = []
                for j, prob in enumerate(probs):
                    if prob > threshold:
                        mechanism_name = mechanism_names[j]
                        predicted_mechanisms.append({
                            'mechanism': mechanism_name,
                            'probability': prob.item(),
                            'description': ANTIBACTERIAL_MECHANISMS[mechanism_name].description
                        })
                
                # 按概率排序
                predicted_mechanisms.sort(key=lambda x: x['probability'], reverse=True)
                
                results.append({
                    'predicted_mechanisms': predicted_mechanisms,
                    'top_mechanism': predicted_mechanisms[0] if predicted_mechanisms else None,
                    'all_probabilities': probs.cpu().numpy()
                })
            
            return results


def get_mechanism_by_name(mechanism_name: str) -> Optional[AntibacterialMechanism]:
    """根据名称获取抗菌机制"""
    return ANTIBACTERIAL_MECHANISMS.get(mechanism_name)


def get_all_mechanisms() -> Dict[str, AntibacterialMechanism]:
    """获取所有抗菌机制"""
    return ANTIBACTERIAL_MECHANISMS.copy()


def get_mechanisms_by_type(mechanism_type: AntibacterialMechanismType) -> List[AntibacterialMechanism]:
    """根据类型获取抗菌机制"""
    return [
        mechanism for mechanism in ANTIBACTERIAL_MECHANISMS.values()
        if mechanism.mechanism_type == mechanism_type
    ]


def get_mechanisms_by_gram_specificity(gram_type: str) -> List[AntibacterialMechanism]:
    """根据革兰氏染色特异性获取机制"""
    return [
        mechanism for mechanism in ANTIBACTERIAL_MECHANISMS.values()
        if mechanism.gram_specificity in [gram_type, 'both']
    ]


def analyze_mechanism_features(smiles: str, mechanism_name: str) -> Dict[str, Any]:
    """
    分析分子的机制相关特征
    
    Args:
        smiles: SMILES字符串
        mechanism_name: 机制名称
        
    Returns:
        特征分析结果
    """
    mechanism = get_mechanism_by_name(mechanism_name)
    if mechanism is None:
        raise ValueError(f"未知的机制: {mechanism_name}")
    
    try:
        from rdkit import Chem
        from rdkit.Chem import rdMolDescriptors
        
        mol = Chem.MolFromSmiles(smiles)
        if mol is None:
            raise ValueError(f"无效的SMILES: {smiles}")
        
        analysis = {
            'mechanism': mechanism_name,
            'mechanism_type': mechanism.mechanism_type.value,
            'targets': mechanism.targets,
            'key_features_present': [],
            'molecular_properties': {},
            'similarity_to_examples': {}
        }
        
        # 检查关键特征
        for feature in mechanism.key_features:
            if _check_feature_presence(mol, feature):
                analysis['key_features_present'].append(feature)
        
        # 计算分子性质
        analysis['molecular_properties'] = {
            'molecular_weight': rdMolDescriptors.CalcExactMolWt(mol),
            'logp': rdMolDescriptors.CalcCrippenDescriptors(mol)[0],
            'hbd': rdMolDescriptors.CalcNumHBD(mol),
            'hba': rdMolDescriptors.CalcNumHBA(mol),
            'tpsa': rdMolDescriptors.CalcTPSA(mol),
            'rotatable_bonds': rdMolDescriptors.CalcNumRotatableBonds(mol)
        }
        
        return analysis
        
    except ImportError:
        logger.warning("RDKit未安装，无法进行分子分析")
        return {'error': 'RDKit未安装'}


def _check_feature_presence(mol, feature: str) -> bool:
    """检查分子中是否存在特定特征"""
    try:
        from rdkit import Chem
        
        # 定义特征对应的SMARTS模式
        feature_patterns = {
            'beta_lactam_ring': '[#6]1-[#6](=O)-[#7]-[#6]-1',
            'quinolone_core': 'c1cc2c(cc1)c(=O)c(cn2)C(=O)O',
            'sulfonamide_group': '[#16](=O)(=O)[#7]',
            'aminoglycoside_structure': '[#6]-[#8]-[#6]1[#6]([#7])[#6]([#8])[#6]([#8])[#6]([#8])[#6]1[#8]',
            'macrolide_ring': '[#6]1[#6][#6][#6][#6][#6][#6][#6][#6][#6][#6][#6][#6][#6][#8]1',
            'tetracycline_core': 'c1cc2c(cc1)C(=O)C3=C(C2=O)C(O)=C4C(=O)C(C(N)=O)=C(O)C4(O)C3',
            'polymyxin_structure': '[#7+]([#6])([#6])([#6])[#6]',
            'trimethoprim_structure': 'COc1cc(cc(OC)c1OC)Cc2cnc(N)nc2N'
        }
        
        pattern_smarts = feature_patterns.get(feature)
        if pattern_smarts:
            pattern = Chem.MolFromSmarts(pattern_smarts)
            if pattern:
                return mol.HasSubstructMatch(pattern)
        
        return False
        
    except Exception:
        return False


def create_mechanism_feature_matrix() -> torch.Tensor:
    """创建机制特征矩阵"""
    mechanisms = list(ANTIBACTERIAL_MECHANISMS.values())
    num_mechanisms = len(mechanisms)
    
    # 特征维度：目标数量 + 关键特征数量 + 其他属性
    max_targets = max(len(m.targets) for m in mechanisms)
    max_features = max(len(m.key_features) for m in mechanisms)
    
    # 创建特征矩阵
    feature_matrix = torch.zeros(num_mechanisms, max_targets + max_features + 3)
    
    for i, mechanism in enumerate(mechanisms):
        # 目标特征（one-hot编码）
        for j, target in enumerate(mechanism.targets[:max_targets]):
            feature_matrix[i, j] = 1.0
        
        # 关键特征（one-hot编码）
        for j, feature in enumerate(mechanism.key_features[:max_features]):
            feature_matrix[i, max_targets + j] = 1.0
        
        # 革兰氏特异性
        if mechanism.gram_specificity == 'positive':
            feature_matrix[i, -3] = 1.0
        elif mechanism.gram_specificity == 'negative':
            feature_matrix[i, -2] = 1.0
        else:  # 'both'
            feature_matrix[i, -1] = 1.0
    
    return feature_matrix
