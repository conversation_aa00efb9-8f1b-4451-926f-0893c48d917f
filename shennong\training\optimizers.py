# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-27
# 描述: 神农框架优化器配置

"""
神农框架优化器配置

提供优化器和学习率调度器的创建和配置功能。
"""

from typing import Dict, Any, Optional
import torch
import torch.optim as optim
from torch.optim.lr_scheduler import (
    CosineAnnealingLR, StepLR, ReduceLROnPlateau, 
    ExponentialLR, MultiStepLR, CyclicLR
)
import logging

logger = logging.getLogger(__name__)


def create_optimizer(
    model_parameters,
    optimizer_config: Dict[str, Any]
) -> torch.optim.Optimizer:
    """
    创建优化器
    
    Args:
        model_parameters: 模型参数
        optimizer_config: 优化器配置
        
    Returns:
        优化器实例
    """
    optimizer_type = optimizer_config.get('type', 'adamw').lower()
    learning_rate = optimizer_config.get('learning_rate', 1e-3)
    weight_decay = optimizer_config.get('weight_decay', 1e-4)
    
    # 获取优化器特定参数
    optimizer_params = optimizer_config.get('params', {})
    
    if optimizer_type == 'adam':
        optimizer = optim.Adam(
            model_parameters,
            lr=learning_rate,
            weight_decay=weight_decay,
            betas=optimizer_params.get('betas', (0.9, 0.999)),
            eps=optimizer_params.get('eps', 1e-8)
        )
    
    elif optimizer_type == 'adamw':
        optimizer = optim.AdamW(
            model_parameters,
            lr=learning_rate,
            weight_decay=weight_decay,
            betas=optimizer_params.get('betas', (0.9, 0.999)),
            eps=optimizer_params.get('eps', 1e-8)
        )
    
    elif optimizer_type == 'sgd':
        optimizer = optim.SGD(
            model_parameters,
            lr=learning_rate,
            weight_decay=weight_decay,
            momentum=optimizer_params.get('momentum', 0.9),
            nesterov=optimizer_params.get('nesterov', False)
        )
    
    elif optimizer_type == 'rmsprop':
        optimizer = optim.RMSprop(
            model_parameters,
            lr=learning_rate,
            weight_decay=weight_decay,
            alpha=optimizer_params.get('alpha', 0.99),
            eps=optimizer_params.get('eps', 1e-8)
        )
    
    elif optimizer_type == 'adagrad':
        optimizer = optim.Adagrad(
            model_parameters,
            lr=learning_rate,
            weight_decay=weight_decay,
            eps=optimizer_params.get('eps', 1e-10)
        )
    
    else:
        raise ValueError(f"不支持的优化器类型: {optimizer_type}")
    
    logger.info(f"创建优化器: {optimizer_type}, 学习率={learning_rate}, 权重衰减={weight_decay}")
    
    return optimizer


def create_scheduler(
    optimizer: torch.optim.Optimizer,
    scheduler_config: Dict[str, Any]
) -> Optional[torch.optim.lr_scheduler._LRScheduler]:
    """
    创建学习率调度器
    
    Args:
        optimizer: 优化器
        scheduler_config: 调度器配置
        
    Returns:
        学习率调度器实例
    """
    if not scheduler_config or not scheduler_config.get('enabled', True):
        return None
    
    scheduler_type = scheduler_config.get('type', 'cosine').lower()
    
    if scheduler_type == 'cosine':
        scheduler = CosineAnnealingLR(
            optimizer,
            T_max=scheduler_config.get('T_max', 100),
            eta_min=scheduler_config.get('eta_min', 1e-6),
            last_epoch=scheduler_config.get('last_epoch', -1)
        )
    
    elif scheduler_type == 'step':
        scheduler = StepLR(
            optimizer,
            step_size=scheduler_config.get('step_size', 30),
            gamma=scheduler_config.get('gamma', 0.1),
            last_epoch=scheduler_config.get('last_epoch', -1)
        )
    
    elif scheduler_type == 'multistep':
        scheduler = MultiStepLR(
            optimizer,
            milestones=scheduler_config.get('milestones', [30, 60, 90]),
            gamma=scheduler_config.get('gamma', 0.1),
            last_epoch=scheduler_config.get('last_epoch', -1)
        )
    
    elif scheduler_type == 'exponential':
        scheduler = ExponentialLR(
            optimizer,
            gamma=scheduler_config.get('gamma', 0.95),
            last_epoch=scheduler_config.get('last_epoch', -1)
        )
    
    elif scheduler_type == 'plateau':
        scheduler = ReduceLROnPlateau(
            optimizer,
            mode=scheduler_config.get('mode', 'min'),
            factor=scheduler_config.get('factor', 0.5),
            patience=scheduler_config.get('patience', 10),
            threshold=scheduler_config.get('threshold', 1e-4),
            threshold_mode=scheduler_config.get('threshold_mode', 'rel'),
            cooldown=scheduler_config.get('cooldown', 0),
            min_lr=scheduler_config.get('min_lr', 1e-6),
            eps=scheduler_config.get('eps', 1e-8)
        )
    
    elif scheduler_type == 'cyclic':
        scheduler = CyclicLR(
            optimizer,
            base_lr=scheduler_config.get('base_lr', 1e-5),
            max_lr=scheduler_config.get('max_lr', 1e-2),
            step_size_up=scheduler_config.get('step_size_up', 2000),
            step_size_down=scheduler_config.get('step_size_down', None),
            mode=scheduler_config.get('mode', 'triangular'),
            gamma=scheduler_config.get('gamma', 1.0),
            scale_fn=scheduler_config.get('scale_fn', None),
            scale_mode=scheduler_config.get('scale_mode', 'cycle'),
            cycle_momentum=scheduler_config.get('cycle_momentum', True),
            base_momentum=scheduler_config.get('base_momentum', 0.8),
            max_momentum=scheduler_config.get('max_momentum', 0.9),
            last_epoch=scheduler_config.get('last_epoch', -1)
        )
    
    else:
        raise ValueError(f"不支持的调度器类型: {scheduler_type}")
    
    logger.info(f"创建学习率调度器: {scheduler_type}")
    
    return scheduler


def get_optimizer_info(optimizer: torch.optim.Optimizer) -> Dict[str, Any]:
    """
    获取优化器信息
    
    Args:
        optimizer: 优化器
        
    Returns:
        优化器信息字典
    """
    info = {
        'type': type(optimizer).__name__,
        'param_groups': len(optimizer.param_groups),
        'state_dict_keys': list(optimizer.state_dict().keys())
    }
    
    # 获取第一个参数组的信息
    if optimizer.param_groups:
        first_group = optimizer.param_groups[0]
        info['learning_rate'] = first_group.get('lr', 'N/A')
        info['weight_decay'] = first_group.get('weight_decay', 'N/A')
        
        # 特定优化器的参数
        if 'betas' in first_group:
            info['betas'] = first_group['betas']
        if 'momentum' in first_group:
            info['momentum'] = first_group['momentum']
        if 'eps' in first_group:
            info['eps'] = first_group['eps']
    
    return info


def get_scheduler_info(scheduler: torch.optim.lr_scheduler._LRScheduler) -> Dict[str, Any]:
    """
    获取调度器信息
    
    Args:
        scheduler: 学习率调度器
        
    Returns:
        调度器信息字典
    """
    if scheduler is None:
        return {'type': 'None'}
    
    info = {
        'type': type(scheduler).__name__,
        'last_epoch': scheduler.last_epoch,
        'state_dict_keys': list(scheduler.state_dict().keys())
    }
    
    # 特定调度器的参数
    if hasattr(scheduler, 'T_max'):
        info['T_max'] = scheduler.T_max
    if hasattr(scheduler, 'eta_min'):
        info['eta_min'] = scheduler.eta_min
    if hasattr(scheduler, 'step_size'):
        info['step_size'] = scheduler.step_size
    if hasattr(scheduler, 'gamma'):
        info['gamma'] = scheduler.gamma
    if hasattr(scheduler, 'patience'):
        info['patience'] = scheduler.patience
    if hasattr(scheduler, 'factor'):
        info['factor'] = scheduler.factor
    
    return info


class WarmupScheduler:
    """
    学习率预热调度器
    
    在训练初期逐渐增加学习率，然后使用主调度器。
    """
    
    def __init__(
        self,
        optimizer: torch.optim.Optimizer,
        warmup_epochs: int,
        main_scheduler: Optional[torch.optim.lr_scheduler._LRScheduler] = None,
        warmup_start_lr: float = 1e-6
    ):
        """
        初始化预热调度器
        
        Args:
            optimizer: 优化器
            warmup_epochs: 预热轮数
            main_scheduler: 主调度器
            warmup_start_lr: 预热起始学习率
        """
        self.optimizer = optimizer
        self.warmup_epochs = warmup_epochs
        self.main_scheduler = main_scheduler
        self.warmup_start_lr = warmup_start_lr
        
        # 保存原始学习率
        self.base_lrs = [group['lr'] for group in optimizer.param_groups]
        
        self.current_epoch = 0
        
        logger.info(f"初始化预热调度器: 预热轮数={warmup_epochs}, 起始学习率={warmup_start_lr}")
    
    def step(self, epoch: Optional[int] = None):
        """更新学习率"""
        if epoch is not None:
            self.current_epoch = epoch
        else:
            self.current_epoch += 1
        
        if self.current_epoch < self.warmup_epochs:
            # 预热阶段：线性增加学习率
            warmup_factor = self.current_epoch / self.warmup_epochs
            
            for i, param_group in enumerate(self.optimizer.param_groups):
                lr = self.warmup_start_lr + (self.base_lrs[i] - self.warmup_start_lr) * warmup_factor
                param_group['lr'] = lr
        
        else:
            # 主调度器阶段
            if self.main_scheduler is not None:
                if hasattr(self.main_scheduler, 'step'):
                    self.main_scheduler.step()
    
    def get_last_lr(self):
        """获取最后的学习率"""
        return [group['lr'] for group in self.optimizer.param_groups]
    
    def state_dict(self):
        """获取状态字典"""
        state = {
            'current_epoch': self.current_epoch,
            'base_lrs': self.base_lrs,
            'warmup_epochs': self.warmup_epochs,
            'warmup_start_lr': self.warmup_start_lr
        }
        
        if self.main_scheduler is not None:
            state['main_scheduler'] = self.main_scheduler.state_dict()
        
        return state
    
    def load_state_dict(self, state_dict):
        """加载状态字典"""
        self.current_epoch = state_dict['current_epoch']
        self.base_lrs = state_dict['base_lrs']
        self.warmup_epochs = state_dict['warmup_epochs']
        self.warmup_start_lr = state_dict['warmup_start_lr']
        
        if self.main_scheduler is not None and 'main_scheduler' in state_dict:
            self.main_scheduler.load_state_dict(state_dict['main_scheduler'])
