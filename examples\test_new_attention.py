# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-27
# 描述: 测试新的注意力机制

"""
测试新的注意力机制

验证基于化学结构的注意力机制的功能和性能。
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

import torch
import torch.nn as nn
import numpy as np
import logging
from typing import Dict, Any

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_individual_attention_mechanisms():
    """测试各个注意力机制"""
    print("\n🔬 测试各个注意力机制")
    print("=" * 60)
    
    try:
        from shennong.nn.structure_aware_attention import (
            PharmacophoreAttention,
            ScaffoldSimilarityAttention,
            AtomImportanceAttention,
            DescriptorCorrelationAttention,
            MultiScaleStructureAttention
        )
        
        # 测试参数
        batch_size = 4
        seq_len = 10
        feature_dim = 128
        
        # 创建测试数据
        test_features = torch.randn(batch_size, seq_len, feature_dim)
        structure_info = {
            'atom_types': torch.randint(0, 10, (batch_size, seq_len))
        }
        
        # 测试各个注意力机制
        attention_mechanisms = {
            'PharmacophoreAttention': PharmacophoreAttention(feature_dim),
            'ScaffoldSimilarityAttention': ScaffoldSimilarityAttention(feature_dim),
            'AtomImportanceAttention': AtomImportanceAttention(feature_dim),
            'DescriptorCorrelationAttention': DescriptorCorrelationAttention(feature_dim),
            'MultiScaleStructureAttention': MultiScaleStructureAttention(feature_dim)
        }
        
        results = {}
        
        for name, mechanism in attention_mechanisms.items():
            try:
                print(f"\n🧪 测试 {name}...")
                
                # 前向传播
                if name == 'MultiScaleStructureAttention':
                    output, attention_weights = mechanism(test_features, structure_info, return_attention=True)
                else:
                    output, attention_weights = mechanism(test_features, structure_info, return_attention=True)
                
                # 检查输出形状
                expected_shape = test_features.shape
                if output.shape == expected_shape:
                    print(f"  ✅ 输出形状正确: {output.shape}")
                else:
                    print(f"  ❌ 输出形状错误: 期望{expected_shape}, 实际{output.shape}")
                
                # 检查注意力权重
                if attention_weights is not None:
                    if isinstance(attention_weights, dict):
                        print(f"  ✅ 返回注意力权重字典，包含{len(attention_weights)}个键")
                        for key, weights in attention_weights.items():
                            if isinstance(weights, torch.Tensor):
                                print(f"    {key}: {weights.shape}")
                    else:
                        print(f"  ✅ 返回注意力权重: {attention_weights.shape}")
                else:
                    print(f"  ⚠️ 未返回注意力权重")
                
                # 检查梯度
                loss = output.sum()
                loss.backward()
                
                has_grad = any(p.grad is not None for p in mechanism.parameters())
                print(f"  {'✅' if has_grad else '❌'} 梯度计算: {'正常' if has_grad else '异常'}")
                
                # 计算参数数量
                num_params = sum(p.numel() for p in mechanism.parameters())
                print(f"  📊 参数数量: {num_params:,}")
                
                results[name] = {
                    'success': True,
                    'output_shape': output.shape,
                    'has_attention': attention_weights is not None,
                    'has_gradients': has_grad,
                    'num_parameters': num_params
                }
                
            except Exception as e:
                print(f"  ❌ 测试失败: {e}")
                results[name] = {'success': False, 'error': str(e)}
        
        return results
        
    except Exception as e:
        print(f"❌ 注意力机制测试失败: {e}")
        return {}


def test_unified_attention():
    """测试统一注意力机制"""
    print("\n🔗 测试统一注意力机制")
    print("=" * 60)
    
    try:
        from shennong.nn.unified_attention import UnifiedStructureAttention, AttentionStrategy
        
        # 测试参数
        batch_size = 4
        seq_len = 10
        feature_dim = 128
        
        # 创建测试数据
        test_features = torch.randn(batch_size, seq_len, feature_dim)
        structure_info = {
            'atom_types': torch.randint(0, 10, (batch_size, seq_len))
        }
        
        # 测试不同策略
        strategies = [
            AttentionStrategy.PHARMACOPHORE,
            AttentionStrategy.SCAFFOLD,
            AttentionStrategy.ATOM_IMPORTANCE,
            AttentionStrategy.DESCRIPTOR_CORRELATION,
            AttentionStrategy.MULTI_SCALE,
            AttentionStrategy.ADAPTIVE,
            AttentionStrategy.ENSEMBLE
        ]
        
        results = {}
        
        for strategy in strategies:
            try:
                print(f"\n🎯 测试策略: {strategy.value}")
                
                # 创建统一注意力机制
                unified_attention = UnifiedStructureAttention(
                    feature_dim=feature_dim,
                    strategy=strategy
                )
                
                # 前向传播
                output, attention_info = unified_attention(
                    test_features, structure_info, return_attention=True
                )
                
                # 检查输出
                print(f"  ✅ 输出形状: {output.shape}")
                
                if attention_info:
                    print(f"  ✅ 注意力信息: {list(attention_info.keys())}")
                
                # 检查策略信息
                strategy_info = unified_attention.get_strategy_info()
                print(f"  📊 参数数量: {strategy_info['num_parameters']:,}")
                
                # 测试策略切换
                if strategy != AttentionStrategy.ADAPTIVE:
                    unified_attention.set_strategy(AttentionStrategy.PHARMACOPHORE)
                    print(f"  🔄 策略切换测试: 成功")
                
                results[strategy.value] = {
                    'success': True,
                    'output_shape': output.shape,
                    'has_attention_info': attention_info is not None,
                    'num_parameters': strategy_info['num_parameters']
                }
                
            except Exception as e:
                print(f"  ❌ 策略测试失败: {e}")
                results[strategy.value] = {'success': False, 'error': str(e)}
        
        return results
        
    except Exception as e:
        print(f"❌ 统一注意力测试失败: {e}")
        return {}


def test_biologically_informed_attention():
    """测试生物学指导的注意力机制"""
    print("\n🧬 测试生物学指导的注意力机制")
    print("=" * 60)
    
    try:
        from shennong.nn.unified_attention import BiologicallyInformedAttention
        
        # 测试参数
        batch_size = 4
        graph_seq_len = 15
        expert_seq_len = 1  # 专家特征通常是分子级别的
        graph_dim = 300
        expert_dim = 1613  # Mordred描述符维度
        output_dim = 256
        
        # 创建测试数据
        graph_features = torch.randn(batch_size, graph_seq_len, graph_dim)
        expert_features = torch.randn(batch_size, expert_seq_len, expert_dim)
        
        structure_info = {
            'atom_types': torch.randint(0, 10, (batch_size, graph_seq_len))
        }
        
        # 创建生物学指导注意力
        bio_attention = BiologicallyInformedAttention(
            graph_dim=graph_dim,
            expert_dim=expert_dim,
            output_dim=output_dim
        )
        
        print(f"📊 模型参数数量: {sum(p.numel() for p in bio_attention.parameters()):,}")
        
        # 前向传播
        output, attention_info = bio_attention(
            graph_features, expert_features, structure_info, return_attention=True
        )
        
        print(f"✅ 输出形状: {output.shape}")
        print(f"✅ 期望形状: ({batch_size}, {output_dim})")
        
        if attention_info:
            print(f"✅ 注意力信息包含: {list(attention_info.keys())}")
            
            for key, value in attention_info.items():
                if isinstance(value, torch.Tensor):
                    print(f"  {key}: {value.shape}")
                elif isinstance(value, dict):
                    print(f"  {key}: 字典，包含{len(value)}个键")
        
        # 测试梯度
        loss = output.sum()
        loss.backward()
        
        has_grad = any(p.grad is not None for p in bio_attention.parameters())
        print(f"{'✅' if has_grad else '❌'} 梯度计算: {'正常' if has_grad else '异常'}")
        
        return {
            'success': True,
            'output_shape': output.shape,
            'expected_shape': (batch_size, output_dim),
            'has_attention_info': attention_info is not None,
            'has_gradients': has_grad
        }
        
    except Exception as e:
        print(f"❌ 生物学指导注意力测试失败: {e}")
        import traceback
        traceback.print_exc()
        return {'success': False, 'error': str(e)}


def test_attention_performance():
    """测试注意力机制性能"""
    print("\n⚡ 测试注意力机制性能")
    print("=" * 60)
    
    try:
        from shennong.nn.unified_attention import UnifiedStructureAttention, AttentionStrategy
        import time
        
        # 测试参数
        batch_size = 8
        seq_len = 20
        feature_dim = 256
        num_iterations = 10
        
        # 创建测试数据
        test_features = torch.randn(batch_size, seq_len, feature_dim)
        
        strategies = [
            AttentionStrategy.PHARMACOPHORE,
            AttentionStrategy.MULTI_SCALE,
            AttentionStrategy.ADAPTIVE
        ]
        
        performance_results = {}
        
        for strategy in strategies:
            print(f"\n🏃 测试策略性能: {strategy.value}")
            
            # 创建注意力机制
            attention = UnifiedStructureAttention(
                feature_dim=feature_dim,
                strategy=strategy
            )
            
            # 预热
            for _ in range(3):
                _ = attention(test_features)
            
            # 性能测试
            start_time = time.time()
            
            for _ in range(num_iterations):
                output, _ = attention(test_features, return_attention=True)
            
            end_time = time.time()
            
            avg_time = (end_time - start_time) / num_iterations
            throughput = batch_size / avg_time
            
            print(f"  ⏱️ 平均时间: {avg_time:.4f}秒")
            print(f"  🚀 吞吐量: {throughput:.2f} 样本/秒")
            
            performance_results[strategy.value] = {
                'avg_time': avg_time,
                'throughput': throughput
            }
        
        return performance_results
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        return {}


def main():
    """主测试函数"""
    print("🧬 神农框架新注意力机制测试")
    print("=" * 80)
    
    # 运行所有测试
    tests = [
        ("各个注意力机制", test_individual_attention_mechanisms),
        ("统一注意力机制", test_unified_attention),
        ("生物学指导注意力", test_biologically_informed_attention),
        ("性能测试", test_attention_performance),
    ]
    
    all_results = {}
    
    for test_name, test_func in tests:
        try:
            print(f"\n🔬 运行测试: {test_name}")
            results = test_func()
            all_results[test_name] = results
            
            if results:
                success_count = sum(1 for r in results.values() if r.get('success', False))
                total_count = len(results)
                print(f"✅ {test_name}: {success_count}/{total_count} 通过")
            else:
                print(f"⚠️ {test_name}: 无结果")
                
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            all_results[test_name] = {'error': str(e)}
    
    # 总结
    print(f"\n📊 测试总结")
    print("=" * 40)
    
    total_tests = 0
    passed_tests = 0
    
    for test_name, results in all_results.items():
        if isinstance(results, dict) and 'error' not in results:
            for sub_test, result in results.items():
                total_tests += 1
                if result.get('success', False):
                    passed_tests += 1
                    print(f"✅ {test_name} - {sub_test}")
                else:
                    print(f"❌ {test_name} - {sub_test}: {result.get('error', '未知错误')}")
        else:
            total_tests += 1
            if 'error' not in results:
                passed_tests += 1
                print(f"✅ {test_name}")
            else:
                print(f"❌ {test_name}: {results.get('error', '未知错误')}")
    
    print(f"\n🎯 总体结果: {passed_tests}/{total_tests} 测试通过 ({passed_tests/total_tests*100:.1f}%)")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！新注意力机制工作正常！")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关问题")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
