# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-27
# 描述: 神农框架评估指标

"""
神农框架评估指标

提供抗菌化合物预测的专业化评估指标。
"""

from typing import Dict, Any, Optional, List, Tuple
import torch
import torch.nn as nn
import numpy as np
from sklearn.metrics import (
    mean_squared_error, mean_absolute_error, r2_score,
    accuracy_score, precision_score, recall_score, f1_score,
    roc_auc_score, average_precision_score, confusion_matrix
)
import logging

logger = logging.getLogger(__name__)


class RegressionMetrics:
    """
    回归任务评估指标
    
    计算RMSE、MAE、R²等回归指标。
    """
    
    def __init__(self):
        """初始化回归指标"""
        self.metrics = {}
    
    def compute(
        self, 
        predictions: torch.Tensor, 
        targets: torch.Tensor,
        mask: Optional[torch.Tensor] = None
    ) -> Dict[str, float]:
        """
        计算回归指标
        
        Args:
            predictions: 预测值
            targets: 真实值
            mask: 有效值掩码
            
        Returns:
            指标字典
        """
        # 转换为numpy数组
        if isinstance(predictions, torch.Tensor):
            predictions = predictions.detach().cpu().numpy()
        if isinstance(targets, torch.Tensor):
            targets = targets.detach().cpu().numpy()
        if mask is not None and isinstance(mask, torch.Tensor):
            mask = mask.detach().cpu().numpy().astype(bool)
        
        # 应用掩码
        if mask is not None:
            predictions = predictions[mask]
            targets = targets[mask]
        
        # 移除无效值
        valid_mask = ~(np.isnan(predictions) | np.isnan(targets) | 
                      np.isinf(predictions) | np.isinf(targets))
        predictions = predictions[valid_mask]
        targets = targets[valid_mask]
        
        if len(predictions) == 0:
            return {
                'rmse': float('nan'),
                'mae': float('nan'),
                'r2': float('nan'),
                'pearson_r': float('nan')
            }
        
        # 计算指标
        rmse = np.sqrt(mean_squared_error(targets, predictions))
        mae = mean_absolute_error(targets, predictions)
        r2 = r2_score(targets, predictions)
        
        # 皮尔逊相关系数
        pearson_r = np.corrcoef(targets, predictions)[0, 1] if len(targets) > 1 else 0.0
        
        return {
            'rmse': float(rmse),
            'mae': float(mae),
            'r2': float(r2),
            'pearson_r': float(pearson_r)
        }


class ClassificationMetrics:
    """
    分类任务评估指标
    
    计算准确率、精确率、召回率、F1分数等分类指标。
    """
    
    def __init__(self, num_classes: int = 2, average: str = 'weighted'):
        """
        初始化分类指标
        
        Args:
            num_classes: 类别数量
            average: 平均方式
        """
        self.num_classes = num_classes
        self.average = average
    
    def compute(
        self, 
        predictions: torch.Tensor, 
        targets: torch.Tensor,
        probabilities: Optional[torch.Tensor] = None
    ) -> Dict[str, float]:
        """
        计算分类指标
        
        Args:
            predictions: 预测类别
            targets: 真实类别
            probabilities: 预测概率
            
        Returns:
            指标字典
        """
        # 转换为numpy数组
        if isinstance(predictions, torch.Tensor):
            predictions = predictions.detach().cpu().numpy()
        if isinstance(targets, torch.Tensor):
            targets = targets.detach().cpu().numpy()
        if probabilities is not None and isinstance(probabilities, torch.Tensor):
            probabilities = probabilities.detach().cpu().numpy()
        
        # 基础分类指标
        accuracy = accuracy_score(targets, predictions)
        precision = precision_score(targets, predictions, average=self.average, zero_division=0)
        recall = recall_score(targets, predictions, average=self.average, zero_division=0)
        f1 = f1_score(targets, predictions, average=self.average, zero_division=0)
        
        metrics = {
            'accuracy': float(accuracy),
            'precision': float(precision),
            'recall': float(recall),
            'f1': float(f1)
        }
        
        # AUC指标（需要概率）
        if probabilities is not None:
            try:
                if self.num_classes == 2:
                    # 二分类
                    if probabilities.ndim == 2:
                        probs = probabilities[:, 1]  # 正类概率
                    else:
                        probs = probabilities
                    
                    auc_roc = roc_auc_score(targets, probs)
                    auc_pr = average_precision_score(targets, probs)
                    
                    metrics['auc_roc'] = float(auc_roc)
                    metrics['auc_pr'] = float(auc_pr)
                
                else:
                    # 多分类
                    auc_roc = roc_auc_score(targets, probabilities, 
                                          multi_class='ovr', average=self.average)
                    metrics['auc_roc'] = float(auc_roc)
            
            except Exception as e:
                logger.warning(f"AUC计算失败: {e}")
        
        return metrics


class AntibacterialMetrics:
    """
    抗菌化合物专用评估指标
    
    包含MIC预测、活性分类、虚拟筛选等专业指标。
    """
    
    def __init__(self, mic_threshold: float = 10.0):
        """
        初始化抗菌指标
        
        Args:
            mic_threshold: MIC活性阈值 (μg/mL)
        """
        self.mic_threshold = mic_threshold
        self.regression_metrics = RegressionMetrics()
        self.classification_metrics = ClassificationMetrics()
    
    def compute_mic_metrics(
        self, 
        predicted_mic: torch.Tensor, 
        true_mic: torch.Tensor
    ) -> Dict[str, float]:
        """
        计算MIC预测指标
        
        Args:
            predicted_mic: 预测的MIC值
            true_mic: 真实的MIC值
            
        Returns:
            MIC指标字典
        """
        # 基础回归指标
        regression_results = self.regression_metrics.compute(predicted_mic, true_mic)
        
        # MIC特异性指标
        if isinstance(predicted_mic, torch.Tensor):
            predicted_mic = predicted_mic.detach().cpu().numpy()
        if isinstance(true_mic, torch.Tensor):
            true_mic = true_mic.detach().cpu().numpy()
        
        # 计算在不同倍数范围内的准确率
        fold_accuracies = {}
        for fold in [2, 4, 8]:
            within_fold = np.abs(np.log2(predicted_mic / true_mic)) <= np.log2(fold)
            fold_accuracies[f'within_{fold}fold'] = float(np.mean(within_fold))
        
        # 活性分类准确率
        true_active = true_mic <= self.mic_threshold
        pred_active = predicted_mic <= self.mic_threshold
        activity_accuracy = float(np.mean(true_active == pred_active))
        
        mic_metrics = {
            **regression_results,
            **fold_accuracies,
            'activity_accuracy': activity_accuracy,
            'mic_threshold': self.mic_threshold
        }
        
        return mic_metrics
    
    def compute_virtual_screening_metrics(
        self, 
        predictions: torch.Tensor, 
        targets: torch.Tensor,
        enrichment_factors: List[float] = [0.01, 0.05, 0.1]
    ) -> Dict[str, float]:
        """
        计算虚拟筛选指标
        
        Args:
            predictions: 预测分数
            targets: 真实活性标签
            enrichment_factors: 富集因子计算的百分比
            
        Returns:
            虚拟筛选指标
        """
        if isinstance(predictions, torch.Tensor):
            predictions = predictions.detach().cpu().numpy()
        if isinstance(targets, torch.Tensor):
            targets = targets.detach().cpu().numpy()
        
        # 按预测分数排序
        sorted_indices = np.argsort(predictions)[::-1]  # 降序
        sorted_targets = targets[sorted_indices]
        
        total_actives = np.sum(targets)
        total_compounds = len(targets)
        
        vs_metrics = {}
        
        # 计算不同百分比的富集因子
        for ef_percent in enrichment_factors:
            top_n = int(ef_percent * total_compounds)
            if top_n == 0:
                continue
            
            actives_in_top_n = np.sum(sorted_targets[:top_n])
            expected_actives = ef_percent * total_actives
            
            if expected_actives > 0:
                enrichment_factor = actives_in_top_n / expected_actives
            else:
                enrichment_factor = 0.0
            
            vs_metrics[f'ef_{ef_percent:.0%}'] = float(enrichment_factor)
        
        # 计算AUC
        try:
            auc = roc_auc_score(targets, predictions)
            vs_metrics['auc'] = float(auc)
        except:
            vs_metrics['auc'] = 0.0
        
        return vs_metrics
    
    def compute_mechanism_metrics(
        self, 
        predicted_mechanisms: torch.Tensor, 
        true_mechanisms: torch.Tensor
    ) -> Dict[str, float]:
        """
        计算机制预测指标
        
        Args:
            predicted_mechanisms: 预测的机制
            true_mechanisms: 真实的机制
            
        Returns:
            机制预测指标
        """
        # 如果是多标签分类
        if true_mechanisms.dim() > 1 and true_mechanisms.size(-1) > 1:
            # 多标签分类指标
            if isinstance(predicted_mechanisms, torch.Tensor):
                pred_probs = torch.sigmoid(predicted_mechanisms).detach().cpu().numpy()
                pred_labels = (pred_probs > 0.5).astype(int)
            else:
                pred_labels = predicted_mechanisms
            
            if isinstance(true_mechanisms, torch.Tensor):
                true_labels = true_mechanisms.detach().cpu().numpy()
            else:
                true_labels = true_mechanisms
            
            # 计算每个机制的指标
            mechanism_metrics = {}
            for i in range(true_labels.shape[1]):
                mechanism_name = f'mechanism_{i}'
                try:
                    precision = precision_score(true_labels[:, i], pred_labels[:, i], zero_division=0)
                    recall = recall_score(true_labels[:, i], pred_labels[:, i], zero_division=0)
                    f1 = f1_score(true_labels[:, i], pred_labels[:, i], zero_division=0)
                    
                    mechanism_metrics[f'{mechanism_name}_precision'] = float(precision)
                    mechanism_metrics[f'{mechanism_name}_recall'] = float(recall)
                    mechanism_metrics[f'{mechanism_name}_f1'] = float(f1)
                except:
                    mechanism_metrics[f'{mechanism_name}_precision'] = 0.0
                    mechanism_metrics[f'{mechanism_name}_recall'] = 0.0
                    mechanism_metrics[f'{mechanism_name}_f1'] = 0.0
            
            # 整体指标
            try:
                overall_precision = precision_score(true_labels, pred_labels, average='macro', zero_division=0)
                overall_recall = recall_score(true_labels, pred_labels, average='macro', zero_division=0)
                overall_f1 = f1_score(true_labels, pred_labels, average='macro', zero_division=0)
                
                mechanism_metrics['overall_precision'] = float(overall_precision)
                mechanism_metrics['overall_recall'] = float(overall_recall)
                mechanism_metrics['overall_f1'] = float(overall_f1)
            except:
                mechanism_metrics['overall_precision'] = 0.0
                mechanism_metrics['overall_recall'] = 0.0
                mechanism_metrics['overall_f1'] = 0.0
            
            return mechanism_metrics
        
        else:
            # 单标签分类
            if predicted_mechanisms.dim() > 1:
                predicted_mechanisms = torch.argmax(predicted_mechanisms, dim=-1)
            
            return self.classification_metrics.compute(predicted_mechanisms, true_mechanisms)


class MultiTaskMetrics:
    """
    多任务评估指标
    
    综合评估多个任务的性能。
    """
    
    def __init__(self, task_configs: Dict[str, Dict[str, Any]]):
        """
        初始化多任务指标
        
        Args:
            task_configs: 任务配置字典
        """
        self.task_configs = task_configs
        self.task_metrics = {}
        
        for task_name, config in task_configs.items():
            task_type = config.get('type', 'regression')
            
            if task_type == 'regression':
                self.task_metrics[task_name] = RegressionMetrics()
            elif task_type == 'classification':
                num_classes = config.get('num_classes', 2)
                self.task_metrics[task_name] = ClassificationMetrics(num_classes)
            elif task_type == 'antibacterial':
                mic_threshold = config.get('mic_threshold', 10.0)
                self.task_metrics[task_name] = AntibacterialMetrics(mic_threshold)
    
    def compute(
        self, 
        predictions: Dict[str, torch.Tensor], 
        targets: Dict[str, torch.Tensor]
    ) -> Dict[str, Dict[str, float]]:
        """
        计算多任务指标
        
        Args:
            predictions: 各任务预测结果
            targets: 各任务真实值
            
        Returns:
            多任务指标字典
        """
        all_metrics = {}
        
        for task_name in self.task_configs.keys():
            if task_name not in predictions or task_name not in targets:
                continue
            
            task_pred = predictions[task_name]
            task_target = targets[task_name]
            
            if task_name in self.task_metrics:
                if isinstance(self.task_metrics[task_name], AntibacterialMetrics):
                    task_metrics = self.task_metrics[task_name].compute_mic_metrics(
                        task_pred, task_target
                    )
                else:
                    task_metrics = self.task_metrics[task_name].compute(
                        task_pred, task_target
                    )
                
                all_metrics[task_name] = task_metrics
        
        return all_metrics
