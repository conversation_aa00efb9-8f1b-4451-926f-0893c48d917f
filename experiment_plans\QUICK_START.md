# 🚀 神农框架实验快速启动指南

**作者**: ZK  
**邮箱**: <EMAIL>  
**日期**: 2025-06-30  

## ⚡ 5分钟快速开始

### 如果您想立即开始实验:

#### 1. 选择实验方案 (30秒)
```bash
# 推荐方案: 简化验证 (4周完成)
实验文件: 02_simplified_validation_plan.md + 03_practical_action_plan.md

# 完整方案: 学术验证 (16周完成)  
实验文件: 01_framework_validation_plan.md
```

#### 2. 检查数据需求 (2分钟)
```bash
# 阅读数据需求
cat 06_data_requirements.md

# 核心需求:
- ChEMBL抗菌活性数据 (≥5000个化合物)
- MIC值 < 16 μg/mL 定义为活性
- SMILES格式分子结构
```

#### 3. 立即行动清单 (2分钟)
```bash
# 本周必须完成:
□ 下载ChEMBL抗菌数据
□ 安装ChemProp: pip install chemprop  
□ 安装AutoGluon: pip install autogluon
□ 生成Mordred特征: python scripts/generate_features.py
```

## 📋 实验方案选择指南

### 🟢 推荐: 简化验证方案
**适合**: 时间有限，专注实用价值
```
时间: 4周
对比: ChemProp vs AutoGluon vs Shennong
任务: 抗菌化合物分类
目标: 证明药物筛选实用价值
```

**核心文件**:
- `02_simplified_validation_plan.md` - 实验设计
- `03_practical_action_plan.md` - 行动计划  
- `05_experiment_timeline.md` - 详细时间线

### 🟡 完整: 学术验证方案
**适合**: 追求学术严谨，时间充裕
```
时间: 16周
对比: 7-8个SOTA方法
任务: 回归+分类+消融实验
目标: 顶级期刊发表
```

**核心文件**:
- `01_framework_validation_plan.md` - 完整计划
- `04_literature_analysis.md` - 文献分析

## 🎯 成功标准速查

### 最低成功标准 (可发表)
```python
minimum_success = {
    'AUC_improvement': '+0.015 vs ChemProp',
    'statistical_significance': 'p < 0.05',
    'enrichment_factor': '> 1.5',
    'consistency': '不显著差于基线'
}
```

### 目标成功标准 (理想)
```python
target_success = {
    'AUC_improvement': '+0.025 vs ChemProp', 
    'F1_improvement': '+0.02',
    'enrichment_factor': '> 2.5',
    'top1_precision': '> 25%'
}
```

## 📊 核心实验设计

### 对比组 (简化方案)
```python
comparison_groups = {
    'ChemProp': 'D-MPNN图神经网络基线',
    'AutoGluon': '自动ML + Mordred特征',  
    'Shennong': '图+Mordred+注意力融合'
}
```

### 评估指标
```python
key_metrics = {
    'classification': ['AUC', 'F1', 'Precision', 'Recall'],
    'screening': ['Top-1% Precision', '富集因子'],
    'efficiency': ['推理速度', '内存使用']
}
```

### 统计要求
```python
statistical_requirements = {
    'independent_runs': 3,
    'cross_validation': '5-fold',
    'significance_test': 'McNemar检验',
    'p_value_threshold': 0.05
}
```

## ⏰ 4周时间线速览

| 周次 | 主要任务 | 关键产出 | 成功标准 |
|------|----------|----------|----------|
| **Week 1** | 数据准备+环境搭建 | 标准数据集+ChemProp基线 | ChemProp AUC > 0.70 |
| **Week 2** | AutoGluon+神农实现 | 3个模型初步结果 | 所有模型正常训练 |
| **Week 3** | 完整对比实验 | 统计显著性结果 | 神农 > ChemProp (p<0.05) |
| **Week 4** | 应用验证+报告 | 论文初稿 | 虚拟筛选富集因子>2.0 |

## 🛠️ 技术实现检查清单

### 环境准备
```bash
# Python环境
□ Python 3.8+
□ PyTorch 1.9+
□ RDKit
□ Mordred

# 外部工具
□ ChemProp: pip install chemprop
□ AutoGluon: pip install autogluon
□ 神农框架: 现有代码库
```

### 数据准备
```bash
# 数据收集
□ ChEMBL抗菌数据下载
□ 数据清洗和标准化  
□ 二分类标签生成
□ 训练/测试划分

# 特征生成
□ Mordred特征: python scripts/generate_features.py
□ 图特征: 神农框架内置
□ 特征质量检查
```

### 模型实现
```bash
# ChemProp基线
□ 数据格式转换
□ 模型训练和评估
□ 结果保存

# AutoGluon基线  
□ Mordred特征加载
□ TabularPredictor训练
□ 性能评估

# 神农框架
□ 分类任务适配
□ 注意力融合实现
□ 完整训练流程
```

## 🚨 常见问题快速解决

### Q1: 应该选择哪个实验方案？
**A**: 如果时间有限且专注应用价值 → 选择简化方案；如果追求学术严谨性 → 选择完整方案

### Q2: 数据从哪里获取？
**A**: 主要从ChEMBL数据库，参考 `06_data_requirements.md` 的详细说明

### Q3: 实验失败怎么办？
**A**: 参考 `09_risk_mitigation.md` 的应对策略，调整期望或方法

### Q4: 如何判断实验成功？
**A**: 参考 `10_success_criteria.md` 的成功标准，最低要求是统计显著性

### Q5: 改进幅度很小怎么办？
**A**: 专注统计显著性和实用价值，即使AUC提升0.02也有意义

## 📞 获取帮助

### 文档导航
- **实验设计**: `02_simplified_validation_plan.md`
- **行动计划**: `03_practical_action_plan.md`  
- **时间安排**: `05_experiment_timeline.md`
- **数据需求**: `06_data_requirements.md`
- **评估指标**: `08_evaluation_metrics.md`
- **成功标准**: `10_success_criteria.md`

### 实验支持
- 技术问题: 查看相应的技术文档
- 进度跟踪: 使用 `05_experiment_timeline.md` 的检查清单
- 结果分析: 参考 `08_evaluation_metrics.md` 的指标定义

## 🎯 立即开始

### 今天就可以做的事:
1. **阅读**: `03_practical_action_plan.md` (10分钟)
2. **下载**: ChEMBL抗菌数据 (30分钟)
3. **安装**: ChemProp和AutoGluon (20分钟)
4. **测试**: 运行一个简单的ChemProp示例 (30分钟)

### 本周目标:
- 完成数据准备和环境搭建
- 获得第一个ChemProp基线结果
- 验证所有工具正常工作

---

**记住**: 实验的关键是开始行动。不要追求完美的计划，先开始做，在做的过程中不断改进。

**🧬 神农尝百草，AI识良药 - 现在就开始您的实验之旅！** ✨
