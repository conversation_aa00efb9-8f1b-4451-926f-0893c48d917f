# 🧬 神农框架教程指南

**作者**: ZK  
**邮箱**: <EMAIL>  
**日期**: 2025-06-29

欢迎使用神农框架教程！本指南将帮助您快速上手神农框架，特别是与ChemProp的集成使用。

## 📋 教程列表

### 🚀 快速开始
- **`01_quick_start.ipynb`** - 神农框架快速入门
- **`02_data_preparation.ipynb`** - 数据准备和预处理
- **`03_model_training.ipynb`** - 模型训练详解
- **`04_prediction_analysis.ipynb`** - 预测和结果分析

### 🔍 高级功能
- **`05_interpretability_analysis.ipynb`** - 可解释性分析详解
- **`chemprop_integration.ipynb`** - ChemProp数据集成教程 ⭐

### 🎯 专题教程
- **`attention_mechanisms.ipynb`** - 注意力机制深度解析
- **`mechanism_prediction.ipynb`** - 抗菌机制预测
- **`descriptor_analysis.ipynb`** - Mordred描述符分析

## 🎯 ChemProp集成教程 (推荐)

### 📁 文件说明
- **`chemprop_integration.ipynb`** - 完整的Jupyter教程
- **`../run_chemprop_tutorial.py`** - Python运行脚本
- **`../run_tutorial.bat`** - Windows批处理启动脚本

### 🚀 快速启动

#### 方法1: 使用批处理脚本 (推荐)
```bash
# 在Windows中双击运行
run_tutorial.bat

# 或在命令行中运行
cd E:\新建文件夹\Shennong
run_tutorial.bat
```

#### 方法2: 使用Python脚本
```bash
cd E:\新建文件夹\Shennong
python run_chemprop_tutorial.py
```

#### 方法3: 直接启动Jupyter
```bash
cd E:\新建文件夹\Shennong
jupyter notebook tutorials/chemprop_integration.ipynb
```

### 📊 教程内容

1. **环境配置** - 自动配置神农框架和ChemProp路径
2. **数据兼容** - 展示ChemProp数据格式的完美兼容
3. **数据生成** - 创建抗菌化合物示例数据集
4. **可视化分析** - 数据分布和机制分析图表
5. **可解释性演示** - 完整的AI解释功能展示
6. **CLI命令示例** - 实用的命令行使用方法

### 🎯 学习目标

完成本教程后，您将能够：
- ✅ 使用ChemProp格式的数据训练神农模型
- ✅ 理解神农框架的可解释性功能
- ✅ 生成化学家友好的AI解释报告
- ✅ 掌握CLI命令的使用方法
- ✅ 对比神农框架与ChemProp的优势

## 🔧 环境要求

### 必需依赖
```bash
torch>=2.0.0
numpy>=1.24.0
pandas>=1.5.0
matplotlib>=3.6.0
seaborn>=0.12.0
rdkit-pypi>=2022.9.5
scikit-learn>=1.3.0
jupyter
```

### 可选依赖
```bash
chemprop>=2.0.0  # 用于对比和兼容性测试
mordred>=1.2.0   # 用于高级描述符计算
```

### 安装命令
```bash
# 基础依赖
pip install torch numpy pandas matplotlib seaborn rdkit-pypi scikit-learn jupyter

# 神农框架
cd E:\新建文件夹\Shennong
pip install -e .

# 可选: ChemProp
pip install chemprop
```

## 📁 数据格式

### ChemProp标准格式
```csv
smiles,activity,mechanism,log_activity,active
CCN(CC)CCCC(C)NC1=C2C=CC(=CC2=NC=C1)CF3,0.25,dna_replication,-0.602,1
CC1([C@@H](N2[C@H](S1)[C@@H](C2=O)NC(=O)CC3=CC=CC=C3)C(=O)O)C,0.1,cell_wall_synthesis,-1.0,1
```

### 神农框架增强格式
- **smiles**: SMILES字符串
- **activity**: 抗菌活性 (MIC, μg/mL)
- **mechanism**: 抗菌机制类别
- **log_activity**: 活性对数值
- **active**: 二分类标签 (活性/非活性)

## 🎯 CLI命令参考

### 训练模型
```bash
shennong train \
    --data-path data/antibacterial_train.csv \
    --smiles-column smiles \
    --target-columns activity log_activity \
    --mechanism-column mechanism \
    --save-dir models/antibacterial \
    --epochs 50 \
    --batch-size 32 \
    --enable-attention \
    --enable-mechanism-prediction
```

### 预测分析
```bash
shennong predict \
    --model-path models/antibacterial/best_model.pt \
    --test-path data/antibacterial_test.csv \
    --output-path predictions.csv \
    --return-attention \
    --return-mechanism \
    --return-uncertainty
```

### 可解释性分析
```bash
shennong explain \
    --model-path models/antibacterial/best_model.pt \
    --input-path data/antibacterial_test.csv \
    --output-dir explanations/ \
    --explain-all \
    --output-format html \
    --save-visualizations
```

## 🔍 可解释性功能

### 🎯 多维度解释
- **注意力权重解释** - AI模型关注的分子区域
- **Mordred描述符分析** - 重要分子性质的贡献
- **抗菌机制解释** - 基于结构的机制验证
- **药效团识别** - 关键药效团特征分析

### 📊 输出格式
- **文本报告** - 详细的化学解释
- **HTML报告** - 交互式网页报告
- **JSON数据** - 结构化数据，便于进一步分析
- **可视化图表** - 注意力热图、重要性排名等

### 💡 化学家价值
- **理解AI决策** - 明确预测依据
- **验证预测合理性** - 基于化学知识验证
- **指导分子设计** - 识别关键结构特征
- **机制假设生成** - 为实验提供理论指导

## 🆚 与ChemProp的对比

| 功能 | ChemProp | 神农框架 |
|------|----------|----------|
| 数据格式兼容 | ✅ | ✅ |
| 图神经网络 | ✅ | ✅ |
| 注意力机制 | ❌ | ✅ |
| 机制预测 | ❌ | ✅ |
| 可解释性 | 基础 | 高级 |
| 多任务学习 | 基础 | 增强 |
| 化学解释 | ❌ | ✅ |
| 药效团分析 | ❌ | ✅ |

## 🚀 下一步

1. **运行教程** - 完成ChemProp集成教程
2. **探索功能** - 深入了解可解释性功能
3. **实际应用** - 使用真实数据进行训练
4. **结果对比** - 与ChemProp结果进行对比
5. **定制开发** - 根据需求定制功能

## 🆘 常见问题

### Q: 如何解决路径问题？
A: 确保在`run_chemprop_tutorial.py`中正确设置了项目路径：
```python
project_root = Path(r'E:\新建文件夹\Shennong')
chemprop_root = Path(r'E:\新建文件夹\chemprop')
```

### Q: 如何处理依赖包缺失？
A: 运行安装命令：
```bash
pip install torch numpy pandas matplotlib seaborn rdkit-pypi scikit-learn jupyter
```

### Q: ChemProp不是必需的吗？
A: 不是必需的。神农框架可以独立运行，ChemProp仅用于格式兼容性演示。

### Q: 如何使用自己的数据？
A: 将数据整理为ChemProp格式（CSV文件，包含smiles列），然后使用相应的CLI命令。

## 📞 联系支持

- **邮箱**: <EMAIL>
- **项目**: 神农框架 - 基于深度学习的抗菌化合物预测系统

---

🧬 **神农尝百草，AI识良药** - 让AI预测更加透明可信！
