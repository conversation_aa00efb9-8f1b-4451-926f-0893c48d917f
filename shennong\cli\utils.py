# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-29
# 描述: 神农框架CLI工具函数

"""
神农框架CLI工具函数

提供命令行接口所需的各种工具函数，包括日志设置、参数处理等。
"""

import logging
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Optional

# 日志级别映射
LOG_LEVELS = {
    0: logging.INFO,      # 默认级别
    1: logging.WARNING,   # -q
    2: logging.ERROR,     # -qq
}

# 日志目录
LOG_DIR = Path("logs")


def setup_logging(
    logfile: Optional[str] = None,
    mode: str = "shennong",
    verbose: bool = False,
    quiet: int = 0
) -> None:
    """
    设置日志系统
    
    Args:
        logfile: 日志文件路径
        mode: 运行模式 (用于默认日志文件名)
        verbose: 是否启用详细日志
        quiet: 安静级别 (0=INFO, 1=WARNING, 2=ERROR)
    """
    # 确定日志级别
    if verbose:
        level = logging.DEBUG
    else:
        level = LOG_LEVELS.get(quiet, logging.INFO)
    
    # 创建格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 设置根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    
    # 清除现有处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 确定日志处理器
    if logfile is None:
        # 控制台输出
        handler = logging.StreamHandler(sys.stderr)
    elif logfile == "default":
        # 默认日志文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_path = LOG_DIR / mode / f"{timestamp}.log"
        log_path.parent.mkdir(parents=True, exist_ok=True)
        handler = logging.FileHandler(str(log_path))
    else:
        # 指定的日志文件
        log_path = Path(logfile)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        handler = logging.FileHandler(str(log_path))
    
    handler.setFormatter(formatter)
    root_logger.addHandler(handler)
    
    # 设置第三方库的日志级别
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('matplotlib').setLevel(logging.WARNING)


def pop_attr(obj: Any, attr: str, default: Any = None) -> Any:
    """
    从对象中弹出属性值
    
    Args:
        obj: 目标对象
        attr: 属性名
        default: 默认值
        
    Returns:
        属性值
    """
    value = getattr(obj, attr, default)
    if hasattr(obj, attr):
        delattr(obj, attr)
    return value


def validate_file_path(path: str, must_exist: bool = True) -> Path:
    """
    验证文件路径
    
    Args:
        path: 文件路径字符串
        must_exist: 是否必须存在
        
    Returns:
        验证后的Path对象
        
    Raises:
        ValueError: 路径无效
    """
    file_path = Path(path)
    
    if must_exist and not file_path.exists():
        raise ValueError(f"文件不存在: {path}")
    
    if must_exist and not file_path.is_file():
        raise ValueError(f"路径不是文件: {path}")
    
    return file_path


def validate_dir_path(path: str, create: bool = False) -> Path:
    """
    验证目录路径
    
    Args:
        path: 目录路径字符串
        create: 是否自动创建目录
        
    Returns:
        验证后的Path对象
        
    Raises:
        ValueError: 路径无效
    """
    dir_path = Path(path)
    
    if create:
        dir_path.mkdir(parents=True, exist_ok=True)
    elif not dir_path.exists():
        raise ValueError(f"目录不存在: {path}")
    elif not dir_path.is_dir():
        raise ValueError(f"路径不是目录: {path}")
    
    return dir_path


def format_time(seconds: float) -> str:
    """
    格式化时间显示
    
    Args:
        seconds: 秒数
        
    Returns:
        格式化的时间字符串
    """
    if seconds < 60:
        return f"{seconds:.1f}秒"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f}分钟"
    else:
        hours = seconds / 3600
        return f"{hours:.1f}小时"


def print_banner(title: str, width: int = 60) -> None:
    """
    打印标题横幅
    
    Args:
        title: 标题文本
        width: 横幅宽度
    """
    print("=" * width)
    print(f"{title:^{width}}")
    print("=" * width)
