# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-27
# 描述: Chemprop RDKit vs 神农Mordred特征对比分析

"""
Chemprop RDKit vs 神农Mordred特征对比分析

深入分析两种特征化策略的区别、优势和适用场景。
"""

from typing import Dict, List, Any, Optional, Tuple
import numpy as np
import logging
from pathlib import Path
import json
import time

logger = logging.getLogger(__name__)

try:
    from rdkit import Chem
    from rdkit.Chem import Descriptors, Crippen, Lipinski, rdMolDescriptors
    RDKIT_AVAILABLE = True
except ImportError:
    RDKIT_AVAILABLE = False

try:
    from mordred import Calculator, descriptors
    MORDRED_AVAILABLE = True
except ImportError:
    MORDRED_AVAILABLE = False

try:
    from chemprop.features import get_features_generator
    CHEMPROP_AVAILABLE = True
except ImportError:
    CHEMPROP_AVAILABLE = False


class FeatureComparisonAnalyzer:
    """
    特征对比分析器
    
    对比Chemprop原生RDKit特征和神农框架Mordred特征的差异。
    """
    
    def __init__(self):
        """初始化分析器"""
        self.available_tools = {
            'rdkit': RDKIT_AVAILABLE,
            'mordred': MORDRED_AVAILABLE,
            'chemprop': CHEMPROP_AVAILABLE
        }
        
        # 测试分子集
        self.test_molecules = [
            ("青霉素G", "CC1([C@@H](N2[C@H](S1)[C@@H](C2=O)NC(=O)CC3=CC=CC=C3)C(=O)O)C"),
            ("环丙沙星", "C1CC1N2C=C(C(=O)C3=CC(=C(C=C32)N4CCNCC4)F)C(=O)O"),
            ("红霉素", "CCC1C(C(C(C(=O)C(CC(C(C(C(C(C(=O)O1)C)OC2C(C(C(C(O2)C)O)N(C)C)O)C)OC3C(C(CC(O3)C)N(C)C)O)CC=O)C)C)O)OC"),
            ("阿司匹林", "CC(=O)OC1=CC=CC=C1C(=O)O"),
            ("咖啡因", "CN1C=NC2=C1C(=O)N(C(=O)N2C)C"),
        ]
        
        logger.info(f"特征对比分析器初始化: RDKit={RDKIT_AVAILABLE}, Mordred={MORDRED_AVAILABLE}, Chemprop={CHEMPROP_AVAILABLE}")
    
    def analyze_chemprop_rdkit_features(self) -> Dict[str, Any]:
        """分析Chemprop原生RDKit特征"""
        logger.info("分析Chemprop原生RDKit特征...")
        
        chemprop_analysis = {
            'feature_types': {},
            'feature_counts': {},
            'computation_time': {},
            'feature_examples': {},
            'availability': self.available_tools['chemprop']
        }
        
        if not self.available_tools['chemprop']:
            chemprop_analysis['error'] = 'Chemprop不可用'
            return chemprop_analysis
        
        try:
            # 获取Chemprop的特征生成器
            rdkit_generator = get_features_generator('rdkit_2d')
            
            # 分析特征
            test_mol = Chem.MolFromSmiles(self.test_molecules[0][1])
            if test_mol:
                start_time = time.time()
                features = rdkit_generator(test_mol)
                computation_time = time.time() - start_time
                
                chemprop_analysis['feature_counts']['rdkit_2d'] = len(features) if features is not None else 0
                chemprop_analysis['computation_time']['rdkit_2d'] = computation_time
                
                # 获取特征名称（如果可能）
                if hasattr(rdkit_generator, 'feature_names'):
                    chemprop_analysis['feature_examples']['rdkit_2d'] = rdkit_generator.feature_names[:10]
        
        except Exception as e:
            logger.error(f"Chemprop RDKit特征分析失败: {e}")
            chemprop_analysis['error'] = str(e)
        
        # 分析RDKit原生描述符
        if self.available_tools['rdkit']:
            try:
                # 常用的RDKit描述符
                rdkit_descriptors = [
                    ('MolWt', Descriptors.MolWt),
                    ('LogP', Descriptors.MolLogP),
                    ('NumHDonors', Descriptors.NumHDonors),
                    ('NumHAcceptors', Descriptors.NumHAcceptors),
                    ('TPSA', Descriptors.TPSA),
                    ('NumRotatableBonds', Descriptors.NumRotatableBonds),
                    ('NumAromaticRings', Descriptors.NumAromaticRings),
                    ('NumSaturatedRings', Descriptors.NumSaturatedRings),
                    ('NumAliphaticRings', Descriptors.NumAliphaticRings),
                    ('RingCount', Descriptors.RingCount),
                    ('FractionCsp3', Descriptors.FractionCsp3),
                    ('NumHeteroatoms', Descriptors.NumHeteroatoms),
                    ('BertzCT', Descriptors.BertzCT),
                ]
                
                chemprop_analysis['feature_types']['rdkit_basic'] = {
                    'count': len(rdkit_descriptors),
                    'examples': [name for name, _ in rdkit_descriptors[:5]],
                    'description': 'RDKit基础描述符，主要是药物化学相关的简单描述符'
                }
                
                # 测试计算
                test_mol = Chem.MolFromSmiles(self.test_molecules[0][1])
                if test_mol:
                    start_time = time.time()
                    basic_features = [func(test_mol) for _, func in rdkit_descriptors]
                    computation_time = time.time() - start_time
                    
                    chemprop_analysis['computation_time']['rdkit_basic'] = computation_time
                    chemprop_analysis['feature_counts']['rdkit_basic'] = len(basic_features)
            
            except Exception as e:
                logger.error(f"RDKit基础描述符分析失败: {e}")
        
        return chemprop_analysis
    
    def analyze_mordred_features(self) -> Dict[str, Any]:
        """分析神农框架Mordred特征"""
        logger.info("分析神农框架Mordred特征...")
        
        mordred_analysis = {
            'feature_types': {},
            'feature_counts': {},
            'computation_time': {},
            'feature_categories': {},
            'availability': self.available_tools['mordred']
        }
        
        if not self.available_tools['mordred']:
            mordred_analysis['error'] = 'Mordred不可用'
            return mordred_analysis
        
        try:
            # 创建Mordred计算器
            calc = Calculator(descriptors, ignore_3D=True)
            
            # 分析特征类别
            descriptor_categories = {}
            for desc in calc.descriptors:
                category = desc.__class__.__module__.split('.')[-1]
                if category not in descriptor_categories:
                    descriptor_categories[category] = []
                descriptor_categories[category].append(str(desc))
            
            mordred_analysis['feature_categories'] = {
                cat: {
                    'count': len(descs),
                    'examples': descs[:3]
                }
                for cat, descs in descriptor_categories.items()
            }
            
            mordred_analysis['feature_counts']['total'] = len(calc.descriptors)
            
            # 测试计算时间
            test_mol = Chem.MolFromSmiles(self.test_molecules[0][1])
            if test_mol:
                start_time = time.time()
                features = calc(test_mol)
                computation_time = time.time() - start_time
                
                mordred_analysis['computation_time']['full_calculation'] = computation_time
                
                # 分析成功率
                successful = sum(1 for f in features if f is not None and not np.isnan(float(f) if isinstance(f, (int, float)) else 0))
                mordred_analysis['success_rate'] = successful / len(features)
        
        except Exception as e:
            logger.error(f"Mordred特征分析失败: {e}")
            mordred_analysis['error'] = str(e)
        
        return mordred_analysis
    
    def compare_feature_coverage(self) -> Dict[str, Any]:
        """对比特征覆盖范围"""
        logger.info("对比特征覆盖范围...")
        
        coverage_analysis = {
            'molecular_properties': {},
            'chemical_space': {},
            'biological_relevance': {},
            'computational_complexity': {}
        }
        
        # 分子性质覆盖对比
        coverage_analysis['molecular_properties'] = {
            'chemprop_rdkit': {
                'basic_properties': ['分子量', '脂溶性', '氢键', '拓扑极性表面积'],
                'structural_features': ['环数', '芳香性', '旋转键'],
                'drug_like_properties': ['Lipinski规则', 'QED', '合成可及性'],
                'coverage_scope': 'narrow_but_focused',
                'description': '专注于药物化学中最重要的性质'
            },
            'shennong_mordred': {
                'basic_properties': ['分子量', '脂溶性', '氢键', '拓扑极性表面积'],
                'structural_features': ['环数', '芳香性', '旋转键', '连接性指数', '拓扑指数'],
                'advanced_features': ['信息指数', '自相关', '电荷分布', '分子形状'],
                'specialized_descriptors': ['药效团', '片段复杂度', '框架分析'],
                'coverage_scope': 'comprehensive',
                'description': '全面覆盖分子的各个方面，包括高级拓扑和电子性质'
            }
        }
        
        # 化学空间覆盖
        coverage_analysis['chemical_space'] = {
            'chemprop_rdkit': {
                'optimization_target': '药物样分子',
                'bias': '偏向已知药物化学空间',
                'novel_scaffolds': 'limited',
                'description': '在传统药物化学空间表现优异'
            },
            'shennong_mordred': {
                'optimization_target': '广泛的化学空间',
                'bias': '较少偏向性',
                'novel_scaffolds': 'better_coverage',
                'description': '能更好地处理新颖分子骨架'
            }
        }
        
        # 生物学相关性
        coverage_analysis['biological_relevance'] = {
            'chemprop_rdkit': {
                'pharmacokinetics': 'excellent',
                'drug_metabolism': 'good',
                'toxicity_prediction': 'good',
                'target_interaction': 'limited',
                'mechanism_prediction': 'basic'
            },
            'shennong_mordred': {
                'pharmacokinetics': 'good',
                'drug_metabolism': 'good',
                'toxicity_prediction': 'excellent',
                'target_interaction': 'better',
                'mechanism_prediction': 'advanced',
                'antibacterial_specificity': 'superior'
            }
        }
        
        return coverage_analysis
    
    def analyze_performance_differences(self) -> Dict[str, Any]:
        """分析性能差异"""
        logger.info("分析性能差异...")
        
        performance_analysis = {
            'computation_speed': {},
            'memory_usage': {},
            'scalability': {},
            'robustness': {}
        }
        
        # 计算速度对比
        if self.available_tools['rdkit'] and self.available_tools['mordred']:
            rdkit_times = []
            mordred_times = []
            
            for name, smiles in self.test_molecules:
                mol = Chem.MolFromSmiles(smiles)
                if mol is None:
                    continue
                
                # RDKit基础描述符时间
                try:
                    start_time = time.time()
                    basic_descriptors = [
                        Descriptors.MolWt(mol),
                        Descriptors.MolLogP(mol),
                        Descriptors.NumHDonors(mol),
                        Descriptors.NumHAcceptors(mol),
                        Descriptors.TPSA(mol)
                    ]
                    rdkit_time = time.time() - start_time
                    rdkit_times.append(rdkit_time)
                except:
                    pass
                
                # Mordred描述符时间
                try:
                    start_time = time.time()
                    calc = Calculator(descriptors, ignore_3D=True)
                    mordred_features = calc(mol)
                    mordred_time = time.time() - start_time
                    mordred_times.append(mordred_time)
                except:
                    pass
            
            if rdkit_times and mordred_times:
                performance_analysis['computation_speed'] = {
                    'rdkit_avg_time': np.mean(rdkit_times),
                    'mordred_avg_time': np.mean(mordred_times),
                    'speed_ratio': np.mean(mordred_times) / np.mean(rdkit_times),
                    'rdkit_std': np.std(rdkit_times),
                    'mordred_std': np.std(mordred_times)
                }
        
        # 稳定性分析
        performance_analysis['robustness'] = {
            'chemprop_rdkit': {
                'error_rate': 'very_low',
                'edge_cases': 'handles_well',
                'invalid_molecules': 'graceful_degradation',
                'description': 'RDKit经过长期优化，非常稳定'
            },
            'shennong_mordred': {
                'error_rate': 'moderate',
                'edge_cases': 'some_issues',
                'invalid_molecules': 'needs_error_handling',
                'description': 'Mordred功能强大但需要更多错误处理'
            }
        }
        
        return performance_analysis
    
    def generate_recommendations(self) -> Dict[str, Any]:
        """生成使用建议"""
        logger.info("生成使用建议...")
        
        recommendations = {
            'use_cases': {},
            'hybrid_strategies': {},
            'migration_path': {},
            'best_practices': {}
        }
        
        # 使用场景建议
        recommendations['use_cases'] = {
            'chemprop_rdkit_preferred': {
                'scenarios': [
                    '快速原型开发',
                    '传统药物发现',
                    '已知化学空间优化',
                    '计算资源受限',
                    '需要极高稳定性'
                ],
                'advantages': [
                    '计算速度快',
                    '稳定性极高',
                    '内存占用小',
                    '与Chemprop完美集成'
                ]
            },
            'shennong_mordred_preferred': {
                'scenarios': [
                    '抗菌化合物预测',
                    '新颖分子骨架',
                    '机制预测',
                    '毒性预测',
                    '全面的QSAR建模'
                ],
                'advantages': [
                    '特征丰富度高',
                    '生物学解释性强',
                    '化学空间覆盖广',
                    '抗菌特异性优化'
                ]
            }
        }
        
        # 混合策略
        recommendations['hybrid_strategies'] = {
            'dual_branch_approach': {
                'description': '神农框架的双分支架构',
                'implementation': 'Graph features + Mordred expert features',
                'benefits': '结合图特征的结构信息和Mordred的全面描述符'
            },
            'selective_features': {
                'description': '选择性特征组合',
                'implementation': 'RDKit基础 + Mordred专业',
                'benefits': '平衡计算效率和特征丰富度'
            },
            'task_specific': {
                'description': '任务特异性选择',
                'implementation': '根据预测任务选择最适合的特征集',
                'benefits': '针对性优化，避免特征冗余'
            }
        }
        
        # 迁移路径
        recommendations['migration_path'] = {
            'from_chemprop_to_shennong': {
                'step1': '保持RDKit特征作为基线',
                'step2': '逐步引入Mordred特征',
                'step3': '对比性能并优化',
                'step4': '根据结果决定最终策略'
            },
            'risk_mitigation': [
                '并行运行两套系统',
                '建立性能基准测试',
                '保留回退机制',
                '逐步迁移数据'
            ]
        }
        
        return recommendations
    
    def generate_comprehensive_report(self, output_path: Optional[str] = None) -> Dict[str, Any]:
        """生成综合对比报告"""
        logger.info("生成Chemprop RDKit vs 神农Mordred综合对比报告...")
        
        report = {
            'timestamp': str(np.datetime64('now')),
            'tool_availability': self.available_tools,
            'chemprop_analysis': self.analyze_chemprop_rdkit_features(),
            'mordred_analysis': self.analyze_mordred_features(),
            'coverage_comparison': self.compare_feature_coverage(),
            'performance_analysis': self.analyze_performance_differences(),
            'recommendations': self.generate_recommendations(),
            'executive_summary': {}
        }
        
        # 生成执行摘要
        report['executive_summary'] = self._generate_executive_summary(report)
        
        # 保存报告
        if output_path:
            try:
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(report, f, indent=2, ensure_ascii=False, default=str)
                logger.info(f"特征对比报告已保存到: {output_path}")
            except Exception as e:
                logger.error(f"保存报告失败: {e}")
        
        return report
    
    def _generate_executive_summary(self, report: Dict[str, Any]) -> Dict[str, Any]:
        """生成执行摘要"""
        summary = {
            'key_differences': [],
            'performance_comparison': {},
            'recommendation': '',
            'decision_factors': []
        }
        
        # 关键差异
        summary['key_differences'] = [
            'Chemprop RDKit: ~20个精选描述符 vs Mordred: 1613个全面描述符',
            'RDKit: 药物化学优化 vs Mordred: 广泛化学空间覆盖',
            'RDKit: 极高稳定性 vs Mordred: 丰富功能但需错误处理',
            'RDKit: 快速计算 vs Mordred: 计算密集但信息丰富'
        ]
        
        # 性能对比
        if 'computation_speed' in report.get('performance_analysis', {}):
            speed_data = report['performance_analysis']['computation_speed']
            if 'speed_ratio' in speed_data:
                ratio = speed_data['speed_ratio']
                summary['performance_comparison']['speed'] = f"Mordred比RDKit慢{ratio:.1f}倍"
        
        # 最终建议
        summary['recommendation'] = "对于神农框架的抗菌化合物预测，建议使用Mordred特征，因为其提供的丰富描述符更适合复杂的生物活性预测任务"
        
        # 决策因素
        summary['decision_factors'] = [
            '任务复杂度：抗菌预测需要丰富特征',
            '计算资源：可接受的计算开销换取更好性能',
            '解释性：Mordred提供更好的生物学解释',
            '创新性：支持新颖分子骨架的预测'
        ]
        
        return summary


def run_feature_comparison(output_dir: str = "feature_comparison_reports") -> Dict[str, Any]:
    """
    运行完整的特征对比分析
    
    Args:
        output_dir: 输出目录
        
    Returns:
        对比报告
    """
    # 创建输出目录
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # 运行分析
    analyzer = FeatureComparisonAnalyzer()
    report = analyzer.generate_comprehensive_report(
        output_path=str(output_path / "chemprop_vs_mordred_comparison.json")
    )
    
    return report
