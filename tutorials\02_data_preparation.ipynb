{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 📊 神农框架数据准备教程\n", "\n", "**作者**: ZK  \n", "**邮箱**: <EMAIL>  \n", "**日期**: 2025-06-29\n", "\n", "本教程将详细介绍如何为神农框架准备和预处理抗菌化合物数据。\n", "\n", "## 📋 教程内容\n", "\n", "1. 数据格式要求\n", "2. 数据质量检查\n", "3. SMILES验证和清洗\n", "4. 分子描述符计算\n", "5. 数据划分策略\n", "6. 异常值检测和处理\n", "7. 数据集统计和可视化\n", "\n", "预计完成时间：**30-40分钟**"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔧 环境准备"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from pathlib import Path\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# 导入神农框架模块\n", "from shennong.data.loaders import CSVDataLoader, create_sample_data\n", "from shennong.data.validation import DataValidator, validate_smiles, clean_smiles\n", "from shennong.data.splitting import scaffold_split, random_split, stratified_split\n", "from shennong.featurizers.molecule import MordredFeaturizer\n", "\n", "# 设置绘图风格\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"✅ 环境准备完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📁 数据格式要求\n", "\n", "神农框架支持标准的CSV格式数据，以下是推荐的数据格式："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 创建示例数据展示格式要求\n", "sample_data = {\n", "    'compound_id': ['COMP_001', 'COMP_002', 'COMP_003'],\n", "    'smiles': [\n", "        'CCN(CC)CCCC(C)NC1=C2C=CC(=CC2=NC=C1)CF3',\n", "        'CC(C)CC1=CC=C(C=C1)C(C)C(=O)O',\n", "        'CN1CCN(CC1)C2=C(C=C3C(=C2F)N(C=C(C3=O)C(=O)O)C4CC4)F'\n", "    ],\n", "    'activity': [0.5, 2.1, 0.8],\n", "    'mechanism': ['protein_synthesis', 'cell_membrane', 'dna_replication'],\n", "    'organism': ['<PERSON><PERSON>coli', '<PERSON><PERSON>aureus', '<PERSON>.aeruginosa'],\n", "    'assay_type': ['MIC', 'MIC', 'MIC']\n", "}\n", "\n", "df_format = pd.DataFrame(sample_data)\n", "print(\"📋 推荐的数据格式:\")\n", "print(df_format)\n", "\n", "print(\"\\n📝 列说明:\")\n", "print(\"- compound_id: 化合物唯一标识符 (可选)\")\n", "print(\"- smiles: 分子SMILES字符串 (必需)\")\n", "print(\"- activity: 抗菌活性值，如MIC (μg/mL) (必需)\")\n", "print(\"- mechanism: 抗菌机制 (可选，用于机制预测)\")\n", "print(\"- organism: 测试菌株 (可选)\")\n", "print(\"- assay_type: 实验类型 (可选)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔍 数据质量检查\n", "\n", "让我们创建一个更大的示例数据集并进行质量检查。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 创建示例数据集\n", "data_dir = Path(\"data\")\n", "data_dir.mkdir(exist_ok=True)\n", "\n", "sample_file = create_sample_data(\n", "    output_path=data_dir / \"antibacterial_dataset.csv\",\n", "    num_samples=200\n", ")\n", "\n", "# 加载数据\n", "df = pd.read_csv(sample_file)\n", "print(f\"📊 加载数据集: {len(df)} 个样本\")\n", "print(f\"列名: {list(df.columns)}\")\n", "\n", "# 基本统计信息\n", "print(\"\\n📈 基本统计:\")\n", "print(df.describe())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 添加一些有问题的数据用于演示\n", "problematic_data = {\n", "    'smiles': [\n", "        'INVALID_SMILES',  # 无效SMILES\n", "        'CCN(CC)CCCC(C)NC1=C2C=CC(=CC2=NC=C1)CF3',  # 重复SMILES\n", "        'C' * 200,  # 异常长的SMILES\n", "        '',  # 空SMILES\n", "    ],\n", "    'activity': [1.5, 0.5, 100.0, np.nan],\n", "    'mechanism': ['unknown', 'protein_synthesis', 'unknown', 'unknown'],\n", "    'compound_id': ['PROB_001', 'PROB_002', 'PROB_003', 'PROB_004']\n", "}\n", "\n", "df_problems = pd.DataFrame(problematic_data)\n", "df_combined = pd.concat([df, df_problems], ignore_index=True)\n", "\n", "print(f\"📊 添加问题数据后: {len(df_combined)} 个样本\")\n", "print(\"\\n🔍 开始数据质量检查...\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## ✅ SMILES验证和清洗"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 使用数据验证器\n", "from shennong.data.loaders import CSVDataLoader\n", "\n", "# 创建数据加载器\n", "loader = CSVDataLoader(\n", "    smiles_column='smiles',\n", "    target_columns=['activity'],\n", "    mechanism_column='mechanism',\n", "    id_column='compound_id'\n", ")\n", "\n", "# 保存合并后的数据\n", "test_file = data_dir / \"test_dataset.csv\"\n", "df_combined.to_csv(test_file, index=False)\n", "\n", "# 加载数据点\n", "try:\n", "    datapoints = loader.load_from_csv(\n", "        test_file,\n", "        validate_smiles=True,\n", "        compute_descriptors=False  # 暂时跳过描述符计算以加快速度\n", "    )\n", "    print(f\"✅ 成功加载 {len(datapoints)} 个有效数据点\")\n", "except Exception as e:\n", "    print(f\"❌ 数据加载失败: {e}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 使用数据验证器进行详细检查\n", "validator = DataValidator()\n", "\n", "if 'datapoints' in locals():\n", "    validation_results = validator.validate_dataset(\n", "        datapoints,\n", "        check_duplicates=True,\n", "        check_outliers=True,\n", "        check_drug_likeness=True\n", "    )\n", "    \n", "    print(\"📋 验证结果:\")\n", "    print(f\"总样本数: {validation_results['total_samples']}\")\n", "    print(f\"有效样本数: {validation_results['valid_samples']}\")\n", "    print(f\"无效样本数: {validation_results['invalid_samples']}\")\n", "    \n", "    if validation_results.get('issues'):\n", "        print(\"\\n⚠️ 发现的问题:\")\n", "        for issue in validation_results['issues']:\n", "            print(f\"  - {issue}\")\n", "else:\n", "    print(\"⚠️ 跳过验证，因为数据加载失败\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🧪 分子描述符计算\n", "\n", "神农框架支持多种分子描述符，主要使用Mordred描述符。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 初始化Mordred特征化器\n", "featurizer = MordredFeaturizer()\n", "\n", "# 测试几个分子的描述符计算\n", "test_smiles = [\n", "    'CCN(CC)CCCC(C)NC1=C2C=CC(=CC2=NC=C1)CF3',\n", "    'CC(C)CC1=CC=C(C=C1)C(C)C(=O)O',\n", "    'CN1CCN(CC1)C2=C(C=C3C(=C2F)N(C=C(C3=O)C(=O)O)C4CC4)F'\n", "]\n", "\n", "print(\"🧪 计算分子描述符...\")\n", "for i, smiles in enumerate(test_smiles):\n", "    try:\n", "        descriptors = featurizer.featurize(smiles)\n", "        print(f\"分子 {i+1}: {len(descriptors)} 个描述符\")\n", "        print(f\"  前5个描述符: {descriptors[:5]}\")\n", "    except Exception as e:\n", "        print(f\"分子 {i+1} 描述符计算失败: {e}\")\n", "\n", "print(f\"\\n📊 Mordred描述符总数: {featurizer.get_descriptor_count()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📊 数据划分策略\n", "\n", "神农框架提供多种数据划分方法，适用于不同的场景。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 使用清洗后的数据进行划分演示\n", "if 'datapoints' in locals() and len(datapoints) > 10:\n", "    print(\"📊 数据划分策略演示:\")\n", "    \n", "    # 1. 随机划分\n", "    train_random, val_random, test_random = random_split(\n", "        datapoints, \n", "        split_ratio=[0.7, 0.15, 0.15],\n", "        seed=42\n", "    )\n", "    print(f\"\\n1. 随机划分:\")\n", "    print(f\"   训练集: {len(train_random)} 样本\")\n", "    print(f\"   验证集: {len(val_random)} 样本\")\n", "    print(f\"   测试集: {len(test_random)} 样本\")\n", "    \n", "    # 2. 骨架划分\n", "    try:\n", "        train_scaffold, val_scaffold, test_scaffold = scaffold_split(\n", "            datapoints,\n", "            split_ratio=[0.7, 0.15, 0.15],\n", "            seed=42\n", "        )\n", "        print(f\"\\n2. 骨架划分:\")\n", "        print(f\"   训练集: {len(train_scaffold)} 样本\")\n", "        print(f\"   验证集: {len(val_scaffold)} 样本\")\n", "        print(f\"   测试集: {len(test_scaffold)} 样本\")\n", "    except Exception as e:\n", "        print(f\"\\n2. 骨架划分失败: {e}\")\n", "    \n", "    # 3. 分层划分\n", "    try:\n", "        train_strat, val_strat, test_strat = stratified_split(\n", "            datapoints,\n", "            split_ratio=[0.7, 0.15, 0.15],\n", "            target_key='activity',\n", "            n_bins=5,\n", "            seed=42\n", "        )\n", "        print(f\"\\n3. 分层划分:\")\n", "        print(f\"   训练集: {len(train_strat)} 样本\")\n", "        print(f\"   验证集: {len(val_strat)} 样本\")\n", "        print(f\"   测试集: {len(test_strat)} 样本\")\n", "    except Exception as e:\n", "        print(f\"\\n3. 分层划分失败: {e}\")\n", "        \n", "else:\n", "    print(\"⚠️ 跳过数据划分演示，数据不足\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📈 数据可视化和统计分析"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 使用原始清洁数据进行可视化\n", "df_clean = df  # 使用原始示例数据\n", "\n", "# 创建综合可视化\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "\n", "# 1. 活性分布\n", "axes[0, 0].hist(df_clean['activity'], bins=20, alpha=0.7, color='skyblue', edgecolor='black')\n", "axes[0, 0].set_xlabel('抗菌活性 (MIC, μg/mL)')\n", "axes[0, 0].set_ylabel('频次')\n", "axes[0, 0].set_title('抗菌活性分布')\n", "axes[0, 0].grid(True, alpha=0.3)\n", "\n", "# 2. 机制分布\n", "mechanism_counts = df_clean['mechanism'].value_counts()\n", "axes[0, 1].pie(mechanism_counts.values, labels=mechanism_counts.index, autopct='%1.1f%%')\n", "axes[0, 1].set_title('抗菌机制分布')\n", "\n", "# 3. 活性 vs 机制箱线图\n", "sns.boxplot(data=df_clean, x='mechanism', y='activity', ax=axes[1, 0])\n", "axes[1, 0].set_xticklabels(axes[1, 0].get_xticklabels(), rotation=45)\n", "axes[1, 0].set_title('不同机制的活性分布')\n", "axes[1, 0].set_ylabel('抗菌活性 (MIC, μg/mL)')\n", "\n", "# 4. 活性对数分布\n", "log_activity = np.log10(df_clean['activity'])\n", "axes[1, 1].hist(log_activity, bins=20, alpha=0.7, color='lightcoral', edgecolor='black')\n", "axes[1, 1].set_xlabel('Log10(活性)')\n", "axes[1, 1].set_ylabel('频次')\n", "axes[1, 1].set_title('活性对数分布')\n", "axes[1, 1].grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"📊 数据可视化完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔬 分子多样性分析"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from rdkit import Chem\n", "from rdkit.Chem import Descriptors\n", "\n", "# 计算分子描述符用于多样性分析\n", "molecular_props = []\n", "\n", "for smiles in df_clean['smiles'].head(50):  # 分析前50个分子\n", "    mol = Chem.Mo<PERSON>rom<PERSON>(smiles)\n", "    if mol is not None:\n", "        props = {\n", "            'MW': Descriptors.<PERSON><PERSON><PERSON><PERSON>(mol),\n", "            'LogP': Descriptors.MolLogP(mol),\n", "            'HBD': Descriptors.NumHDonors(mol),\n", "            'HBA': Descriptors.NumHAcceptors(mol),\n", "            'TPSA': Descriptors.TPSA(mol),\n", "            'RotBonds': Descriptors.NumRotatableBonds(mol)\n", "        }\n", "        molecular_props.append(props)\n", "\n", "df_props = pd.DataFrame(molecular_props)\n", "\n", "# 分子性质统计\n", "print(\"🔬 分子性质统计:\")\n", "print(df_props.describe())\n", "\n", "# 分子性质相关性热图\n", "plt.figure(figsize=(10, 8))\n", "correlation_matrix = df_props.corr()\n", "sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0,\n", "            square=True, linewidths=0.5)\n", "plt.title('分子描述符相关性热图')\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"📊 分子多样性分析完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 💾 保存处理后的数据\n", "\n", "最后，让我们保存清洗和处理后的数据。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 创建最终的清洁数据集\n", "output_dir = Path(\"processed_data\")\n", "output_dir.mkdir(exist_ok=True)\n", "\n", "# 保存清洁数据\n", "clean_file = output_dir / \"antibacterial_clean.csv\"\n", "df_clean.to_csv(clean_file, index=False)\n", "\n", "# 保存数据统计报告\n", "report = {\n", "    'dataset_info': {\n", "        'total_samples': len(df_clean),\n", "        'unique_smiles': df_clean['smiles'].nun<PERSON>(),\n", "        'activity_range': [df_clean['activity'].min(), df_clean['activity'].max()],\n", "        'mechanisms': df_clean['mechanism'].value_counts().to_dict()\n", "    },\n", "    'molecular_properties': df_props.describe().to_dict() if 'df_props' in locals() else None\n", "}\n", "\n", "import json\n", "report_file = output_dir / \"data_report.json\"\n", "with open(report_file, 'w', encoding='utf-8') as f:\n", "    json.dump(report, f, indent=2, ensure_ascii=False)\n", "\n", "print(f\"💾 数据处理完成:\")\n", "print(f\"  清洁数据: {clean_file}\")\n", "print(f\"  统计报告: {report_file}\")\n", "print(f\"  总样本数: {len(df_clean)}\")\n", "print(f\"  唯一SMILES: {df_clean['smiles'].nunique()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 总结\n", "\n", "在本教程中，您学习了：\n", "\n", "### ✅ 完成的任务：\n", "1. **数据格式要求** - 了解神农框架的标准数据格式\n", "2. **数据质量检查** - 使用DataValidator进行全面的数据验证\n", "3. **SMILES验证** - 检查和清洗分子SMILES字符串\n", "4. **描述符计算** - 使用Mordred计算分子描述符\n", "5. **数据划分** - 学习不同的数据划分策略\n", "6. **数据可视化** - 创建有意义的数据分析图表\n", "7. **多样性分析** - 分析分子性质的分布和相关性\n", "\n", "### 🔧 关键工具：\n", "- `CSVDataLoader` - 数据加载和预处理\n", "- `DataValidator` - 数据质量检查\n", "- `MordredFeaturizer` - 分子描述符计算\n", "- 数据划分函数 - 多种划分策略\n", "\n", "### 📈 最佳实践：\n", "1. 始终验证SMILES的有效性\n", "2. 检查和处理重复数据\n", "3. 进行异常值检测\n", "4. 选择合适的数据划分策略\n", "5. 可视化数据分布和特征\n", "\n", "### 🚀 下一步：\n", "继续学习 **03_model_training.ipynb** 来了解如何使用处理好的数据训练神农框架模型。\n", "\n", "**神农尝百草，AI识良药** 🌿"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 4}