# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-27
# 描述: 神农框架分子图表示模块

"""
神农框架分子图表示

基于Chemprop的分子图表示，扩展了抗菌化合物特异性的特征。
集成了药效团识别和抗菌机制相关的图特征。
"""

from typing import List, Optional, Dict, Any, Tuple
import numpy as np
import torch
from rdkit import Chem
from rdkit.Chem import rdMolDescriptors, Descriptors

# Chemprop导入
try:
    from chemprop.data.molgraph import MolGraph
    from chemprop.featurizers import SimpleMoleculeMolGraphFeaturizer
except ImportError:
    raise ImportError(
        "Chemprop未安装或版本不兼容。请安装: "
        "pip install git+https://github.com/chemprop/chemprop.git@v2.0.0"
    )


class ShennongMolGraph(MolGraph):
    """
    神农框架分子图类

    扩展Chemprop的MolGraph，添加抗菌化合物特异性特征：
    - 药效团识别
    - 抗菌机制相关特征
    - 增强的原子和键特征
    """

    def __init__(
        self,
        mol: Chem.Mol,
        atom_features: Optional[List[List[float]]] = None,
        bond_features: Optional[List[List[float]]] = None,
        antibacterial_features: Optional[Dict[str, Any]] = None
    ):
        """
        初始化神农分子图

        Args:
            mol: RDKit分子对象
            atom_features: 原子特征列表
            bond_features: 键特征列表
            antibacterial_features: 抗菌特异性特征
        """
        # 调用父类构造函数
        super().__init__(mol, atom_features, bond_features)

        # 抗菌特异性特征
        self.antibacterial_features = antibacterial_features or {}
        self.pharmacophores = self._identify_pharmacophores()
        self.mechanism_features = self._extract_mechanism_features()

        # 缓存计算结果
        self._cached_properties = {}

    @classmethod
    def from_mol(
        cls,
        mol: Chem.Mol,
        featurizer: Optional[SimpleMoleculeMolGraphFeaturizer] = None
    ) -> 'ShennongMolGraph':
        """
        从RDKit分子对象创建神农分子图

        Args:
            mol: RDKit分子对象
            featurizer: 分子图特征化器

        Returns:
            神农分子图实例
        """
        if featurizer is None:
            featurizer = SimpleMoleculeMolGraphFeaturizer()

        # 使用Chemprop特征化器生成基础特征
        base_molgraph = featurizer(mol)

        # 直接返回基础MolGraph
        # 添加神农特异性属性（如果可能）
        try:
            base_molgraph.mol = mol
        except AttributeError:
            # 如果不能设置属性，就忽略
            pass

        shennong_graph = base_molgraph

        return shennong_graph

    def _identify_pharmacophores(self) -> Dict[str, List[int]]:
        """
        识别抗菌药效团

        Returns:
            药效团字典，键为药效团类型，值为原子索引列表
        """
        pharmacophores = {}

        # 定义抗菌药效团SMARTS模式
        antibacterial_patterns = {
            'beta_lactam': '[#6]1-[#6](=O)-[#7]-[#6]-1',  # β-内酰胺环
            'quinolone_core': 'c1cc2c(cc1)c(=O)c(cn2)C(=O)O',  # 喹诺酮核心
            'sulfonamide': '[#16](=O)(=O)[#7]',  # 磺酰胺基团
            'aminoglycoside': '[#6]-[#8]-[#6]1[#6]([#7])[#6]([#8])[#6]([#8])[#6]([#8])[#6]1[#8]',  # 氨基糖苷
            'macrolide_ring': '[#6]1[#6][#6][#6][#6][#6][#6][#6][#6][#6][#6][#6][#6][#6][#8]1',  # 大环内酯
            'phenol': 'c1ccc(cc1)O',  # 酚羟基
            'quaternary_ammonium': '[#7+]([#6])([#6])([#6])[#6]',  # 季铵化合物
            'nitro_group': '[#7+](=O)[O-]',  # 硝基
        }

        for pattern_name, smarts in antibacterial_patterns.items():
            pattern = Chem.MolFromSmarts(smarts)
            if pattern is not None:
                matches = self.mol.GetSubstructMatches(pattern)
                if matches:
                    # 展平匹配的原子索引
                    atom_indices = list(set([atom_idx for match in matches for atom_idx in match]))
                    pharmacophores[pattern_name] = atom_indices

        return pharmacophores

    def _extract_mechanism_features(self) -> Dict[str, float]:
        """
        提取抗菌机制相关特征

        Returns:
            机制特征字典
        """
        features = {}

        # 细胞壁作用相关特征
        features['cell_wall_affinity'] = self._calculate_cell_wall_affinity()

        # 膜穿透能力
        features['membrane_permeability'] = self._calculate_membrane_permeability()

        # DNA结合能力
        features['dna_binding_potential'] = self._calculate_dna_binding_potential()

        # 蛋白质结合特征
        features['protein_binding_score'] = self._calculate_protein_binding_score()

        # 代谢稳定性
        features['metabolic_stability'] = self._calculate_metabolic_stability()

        return features

    def _calculate_cell_wall_affinity(self) -> float:
        """计算细胞壁亲和力评分"""
        # 基于分子中的极性基团和氢键供体/受体
        hbd = rdMolDescriptors.CalcNumHBD(self.mol)
        hba = rdMolDescriptors.CalcNumHBA(self.mol)
        polar_surface_area = rdMolDescriptors.CalcTPSA(self.mol)

        # 归一化评分
        affinity_score = (hbd + hba) * 0.1 + polar_surface_area * 0.01
        return min(affinity_score, 1.0)

    def _calculate_membrane_permeability(self) -> float:
        """计算膜穿透能力评分"""
        # 基于脂水分配系数和分子大小
        try:
            logp = Descriptors.MolLogP(self.mol)
            mw = Descriptors.MolWt(self.mol)

            # Lipinski规则的修改版本
            permeability = 1.0 / (1.0 + np.exp(-(logp - 2.5)))  # Sigmoid函数
            permeability *= 1.0 / (1.0 + np.exp((mw - 500) / 100))  # 分子量惩罚

            return permeability
        except:
            return 0.5  # 默认值

    def _calculate_dna_binding_potential(self) -> float:
        """计算DNA结合潜力"""
        # 基于芳香环数量和正电荷
        aromatic_rings = rdMolDescriptors.CalcNumAromaticRings(self.mol)
        formal_charge = Chem.rdmolops.GetFormalCharge(self.mol)

        # DNA结合通常需要芳香性和正电荷
        binding_potential = aromatic_rings * 0.2 + max(formal_charge, 0) * 0.3
        return min(binding_potential, 1.0)

    def _calculate_protein_binding_score(self) -> float:
        """计算蛋白质结合评分"""
        # 基于疏水性和分子形状
        try:
            logp = Descriptors.MolLogP(self.mol)
            rotatable_bonds = rdMolDescriptors.CalcNumRotatableBonds(self.mol)

            # 适度的疏水性和柔性有利于蛋白质结合
            hydrophobic_score = 1.0 / (1.0 + np.exp(-abs(logp - 2.0)))
            flexibility_score = 1.0 / (1.0 + np.exp((rotatable_bonds - 5) / 2))

            return (hydrophobic_score + flexibility_score) / 2
        except:
            return 0.5

    def _calculate_metabolic_stability(self) -> float:
        """计算代谢稳定性评分"""
        # 基于分子复杂度和官能团
        try:
            bertz_ct = rdMolDescriptors.BertzCT(self.mol)
            num_heteroatoms = rdMolDescriptors.CalcNumHeteroatoms(self.mol)

            # 适度的复杂度有利于稳定性
            complexity_score = 1.0 / (1.0 + np.exp((bertz_ct - 1000) / 200))
            hetero_score = min(num_heteroatoms / 10.0, 1.0)

            return (complexity_score + hetero_score) / 2
        except:
            return 0.5

    def get_pharmacophore_mask(self, pharmacophore_type: str) -> Optional[torch.Tensor]:
        """
        获取指定药效团的原子掩码

        Args:
            pharmacophore_type: 药效团类型

        Returns:
            原子掩码张量，如果不存在该药效团则返回None
        """
        if pharmacophore_type not in self.pharmacophores:
            return None

        mask = torch.zeros(self.n_atoms, dtype=torch.bool)
        atom_indices = self.pharmacophores[pharmacophore_type]
        mask[atom_indices] = True

        return mask

    def get_mechanism_features_tensor(self) -> torch.Tensor:
        """
        获取机制特征张量

        Returns:
            机制特征张量
        """
        features = [
            self.mechanism_features.get('cell_wall_affinity', 0.0),
            self.mechanism_features.get('membrane_permeability', 0.0),
            self.mechanism_features.get('dna_binding_potential', 0.0),
            self.mechanism_features.get('protein_binding_score', 0.0),
            self.mechanism_features.get('metabolic_stability', 0.0),
        ]

        return torch.tensor(features, dtype=torch.float32)

    def get_enhanced_atom_features(self) -> torch.Tensor:
        """
        获取增强的原子特征（包含药效团信息）

        Returns:
            增强的原子特征张量
        """
        base_features = torch.tensor(self.atom_features, dtype=torch.float32)

        # 添加药效团特征
        pharmacophore_features = torch.zeros(self.n_atoms, len(self.pharmacophores))

        for i, (pharm_type, atom_indices) in enumerate(self.pharmacophores.items()):
            pharmacophore_features[atom_indices, i] = 1.0

        # 拼接基础特征和药效团特征
        enhanced_features = torch.cat([base_features, pharmacophore_features], dim=1)

        return enhanced_features

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        base_dict = super().to_dict() if hasattr(super(), 'to_dict') else {}

        shennong_dict = {
            'pharmacophores': self.pharmacophores,
            'mechanism_features': self.mechanism_features,
            'antibacterial_features': self.antibacterial_features,
        }

        return {**base_dict, **shennong_dict}

    def __repr__(self) -> str:
        """字符串表示"""
        pharm_count = len(self.pharmacophores)
        mech_count = len(self.mechanism_features)

        return (f"ShennongMolGraph(atoms={self.n_atoms}, bonds={self.n_bonds}, "
                f"pharmacophores={pharm_count}, mechanisms={mech_count})")
