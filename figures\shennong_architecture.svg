<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="800" viewBox="0 0 1200 800" xmlns="http://www.w3.org/2000/svg">
  <!-- 定义样式 -->
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="inputGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#E3F2FD;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#BBDEFB;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="gnnGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#E8F5E8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#C8E6C9;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="expertGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFF3E0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFE0B2;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="attentionGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FCE4EC;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F8BBD9;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="fusionGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F1F8E9;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#DCEDC8;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="outputGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F9FBE7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F0F4C3;stop-opacity:1" />
    </linearGradient>
    
    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
    
    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333333" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="600" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#2E7D32">
    Shennong Framework: Chemistry-Guided Dual-Modal Architecture for Antibacterial Activity Prediction
  </text>
  
  <!-- 输入层 -->
  <g id="input-layer">
    <rect x="50" y="80" width="200" height="80" rx="10" fill="url(#inputGradient)" stroke="#1976D2" stroke-width="2" filter="url(#shadow)"/>
    <text x="150" y="105" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#1976D2">Input Layer</text>
    <text x="150" y="125" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">SMILES Molecular Structure</text>
    <text x="150" y="140" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#333">Antibacterial Activity (MIC)</text>
  </g>
  
  <!-- 特征化层 -->
  <g id="featurization-layer">
    <!-- 图特征化 -->
    <rect x="50" y="200" width="180" height="100" rx="8" fill="url(#gnnGradient)" stroke="#388E3C" stroke-width="2" filter="url(#shadow)"/>
    <text x="140" y="220" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#2E7D32">Graph Featurization</text>
    <text x="140" y="240" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#333">• Atom Features</text>
    <text x="140" y="255" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#333">• Bond Features</text>
    <text x="140" y="270" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#333">• Molecular Graph</text>
    
    <!-- Mordred特征化 -->
    <rect x="270" y="200" width="180" height="100" rx="8" fill="url(#expertGradient)" stroke="#F57C00" stroke-width="2" filter="url(#shadow)"/>
    <text x="360" y="220" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#E65100">Mordred Descriptors</text>
    <text x="360" y="240" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#333">• 1800+ Descriptors</text>
    <text x="360" y="255" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#333">• 2D/3D Properties</text>
    <text x="360" y="270" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#333">• Topological Indices</text>
  </g>
  
  <!-- 双模态神经网络 -->
  <g id="dual-modal-network">
    <!-- GNN分支 -->
    <rect x="50" y="340" width="180" height="120" rx="8" fill="url(#gnnGradient)" stroke="#388E3C" stroke-width="2" filter="url(#shadow)"/>
    <text x="140" y="360" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#2E7D32">GNN Branch</text>
    <rect x="60" y="370" width="40" height="20" rx="3" fill="#A5D6A7" stroke="#4CAF50" stroke-width="1"/>
    <text x="80" y="383" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">GCN1</text>
    <rect x="110" y="370" width="40" height="20" rx="3" fill="#A5D6A7" stroke="#4CAF50" stroke-width="1"/>
    <text x="130" y="383" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">GCN2</text>
    <rect x="160" y="370" width="40" height="20" rx="3" fill="#A5D6A7" stroke="#4CAF50" stroke-width="1"/>
    <text x="180" y="383" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">GCN3</text>
    <rect x="85" y="400" width="70" height="25" rx="3" fill="#81C784" stroke="#4CAF50" stroke-width="1"/>
    <text x="120" y="418" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#333">Graph Pooling</text>
    
    <!-- Expert分支 -->
    <rect x="270" y="340" width="180" height="120" rx="8" fill="url(#expertGradient)" stroke="#F57C00" stroke-width="2" filter="url(#shadow)"/>
    <text x="360" y="360" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#E65100">Expert Branch</text>
    <rect x="280" y="370" width="50" height="20" rx="3" fill="#FFCC80" stroke="#FF9800" stroke-width="1"/>
    <text x="305" y="383" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">Dense1</text>
    <rect x="340" y="370" width="50" height="20" rx="3" fill="#FFCC80" stroke="#FF9800" stroke-width="1"/>
    <text x="365" y="383" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">Dense2</text>
    <rect x="400" y="370" width="40" height="20" rx="3" fill="#FFCC80" stroke="#FF9800" stroke-width="1"/>
    <text x="420" y="383" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">Dense3</text>
    <rect x="310" y="400" width="60" height="25" rx="3" fill="#FFB74D" stroke="#FF9800" stroke-width="1"/>
    <text x="340" y="418" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#333">Dropout</text>
  </g>
  
  <!-- 化学导向注意力机制 -->
  <g id="chemistry-attention">
    <rect x="500" y="200" width="250" height="260" rx="10" fill="url(#attentionGradient)" stroke="#AD1457" stroke-width="2" filter="url(#shadow)"/>
    <text x="625" y="220" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#880E4F">Chemistry-Guided Attention</text>
    
    <!-- 官能团注意力 -->
    <rect x="520" y="240" width="200" height="50" rx="5" fill="#F8BBD9" stroke="#E91E63" stroke-width="1"/>
    <text x="620" y="255" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#880E4F">Functional Group Attention</text>
    <text x="620" y="270" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">H-bond • Aromatic • Halogen • Polar</text>
    
    <!-- 分子性质注意力 -->
    <rect x="520" y="300" width="200" height="50" rx="5" fill="#F8BBD9" stroke="#E91E63" stroke-width="1"/>
    <text x="620" y="315" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#880E4F">Molecular Property Attention</text>
    <text x="620" y="330" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">LogP • Size • Flexibility • Charge</text>
    
    <!-- 活性相关注意力 -->
    <rect x="520" y="360" width="200" height="50" rx="5" fill="#F8BBD9" stroke="#E91E63" stroke-width="1"/>
    <text x="620" y="375" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#880E4F">Activity-Related Attention</text>
    <text x="620" y="390" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">ADMET • Toxicity • Selectivity</text>
  </g>
  
  <!-- 特征融合层 -->
  <g id="fusion-layer">
    <rect x="800" y="300" width="150" height="80" rx="8" fill="url(#fusionGradient)" stroke="#689F38" stroke-width="2" filter="url(#shadow)"/>
    <text x="875" y="320" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#33691E">Feature Fusion</text>
    <text x="875" y="340" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#333">Adaptive Fusion</text>
    <text x="875" y="355" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#333">Feature Alignment</text>
    <text x="875" y="370" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#333">Residual Connection</text>
  </g>
  
  <!-- 预测头 -->
  <g id="prediction-head">
    <rect x="800" y="420" width="150" height="80" rx="8" fill="url(#outputGradient)" stroke="#827717" stroke-width="2" filter="url(#shadow)"/>
    <text x="875" y="440" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#827717">Prediction Head</text>
    <text x="875" y="460" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#333">Activity Prediction</text>
    <text x="875" y="475" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#333">Uncertainty Estimation</text>
    <text x="875" y="490" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#333">MIC (μg/mL)</text>
  </g>
  
  <!-- 可解释性模块 -->
  <g id="interpretability">
    <rect x="800" y="540" width="350" height="120" rx="8" fill="#FFF8E1" stroke="#FF8F00" stroke-width="2" filter="url(#shadow)"/>
    <text x="975" y="560" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#E65100">Chemical Interpretability Module</text>
    
    <!-- 原子贡献 -->
    <rect x="820" y="580" width="80" height="30" rx="3" fill="#FFECB3" stroke="#FFC107" stroke-width="1"/>
    <text x="860" y="595" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">Atom</text>
    <text x="860" y="605" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">Contribution</text>
    
    <!-- 基团识别 -->
    <rect x="910" y="580" width="80" height="30" rx="3" fill="#FFECB3" stroke="#FFC107" stroke-width="1"/>
    <text x="950" y="595" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">Functional</text>
    <text x="950" y="605" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">Groups</text>
    
    <!-- 性质分析 -->
    <rect x="1000" y="580" width="80" height="30" rx="3" fill="#FFECB3" stroke="#FFC107" stroke-width="1"/>
    <text x="1040" y="595" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">Property</text>
    <text x="1040" y="605" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">Analysis</text>
    
    <!-- 优化建议 -->
    <rect x="1090" y="580" width="80" height="30" rx="3" fill="#FFECB3" stroke="#FFC107" stroke-width="1"/>
    <text x="1130" y="595" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">Optimization</text>
    <text x="1130" y="605" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#333">Guidance</text>
  </g>
  
  <!-- 连接箭头 -->
  <!-- 输入到特征化 -->
  <line x1="150" y1="160" x2="140" y2="200" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="150" y1="160" x2="360" y2="200" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 特征化到神经网络 -->
  <line x1="140" y1="300" x2="140" y2="340" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="360" y1="300" x2="360" y2="340" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 神经网络到注意力 -->
  <line x1="230" y1="400" x2="500" y2="330" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="450" y1="400" x2="500" y2="330" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 注意力到融合 -->
  <line x1="750" y1="330" x2="800" y2="340" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 融合到预测 -->
  <line x1="875" y1="380" x2="875" y2="420" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 预测到可解释性 -->
  <line x1="875" y1="500" x2="875" y2="540" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 注意力到可解释性 -->
  <line x1="625" y1="460" x2="625" y2="520" stroke="#333" stroke-width="1" stroke-dasharray="5,5"/>
  <line x1="625" y1="520" x2="800" y2="580" stroke="#333" stroke-width="1" stroke-dasharray="5,5" marker-end="url(#arrowhead)"/>
  
  <!-- 图例 -->
  <g id="legend">
    <rect x="50" y="700" width="500" height="80" rx="5" fill="#F5F5F5" stroke="#BDBDBD" stroke-width="1"/>
    <text x="300" y="720" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#333">Key Features</text>
    
    <circle cx="80" cy="740" r="5" fill="#4CAF50"/>
    <text x="95" y="745" font-family="Arial, sans-serif" font-size="9" fill="#333">Dual-Modal Architecture</text>
    
    <circle cx="250" cy="740" r="5" fill="#E91E63"/>
    <text x="265" y="745" font-family="Arial, sans-serif" font-size="9" fill="#333">Chemistry-Guided Attention</text>
    
    <circle cx="420" cy="740" r="5" fill="#FF8F00"/>
    <text x="435" y="745" font-family="Arial, sans-serif" font-size="9" fill="#333">Chemical Interpretability</text>
    
    <text x="80" y="765" font-family="Arial, sans-serif" font-size="9" fill="#666">• Focus on antibacterial activity prediction</text>
    <text x="80" y="775" font-family="Arial, sans-serif" font-size="9" fill="#666">• Identify key functional groups and molecular properties</text>
  </g>
  
</svg>
