# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-29
# 描述: 神农框架可解释性分析CLI命令

"""
神农框架可解释性分析CLI命令

提供AI预测结果的化学解释功能，将注意力权重和描述符转化为化学家能理解的信息。
"""

import logging
import json
import pandas as pd
from pathlib import Path
from typing import List, Optional

try:
    from configargparse import ArgumentParser, Namespace
except ImportError:
    from argparse import ArgumentParser, Namespace

from .utils import validate_file_path, print_banner
from ..interpretation.chemical_explainer import ChemicalExplainer
from ..models.shennong_core import ShennongFramework
from ..featurizers.molecule import MordredFeaturizer

logger = logging.getLogger(__name__)


class ExplainSubcommand:
    """可解释性分析子命令类"""

    COMMAND = "explain"
    HELP = "生成AI预测的化学解释"

    @classmethod
    def add(cls, subparsers, parents):
        """添加可解释性分析子命令到解析器"""
        parser = subparsers.add_parser(
            cls.COMMAND,
            help=cls.HELP,
            parents=parents,
            description="分析AI预测结果，生成化学家友好的解释报告"
        )

        # 模型参数
        model_group = parser.add_argument_group("模型参数")
        model_group.add_argument(
            "--model-path",
            type=str,
            required=True,
            help="训练好的模型文件路径"
        )

        # 输入数据参数
        input_group = parser.add_argument_group("输入数据参数")
        input_group.add_argument(
            "--input-path",
            type=str,
            help="输入数据CSV文件路径"
        )
        input_group.add_argument(
            "--smiles",
            type=str,
            nargs="+",
            help="直接指定SMILES字符串列表"
        )
        input_group.add_argument(
            "--smiles-column",
            type=str,
            default="smiles",
            help="SMILES列名 (默认: smiles)"
        )

        # 解释参数
        explain_group = parser.add_argument_group("解释参数")
        explain_group.add_argument(
            "--explain-attention",
            action="store_true",
            help="解释注意力权重"
        )
        explain_group.add_argument(
            "--explain-descriptors",
            action="store_true",
            help="解释分子描述符"
        )
        explain_group.add_argument(
            "--explain-functional-groups",
            action="store_true",
            help="解释官能团贡献"
        )
        explain_group.add_argument(
            "--explain-pharmacophore",
            action="store_true",
            help="解释药效团特征"
        )
        explain_group.add_argument(
            "--explain-all",
            action="store_true",
            help="生成完整的化学解释"
        )

        # 输出参数
        output_group = parser.add_argument_group("输出参数")
        output_group.add_argument(
            "--output-dir",
            type=str,
            required=True,
            help="解释结果输出目录"
        )
        output_group.add_argument(
            "--output-format",
            type=str,
            choices=["text", "json", "html"],
            default="text",
            help="输出格式 (默认: text)"
        )
        output_group.add_argument(
            "--save-visualizations",
            action="store_true",
            help="保存可视化图表"
        )

        # 硬件参数
        hardware_group = parser.add_argument_group("硬件参数")
        hardware_group.add_argument(
            "--device",
            type=str,
            choices=["auto", "cpu", "cuda", "mps"],
            default="auto",
            help="计算设备 (默认: auto)"
        )

        parser.set_defaults(func=cls.func)
        return parser

    @classmethod
    def func(cls, args: Namespace):
        """执行可解释性分析命令"""
        print_banner("🔍 神农框架可解释性分析")

        # 验证参数
        cls._validate_args(args)

        # 加载模型
        model = cls._load_model(args)

        # 加载输入数据
        smiles_list = cls._load_input_data(args)

        # 执行预测和解释
        logger.info("开始执行预测和可解释性分析...")
        explanations = cls._generate_explanations(model, smiles_list, args)

        # 保存结果
        cls._save_explanations(explanations, args)

        logger.info("可解释性分析完成!")
        print("✅ 分析成功完成!")

    @classmethod
    def _validate_args(cls, args: Namespace):
        """验证命令行参数"""
        # 验证模型文件
        validate_file_path(args.model_path, must_exist=True)

        # 验证输入数据
        if not args.input_path and not args.smiles:
            raise ValueError("必须指定 --input-path 或 --smiles")

        if args.input_path and args.smiles:
            raise ValueError("--input-path 和 --smiles 不能同时指定")

        if args.input_path:
            validate_file_path(args.input_path, must_exist=True)

        # 验证输出目录
        output_dir = Path(args.output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

        # 如果没有指定具体解释类型，默认解释全部
        if not any([args.explain_attention, args.explain_descriptors,
                   args.explain_functional_groups, args.explain_pharmacophore]):
            args.explain_all = True

    @classmethod
    def _load_model(cls, args: Namespace) -> ShennongFramework:
        """加载模型"""
        logger.info(f"加载模型: {args.model_path}")

        try:
            model = ShennongFramework.load_model(args.model_path)

            # 设置设备
            if args.device != "auto":
                model = model.to(args.device)

            model.eval()
            logger.info("模型加载完成")
            return model

        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            raise

    @classmethod
    def _load_input_data(cls, args: Namespace) -> List[str]:
        """加载输入数据"""
        if args.smiles:
            logger.info(f"使用命令行指定的 {len(args.smiles)} 个SMILES")
            return args.smiles

        elif args.input_path:
            logger.info(f"从文件加载数据: {args.input_path}")

            try:
                df = pd.read_csv(args.input_path)

                if args.smiles_column not in df.columns:
                    raise ValueError(f"未找到SMILES列: {args.smiles_column}")

                smiles_list = df[args.smiles_column].tolist()
                logger.info(f"加载了 {len(smiles_list)} 个SMILES")
                return smiles_list

            except Exception as e:
                logger.error(f"数据加载失败: {e}")
                raise

    @classmethod
    def _generate_explanations(cls, model: ShennongFramework, smiles_list: List[str], args: Namespace) -> List[dict]:
        """生成可解释性分析"""
        explanations = []

        # 初始化化学解释器
        explainer = ChemicalExplainer()

        # 初始化特征化器
        featurizer = MordredFeaturizer()

        for i, smiles in enumerate(smiles_list):
            logger.info(f"分析分子 {i+1}/{len(smiles_list)}: {smiles}")

            try:
                # 执行预测
                prediction_result = model.predict(
                    [smiles],
                    return_attention=True,
                    return_mechanism=True,
                    return_uncertainty=True
                )

                # 计算描述符
                descriptors = featurizer.featurize(smiles)
                descriptor_names = featurizer.get_descriptor_names()

                # 生成化学解释
                if args.explain_all:
                    explanation = explainer.explain_prediction(
                        smiles=smiles,
                        predicted_activity=prediction_result['activity'][0],
                        predicted_mechanism=prediction_result['mechanism'][0],
                        attention_weights=prediction_result['attention_weights'],
                        descriptor_values=descriptors,
                        descriptor_names=descriptor_names,
                        confidence=prediction_result.get('uncertainty', [0.8])[0]
                    )
                else:
                    # 部分解释（根据用户选择）
                    explanation = cls._generate_partial_explanation(
                        explainer, smiles, prediction_result,
                        descriptors, descriptor_names, args
                    )

                explanations.append({
                    'smiles': smiles,
                    'prediction': prediction_result,
                    'explanation': explanation
                })

            except Exception as e:
                logger.warning(f"分子 {smiles} 分析失败: {e}")
                explanations.append({
                    'smiles': smiles,
                    'error': str(e)
                })

        return explanations

    @classmethod
    def _generate_partial_explanation(cls, explainer, smiles, prediction_result, descriptors, descriptor_names, args):
        """生成部分解释（根据用户选择）"""
        # 这里可以根据args中的具体选项生成部分解释
        # 为简化，暂时返回完整解释
        return explainer.explain_prediction(
            smiles=smiles,
            predicted_activity=prediction_result['activity'][0],
            predicted_mechanism=prediction_result['mechanism'][0],
            attention_weights=prediction_result['attention_weights'],
            descriptor_values=descriptors,
            descriptor_names=descriptor_names,
            confidence=prediction_result.get('uncertainty', [0.8])[0]
        )

    @classmethod
    def _save_explanations(cls, explanations: List[dict], args: Namespace):
        """保存解释结果"""
        output_dir = Path(args.output_dir)

        logger.info(f"保存解释结果到: {output_dir}")

        # 保存详细解释
        if args.output_format == "text":
            cls._save_text_explanations(explanations, output_dir)
        elif args.output_format == "json":
            cls._save_json_explanations(explanations, output_dir)
        elif args.output_format == "html":
            cls._save_html_explanations(explanations, output_dir)

        # 保存汇总报告
        cls._save_summary_report(explanations, output_dir)

        # 保存可视化（如果需要）
        if args.save_visualizations:
            cls._save_visualizations(explanations, output_dir)

        logger.info("解释结果保存完成")

    @classmethod
    def _save_text_explanations(cls, explanations: List[dict], output_dir: Path):
        """保存文本格式解释"""
        for i, exp in enumerate(explanations):
            if 'error' in exp:
                continue

            filename = f"explanation_{i+1}.txt"
            filepath = output_dir / filename

            # 生成文本报告
            explainer = ChemicalExplainer()
            report = explainer.generate_report(exp['explanation'])

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(report)

    @classmethod
    def _save_json_explanations(cls, explanations: List[dict], output_dir: Path):
        """保存JSON格式解释"""
        # 将解释结果序列化为JSON
        json_data = []

        for exp in explanations:
            if 'error' in exp:
                json_data.append({
                    'smiles': exp['smiles'],
                    'error': exp['error']
                })
            else:
                # 简化解释对象为可序列化的字典
                simplified_explanation = cls._simplify_explanation_for_json(exp['explanation'])
                json_data.append({
                    'smiles': exp['smiles'],
                    'prediction': cls._simplify_prediction_for_json(exp['prediction']),
                    'explanation': simplified_explanation
                })

        # 保存JSON文件
        json_file = output_dir / "explanations.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, indent=2, ensure_ascii=False)

    @classmethod
    def _save_html_explanations(cls, explanations: List[dict], output_dir: Path):
        """保存HTML格式解释"""
        # 生成HTML报告
        html_content = cls._generate_html_report(explanations)

        html_file = output_dir / "explanations.html"
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(html_content)

    @classmethod
    def _save_summary_report(cls, explanations: List[dict], output_dir: Path):
        """保存汇总报告"""
        summary_data = []

        for exp in explanations:
            if 'error' in exp:
                continue

            explanation = exp['explanation']
            summary = explanation.summary

            summary_data.append({
                'SMILES': exp['smiles'],
                '预测活性': explanation.predicted_activity,
                '预测机制': explanation.predicted_mechanism,
                '置信度': explanation.confidence,
                '分子量': summary['basic_properties']['molecular_weight'],
                'LogP': summary['basic_properties']['logp'],
                '药物相似性': '是' if summary['drug_likeness']['is_drug_like'] else '否',
                '活性等级': summary['activity_level']['level']
            })

        # 保存为CSV
        if summary_data:
            df = pd.DataFrame(summary_data)
            summary_file = output_dir / "summary_report.csv"
            df.to_csv(summary_file, index=False, encoding='utf-8-sig')

    @classmethod
    def _save_visualizations(cls, explanations: List[dict], output_dir: Path):
        """保存可视化图表"""
        # 这里可以添加可视化生成代码
        # 例如注意力权重热图、描述符重要性图表等
        logger.info("可视化功能待实现")

    @classmethod
    def _simplify_explanation_for_json(cls, explanation) -> dict:
        """简化解释对象为JSON可序列化的字典"""
        return {
            'predicted_activity': explanation.predicted_activity,
            'predicted_mechanism': explanation.predicted_mechanism,
            'confidence': explanation.confidence,
            'summary': explanation.summary
        }

    @classmethod
    def _simplify_prediction_for_json(cls, prediction: dict) -> dict:
        """简化预测结果为JSON可序列化的字典"""
        simplified = {}
        for key, value in prediction.items():
            if key == 'attention_weights':
                # 简化注意力权重
                simplified[key] = {k: v.tolist() if hasattr(v, 'tolist') else v
                                 for k, v in value.items()}
            elif hasattr(value, 'tolist'):
                simplified[key] = value.tolist()
            else:
                simplified[key] = value
        return simplified

    @classmethod
    def _generate_html_report(cls, explanations: List[dict]) -> str:
        """生成HTML报告"""
        html_template = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>神农框架可解释性分析报告</title>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { background-color: #f0f8ff; padding: 20px; border-radius: 5px; }
                .molecule { border: 1px solid #ddd; margin: 20px 0; padding: 15px; border-radius: 5px; }
                .summary { background-color: #f9f9f9; padding: 10px; margin: 10px 0; }
                .error { color: red; background-color: #ffe6e6; padding: 10px; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🧬 神农框架可解释性分析报告</h1>
                <p>生成时间: {timestamp}</p>
                <p>分析分子数量: {total_molecules}</p>
            </div>
            {content}
        </body>
        </html>
        """

        # 生成内容
        content = ""
        for i, exp in enumerate(explanations):
            if 'error' in exp:
                content += f"""
                <div class="molecule">
                    <h3>分子 {i+1}: {exp['smiles']}</h3>
                    <div class="error">❌ 分析失败: {exp['error']}</div>
                </div>
                """
            else:
                explanation = exp['explanation']
                content += f"""
                <div class="molecule">
                    <h3>分子 {i+1}: {exp['smiles']}</h3>
                    <div class="summary">
                        <p><strong>预测活性:</strong> {explanation.predicted_activity:.2f} μg/mL</p>
                        <p><strong>预测机制:</strong> {explanation.predicted_mechanism}</p>
                        <p><strong>置信度:</strong> {explanation.confidence:.2%}</p>
                        <p><strong>活性等级:</strong> {explanation.summary['activity_level']['level']}</p>
                    </div>
                </div>
                """

        import datetime
        return html_template.format(
            timestamp=datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            total_molecules=len(explanations),
            content=content
        )
