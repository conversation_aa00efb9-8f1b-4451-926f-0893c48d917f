# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-27
# 描述: 神农框架抗菌专业模块初始化

"""
神农框架抗菌专业模块

提供抗菌化合物预测的专业化功能，包括抗菌机制定义、
靶点信息、生物学解释等。

主要组件:
- mechanisms: 抗菌机制定义
- targets: 抗菌靶点信息
- interpretation: 生物学解释
"""

from .mechanisms import (
    ANTIBACTERIAL_MECHANISMS,
    MechanismClassifier,
    get_mechanism_by_name,
    get_all_mechanisms
)

from .targets import (
    BACTERIAL_TARGETS,
    TargetPredictor,
    get_target_by_strain,
    get_all_targets
)

from .interpretation import (
    BiologicalInterpreter,
    AttentionAnalyzer,
    MechanismExplainer
)

__all__ = [
    # 抗菌机制
    'ANTIBACTERIAL_MECHANISMS',
    'MechanismClassifier',
    'get_mechanism_by_name',
    'get_all_mechanisms',
    
    # 细菌靶点
    'BACTERIAL_TARGETS',
    'TargetPredictor',
    'get_target_by_strain',
    'get_all_targets',
    
    # 生物学解释
    'BiologicalInterpreter',
    'AttentionAnalyzer',
    'MechanismExplainer',
]
