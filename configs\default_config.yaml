# 神农框架默认配置文件
# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-27

# 模型配置
model:
  # 图神经网络分支配置
  graph_config:
    # Chemprop MPN配置
    atom_features_size: 133
    bond_features_size: 14
    hidden_size: 300
    depth: 3
    dropout: 0.0
    undirected: false
    atom_messages: false
    output_dim: 300

    # 抗菌特异性配置
    antibacterial_features: true
    pharmacophore_enhancement: true
    mechanism_aware_features: true

    # 预训练模型
    pretrained_model: null
    freeze_pretrained: false

  # 专家特征分支配置
  expert_config:
    # 描述符配置
    descriptor_dim: 1613  # Mordred全描述符
    descriptor_type: "mordred_full"

    # 自适应网络架构
    adaptive_architecture: true
    hidden_dims: [512, 256, 128]  # 如果不使用自适应架构
    output_dim: 128
    dropout: 0.3
    batch_norm: true
    activation: "relu"

    # 特征选择
    feature_selection: false
    selection_method: "variance"
    selection_ratio: 0.8

  # 注意力融合配置
  fusion_config:
    output_dim: 256
    attention_heads: 4
    dropout: 0.1
    mechanism_aware: true

    # 生物启发注意力
    biological_attention: true
    pharmacophore_attention: true
    flexibility_attention: true
    charge_attention: true
    hydrophobic_attention: true

  # 任务配置
  task_config:
    num_tasks: 1
    task_type: "regression"  # "regression" or "classification"
    task_names: ["activity"]

    # 多任务学习
    multitask: false
    mechanism_classification: true
    uncertainty_estimation: false

    # 输出配置
    output_activation: null  # null for regression, "sigmoid" for classification
    output_transform: null   # "log", "exp", etc.

# 训练配置
training:
  # 基础训练参数
  learning_rate: 1e-3
  weight_decay: 1e-4
  batch_size: 64
  max_epochs: 100
  patience: 20

  # 优化器配置
  optimizer: "adamw"
  optimizer_params:
    betas: [0.9, 0.999]
    eps: 1e-8

  # 学习率调度器
  scheduler:
    type: "cosine"  # "cosine", "step", "plateau"
    T_max: 100
    eta_min: 1e-6

    # Step调度器参数
    step_size: 30
    gamma: 0.1

    # Plateau调度器参数
    factor: 0.5
    patience: 10
    min_lr: 1e-6

  # 损失权重 (v2.0: 移除机制预测，专注活性预测)
  loss_weights:
    activity: 1.0
    uncertainty: 0.05
    attention: 0.01

  # 正则化
  gradient_clip_val: 1.0
  gradient_clip_algorithm: "norm"

  # 早停
  early_stopping:
    monitor: "val_loss"
    patience: 20
    min_delta: 1e-4
    mode: "min"

# 数据配置
data:
  # 数据集配置
  dataset_type: "antibacterial"
  data_path: "data/antibacterial_dataset.csv"

  # 预计算特征配置 (v2.0新增)
  features_path: null  # 预计算特征文件路径 (.npz格式)

  # 数据列配置
  smiles_column: "smiles"
  target_columns: ["activity"]
  descriptor_columns: null  # 自动检测或指定列名列表

  # 数据预处理
  preprocessing:
    remove_invalid_smiles: true
    remove_duplicates: true
    standardize_smiles: true
    filter_by_properties: true

    # 属性过滤
    property_filters:
      molecular_weight: [50, 1000]
      logp: [-5, 10]
      num_atoms: [5, 100]

  # 数据划分
  splitting:
    method: "scaffold"  # "random", "scaffold", "stratified", "cluster"
    train_ratio: 0.8
    val_ratio: 0.1
    test_ratio: 0.1
    random_state: 42

    # 分层划分参数
    stratify_task: "activity"
    n_bins: 5

    # 聚类划分参数
    n_clusters: null  # 自动确定
    fingerprint_type: "morgan"

  # 数据加载
  dataloader:
    num_workers: 4
    pin_memory: true
    persistent_workers: true
    prefetch_factor: 2

    # 自适应批次大小
    adaptive_batch_size: false
    max_atoms_per_batch: 10000

# 特征化配置
featurization:
  # 分子图特征化
  mol_graph:
    featurizer_type: "chemprop"
    include_chirality: false

    # 抗菌特异性特征
    antibacterial_features: true
    pharmacophore_features: true
    mechanism_features: true

  # 分子描述符
  descriptors:
    type: "auto"  # "rdkit", "mordred", "auto", "combined"

    # RDKit配置
    rdkit_config:
      selected_descriptors: "antibacterial"

    # Mordred配置
    mordred_config:
      ignore_3D: true
      selected_descriptors: "all"

    # 描述符后处理
    postprocessing:
      remove_constant: true
      remove_correlated: true
      correlation_threshold: 0.95
      standardize: true

# 验证和测试配置
evaluation:
  # 评估指标
  metrics:
    regression: ["rmse", "mae", "r2", "pearson_r"]
    classification: ["accuracy", "precision", "recall", "f1", "auc_roc"]

    # 抗菌特异性指标
    antibacterial_metrics: true
    mic_threshold: 10.0  # μg/mL
    virtual_screening_metrics: true
    enrichment_factors: [1, 5, 10]

  # 模型解释
  interpretability:
    attention_analysis: true
    feature_importance: true
    mechanism_prediction: true
    pharmacophore_mapping: true

  # 不确定性量化
  uncertainty:
    method: "ensemble"  # "ensemble", "dropout", "bayesian"
    num_samples: 100
    confidence_intervals: [0.68, 0.95]

# 硬件配置
hardware:
  # GPU配置
  accelerator: "auto"  # "auto", "gpu", "cpu"
  devices: "auto"
  precision: 32

  # 内存管理
  max_memory_gb: 16
  memory_efficient: true

  # 分布式训练
  strategy: null  # "ddp", "ddp_spawn", "deepspeed"
  num_nodes: 1

# 日志和监控配置
logging:
  # 基础日志
  log_level: "INFO"
  log_dir: "logs"
  experiment_name: "shennong_experiment"

  # 模型检查点
  checkpoint:
    monitor: "val_loss"
    mode: "min"
    save_top_k: 3
    save_last: true
    filename: "shennong-{epoch:02d}-{val_loss:.2f}"

  # 实验跟踪
  wandb:
    enabled: false
    project: "shennong-framework"
    entity: null
    tags: ["antibacterial", "chemprop", "attention"]

  tensorboard:
    enabled: true
    log_graph: true

# 推理配置
inference:
  # 批量预测
  batch_size: 128
  num_workers: 4

  # 输出格式
  output_format: "json"  # "json", "csv", "pickle"
  include_attention: false
  include_uncertainty: false
  include_features: false

  # 后处理
  postprocessing:
    apply_inverse_transform: true
    clip_predictions: true
    prediction_bounds: [0, 1000]  # MIC范围

# 超参数优化配置
hyperopt:
  # 优化方法
  method: "optuna"  # "optuna", "ray_tune", "grid_search"
  n_trials: 100
  timeout: 3600  # 秒

  # 搜索空间
  search_space:
    learning_rate: [1e-5, 1e-2]
    batch_size: [32, 64, 128]
    hidden_size: [200, 300, 400]
    attention_heads: [2, 4, 8]
    dropout: [0.0, 0.5]

  # 优化目标
  objective: "val_loss"
  direction: "minimize"

# 部署配置
deployment:
  # 模型服务
  api:
    host: "0.0.0.0"
    port: 8000
    workers: 4

  # 模型优化
  optimization:
    quantization: false
    pruning: false
    distillation: false

  # 缓存
  cache:
    enabled: true
    backend: "redis"
    ttl: 3600
