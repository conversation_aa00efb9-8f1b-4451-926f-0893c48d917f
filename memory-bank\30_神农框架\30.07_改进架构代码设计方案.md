# 神农框架改进架构代码设计方案

## 概述

本文档提供了2D图+3D描述符分离式架构的详细代码设计方案，包括核心组件的接口定义、实现细节和集成方案。

## 1. 整体架构设计

### 1.1 模块组织结构

```
shennong/
├── models/
│   ├── v2/                          # 新架构模块
│   │   ├── __init__.py
│   │   ├── shennong_v2.py          # 主模型
│   │   ├── topological_gnn.py      # 2D拓扑GNN分支
│   │   ├── enhanced_3d_branch.py   # 增强3D描述符分支
│   │   └── multi_scale_fusion.py   # 多尺度融合机制
│   └── legacy/                      # 当前架构（向后兼容）
├── featurizers/
│   ├── v2/                          # 新特征化器
│   │   ├── robust_3d_calculator.py # 鲁棒3D计算器
│   │   ├── quantum_calculator.py   # 量子化学计算器
│   │   └── md_derived_calculator.py # MD衍生特征
├── nn/
│   ├── v2/                          # 新神经网络组件
│   │   ├── topological_layers.py   # 拓扑专门化层
│   │   ├── cross_modal_attention.py # 跨模态注意力
│   │   └── chemistry_guided_fusion.py # 化学导向融合
└── utils/
    └── v2/
        ├── conformation_generator.py # 构象生成器
        └── feature_validator.py     # 特征验证器
```

### 1.2 核心接口定义

```python
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Tuple, List
import torch
import torch.nn as nn

class BranchInterface(ABC):
    """分支模块的统一接口"""

    @abstractmethod
    def forward(self, inputs: Dict[str, Any]) -> Dict[str, torch.Tensor]:
        """前向传播接口"""
        pass

    @abstractmethod
    def get_output_dim(self) -> int:
        """获取输出维度"""
        pass

    @abstractmethod
    def get_feature_names(self) -> List[str]:
        """获取特征名称列表"""
        pass

class FusionInterface(ABC):
    """融合模块的统一接口"""

    @abstractmethod
    def forward(
        self,
        branch_outputs: Dict[str, Dict[str, torch.Tensor]],
        return_attention: bool = False
    ) -> Tuple[torch.Tensor, Optional[Dict[str, torch.Tensor]]]:
        """融合前向传播"""
        pass
```

## 2. 核心组件详细设计

### 2.1 2D拓扑GNN分支

```python
class TopologicalGNNBranch(nn.Module, BranchInterface):
    """
    专门化的2D拓扑图神经网络分支

    专注于提取分子的拓扑结构信息，完全不考虑3D几何信息
    """

    def __init__(self, config: Dict[str, Any]):
        super().__init__()

        self.config = config
        self.hidden_dim = config.get('hidden_dim', 256)
        self.num_layers = config.get('num_layers', 6)
        self.output_dim = config.get('output_dim', 300)

        # 增强的2D原子特征编码器
        self.atom_encoder = Enhanced2DAtomEncoder(
            features=['atomic_num', 'degree', 'formal_charge',
                     'hybridization', 'aromatic', 'in_ring',
                     'radical_electrons', 'is_chiral_center'],
            output_dim=self.hidden_dim
        )

        # 拓扑专门化的键特征编码器
        self.bond_encoder = TopologicalBondEncoder(
            features=['bond_type', 'conjugated', 'in_ring',
                     'stereo', 'aromatic', 'rotatable'],
            output_dim=self.hidden_dim
        )

        # 深层消息传递网络
        self.mp_layers = nn.ModuleList([
            TopologicalMessagePassing(
                node_dim=self.hidden_dim,
                edge_dim=self.hidden_dim,
                hidden_dim=self.hidden_dim,
                activation='relu',
                dropout=config.get('dropout', 0.1)
            ) for _ in range(self.num_layers)
        ])

        # 多策略图池化
        self.graph_pooling = MultiStrategyPooling(
            input_dim=self.hidden_dim,
            strategies=['mean', 'max', 'attention', 'set2set'],
            output_dim=self.output_dim
        )

        # 官能团检测器
        self.functional_group_detector = FunctionalGroupDetector(
            output_dim=64
        )

        # 拓扑特征增强器
        self.topology_enhancer = TopologyEnhancer(
            input_dim=self.output_dim + 64,
            output_dim=self.output_dim
        )

    def forward(self, inputs: Dict[str, Any]) -> Dict[str, torch.Tensor]:
        """
        前向传播

        Args:
            inputs: 包含分子图数据的字典
                - mol_graph: 分子图对象
                - batch_info: 批处理信息

        Returns:
            包含各层特征的字典
        """
        mol_graph = inputs['mol_graph']

        # 原子和键特征编码
        atom_features = self.atom_encoder(mol_graph.x)  # [N, hidden_dim]
        edge_features = self.bond_encoder(mol_graph.edge_attr)  # [E, hidden_dim]

        # 多层消息传递
        h = atom_features
        layer_outputs = []

        for i, layer in enumerate(self.mp_layers):
            h = layer(h, mol_graph.edge_index, edge_features)
            layer_outputs.append(h)

            # 残差连接（每两层）
            if i > 0 and i % 2 == 1:
                h = h + layer_outputs[i-2]

        # 图级池化
        graph_repr = self.graph_pooling(h, mol_graph.batch)  # [B, output_dim]

        # 官能团特征检测
        fg_features = self.functional_group_detector(mol_graph)  # [B, 64]

        # 拓扑特征增强
        enhanced_repr = self.topology_enhancer(
            torch.cat([graph_repr, fg_features], dim=-1)
        )  # [B, output_dim]

        return {
            'graph_representation': enhanced_repr,
            'atom_features': h,  # [N, hidden_dim]
            'layer_outputs': layer_outputs,
            'functional_groups': fg_features,
            'pooled_features': graph_repr
        }

    def get_output_dim(self) -> int:
        return self.output_dim

    def get_feature_names(self) -> List[str]:
        return [
            'graph_representation', 'atom_features',
            'functional_groups', 'pooled_features'
        ]

class Enhanced2DAtomEncoder(nn.Module):
    """增强的2D原子特征编码器"""

    def __init__(self, features: List[str], output_dim: int):
        super().__init__()

        self.features = features
        self.output_dim = output_dim

        # 特征维度映射
        self.feature_dims = {
            'atomic_num': 100,      # 原子序数
            'degree': 6,            # 度数
            'formal_charge': 5,     # 形式电荷
            'hybridization': 5,     # 杂化类型
            'aromatic': 2,          # 芳香性
            'in_ring': 2,           # 是否在环中
            'radical_electrons': 3, # 自由基电子数
            'is_chiral_center': 2   # 是否手性中心
        }

        # 计算总输入维度
        self.input_dim = sum(self.feature_dims[f] for f in features)

        # 编码网络
        self.encoder = nn.Sequential(
            nn.Linear(self.input_dim, output_dim * 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(output_dim * 2, output_dim),
            nn.LayerNorm(output_dim)
        )

    def forward(self, atom_features: torch.Tensor) -> torch.Tensor:
        """编码原子特征"""
        return self.encoder(atom_features)

class TopologicalMessagePassing(nn.Module):
    """拓扑专门化的消息传递层"""

    def __init__(
        self,
        node_dim: int,
        edge_dim: int,
        hidden_dim: int,
        activation: str = 'relu',
        dropout: float = 0.1
    ):
        super().__init__()

        self.node_dim = node_dim
        self.edge_dim = edge_dim
        self.hidden_dim = hidden_dim

        # 消息计算网络
        self.message_net = nn.Sequential(
            nn.Linear(node_dim * 2 + edge_dim, hidden_dim),
            self._get_activation(activation),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim)
        )

        # 节点更新网络
        self.update_net = nn.Sequential(
            nn.Linear(node_dim + hidden_dim, hidden_dim),
            self._get_activation(activation),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, node_dim),
            nn.LayerNorm(node_dim)
        )

    def _get_activation(self, activation: str) -> nn.Module:
        """获取激活函数"""
        activations = {
            'relu': nn.ReLU(),
            'gelu': nn.GELU(),
            'swish': nn.SiLU()
        }
        return activations.get(activation, nn.ReLU())

    def forward(
        self,
        x: torch.Tensor,
        edge_index: torch.Tensor,
        edge_attr: torch.Tensor
    ) -> torch.Tensor:
        """
        消息传递前向传播

        Args:
            x: 节点特征 [N, node_dim]
            edge_index: 边索引 [2, E]
            edge_attr: 边特征 [E, edge_dim]

        Returns:
            更新后的节点特征 [N, node_dim]
        """
        row, col = edge_index

        # 构建消息
        messages = self.message_net(
            torch.cat([x[row], x[col], edge_attr], dim=-1)
        )  # [E, hidden_dim]

        # 聚合消息
        aggregated = torch.zeros(x.size(0), self.hidden_dim, device=x.device)
        aggregated.index_add_(0, col, messages)

        # 更新节点
        updated_x = self.update_net(
            torch.cat([x, aggregated], dim=-1)
        )

        # 残差连接
        return x + updated_x
```

### 2.2 增强3D描述符分支

```python
class Enhanced3DDescriptorBranch(nn.Module, BranchInterface):
    """
    增强的3D描述符分支

    整合Mordred 3D、量子化学和MD衍生特征
    """

    def __init__(self, config: Dict[str, Any]):
        super().__init__()

        self.config = config
        self.output_dim = config.get('output_dim', 256)

        # 三个子计算器
        self.mordred_3d_calculator = Robust3DMordredCalculator(
            config.get('mordred_config', {})
        )

        self.quantum_calculator = QuantumChemistryCalculator(
            method=config.get('qm_method', 'GFN2-xTB'),
            config=config.get('qm_config', {})
        )

        self.md_calculator = MDDerivedCalculator(
            config.get('md_config', {})
        )

        # 特征预处理器
        self.feature_preprocessors = nn.ModuleDict({
            'mordred_3d': FeaturePreprocessor(
                input_dim=213,  # Mordred 3D描述符数量
                output_dim=128,
                normalization='batch_norm'
            ),
            'quantum': FeaturePreprocessor(
                input_dim=50,   # 量子化学特征数量
                output_dim=64,
                normalization='layer_norm'
            ),
            'md_derived': FeaturePreprocessor(
                input_dim=20,   # MD衍生特征数量
                output_dim=32,
                normalization='batch_norm'
            )
        })

        # 分层特征融合器
        self.hierarchical_fusion = HierarchicalFeatureFusion(
            input_dims=[128, 64, 32],
            output_dim=self.output_dim,
            fusion_strategy='attention'
        )

        # 特征质量评估器
        self.quality_assessor = FeatureQualityAssessor()

    def forward(self, inputs: Dict[str, Any]) -> Dict[str, torch.Tensor]:
        """
        前向传播

        Args:
            inputs: 包含分子信息的字典
                - mol: RDKit分子对象
                - smiles: SMILES字符串
                - mol_3d: 3D构象（可选）

        Returns:
            包含3D特征的字典
        """
        mol = inputs['mol']
        smiles = inputs.get('smiles', '')
        mol_3d = inputs.get('mol_3d', None)

        features = {}
        quality_scores = {}

        # 1. Mordred 3D描述符计算
        try:
            mordred_result = self.mordred_3d_calculator.calculate(mol, mol_3d)
            mordred_features = self.feature_preprocessors['mordred_3d'](
                mordred_result['features']
            )
            features['mordred_3d'] = mordred_features
            quality_scores['mordred_3d'] = mordred_result['quality_score']
        except Exception as e:
            logger.warning(f"Mordred 3D计算失败: {e}")
            features['mordred_3d'] = torch.zeros(128, device=self.device)
            quality_scores['mordred_3d'] = 0.0

        # 2. 量子化学特征计算
        try:
            qm_result = self.quantum_calculator.calculate(mol, mol_3d)
            qm_features = self.feature_preprocessors['quantum'](
                qm_result['features']
            )
            features['quantum'] = qm_features
            quality_scores['quantum'] = qm_result['quality_score']
        except Exception as e:
            logger.warning(f"量子化学计算失败: {e}")
            features['quantum'] = torch.zeros(64, device=self.device)
            quality_scores['quantum'] = 0.0

        # 3. MD衍生特征计算
        try:
            md_result = self.md_calculator.calculate_approximate(mol)
            md_features = self.feature_preprocessors['md_derived'](
                md_result['features']
            )
            features['md_derived'] = md_features
            quality_scores['md_derived'] = md_result['quality_score']
        except Exception as e:
            logger.warning(f"MD特征计算失败: {e}")
            features['md_derived'] = torch.zeros(32, device=self.device)
            quality_scores['md_derived'] = 0.0

        # 分层融合所有3D特征
        fusion_result = self.hierarchical_fusion(
            [features['mordred_3d'], features['quantum'], features['md_derived']],
            quality_weights=list(quality_scores.values())
        )

        # 整体质量评估
        overall_quality = self.quality_assessor(features, quality_scores)

        return {
            '3d_representation': fusion_result['fused_features'],
            'mordred_3d': features['mordred_3d'],
            'quantum': features['quantum'],
            'md_derived': features['md_derived'],
            'quality_scores': quality_scores,
            'overall_quality': overall_quality,
            'fusion_weights': fusion_result['attention_weights']
        }

    def get_output_dim(self) -> int:
        return self.output_dim

    def get_feature_names(self) -> List[str]:
        return [
            '3d_representation', 'mordred_3d', 'quantum',
            'md_derived', 'quality_scores'
        ]

    @property
    def device(self) -> torch.device:
        """获取设备"""
        return next(self.parameters()).device

### 2.3 多尺度注意力融合机制

```python
class MultiScaleChemistryFusion(nn.Module, FusionInterface):
    """
    多尺度化学导向的融合机制

    专门设计用于融合2D拓扑和3D描述符信息
    """

    def __init__(self, config: Dict[str, Any]):
        super().__init__()

        self.config = config
        self.d_2d = config.get('d_2d', 300)
        self.d_3d = config.get('d_3d', 256)
        self.d_model = config.get('d_model', 256)
        self.num_heads = config.get('num_heads', 8)

        # 特征对齐层
        self.feature_alignment = nn.ModuleDict({
            '2d_projection': nn.Linear(self.d_2d, self.d_model),
            '3d_projection': nn.Linear(self.d_3d, self.d_model)
        })

        # 多尺度注意力模块
        self.multi_scale_attention = MultiScaleAttention(
            d_model=self.d_model,
            num_heads=self.num_heads,
            scales=['atom', 'bond', 'molecule']
        )

        # 化学导向注意力模块
        self.chemistry_guided_attention = ChemistryGuidedAttention(
            d_model=self.d_model,
            chemistry_aspects=['functional_group', 'stereochemistry', 'electronic']
        )

        # 交叉模态注意力
        self.cross_modal_attention = CrossModalAttention(
            d_model=self.d_model,
            num_heads=self.num_heads
        )

        # 自适应权重学习
        self.adaptive_weighting = AdaptiveWeighting(
            input_dim=self.d_model * 3,
            num_components=3
        )

        # 最终融合层
        self.final_fusion = nn.Sequential(
            nn.Linear(self.d_model * 3, self.d_model * 2),
            nn.ReLU(),
            nn.Dropout(config.get('dropout', 0.1)),
            nn.Linear(self.d_model * 2, self.d_model),
            nn.LayerNorm(self.d_model)
        )

    def forward(
        self,
        branch_outputs: Dict[str, Dict[str, torch.Tensor]],
        return_attention: bool = False
    ) -> Tuple[torch.Tensor, Optional[Dict[str, torch.Tensor]]]:
        """
        多尺度融合前向传播

        Args:
            branch_outputs: 分支输出字典
                - '2d_topological': 2D拓扑分支输出
                - '3d_descriptors': 3D描述符分支输出
            return_attention: 是否返回注意力权重

        Returns:
            (融合特征, 注意力权重字典)
        """
        # 提取分支特征
        graph_output = branch_outputs['2d_topological']
        descriptor_output = branch_outputs['3d_descriptors']

        graph_repr = graph_output['graph_representation']  # [B, d_2d]
        atom_features = graph_output.get('atom_features', None)  # [N, d_atom]
        descriptor_repr = descriptor_output['3d_representation']  # [B, d_3d]

        # 特征对齐到统一维度
        aligned_2d = self.feature_alignment['2d_projection'](graph_repr)  # [B, d_model]
        aligned_3d = self.feature_alignment['3d_projection'](descriptor_repr)  # [B, d_model]

        # 扩展维度用于序列注意力计算
        aligned_2d_seq = aligned_2d.unsqueeze(1)  # [B, 1, d_model]
        aligned_3d_seq = aligned_3d.unsqueeze(1)  # [B, 1, d_model]

        attention_weights = {}

        # 1. 多尺度注意力
        multi_scale_output, ms_weights = self.multi_scale_attention(
            aligned_2d_seq, aligned_3d_seq, atom_features
        )
        if return_attention:
            attention_weights['multi_scale'] = ms_weights

        # 2. 化学导向注意力
        chemistry_output, chem_weights = self.chemistry_guided_attention(
            aligned_2d_seq, aligned_3d_seq,
            graph_output.get('functional_groups', None),
            descriptor_output
        )
        if return_attention:
            attention_weights['chemistry_guided'] = chem_weights

        # 3. 交叉模态注意力
        cross_modal_output, cm_weights = self.cross_modal_attention(
            aligned_2d_seq, aligned_3d_seq
        )
        if return_attention:
            attention_weights['cross_modal'] = cm_weights

        # 压缩序列维度
        multi_scale_features = multi_scale_output.squeeze(1)  # [B, d_model]
        chemistry_features = chemistry_output.squeeze(1)     # [B, d_model]
        cross_modal_features = cross_modal_output.squeeze(1) # [B, d_model]

        # 自适应权重融合
        component_features = [multi_scale_features, chemistry_features, cross_modal_features]
        adaptive_weights = self.adaptive_weighting(
            torch.cat(component_features, dim=-1)
        )  # [B, 3]

        # 加权组合
        weighted_features = torch.stack(component_features, dim=1)  # [B, 3, d_model]
        weighted_sum = torch.sum(
            weighted_features * adaptive_weights.unsqueeze(-1), dim=1
        )  # [B, d_model]

        # 最终融合
        combined_features = torch.cat([
            multi_scale_features, chemistry_features, cross_modal_features
        ], dim=-1)  # [B, d_model * 3]

        fused_representation = self.final_fusion(combined_features)  # [B, d_model]

        # 残差连接
        final_output = fused_representation + weighted_sum

        if return_attention:
            attention_weights['adaptive_weights'] = adaptive_weights
            return final_output, attention_weights
        else:
            return final_output, None

class MultiScaleAttention(nn.Module):
    """多尺度注意力模块"""

    def __init__(self, d_model: int, num_heads: int, scales: List[str]):
        super().__init__()

        self.d_model = d_model
        self.num_heads = num_heads
        self.scales = scales

        # 为每个尺度创建注意力层
        self.scale_attentions = nn.ModuleDict({
            scale: nn.MultiheadAttention(
                embed_dim=d_model,
                num_heads=num_heads,
                dropout=0.1,
                batch_first=True
            ) for scale in scales
        })

        # 尺度融合层
        self.scale_fusion = nn.Linear(len(scales) * d_model, d_model)

    def forward(
        self,
        query_2d: torch.Tensor,
        key_3d: torch.Tensor,
        atom_features: Optional[torch.Tensor] = None
    ) -> Tuple[torch.Tensor, Dict[str, torch.Tensor]]:
        """
        多尺度注意力计算

        Args:
            query_2d: 2D查询特征 [B, 1, d_model]
            key_3d: 3D键值特征 [B, 1, d_model]
            atom_features: 原子级特征 [N, d_atom] (可选)

        Returns:
            (融合特征, 注意力权重)
        """
        scale_outputs = []
        attention_weights = {}

        for scale in self.scales:
            if scale == 'atom' and atom_features is not None:
                # 原子级注意力需要特殊处理
                atom_output, atom_weights = self._atom_level_attention(
                    query_2d, key_3d, atom_features
                )
                scale_outputs.append(atom_output)
                attention_weights[f'{scale}_attention'] = atom_weights
            else:
                # 分子级和键级注意力
                output, weights = self.scale_attentions[scale](
                    query_2d, key_3d, key_3d
                )
                scale_outputs.append(output)
                attention_weights[f'{scale}_attention'] = weights

        # 融合所有尺度的输出
        concatenated = torch.cat(scale_outputs, dim=-1)  # [B, 1, len(scales) * d_model]
        fused_output = self.scale_fusion(concatenated)   # [B, 1, d_model]

        return fused_output, attention_weights

    def _atom_level_attention(
        self,
        query_2d: torch.Tensor,
        key_3d: torch.Tensor,
        atom_features: torch.Tensor
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """原子级注意力计算"""
        # 简化实现：使用平均池化近似原子级信息
        # 实际实现中需要更复杂的原子-分子级交互
        batch_size = query_2d.size(0)

        # 假设原子特征已经按批次组织
        atom_pooled = torch.mean(atom_features.view(batch_size, -1, atom_features.size(-1)), dim=1)
        atom_pooled = atom_pooled.unsqueeze(1)  # [B, 1, d_atom]

        # 投影到统一维度
        atom_proj = nn.Linear(atom_features.size(-1), self.d_model).to(atom_features.device)
        atom_aligned = atom_proj(atom_pooled)  # [B, 1, d_model]

        # 计算注意力
        output, weights = self.scale_attentions['atom'](
            query_2d, atom_aligned, atom_aligned
        )

        return output, weights

class ChemistryGuidedAttention(nn.Module):
    """化学知识导向的注意力模块"""

    def __init__(self, d_model: int, chemistry_aspects: List[str]):
        super().__init__()

        self.d_model = d_model
        self.chemistry_aspects = chemistry_aspects

        # 化学知识编码器
        self.chemistry_encoders = nn.ModuleDict({
            aspect: ChemistryAspectEncoder(d_model, aspect)
            for aspect in chemistry_aspects
        })

        # 知识融合注意力
        self.knowledge_attention = nn.MultiheadAttention(
            embed_dim=d_model,
            num_heads=4,
            dropout=0.1,
            batch_first=True
        )

    def forward(
        self,
        query_2d: torch.Tensor,
        key_3d: torch.Tensor,
        functional_groups: Optional[torch.Tensor] = None,
        descriptor_output: Optional[Dict[str, torch.Tensor]] = None
    ) -> Tuple[torch.Tensor, Dict[str, torch.Tensor]]:
        """化学导向注意力计算"""

        chemistry_features = []
        attention_weights = {}

        for aspect in self.chemistry_aspects:
            # 根据化学方面编码特征
            if aspect == 'functional_group' and functional_groups is not None:
                aspect_features = self.chemistry_encoders[aspect](
                    query_2d, key_3d, functional_groups
                )
            elif aspect == 'stereochemistry' and descriptor_output is not None:
                aspect_features = self.chemistry_encoders[aspect](
                    query_2d, key_3d, descriptor_output.get('mordred_3d', None)
                )
            elif aspect == 'electronic' and descriptor_output is not None:
                aspect_features = self.chemistry_encoders[aspect](
                    query_2d, key_3d, descriptor_output.get('quantum', None)
                )
            else:
                # 默认使用基本注意力
                aspect_features, _ = self.knowledge_attention(query_2d, key_3d, key_3d)

            chemistry_features.append(aspect_features)

        # 融合所有化学方面的特征
        stacked_features = torch.stack(chemistry_features, dim=2)  # [B, 1, num_aspects, d_model]
        averaged_features = torch.mean(stacked_features, dim=2)    # [B, 1, d_model]

        return averaged_features, attention_weights

class CrossModalAttention(nn.Module):
    """跨模态注意力模块"""

    def __init__(self, d_model: int, num_heads: int):
        super().__init__()

        self.d_model = d_model
        self.num_heads = num_heads

        # 双向交叉注意力
        self.attention_2d_to_3d = nn.MultiheadAttention(
            embed_dim=d_model,
            num_heads=num_heads,
            dropout=0.1,
            batch_first=True
        )

        self.attention_3d_to_2d = nn.MultiheadAttention(
            embed_dim=d_model,
            num_heads=num_heads,
            dropout=0.1,
            batch_first=True
        )

        # 融合层
        self.fusion_layer = nn.Linear(d_model * 2, d_model)

    def forward(
        self,
        features_2d: torch.Tensor,
        features_3d: torch.Tensor
    ) -> Tuple[torch.Tensor, Dict[str, torch.Tensor]]:
        """跨模态注意力计算"""

        # 2D -> 3D 注意力
        attended_2d_to_3d, weights_2d_to_3d = self.attention_2d_to_3d(
            features_2d, features_3d, features_3d
        )

        # 3D -> 2D 注意力
        attended_3d_to_2d, weights_3d_to_2d = self.attention_3d_to_2d(
            features_3d, features_2d, features_2d
        )

        # 融合双向注意力结果
        concatenated = torch.cat([attended_2d_to_3d, attended_3d_to_2d], dim=-1)
        fused_output = self.fusion_layer(concatenated)

        attention_weights = {
            '2d_to_3d': weights_2d_to_3d,
            '3d_to_2d': weights_3d_to_2d
        }

        return fused_output, attention_weights
```

## 3. 支撑组件设计

### 3.1 鲁棒3D计算器

```python
class Robust3DMordredCalculator:
    """
    鲁棒的3D Mordred描述符计算器

    解决当前3D描述符计算失败的问题
    """

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.max_attempts = config.get('max_attempts', 5)
        self.timeout = config.get('timeout', 30)  # 秒

        # 多策略fallback配置
        self.fallback_strategies = [
            ('ETDG_v2', {
                'randomSeed': 42,
                'numConfs': 5,
                'pruneRmsThresh': 0.5
            }),
            ('ETKDG_v3', {
                'randomSeed': 42,
                'numConfs': 3,
                'pruneRmsThresh': 0.3
            }),
            ('basic_embed', {
                'randomSeed': 42,
                'maxAttempts': 100
            }),
            ('distance_geometry', {
                'randomSeed': 42,
                'useRandomCoords': True
            }),
            ('2d_approximation', {}),
            ('ml_prediction', {})
        ]

        # 预训练的ML模型（用于最后的fallback）
        self.ml_predictor = self._load_ml_predictor()

        # 结果缓存
        self.cache = LRUCache(maxsize=config.get('cache_size', 1000))

    def calculate(
        self,
        mol: Chem.Mol,
        mol_3d: Optional[Chem.Mol] = None
    ) -> Dict[str, Any]:
        """
        计算3D Mordred描述符

        Args:
            mol: 2D分子对象
            mol_3d: 预生成的3D分子对象（可选）

        Returns:
            包含特征和质量评分的字典
        """
        # 检查缓存
        mol_key = Chem.MolToSmiles(mol)
        if mol_key in self.cache:
            return self.cache[mol_key]

        result = None
        quality_score = 0.0

        # 如果提供了3D构象，直接使用
        if mol_3d is not None:
            try:
                result = self._compute_descriptors(mol_3d)
                quality_score = self._assess_quality(mol_3d, result)
                if quality_score > 0.7:  # 质量阈值
                    self.cache[mol_key] = {
                        'features': result,
                        'quality_score': quality_score,
                        'method': 'provided_3d'
                    }
                    return self.cache[mol_key]
            except Exception as e:
                logger.warning(f"提供的3D构象计算失败: {e}")

        # 尝试各种fallback策略
        for strategy_name, params in self.fallback_strategies:
            try:
                if strategy_name == '2d_approximation':
                    result = self._approximate_3d_from_2d(mol)
                    quality_score = 0.3  # 近似方法质量较低
                elif strategy_name == 'ml_prediction':
                    result = self._ml_predict_3d_features(mol)
                    quality_score = 0.5  # ML预测质量中等
                else:
                    # 生成3D构象
                    mol_3d_generated = self._generate_3d_conformation(
                        mol, strategy_name, params
                    )

                    if mol_3d_generated is not None:
                        result = self._compute_descriptors(mol_3d_generated)
                        quality_score = self._assess_quality(mol_3d_generated, result)

                # 验证结果质量
                if result is not None and quality_score > 0.1:
                    self.cache[mol_key] = {
                        'features': result,
                        'quality_score': quality_score,
                        'method': strategy_name
                    }
                    return self.cache[mol_key]

            except Exception as e:
                logger.warning(f"3D计算策略 {strategy_name} 失败: {e}")
                continue

        # 所有策略都失败，返回零特征
        logger.error(f"所有3D计算策略都失败，分子: {mol_key}")
        zero_features = torch.zeros(213)  # Mordred 3D描述符数量

        result = {
            'features': zero_features,
            'quality_score': 0.0,
            'method': 'failed'
        }

        self.cache[mol_key] = result
        return result

    def _generate_3d_conformation(
        self,
        mol: Chem.Mol,
        strategy: str,
        params: Dict[str, Any]
    ) -> Optional[Chem.Mol]:
        """生成3D构象"""

        mol_copy = Chem.Mol(mol)
        mol_h = Chem.AddHs(mol_copy)

        try:
            if strategy == 'ETDG_v2':
                # 使用改进的ETDG方法
                ps = AllChem.ETKDGv2()
                ps.randomSeed = params['randomSeed']
                ps.numConfs = params['numConfs']
                ps.pruneRmsThresh = params['pruneRmsThresh']

                conf_ids = AllChem.EmbedMultipleConfs(mol_h, **params)
                if len(conf_ids) == 0:
                    return None

                # 优化所有构象
                for conf_id in conf_ids:
                    AllChem.OptimizeMolecule(mol_h, confId=conf_id)

                # 选择能量最低的构象
                energies = []
                for conf_id in conf_ids:
                    ff = AllChem.UFFGetMoleculeForceField(mol_h, confId=conf_id)
                    if ff is not None:
                        energies.append(ff.CalcEnergy())
                    else:
                        energies.append(float('inf'))

                best_conf_id = conf_ids[np.argmin(energies)]

                # 创建只包含最佳构象的分子
                best_mol = Chem.Mol(mol_h)
                best_mol.RemoveAllConformers()
                best_mol.AddConformer(mol_h.GetConformer(best_conf_id))

                return best_mol

            elif strategy == 'ETKDG_v3':
                # 使用ETKDG v3方法
                ps = AllChem.ETKDGv3()
                ps.randomSeed = params['randomSeed']

                result = AllChem.EmbedMolecule(mol_h, ps)
                if result != 0:
                    return None

                AllChem.OptimizeMolecule(mol_h)
                return mol_h

            elif strategy == 'basic_embed':
                # 基本嵌入方法
                result = AllChem.EmbedMolecule(
                    mol_h,
                    randomSeed=params['randomSeed'],
                    maxAttempts=params['maxAttempts']
                )
                if result != 0:
                    return None

                AllChem.OptimizeMolecule(mol_h)
                return mol_h

            elif strategy == 'distance_geometry':
                # 距离几何方法
                bounds_matrix = Chem.rdDistGeom.GetMoleculeBoundsMatrix(mol_h)
                result = Chem.rdDistGeom.EmbedMolecule(
                    mol_h,
                    useRandomCoords=params['useRandomCoords'],
                    randomSeed=params['randomSeed']
                )
                if result != 0:
                    return None

                return mol_h

        except Exception as e:
            logger.warning(f"3D构象生成失败 ({strategy}): {e}")
            return None

        return None

    def _compute_descriptors(self, mol_3d: Chem.Mol) -> torch.Tensor:
        """计算3D描述符"""
        from mordred import Calculator, descriptors

        # 创建3D描述符计算器
        calc = Calculator(descriptors, ignore_3D=False)

        # 计算描述符
        desc_values = calc(mol_3d)

        # 处理结果
        features = []
        for value in desc_values:
            if isinstance(value, (int, float)) and not (np.isnan(value) or np.isinf(value)):
                features.append(float(value))
            else:
                features.append(0.0)  # 用0填充无效值

        return torch.tensor(features, dtype=torch.float32)

    def _assess_quality(self, mol_3d: Chem.Mol, descriptors: torch.Tensor) -> float:
        """评估3D构象和描述符质量"""
        quality_score = 1.0

        try:
            # 检查构象合理性
            conf = mol_3d.GetConformer()

            # 1. 检查原子间距离
            num_atoms = mol_3d.GetNumAtoms()
            min_distance = float('inf')

            for i in range(num_atoms):
                for j in range(i + 1, num_atoms):
                    pos_i = conf.GetAtomPosition(i)
                    pos_j = conf.GetAtomPosition(j)
                    distance = np.sqrt(
                        (pos_i.x - pos_j.x)**2 +
                        (pos_i.y - pos_j.y)**2 +
                        (pos_i.z - pos_j.z)**2
                    )
                    min_distance = min(min_distance, distance)

            # 原子间距离过小说明构象不合理
            if min_distance < 0.8:  # 埃
                quality_score *= 0.5

            # 2. 检查描述符的有效性
            valid_descriptors = torch.sum(~torch.isnan(descriptors) & ~torch.isinf(descriptors))
            total_descriptors = len(descriptors)
            validity_ratio = valid_descriptors.float() / total_descriptors

            quality_score *= validity_ratio.item()

            # 3. 检查能量（如果可用）
            try:
                ff = AllChem.UFFGetMoleculeForceField(mol_3d)
                if ff is not None:
                    energy = ff.CalcEnergy()
                    # 能量过高说明构象不稳定
                    if energy > 1000:  # kcal/mol
                        quality_score *= 0.7
            except:
                pass

        except Exception as e:
            logger.warning(f"质量评估失败: {e}")
            quality_score = 0.1

        return max(0.0, min(1.0, quality_score))

    def _approximate_3d_from_2d(self, mol: Chem.Mol) -> torch.Tensor:
        """使用2D描述符近似3D描述符"""
        from mordred import Calculator, descriptors

        # 计算2D描述符
        calc_2d = Calculator(descriptors, ignore_3D=True)
        desc_2d = calc_2d(mol)

        # 使用预训练的映射模型将2D描述符转换为3D近似
        # 这里简化为线性变换，实际应该使用训练好的模型
        features_2d = []
        for value in desc_2d:
            if isinstance(value, (int, float)) and not (np.isnan(value) or np.isinf(value)):
                features_2d.append(float(value))
            else:
                features_2d.append(0.0)

        # 简化的线性映射（实际应该使用更复杂的模型）
        features_2d_tensor = torch.tensor(features_2d, dtype=torch.float32)

        # 假设我们有一个预训练的映射矩阵
        # 这里使用随机矩阵作为占位符
        mapping_matrix = torch.randn(213, len(features_2d))  # 213是3D描述符数量
        approximated_3d = torch.matmul(mapping_matrix, features_2d_tensor)

        return approximated_3d

    def _ml_predict_3d_features(self, mol: Chem.Mol) -> torch.Tensor:
        """使用机器学习模型预测3D特征"""
        if self.ml_predictor is None:
            # 如果没有预训练模型，返回零特征
            return torch.zeros(213)

        # 使用预训练模型预测
        # 这里需要实现具体的预测逻辑
        smiles = Chem.MolToSmiles(mol)
        predicted_features = self.ml_predictor.predict(smiles)

        return torch.tensor(predicted_features, dtype=torch.float32)

    def _load_ml_predictor(self):
        """加载预训练的ML预测模型"""
        # 这里应该加载实际的预训练模型
        # 目前返回None作为占位符
        return None
```
```