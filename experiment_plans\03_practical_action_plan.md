# 神农框架实用验证行动计划

**目标**: 简单高效地证明神农框架在药物筛选中的实用价值  
**对比**: 主要与ChemProp和AutoGluon对比  
**任务**: 抗菌化合物分类  
**时间**: 3-4周完成  

## 🎯 核心验证目标

```python
validation_goals = {
    'primary': '证明神农框架在分类任务上优于ChemProp和AutoGluon',
    'practical': '展示在虚拟筛选中的实用价值',
    'efficiency': '验证预计算特征带来的效率优势'
}

success_criteria = {
    'performance': 'AUC > ChemProp + 0.02 (统计显著)',
    'screening': 'Top-1%富集因子 > 2.0',
    'speed': '推理速度 > ChemProp (得益于预计算特征)'
}
```

## 📋 简化实验设计

### 对比组 (仅3个)
1. **ChemProp**: 成熟的图神经网络基线
2. **AutoGluon**: 自动ML + Mordred特征
3. **Shennong**: 图+Mordred+注意力融合

### 评估任务
```python
tasks = {
    'binary_classification': {
        'description': '活性/非活性二分类',
        'threshold': 'MIC < 16 μg/mL',
        'metrics': ['AUC', 'F1', 'Precision', 'Recall']
    },
    'virtual_screening': {
        'description': '虚拟筛选模拟',
        'metrics': ['Top-1% Precision', 'Top-5% Precision', '富集因子']
    }
}
```

## ⏰ 4周行动计划

### Week 1: 数据准备与环境搭建
```bash
# Day 1-2: 数据收集
□ 下载ChEMBL抗菌活性数据
□ 数据清洗和标准化
□ 创建二分类标签 (MIC < 16 μg/mL)

# Day 3-4: 特征生成
□ 使用generate_features.py生成Mordred特征
□ 验证特征文件格式和质量
□ 创建标准的训练/测试划分

# Day 5-7: 环境配置
□ 安装ChemProp: pip install chemprop
□ 安装AutoGluon: pip install autogluon
□ 测试所有依赖包
```

### Week 2: 基线模型实现
```bash
# Day 1-3: ChemProp基线
□ 转换数据为ChemProp格式
□ 训练ChemProp分类模型
□ 获得基线性能结果

# Day 4-5: AutoGluon基线
□ 准备Mordred特征数据
□ 训练AutoGluon模型
□ 获得AutoGluon性能结果

# Day 6-7: 结果验证
□ 确保两个基线模型正常工作
□ 初步性能对比分析
```

### Week 3: 神农框架实现与对比
```bash
# Day 1-3: 神农框架分类版本
□ 修改现有框架支持分类任务
□ 实现注意力融合机制
□ 训练神农分类模型

# Day 4-5: 完整对比实验
□ 3次独立运行 × 3个模型
□ 5折交叉验证
□ 收集所有性能指标

# Day 6-7: 统计分析
□ 计算均值和标准差
□ 进行统计显著性检验
□ 分析结果和问题
```

### Week 4: 应用验证与报告
```bash
# Day 1-2: 虚拟筛选模拟
□ 模拟大规模化合物筛选
□ 计算富集因子和命中率
□ 分析实际应用价值

# Day 3-4: 效率分析
□ 测量推理速度
□ 分析内存使用
□ 量化预计算特征的优势

# Day 5-7: 结果整理
□ 制作性能对比图表
□ 撰写实验报告
□ 准备论文初稿
```

## 🛠️ 技术实现要点

### 1. 数据准备脚本
```python
# data_preparation.py
def prepare_antibacterial_data():
    """准备抗菌数据"""
    # 1. 加载ChEMBL数据
    # 2. 过滤抗菌相关assay
    # 3. 创建二分类标签
    # 4. 数据质量检查
    # 5. 保存标准格式
    pass

def create_train_test_split():
    """创建训练测试划分"""
    # 使用scaffold splitting确保化学多样性
    pass
```

### 2. ChemProp集成
```bash
# ChemProp训练命令
chemprop_train \
  --data_path train_data.csv \
  --separate_val_path val_data.csv \
  --save_dir chemprop_model \
  --dataset_type classification \
  --epochs 50 \
  --batch_size 64

# ChemProp预测命令  
chemprop_predict \
  --test_path test_data.csv \
  --checkpoint_dir chemprop_model \
  --preds_path predictions.csv
```

### 3. AutoGluon集成
```python
# autogluon_wrapper.py
from autogluon.tabular import TabularPredictor

def train_autogluon(train_df):
    predictor = TabularPredictor(
        label='activity',
        problem_type='binary'
    )
    predictor.fit(
        train_df,
        time_limit=300,  # 5分钟
        presets='medium_quality_faster_train'
    )
    return predictor
```

### 4. 神农框架分类适配
```python
# 主要修改点
modifications = {
    'loss_function': 'nn.BCELoss()',
    'output_activation': 'nn.Sigmoid()',
    'metrics': 'classification_metrics',
    'data_loader': 'classification_labels'
}
```

## 📊 评估指标体系

### 分类性能指标
```python
classification_metrics = {
    'AUC': 'roc_auc_score',
    'F1': 'f1_score', 
    'Precision': 'precision_score',
    'Recall': 'recall_score',
    'Accuracy': 'accuracy_score'
}
```

### 虚拟筛选指标
```python
screening_metrics = {
    'Top_1_Precision': 'top 1%化合物中活性比例',
    'Top_5_Precision': 'top 5%化合物中活性比例', 
    'Enrichment_Factor': '相对随机选择的富集倍数',
    'Hit_Rate': '在给定阈值下的命中率'
}
```

### 效率指标
```python
efficiency_metrics = {
    'Training_Time': '模型训练时间',
    'Inference_Speed': '每秒预测化合物数',
    'Memory_Usage': '内存占用量',
    'Scalability': '扩展到大数据集的能力'
}
```

## 🎯 预期结果与风险

### 乐观预期
```python
optimistic_results = {
    'AUC_improvement': '+0.03-0.05 vs ChemProp',
    'F1_improvement': '+0.02-0.04 vs ChemProp', 
    'Enrichment_factor': '3-5倍 vs 随机',
    'Speed_advantage': '2-3倍 vs ChemProp (预计算特征)'
}
```

### 保守预期
```python
conservative_results = {
    'AUC_improvement': '+0.01-0.02 vs ChemProp',
    'Statistical_significance': 'p < 0.05',
    'Practical_value': '在虚拟筛选中有明显优势'
}
```

### 主要风险
```python
risks = {
    'limited_improvement': '改进幅度可能很小',
    'data_dependency': '性能可能依赖特定数据集',
    'complexity_cost': '复杂度增加但收益有限'
}
```

## 📝 论文撰写策略

### 核心卖点
1. **实用价值**: 专注于药物筛选应用
2. **效率优势**: 预计算特征的工程价值
3. **方法创新**: 化学导向的注意力融合

### 论文结构 (简化版)
```markdown
1. Introduction (20%)
   - 药物筛选挑战
   - 多模态学习必要性
   - 我们的贡献

2. Methods (30%)
   - 神农框架架构
   - 注意力融合机制
   - 实验设计

3. Results (35%)
   - 性能对比
   - 虚拟筛选验证
   - 效率分析

4. Discussion (15%)
   - 实际应用价值
   - 局限性
   - 未来工作
```

### 关键图表 (3-4个)
1. **架构图**: 神农框架示意图
2. **性能对比**: AUC、F1等指标对比
3. **筛选曲线**: 虚拟筛选富集曲线
4. **效率对比**: 速度和内存使用对比

## 💡 成功关键因素

### 1. 实验公平性
- 相同的数据划分
- 相同的评估指标  
- 相同的硬件环境

### 2. 结果可信度
- 多次独立运行
- 统计显著性检验
- 诚实报告局限性

### 3. 实用价值导向
- 关注药物筛选相关指标
- 强调工程实现优势
- 提供实际应用案例

## 🚀 立即行动

### 本周必须完成
1. **数据收集**: 下载ChEMBL抗菌数据
2. **环境搭建**: 安装ChemProp和AutoGluon
3. **特征生成**: 使用现有脚本生成Mordred特征
4. **基线测试**: 确保ChemProp能正常运行

### 成功标准
- 如果AUC提升0.02+且有统计显著性 → 成功
- 如果虚拟筛选富集因子>2.0 → 有实用价值
- 如果推理速度有明显优势 → 有工程价值

---

**核心思路**: 简单、实用、高效。用最少的实验证明最大的价值。
