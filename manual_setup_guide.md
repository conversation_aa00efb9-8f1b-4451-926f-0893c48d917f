# 神农框架环境手动安装指南

**作者**: ZK  
**邮箱**: <EMAIL>  
**日期**: 2025-06-30  

## 🎯 安装目标

在WSL环境中创建名为"shennong"的conda环境，支持：
- Python 3.11
- PyTorch (CUDA 11.8支持GTX 970)
- 神农框架实验所需的所有依赖

## 📋 手动安装步骤

### 步骤1: 安装Miniconda (如果未安装)

```bash
# 检查是否已安装conda
which conda

# 如果未安装，下载并安装Miniconda
cd ~
wget https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh
bash Miniconda3-latest-Linux-x86_64.sh

# 按照提示完成安装，然后重启终端或运行:
source ~/.bashrc

# 验证安装
conda --version
```

### 步骤2: 创建神农环境

```bash
# 创建Python 3.11环境
conda create -n shennong python=3.11 -y

# 激活环境
conda activate shennong

# 验证Python版本
python --version
```

### 步骤3: 安装PyTorch (CUDA 11.8)

```bash
# 激活环境
conda activate shennong

# 安装PyTorch with CUDA 11.8支持
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# 验证PyTorch和CUDA
python -c "import torch; print(f'PyTorch: {torch.__version__}'); print(f'CUDA available: {torch.cuda.is_available()}')"
```

### 步骤4: 安装RDKit

```bash
# 使用conda安装RDKit (推荐)
conda install -c conda-forge rdkit -y

# 验证RDKit
python -c "from rdkit import Chem; print('RDKit安装成功')"
```

### 步骤5: 安装科学计算基础包

```bash
# 安装基础科学计算包
pip install numpy pandas scikit-learn matplotlib seaborn jupyter

# 安装统计分析包
pip install scipy statsmodels

# 安装实用工具
pip install tqdm psutil memory_profiler
```

### 步骤6: 安装分子描述符包

```bash
# 安装Mordred
pip install mordred

# 验证Mordred
python -c "from mordred import Calculator, descriptors; print('Mordred安装成功')"
```

### 步骤7: 安装ChemProp

```bash
# 安装ChemProp
pip install chemprop

# 验证ChemProp
python -c "import chemprop; print('ChemProp安装成功')"
```

### 步骤8: 安装AutoGluon

```bash
# 安装AutoGluon
pip install autogluon

# 验证AutoGluon
python -c "from autogluon.tabular import TabularPredictor; print('AutoGluon安装成功')"
```

### 步骤9: 安装图神经网络包

```bash
# 安装PyTorch Geometric
pip install torch-geometric

# 安装DGL
pip install dgl -f https://data.dgl.ai/wheels/repo.html

# 验证安装
python -c "import torch_geometric; import dgl; print('图神经网络包安装成功')"
```

### 步骤10: 安装化学信息学包

```bash
# 安装ChEMBL和PubChem访问包
pip install chembl_webresource_client pubchempy

# 安装实验跟踪包
pip install tensorboard wandb

# 安装超参数优化和模型解释包
pip install optuna shap
```

## ✅ 验证安装

创建验证脚本：

```bash
# 创建验证脚本
cat > verify_installation.py << 'EOF'
import sys

def test_imports():
    packages = [
        'torch', 'numpy', 'pandas', 'sklearn', 'rdkit', 
        'mordred', 'chemprop', 'autogluon', 'matplotlib', 
        'seaborn', 'scipy', 'statsmodels', 'torch_geometric'
    ]
    
    print("🧬 神农框架环境验证")
    print("=" * 40)
    print(f"Python版本: {sys.version.split()[0]}")
    print()
    
    success = 0
    for package in packages:
        try:
            if package == 'sklearn':
                import sklearn
                print(f"✅ {package}: {sklearn.__version__}")
            elif package == 'torch_geometric':
                import torch_geometric
                print(f"✅ {package}: {torch_geometric.__version__}")
            else:
                module = __import__(package)
                version = getattr(module, '__version__', 'unknown')
                print(f"✅ {package}: {version}")
            success += 1
        except ImportError as e:
            print(f"❌ {package}: 导入失败")
    
    print(f"\n验证结果: {success}/{len(packages)} 包成功安装")
    
    # 测试CUDA
    try:
        import torch
        print(f"\n🔥 CUDA支持: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"   GPU: {torch.cuda.get_device_name(0)}")
    except:
        print("❌ CUDA测试失败")
    
    # 测试RDKit分子处理
    try:
        from rdkit import Chem
        mol = Chem.MolFromSmiles('CCO')
        print(f"✅ RDKit分子处理: {'成功' if mol else '失败'}")
    except:
        print("❌ RDKit测试失败")

if __name__ == "__main__":
    test_imports()
EOF

# 运行验证
python verify_installation.py

# 清理
rm verify_installation.py
```

## 🚀 环境使用

### 激活环境
```bash
# 每次使用前激活环境
conda activate shennong

# 验证环境
python -c "print('神农环境已激活')"
```

### 常用命令
```bash
# 查看已安装包
conda list

# 安装新包
pip install package_name

# 启动Jupyter
jupyter notebook

# 运行神农框架
cd /path/to/Shennong
python scripts/generate_features.py --help
```

## 🔧 故障排除

### 常见问题

#### 1. CUDA不可用
```bash
# 检查NVIDIA驱动
nvidia-smi

# 重新安装PyTorch
pip uninstall torch torchvision torchaudio
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
```

#### 2. RDKit导入失败
```bash
# 重新安装RDKit
conda remove rdkit
conda install -c conda-forge rdkit -y
```

#### 3. ChemProp安装失败
```bash
# 更新pip并重新安装
pip install --upgrade pip
pip install chemprop --upgrade
```

#### 4. AutoGluon安装失败
```bash
# 分步安装AutoGluon组件
pip install autogluon.core autogluon.features autogluon.tabular
```

#### 5. 内存不足
```bash
# 清理conda缓存
conda clean --all

# 清理pip缓存
pip cache purge
```

## 📦 环境导出和恢复

### 导出环境
```bash
# 导出完整环境
conda env export > shennong_environment.yml

# 仅导出pip包
pip freeze > requirements.txt
```

### 恢复环境
```bash
# 从yml文件创建环境
conda env create -f shennong_environment.yml

# 从requirements.txt安装
pip install -r requirements.txt
```

## 🎯 下一步

环境安装完成后，您可以：

1. **运行实验**: 按照 `experiment_plans/` 中的计划开始实验
2. **生成特征**: 使用 `scripts/generate_features.py`
3. **训练模型**: 使用神农框架进行模型训练
4. **对比实验**: 与ChemProp和AutoGluon进行性能对比

---

**🧬 神农尝百草，AI识良药 - 环境配置完成，开始您的研究之旅！** ✨
