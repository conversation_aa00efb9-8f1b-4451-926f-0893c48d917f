# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-29
# 描述: 神农框架训练子命令

"""
神农框架训练子命令

提供模型训练的命令行接口，支持完整的训练流程。
"""

import logging
from pathlib import Path
from typing import Optional

try:
    from configargparse import ArgumentParser, Namespace
except ImportError:
    from argparse import ArgumentParser, Namespace

from .utils import validate_file_path, validate_dir_path, print_banner
from ..training.trainer import ShennongTrainer
from ..data.datasets import AntibacterialDataset
from ..data.loaders import load_csv_data
from ..utils.config import ShennongConfig

logger = logging.getLogger(__name__)


class TrainSubcommand:
    """训练子命令类"""

    COMMAND = "train"
    HELP = "训练神农框架模型"

    @classmethod
    def add(cls, subparsers, parents):
        """添加训练子命令到解析器"""
        parser = subparsers.add_parser(
            cls.COMMAND,
            help=cls.HELP,
            parents=parents,
            description="训练神农框架抗菌化合物预测模型"
        )

        # 数据相关参数
        data_group = parser.add_argument_group("数据参数")
        data_group.add_argument(
            "--data-path",
            type=str,
            required=True,
            help="训练数据CSV文件路径"
        )
        data_group.add_argument(
            "--smiles-column",
            type=str,
            default="smiles",
            help="SMILES列名 (默认: smiles)"
        )
        data_group.add_argument(
            "--target-columns",
            type=str,
            nargs="+",
            help="目标列名列表"
        )
        data_group.add_argument(
            "--split-method",
            type=str,
            choices=["random", "scaffold", "stratified"],
            default="scaffold",
            help="数据划分方法 (默认: scaffold)"
        )
        data_group.add_argument(
            "--split-ratio",
            type=float,
            nargs=3,
            default=[0.8, 0.1, 0.1],
            help="训练/验证/测试集比例 (默认: 0.8 0.1 0.1)"
        )
        data_group.add_argument(
            "--features-path",
            type=str,
            default=None,
            help="预计算特征文件路径 (.npz格式) - 使用scripts/generate_features.py生成"
        )

        # 模型相关参数
        model_group = parser.add_argument_group("模型参数")
        model_group.add_argument(
            "--model-type",
            type=str,
            choices=["shennong", "graph-only", "expert-only"],
            default="shennong",
            help="模型类型 (默认: shennong)"
        )
        model_group.add_argument(
            "--hidden-size",
            type=int,
            default=300,
            help="隐藏层大小 (默认: 300)"
        )
        model_group.add_argument(
            "--num-layers",
            type=int,
            default=3,
            help="网络层数 (默认: 3)"
        )
        model_group.add_argument(
            "--dropout",
            type=float,
            default=0.1,
            help="Dropout率 (默认: 0.1)"
        )

        # 训练相关参数
        train_group = parser.add_argument_group("训练参数")
        train_group.add_argument(
            "--epochs",
            type=int,
            default=100,
            help="训练轮数 (默认: 100)"
        )
        train_group.add_argument(
            "--batch-size",
            type=int,
            default=32,
            help="批次大小 (默认: 32)"
        )
        train_group.add_argument(
            "--learning-rate",
            type=float,
            default=1e-3,
            help="学习率 (默认: 1e-3)"
        )
        train_group.add_argument(
            "--weight-decay",
            type=float,
            default=1e-4,
            help="权重衰减 (默认: 1e-4)"
        )

        # 输出相关参数
        output_group = parser.add_argument_group("输出参数")
        output_group.add_argument(
            "--output-dir",
            type=str,
            required=True,
            help="输出目录路径"
        )
        output_group.add_argument(
            "--save-interval",
            type=int,
            default=10,
            help="模型保存间隔 (默认: 10轮)"
        )
        output_group.add_argument(
            "--no-save",
            action="store_true",
            help="不保存模型检查点"
        )

        # 硬件相关参数
        hardware_group = parser.add_argument_group("硬件参数")
        hardware_group.add_argument(
            "--device",
            type=str,
            choices=["auto", "cpu", "cuda", "mps"],
            default="auto",
            help="计算设备 (默认: auto)"
        )
        hardware_group.add_argument(
            "--num-workers",
            type=int,
            default=4,
            help="数据加载器工作进程数 (默认: 4)"
        )

        parser.set_defaults(func=cls.func)
        return parser

    @classmethod
    def func(cls, args: Namespace):
        """执行训练命令"""
        print_banner("🧬 神农框架模型训练")

        # 验证参数
        cls._validate_args(args)

        # 加载配置
        config = cls._load_config(args)

        # 创建训练器
        trainer = ShennongTrainer(config)

        # 加载数据
        dataset = cls._load_dataset(args)

        # 开始训练
        logger.info("开始模型训练...")
        trainer.fit(dataset, split_method=args.split_method)

        logger.info("模型训练完成!")
        print("✅ 训练成功完成!")

    @classmethod
    def _validate_args(cls, args: Namespace):
        """验证命令行参数"""
        # 验证数据文件
        validate_file_path(args.data_path, must_exist=True)

        # 验证输出目录
        validate_dir_path(args.output_dir, create=True)

        # 验证分割比例
        if len(args.split_ratio) != 3:
            raise ValueError("split_ratio必须包含3个值")

        if sum(args.split_ratio) != 1.0:
            raise ValueError("split_ratio的和必须等于1.0")

        # 验证数值参数
        if args.epochs <= 0:
            raise ValueError("epochs必须大于0")

        if args.batch_size <= 0:
            raise ValueError("batch_size必须大于0")

        if args.learning_rate <= 0:
            raise ValueError("learning_rate必须大于0")

    @classmethod
    def _load_config(cls, args: Namespace) -> dict:
        """加载配置"""
        # 基础配置
        config = {
            'model': {
                'model_type': args.model_type,
                'hidden_size': args.hidden_size,
                'num_layers': args.num_layers,
                'dropout': args.dropout,
            },
            'training': {
                'epochs': args.epochs,
                'batch_size': args.batch_size,
                'learning_rate': args.learning_rate,
                'weight_decay': args.weight_decay,
            },
            'data': {
                'smiles_column': args.smiles_column,
                'target_columns': args.target_columns,
                'split_ratio': args.split_ratio,
                'features_path': args.features_path,
            },
            'hardware': {
                'device': args.device,
                'num_workers': args.num_workers,
            },
            'output': {
                'output_dir': args.output_dir,
                'save_interval': args.save_interval,
                'save_model': not args.no_save,
            }
        }

        # 如果指定了配置文件，则合并配置
        if hasattr(args, 'config') and args.config:
            file_config = ShennongConfig.load_config(args.config)
            config = ShennongConfig.merge_configs(file_config, config)

        return config

    @classmethod
    def _load_dataset(cls, args: Namespace) -> AntibacterialDataset:
        """加载数据集"""
        logger.info(f"加载数据集: {args.data_path}")

        # 使用CSV数据加载器
        dataset = load_csv_data(
            csv_path=args.data_path,
            smiles_column=args.smiles_column,
            target_columns=args.target_columns,
            dataset_type="antibacterial",
            validate_smiles=True,
            compute_descriptors=not bool(args.features_path),  # 如果有预计算特征则不计算描述符
            features_path=args.features_path
        )

        logger.info(f"数据集加载完成，共 {len(dataset)} 个样本")
        return dataset
