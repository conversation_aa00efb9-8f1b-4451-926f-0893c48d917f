# 评估指标定义

**作者**: ZK  
**邮箱**: <EMAIL>  
**日期**: 2025-06-30  

## 📊 评估指标体系

### 指标分类
```python
metric_categories = {
    'classification_metrics': '分类性能指标',
    'screening_metrics': '虚拟筛选指标', 
    'efficiency_metrics': '计算效率指标',
    'statistical_metrics': '统计分析指标'
}
```

## 🎯 分类性能指标

### 1. 基础分类指标

#### AUC (Area Under Curve)
```python
from sklearn.metrics import roc_auc_score

def calculate_auc(y_true, y_pred_proba):
    """计算AUC值"""
    return roc_auc_score(y_true, y_pred_proba)

# 解释
# AUC = 1.0: 完美分类器
# AUC = 0.5: 随机分类器
# AUC > 0.7: 可接受的性能
# AUC > 0.8: 良好性能
# AUC > 0.9: 优秀性能
```

#### Precision (精确率)
```python
from sklearn.metrics import precision_score

def calculate_precision(y_true, y_pred):
    """计算精确率"""
    return precision_score(y_true, y_pred)

# 公式: Precision = TP / (TP + FP)
# 含义: 预测为活性的化合物中，真正活性的比例
# 重要性: 在药物筛选中，减少假阳性很重要
```

#### Recall (召回率/敏感性)
```python
from sklearn.metrics import recall_score

def calculate_recall(y_true, y_pred):
    """计算召回率"""
    return recall_score(y_true, y_pred)

# 公式: Recall = TP / (TP + FN)
# 含义: 真正活性化合物中，被正确识别的比例
# 重要性: 避免遗漏潜在的活性化合物
```

#### F1-Score
```python
from sklearn.metrics import f1_score

def calculate_f1(y_true, y_pred):
    """计算F1分数"""
    return f1_score(y_true, y_pred)

# 公式: F1 = 2 * (Precision * Recall) / (Precision + Recall)
# 含义: 精确率和召回率的调和平均
# 重要性: 平衡精确率和召回率的综合指标
```

#### Accuracy (准确率)
```python
from sklearn.metrics import accuracy_score

def calculate_accuracy(y_true, y_pred):
    """计算准确率"""
    return accuracy_score(y_true, y_pred)

# 公式: Accuracy = (TP + TN) / (TP + TN + FP + FN)
# 含义: 所有预测中正确的比例
# 注意: 在不平衡数据集中可能误导
```

### 2. 高级分类指标

#### Matthews Correlation Coefficient (MCC)
```python
from sklearn.metrics import matthews_corrcoef

def calculate_mcc(y_true, y_pred):
    """计算MCC"""
    return matthews_corrcoef(y_true, y_pred)

# 范围: [-1, 1]
# MCC = 1: 完美预测
# MCC = 0: 随机预测
# MCC = -1: 完全错误预测
# 优势: 对不平衡数据集更稳健
```

#### Balanced Accuracy
```python
from sklearn.metrics import balanced_accuracy_score

def calculate_balanced_accuracy(y_true, y_pred):
    """计算平衡准确率"""
    return balanced_accuracy_score(y_true, y_pred)

# 公式: (Sensitivity + Specificity) / 2
# 优势: 对类别不平衡更敏感
```

## 🔬 虚拟筛选指标

### 1. Top-k 精确率

#### Top-1% Precision
```python
def calculate_topk_precision(y_true, y_pred_proba, k=0.01):
    """计算Top-k精确率"""
    n_top = int(k * len(y_true))
    top_indices = np.argsort(y_pred_proba)[-n_top:]
    top_labels = y_true[top_indices]
    return np.mean(top_labels)

# 含义: 预测概率最高的1%化合物中活性化合物的比例
# 应用: 模拟实际筛选中只测试少量化合物的情况
```

#### Top-5% Precision
```python
def calculate_top5_precision(y_true, y_pred_proba):
    """计算Top-5%精确率"""
    return calculate_topk_precision(y_true, y_pred_proba, k=0.05)

# 含义: 预测概率最高的5%化合物中活性化合物的比例
```

### 2. 富集因子 (Enrichment Factor)

#### EF at 1%
```python
def calculate_enrichment_factor(y_true, y_pred_proba, k=0.01):
    """计算富集因子"""
    topk_precision = calculate_topk_precision(y_true, y_pred_proba, k)
    random_precision = np.mean(y_true)
    
    if random_precision == 0:
        return 0
    
    return topk_precision / random_precision

# 含义: 相对于随机选择的富集倍数
# EF = 1: 与随机选择相同
# EF > 2: 有实用价值
# EF > 5: 优秀性能
```

### 3. 早期识别能力

#### BEDROC (Boltzmann-Enhanced Discrimination of ROC)
```python
def calculate_bedroc(y_true, y_pred_proba, alpha=20.0):
    """计算BEDROC分数"""
    # 按预测概率排序
    sorted_indices = np.argsort(y_pred_proba)[::-1]
    sorted_labels = y_true[sorted_indices]
    
    n = len(y_true)
    n_actives = np.sum(y_true)
    
    if n_actives == 0:
        return 0
    
    # 计算BEDROC
    sum_exp = 0
    for i, label in enumerate(sorted_labels):
        if label == 1:
            sum_exp += np.exp(-alpha * i / n)
    
    bedroc = sum_exp / n_actives
    bedroc = bedroc * (1 - np.exp(-alpha)) / (np.exp(alpha/n) - 1)
    
    return bedroc

# 优势: 更重视早期识别的活性化合物
# 应用: 评估虚拟筛选的早期富集能力
```

## ⚡ 计算效率指标

### 1. 时间效率

#### 训练时间
```python
import time

def measure_training_time(model, train_loader, epochs):
    """测量训练时间"""
    start_time = time.time()
    
    for epoch in range(epochs):
        for batch in train_loader:
            # 训练步骤
            pass
    
    end_time = time.time()
    return end_time - start_time
```

#### 推理速度
```python
def measure_inference_speed(model, test_data, batch_size=1000):
    """测量推理速度 (化合物/秒)"""
    start_time = time.time()
    
    n_compounds = len(test_data)
    predictions = model.predict(test_data)
    
    end_time = time.time()
    inference_time = end_time - start_time
    
    return n_compounds / inference_time  # 化合物/秒
```

### 2. 资源使用

#### 内存使用
```python
import psutil
import os

def measure_memory_usage():
    """测量内存使用"""
    process = psutil.Process(os.getpid())
    memory_info = process.memory_info()
    
    return {
        'rss': memory_info.rss / 1024 / 1024,  # MB
        'vms': memory_info.vms / 1024 / 1024   # MB
    }
```

#### GPU使用 (如果适用)
```python
import torch

def measure_gpu_usage():
    """测量GPU使用"""
    if torch.cuda.is_available():
        return {
            'allocated': torch.cuda.memory_allocated() / 1024 / 1024,  # MB
            'cached': torch.cuda.memory_reserved() / 1024 / 1024       # MB
        }
    return None
```

## 📈 统计分析指标

### 1. 描述性统计

#### 基本统计量
```python
def calculate_descriptive_stats(values):
    """计算描述性统计"""
    return {
        'mean': np.mean(values),
        'std': np.std(values),
        'min': np.min(values),
        'max': np.max(values),
        'median': np.median(values),
        'q25': np.percentile(values, 25),
        'q75': np.percentile(values, 75)
    }
```

### 2. 显著性检验

#### McNemar检验 (分类任务)
```python
from statsmodels.stats.contingency_tables import mcnemar

def mcnemar_test(y_true, pred1, pred2):
    """McNemar检验比较两个分类器"""
    # 构建列联表
    correct1 = (pred1 == y_true)
    correct2 = (pred2 == y_true)
    
    table = np.array([
        [np.sum(correct1 & correct2), np.sum(correct1 & ~correct2)],
        [np.sum(~correct1 & correct2), np.sum(~correct1 & ~correct2)]
    ])
    
    result = mcnemar(table, exact=True)
    return result.pvalue
```

#### 配对t检验
```python
from scipy.stats import ttest_rel

def paired_t_test(scores1, scores2):
    """配对t检验"""
    statistic, p_value = ttest_rel(scores1, scores2)
    return {
        'statistic': statistic,
        'p_value': p_value,
        'significant': p_value < 0.05
    }
```

### 3. 效应量

#### Cohen's d
```python
def cohens_d(group1, group2):
    """计算Cohen's d效应量"""
    n1, n2 = len(group1), len(group2)
    s1, s2 = np.std(group1, ddof=1), np.std(group2, ddof=1)
    
    # 合并标准差
    pooled_std = np.sqrt(((n1-1)*s1**2 + (n2-1)*s2**2) / (n1+n2-2))
    
    # Cohen's d
    d = (np.mean(group1) - np.mean(group2)) / pooled_std
    
    return d

# 解释:
# |d| < 0.2: 小效应
# 0.2 ≤ |d| < 0.5: 中等效应  
# 0.5 ≤ |d| < 0.8: 大效应
# |d| ≥ 0.8: 非常大效应
```

## 📊 综合评估框架

### 评估报告模板
```python
def generate_evaluation_report(y_true, predictions_dict):
    """生成综合评估报告"""
    report = {}
    
    for method_name, pred_proba in predictions_dict.items():
        pred_binary = (pred_proba > 0.5).astype(int)
        
        # 分类指标
        classification_metrics = {
            'auc': calculate_auc(y_true, pred_proba),
            'precision': calculate_precision(y_true, pred_binary),
            'recall': calculate_recall(y_true, pred_binary),
            'f1': calculate_f1(y_true, pred_binary),
            'accuracy': calculate_accuracy(y_true, pred_binary),
            'mcc': calculate_mcc(y_true, pred_binary)
        }
        
        # 虚拟筛选指标
        screening_metrics = {
            'top1_precision': calculate_topk_precision(y_true, pred_proba, 0.01),
            'top5_precision': calculate_topk_precision(y_true, pred_proba, 0.05),
            'enrichment_factor_1': calculate_enrichment_factor(y_true, pred_proba, 0.01),
            'enrichment_factor_5': calculate_enrichment_factor(y_true, pred_proba, 0.05)
        }
        
        report[method_name] = {
            'classification': classification_metrics,
            'screening': screening_metrics
        }
    
    return report
```

### 可视化指标
```python
def plot_evaluation_metrics(report):
    """绘制评估指标对比图"""
    import matplotlib.pyplot as plt
    
    methods = list(report.keys())
    metrics = ['auc', 'f1', 'top1_precision', 'enrichment_factor_1']
    
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    axes = axes.flatten()
    
    for i, metric in enumerate(metrics):
        values = []
        for method in methods:
            if metric in report[method]['classification']:
                values.append(report[method]['classification'][metric])
            else:
                values.append(report[method]['screening'][metric])
        
        axes[i].bar(methods, values)
        axes[i].set_title(metric.upper())
        axes[i].set_ylabel('Score')
    
    plt.tight_layout()
    plt.savefig('evaluation_metrics.png', dpi=300, bbox_inches='tight')
    plt.show()
```

## 🎯 成功标准

### 最低成功标准
```python
minimum_success_criteria = {
    'auc_improvement': 0.02,  # AUC提升至少0.02
    'statistical_significance': 0.05,  # p < 0.05
    'enrichment_factor': 2.0,  # 富集因子 > 2
    'consistency': 'all_metrics_positive'  # 所有指标都不显著差于基线
}
```

### 理想成功标准
```python
ideal_success_criteria = {
    'auc_improvement': 0.05,  # AUC提升0.05
    'f1_improvement': 0.03,   # F1提升0.03
    'enrichment_factor': 3.0,  # 富集因子 > 3
    'top1_precision': 0.3,    # Top-1%精确率 > 30%
    'effect_size': 0.5        # Cohen's d > 0.5 (中等效应)
}
```

---

**评估原则**: 使用多个指标综合评估，重点关注实际应用价值，诚实报告所有结果。
