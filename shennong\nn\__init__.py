# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-27
# 描述: 神农框架神经网络组件模块初始化

"""
神农框架神经网络组件模块

提供构建神农框架所需的各种神经网络组件，包括注意力机制、
消息传递层、聚合层、预测器等。

主要组件:
- attention: 生物启发的注意力机制
- message_passing: 消息传递层
- aggregation: 聚合层
- predictors: 预测器
- metrics: 评估指标
- losses: 损失函数
"""

from .attention import (
    BiologicalAttentionFusion,
    MechanismAwareAttention,
    MultiHeadAttention,
    CrossAttention
)

from .message_passing import (
    AntibacterialMessagePassing,
    EnhancedBondMessagePassing
)

from .aggregation import (
    GraphAggregation,
    AttentionAggregation,
    SetToSetAggregation
)

from .predictors import (
    ActivityPredictor,
    MechanismClassifier,
    MultiTaskPredictor,
    UncertaintyPredictor
)

from .metrics import (
    AntibacterialMetrics,
    RegressionMetrics,
    ClassificationMetrics,
    MultiTaskMetrics
)

from .losses import (
    AntibacterialLoss,
    MultiTaskLoss,
    UncertaintyLoss,
    AttentionRegularizationLoss
)

__all__ = [
    # 注意力机制
    'BiologicalAttentionFusion',
    'MechanismAwareAttention', 
    'MultiHeadAttention',
    'CrossAttention',
    
    # 消息传递
    'AntibacterialMessagePassing',
    'EnhancedBondMessagePassing',
    
    # 聚合层
    'GraphAggregation',
    'AttentionAggregation',
    'SetToSetAggregation',
    
    # 预测器
    'ActivityPredictor',
    'MechanismClassifier',
    'MultiTaskPredictor',
    'UncertaintyPredictor',
    
    # 评估指标
    'AntibacterialMetrics',
    'RegressionMetrics',
    'ClassificationMetrics',
    'MultiTaskMetrics',
    
    # 损失函数
    'AntibacterialLoss',
    'MultiTaskLoss',
    'UncertaintyLoss',
    'AttentionRegularizationLoss',
]
