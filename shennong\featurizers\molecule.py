# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-27
# 描述: 神农框架分子特征化器

"""
神农框架分子特征化器

提供多种分子描述符计算方法，支持Mordred、RDKit等描述符库。
针对抗菌化合物优化特征选择和计算。
"""

from typing import List, Dict, Any, Optional, Union
import numpy as np
import pandas as pd
from abc import ABC, abstractmethod
import logging

# RDKit导入
try:
    from rdkit import Chem
    from rdkit.Chem import Descriptors, rdMolDescriptors
    from rdkit.ML.Descriptors import MoleculeDescriptors
except ImportError:
    raise ImportError("RDKit未安装，请运行: pip install rdkit-pypi")

# Mordred导入（可选）
try:
    from mordred import Calculator, descriptors
    import mordred
    MORDRED_AVAILABLE = True
    MORDRED_VERSION = getattr(mordred, '__version__', 'unknown')
    logger.info(f"Mordred库已加载，版本: {MORDRED_VERSION}")
except ImportError:
    MORDRED_AVAILABLE = False
    MORDRED_VERSION = None
    logger.warning("Mordred未安装，部分功能不可用。安装: pip install mordred")

logger = logging.getLogger(__name__)


class MordredDescriptorManager:
    """
    Mordred描述符管理器

    提供动态描述符检测、版本兼容性处理和智能错误恢复。
    """

    _instance = None
    _descriptor_cache = {}

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if self._initialized:
            return

        self.available = MORDRED_AVAILABLE
        self.version = MORDRED_VERSION
        self.descriptor_counts = {}
        self.failed_descriptors = set()

        if self.available:
            self._analyze_descriptors()

        self._initialized = True

    def _analyze_descriptors(self):
        """分析可用的描述符"""
        try:
            # 获取所有描述符类
            all_descriptors = [
                descriptors.Weight, descriptors.LogP, descriptors.MolecularId,
                descriptors.AtomCount, descriptors.BondCount, descriptors.RingCount,
                descriptors.Aromatic, descriptors.Hybridization, descriptors.KappaShapeIndex,
                descriptors.PetitjeanIndex, descriptors.WalkCount, descriptors.PathCount,
                descriptors.Connectivity, descriptors.InformationIndex, descriptors.TopologicalIndex,
                descriptors.EccentricConnectivityIndex, descriptors.DistanceMatrix,
                descriptors.McGowanVolume, descriptors.MoeType, descriptors.LabuteASA,
                descriptors.PEOE, descriptors.Charge, descriptors.Constitutional,
                descriptors.TopoPSA, descriptors.FragmentComplexity, descriptors.Framework,
                descriptors.SLogP, descriptors.SMR, descriptors.Estate, descriptors.VSA,
                descriptors.DetourMatrix, descriptors.Autocorrelation, descriptors.CPSA,
                descriptors.RDFPROBE, descriptors.GeometricalIndex, descriptors.WHIM,
                descriptors.GETAWAY, descriptors.Pharmacophore
            ]

            # 测试每个描述符类别
            for desc_class in all_descriptors:
                try:
                    calc = Calculator(desc_class, ignore_3D=True)
                    count = len(calc.descriptors)
                    self.descriptor_counts[desc_class.__name__] = count
                    logger.debug(f"描述符类 {desc_class.__name__}: {count}个")
                except Exception as e:
                    logger.warning(f"描述符类 {desc_class.__name__} 不可用: {e}")
                    self.failed_descriptors.add(desc_class.__name__)

            # 计算总数
            total_2d = self._get_total_descriptor_count(ignore_3D=True)
            total_3d = self._get_total_descriptor_count(ignore_3D=False)

            logger.info(f"Mordred描述符分析完成:")
            logger.info(f"  - 2D描述符总数: {total_2d}")
            logger.info(f"  - 3D描述符总数: {total_3d}")
            logger.info(f"  - 失败的描述符类: {len(self.failed_descriptors)}")

        except Exception as e:
            logger.error(f"Mordred描述符分析失败: {e}")

    def _get_total_descriptor_count(self, ignore_3D: bool = True) -> int:
        """获取描述符总数"""
        try:
            calc = Calculator(descriptors, ignore_3D=ignore_3D)
            return len(calc.descriptors)
        except Exception as e:
            logger.error(f"无法计算描述符总数: {e}")
            return 0

    def get_descriptor_count(self, ignore_3D: bool = True) -> int:
        """获取当前环境下的描述符数量"""
        if not self.available:
            return 0

        cache_key = f"count_3d_{not ignore_3D}"
        if cache_key not in self._descriptor_cache:
            count = self._get_total_descriptor_count(ignore_3D)
            self._descriptor_cache[cache_key] = count

        return self._descriptor_cache[cache_key]

    def create_calculator(
        self,
        descriptor_selection: str = 'all',
        ignore_3D: bool = True,
        exclude_failed: bool = True
    ) -> Optional['Calculator']:
        """
        创建Mordred计算器

        Args:
            descriptor_selection: 描述符选择策略
            ignore_3D: 是否忽略3D描述符
            exclude_failed: 是否排除失败的描述符

        Returns:
            Mordred计算器实例
        """
        if not self.available:
            return None

        try:
            if descriptor_selection == 'all':
                calc = Calculator(descriptors, ignore_3D=ignore_3D)
            elif descriptor_selection == 'safe':
                # 使用已知稳定的描述符
                safe_descriptors = [
                    descriptors.Weight, descriptors.LogP, descriptors.AtomCount,
                    descriptors.BondCount, descriptors.RingCount, descriptors.Aromatic,
                    descriptors.Constitutional, descriptors.TopoPSA, descriptors.Estate
                ]
                calc = Calculator(safe_descriptors, ignore_3D=ignore_3D)
            elif isinstance(descriptor_selection, list):
                calc = Calculator(descriptor_selection, ignore_3D=ignore_3D)
            else:
                raise ValueError(f"不支持的描述符选择: {descriptor_selection}")

            return calc

        except Exception as e:
            logger.error(f"创建Mordred计算器失败: {e}")
            return None

    def get_version_info(self) -> Dict[str, Any]:
        """获取版本信息"""
        return {
            'available': self.available,
            'version': self.version,
            'descriptor_counts': self.descriptor_counts,
            'failed_descriptors': list(self.failed_descriptors),
            'total_2d': self.get_descriptor_count(ignore_3D=True),
            'total_3d': self.get_descriptor_count(ignore_3D=False)
        }


class BaseMoleculeFeaturizer(ABC):
    """分子特征化器基类"""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化特征化器

        Args:
            config: 配置字典
        """
        self.config = config or {}
        self.feature_names = []
        self.feature_dim = 0

    @abstractmethod
    def featurize(self, mol: Chem.Mol) -> np.ndarray:
        """
        特征化分子

        Args:
            mol: RDKit分子对象

        Returns:
            特征向量
        """
        pass

    def get_feature_names(self) -> List[str]:
        """获取特征名称列表"""
        return self.feature_names

    def get_feature_dim(self) -> int:
        """获取特征维度"""
        return self.feature_dim


class RDKitFeaturizer(BaseMoleculeFeaturizer):
    """
    RDKit描述符特征化器

    使用RDKit内置的分子描述符，包括基础的物理化学性质。
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化RDKit特征化器"""
        super().__init__(config)

        # 选择要计算的描述符
        self.selected_descriptors = self.config.get('selected_descriptors', 'all')

        if self.selected_descriptors == 'all':
            # 使用所有可用的描述符
            self.descriptor_names = [desc[0] for desc in Descriptors._descList]
        elif isinstance(self.selected_descriptors, list):
            # 使用指定的描述符
            self.descriptor_names = self.selected_descriptors
        else:
            # 使用预定义的描述符集合
            self.descriptor_names = self._get_predefined_descriptors(self.selected_descriptors)

        # 创建描述符计算器
        self.calculator = MoleculeDescriptors.MolecularDescriptorCalculator(self.descriptor_names)

        self.feature_names = self.descriptor_names
        self.feature_dim = len(self.descriptor_names)

        logger.info(f"初始化RDKit特征化器: {self.feature_dim}个描述符")

    def _get_predefined_descriptors(self, descriptor_set: str) -> List[str]:
        """获取预定义的描述符集合"""
        if descriptor_set == 'basic':
            return [
                'MolWt', 'LogP', 'NumHDonors', 'NumHAcceptors', 'TPSA',
                'NumRotatableBonds', 'NumAromaticRings', 'NumSaturatedRings',
                'FractionCsp3', 'HeavyAtomCount'
            ]
        elif descriptor_set == 'lipinski':
            return ['MolWt', 'LogP', 'NumHDonors', 'NumHAcceptors']
        elif descriptor_set == 'antibacterial':
            return [
                'MolWt', 'LogP', 'NumHDonors', 'NumHAcceptors', 'TPSA',
                'NumRotatableBonds', 'NumAromaticRings', 'NumAliphaticRings',
                'FractionCsp3', 'HeavyAtomCount', 'NumHeteroatoms',
                'BertzCT', 'BalabanJ', 'Chi0v', 'Chi1v', 'Kappa1', 'Kappa2'
            ]
        else:
            raise ValueError(f"未知的描述符集合: {descriptor_set}")

    def featurize(self, mol: Chem.Mol) -> np.ndarray:
        """计算RDKit描述符"""
        if mol is None:
            return np.zeros(self.feature_dim)

        try:
            # 计算描述符
            descriptors = self.calculator.CalcDescriptors(mol)

            # 处理无效值
            descriptors = [
                desc if not (np.isnan(desc) or np.isinf(desc)) else 0.0
                for desc in descriptors
            ]

            return np.array(descriptors, dtype=np.float32)

        except Exception as e:
            logger.warning(f"计算RDKit描述符失败: {e}")
            return np.zeros(self.feature_dim)


class MordredFeaturizer(BaseMoleculeFeaturizer):
    """
    增强的Mordred描述符特征化器

    使用Mordred库计算大量的分子描述符，支持动态维度检测和智能错误处理。
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化Mordred特征化器"""
        super().__init__(config)

        # 获取描述符管理器
        self.manager = MordredDescriptorManager()

        if not self.manager.available:
            error_msg = "Mordred库未安装或不可用，无法使用MordredFeaturizer"
            logger.error(error_msg)
            raise ImportError(error_msg)

        # 配置选项
        self.ignore_3D = self.config.get('ignore_3D', True)
        self.selected_descriptors = self.config.get('selected_descriptors', 'all')
        self.handle_errors = self.config.get('handle_errors', 'warn')
        self.use_dynamic_detection = self.config.get('use_dynamic_detection', True)

        # 创建计算器
        if self.use_dynamic_detection:
            # 使用管理器创建计算器
            self.calculator = self.manager.create_calculator(
                descriptor_selection=self.selected_descriptors,
                ignore_3D=self.ignore_3D
            )
        else:
            # 传统方式创建计算器
            if self.selected_descriptors == 'all':
                self.calculator = Calculator(descriptors, ignore_3D=self.ignore_3D)
            else:
                selected_desc_classes = self._get_selected_descriptor_classes()
                self.calculator = Calculator(selected_desc_classes, ignore_3D=self.ignore_3D)

        if self.calculator is None:
            raise RuntimeError("无法创建Mordred计算器")

        # 获取描述符名称和维度
        self.feature_names = [str(desc) for desc in self.calculator.descriptors]
        self.feature_dim = len(self.feature_names)

        # 记录实际的描述符数量
        actual_count = self.manager.get_descriptor_count(ignore_3D=self.ignore_3D)

        logger.info(f"初始化Mordred特征化器:")
        logger.info(f"  - 实际描述符数量: {self.feature_dim}")
        logger.info(f"  - 理论最大数量: {actual_count}")
        logger.info(f"  - Mordred版本: {self.manager.version}")
        logger.info(f"  - 3D描述符: {'忽略' if self.ignore_3D else '包含'}")

        # 验证硬编码的1613是否准确
        if actual_count != 1613:
            logger.warning(f"⚠️ 描述符数量与硬编码值不符！实际: {actual_count}, 硬编码: 1613")
            logger.warning("建议更新配置文件中的descriptor_dim参数")

    def _get_selected_descriptor_classes(self) -> List:
        """获取选择的描述符类别"""
        if isinstance(self.selected_descriptors, list):
            return self.selected_descriptors

        # 预定义的描述符类别组合
        if self.selected_descriptors == 'basic':
            return [
                descriptors.Weight, descriptors.LogP, descriptors.MolecularId,
                descriptors.AtomCount, descriptors.BondCount, descriptors.RingCount
            ]
        elif self.selected_descriptors == 'topological':
            return [
                descriptors.TopologicalIndex, descriptors.WalkCount,
                descriptors.PathCount, descriptors.Connectivity
            ]
        elif self.selected_descriptors == 'constitutional':
            return [
                descriptors.Constitutional, descriptors.AtomCount,
                descriptors.BondCount, descriptors.RingCount
            ]
        else:
            # 默认返回所有描述符
            return descriptors

    def featurize(self, mol: Chem.Mol, return_failed: bool = False) -> Union[np.ndarray, Tuple[np.ndarray, Dict[str, Any]]]:
        """
        计算Mordred描述符

        Args:
            mol: RDKit分子对象
            return_failed: 是否返回失败信息

        Returns:
            描述符数组，可选地返回失败信息
        """
        if mol is None:
            zero_features = np.zeros(self.feature_dim, dtype=np.float32)
            if return_failed:
                return zero_features, {'error': 'Molecule is None', 'failed_count': self.feature_dim}
            return zero_features

        try:
            # 计算描述符
            desc_values = self.calculator(mol)

            # 详细的错误处理和统计
            features = []
            failed_descriptors = []
            error_types = {'none': 0, 'nan': 0, 'inf': 0, 'type_error': 0, 'other': 0}

            for i, (desc, value) in enumerate(zip(self.calculator.descriptors, desc_values)):
                try:
                    if value is None:
                        features.append(0.0)
                        failed_descriptors.append(str(desc))
                        error_types['none'] += 1
                    elif isinstance(value, (int, float)):
                        if np.isnan(value):
                            features.append(0.0)
                            failed_descriptors.append(str(desc))
                            error_types['nan'] += 1
                        elif np.isinf(value):
                            features.append(0.0)
                            failed_descriptors.append(str(desc))
                            error_types['inf'] += 1
                        else:
                            features.append(float(value))
                    else:
                        # 尝试转换为float
                        try:
                            float_value = float(value)
                            if np.isnan(float_value) or np.isinf(float_value):
                                features.append(0.0)
                                failed_descriptors.append(str(desc))
                                error_types['nan'] += 1
                            else:
                                features.append(float_value)
                        except (ValueError, TypeError):
                            features.append(0.0)
                            failed_descriptors.append(str(desc))
                            error_types['type_error'] += 1

                except Exception as e:
                    features.append(0.0)
                    failed_descriptors.append(str(desc))
                    error_types['other'] += 1

            features_array = np.array(features, dtype=np.float32)

            # 记录错误信息
            failed_info = {
                'failed_descriptors': failed_descriptors,
                'failed_count': len(failed_descriptors),
                'total_count': len(desc_values),
                'success_rate': (len(desc_values) - len(failed_descriptors)) / len(desc_values),
                'error_types': error_types
            }

            # 根据错误处理策略记录日志
            if failed_descriptors:
                if self.handle_errors == 'warn' and len(failed_descriptors) > len(desc_values) * 0.1:
                    logger.warning(f"Mordred描述符计算失败率较高: {len(failed_descriptors)}/{len(desc_values)} "
                                 f"({failed_info['success_rate']:.2%} 成功)")
                elif self.handle_errors == 'raise' and len(failed_descriptors) > len(desc_values) * 0.5:
                    raise RuntimeError(f"Mordred描述符计算失败率过高: {len(failed_descriptors)}/{len(desc_values)}")

            if return_failed:
                return features_array, failed_info
            return features_array

        except Exception as e:
            error_msg = f"计算Mordred描述符失败: {e}"

            if self.handle_errors == 'raise':
                raise RuntimeError(error_msg)
            elif self.handle_errors == 'warn':
                logger.warning(error_msg)

            zero_features = np.zeros(self.feature_dim, dtype=np.float32)
            if return_failed:
                return zero_features, {
                    'error': str(e),
                    'failed_count': self.feature_dim,
                    'total_count': self.feature_dim,
                    'success_rate': 0.0
                }
            return zero_features

    def get_descriptor_info(self) -> Dict[str, Any]:
        """获取描述符详细信息"""
        return {
            'feature_count': self.feature_dim,
            'ignore_3D': self.ignore_3D,
            'selected_descriptors': self.selected_descriptors,
            'manager_info': self.manager.get_version_info(),
            'feature_names': self.feature_names[:10] + ['...'] if len(self.feature_names) > 10 else self.feature_names
        }


class MoleculeFeaturizer(BaseMoleculeFeaturizer):
    """
    通用分子特征化器

    支持多种描述符库的组合使用，可以根据配置自动选择最合适的特征化方法。
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化通用分子特征化器"""
        super().__init__(config)

        # 特征化器类型
        self.featurizer_type = self.config.get('type', 'auto')
        self.combine_features = self.config.get('combine_features', False)

        # 初始化子特征化器
        self.featurizers = {}
        self._initialize_featurizers()

        # 计算总特征维度
        self._compute_feature_info()

        logger.info(f"初始化通用分子特征化器: {self.featurizer_type}, "
                   f"总维度={self.feature_dim}")

    def _initialize_featurizers(self):
        """初始化子特征化器"""
        if self.featurizer_type == 'auto':
            # 自动选择：优先使用Mordred，回退到RDKit
            if MORDRED_AVAILABLE:
                self.featurizers['mordred'] = MordredFeaturizer(
                    self.config.get('mordred_config', {})
                )
            else:
                self.featurizers['rdkit'] = RDKitFeaturizer(
                    self.config.get('rdkit_config', {})
                )

        elif self.featurizer_type == 'rdkit':
            self.featurizers['rdkit'] = RDKitFeaturizer(
                self.config.get('rdkit_config', {})
            )

        elif self.featurizer_type == 'mordred':
            if not MORDRED_AVAILABLE:
                raise ImportError("Mordred未安装，无法使用mordred特征化器")
            self.featurizers['mordred'] = MordredFeaturizer(
                self.config.get('mordred_config', {})
            )

        elif self.featurizer_type == 'combined':
            # 组合多种特征化器
            self.featurizers['rdkit'] = RDKitFeaturizer(
                self.config.get('rdkit_config', {})
            )
            if MORDRED_AVAILABLE:
                self.featurizers['mordred'] = MordredFeaturizer(
                    self.config.get('mordred_config', {})
                )

        else:
            raise ValueError(f"未知的特征化器类型: {self.featurizer_type}")

    def _compute_feature_info(self):
        """计算特征信息"""
        self.feature_names = []
        self.feature_dim = 0

        for name, featurizer in self.featurizers.items():
            # 添加前缀以区分不同特征化器的特征
            prefixed_names = [f"{name}_{feat_name}" for feat_name in featurizer.get_feature_names()]
            self.feature_names.extend(prefixed_names)
            self.feature_dim += featurizer.get_feature_dim()

    def featurize(self, mol: Chem.Mol) -> np.ndarray:
        """计算分子特征"""
        if mol is None:
            return np.zeros(self.feature_dim)

        # 计算各个特征化器的特征
        all_features = []

        for name, featurizer in self.featurizers.items():
            try:
                features = featurizer.featurize(mol)
                all_features.append(features)
            except Exception as e:
                logger.warning(f"特征化器{name}计算失败: {e}")
                # 使用零向量作为备用
                all_features.append(np.zeros(featurizer.get_feature_dim()))

        # 拼接所有特征
        if all_features:
            combined_features = np.concatenate(all_features)
        else:
            combined_features = np.zeros(self.feature_dim)

        return combined_features.astype(np.float32)

    def get_featurizer_info(self) -> Dict[str, Dict[str, Any]]:
        """获取各个子特征化器的信息"""
        info = {}
        for name, featurizer in self.featurizers.items():
            info[name] = {
                'type': type(featurizer).__name__,
                'feature_dim': featurizer.get_feature_dim(),
                'feature_names': featurizer.get_feature_names()
            }
        return info


def create_molecule_featurizer(
    featurizer_type: str = 'auto',
    config: Optional[Dict[str, Any]] = None
) -> BaseMoleculeFeaturizer:
    """
    创建分子特征化器的工厂函数

    Args:
        featurizer_type: 特征化器类型
        config: 配置字典

    Returns:
        分子特征化器实例
    """
    if featurizer_type == 'rdkit':
        return RDKitFeaturizer(config)
    elif featurizer_type == 'mordred':
        return MordredFeaturizer(config)
    elif featurizer_type in ['auto', 'combined']:
        return MoleculeFeaturizer(config)
    else:
        raise ValueError(f"未知的特征化器类型: {featurizer_type}")


def get_available_featurizers() -> List[str]:
    """获取可用的特征化器列表"""
    available = ['rdkit', 'auto', 'combined']
    if MORDRED_AVAILABLE:
        available.append('mordred')
    return available
