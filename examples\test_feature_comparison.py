# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-27
# 描述: 测试Chemprop RDKit vs 神农Mordred特征对比

"""
测试Chemprop RDKit vs 神农Mordred特征对比

深入分析两种特征化策略的差异和优势。
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def print_feature_analysis(analysis: dict, title: str):
    """打印特征分析结果"""
    print(f"\n📊 {title}")
    print("-" * 50)
    
    if 'error' in analysis:
        print(f"❌ 错误: {analysis['error']}")
        return
    
    if 'feature_counts' in analysis:
        print("🔢 特征数量:")
        for feature_type, count in analysis['feature_counts'].items():
            print(f"  {feature_type}: {count}个")
    
    if 'computation_time' in analysis:
        print("⏱️ 计算时间:")
        for feature_type, time_val in analysis['computation_time'].items():
            print(f"  {feature_type}: {time_val:.4f}秒")
    
    if 'feature_categories' in analysis:
        print("📂 特征类别:")
        for category, info in analysis['feature_categories'].items():
            print(f"  {category}: {info['count']}个")
            if 'examples' in info:
                print(f"    示例: {', '.join(info['examples'])}")
    
    if 'success_rate' in analysis:
        print(f"✅ 成功率: {analysis['success_rate']:.2%}")

def print_coverage_comparison(coverage: dict):
    """打印覆盖范围对比"""
    print(f"\n🎯 特征覆盖范围对比")
    print("=" * 60)
    
    if 'molecular_properties' in coverage:
        props = coverage['molecular_properties']
        
        print("\n🧪 分子性质覆盖:")
        for system, info in props.items():
            print(f"\n  {system}:")
            print(f"    覆盖范围: {info.get('coverage_scope', 'N/A')}")
            print(f"    描述: {info.get('description', 'N/A')}")
            
            if 'basic_properties' in info:
                print(f"    基础性质: {', '.join(info['basic_properties'][:3])}...")
            
            if 'advanced_features' in info:
                print(f"    高级特征: {', '.join(info['advanced_features'][:3])}...")
    
    if 'biological_relevance' in coverage:
        bio = coverage['biological_relevance']
        
        print(f"\n🦠 生物学相关性:")
        for system, relevance in bio.items():
            print(f"\n  {system}:")
            for aspect, level in relevance.items():
                if isinstance(level, str):
                    print(f"    {aspect}: {level}")

def print_performance_analysis(performance: dict):
    """打印性能分析"""
    print(f"\n⚡ 性能分析")
    print("=" * 40)
    
    if 'computation_speed' in performance:
        speed = performance['computation_speed']
        print(f"🏃 计算速度对比:")
        print(f"  RDKit平均时间: {speed.get('rdkit_avg_time', 0):.4f}秒")
        print(f"  Mordred平均时间: {speed.get('mordred_avg_time', 0):.4f}秒")
        print(f"  速度比例: Mordred是RDKit的{speed.get('speed_ratio', 0):.1f}倍")
    
    if 'robustness' in performance:
        robust = performance['robustness']
        print(f"\n🛡️ 稳定性对比:")
        for system, info in robust.items():
            print(f"  {system}:")
            print(f"    错误率: {info.get('error_rate', 'N/A')}")
            print(f"    边界情况: {info.get('edge_cases', 'N/A')}")

def print_recommendations(recommendations: dict):
    """打印建议"""
    print(f"\n💡 使用建议")
    print("=" * 40)
    
    if 'use_cases' in recommendations:
        cases = recommendations['use_cases']
        
        for system, info in cases.items():
            print(f"\n🎯 {system}:")
            if 'scenarios' in info:
                print(f"  适用场景:")
                for scenario in info['scenarios'][:3]:
                    print(f"    • {scenario}")
            
            if 'advantages' in info:
                print(f"  主要优势:")
                for advantage in info['advantages'][:3]:
                    print(f"    ✅ {advantage}")
    
    if 'hybrid_strategies' in recommendations:
        hybrid = recommendations['hybrid_strategies']
        print(f"\n🔄 混合策略:")
        for strategy, info in hybrid.items():
            print(f"  {strategy}: {info.get('description', 'N/A')}")

def main():
    """主函数"""
    print("🔬 Chemprop RDKit vs 神农Mordred特征对比分析")
    print("=" * 80)
    
    try:
        from shennong.utils.feature_comparison import run_feature_comparison
        
        # 运行对比分析
        print("🚀 开始特征对比分析...")
        report = run_feature_comparison()
        
        # 打印工具可用性
        print(f"\n🛠️ 工具可用性:")
        for tool, available in report['tool_availability'].items():
            status = "✅" if available else "❌"
            print(f"  {tool}: {status}")
        
        # 打印Chemprop分析
        if 'chemprop_analysis' in report:
            print_feature_analysis(report['chemprop_analysis'], "Chemprop RDKit特征分析")
        
        # 打印Mordred分析
        if 'mordred_analysis' in report:
            print_feature_analysis(report['mordred_analysis'], "神农Mordred特征分析")
        
        # 打印覆盖范围对比
        if 'coverage_comparison' in report:
            print_coverage_comparison(report['coverage_comparison'])
        
        # 打印性能分析
        if 'performance_analysis' in report:
            print_performance_analysis(report['performance_analysis'])
        
        # 打印建议
        if 'recommendations' in report:
            print_recommendations(report['recommendations'])
        
        # 打印执行摘要
        if 'executive_summary' in report:
            summary = report['executive_summary']
            
            print(f"\n📋 执行摘要")
            print("=" * 40)
            
            if 'key_differences' in summary:
                print(f"🔍 关键差异:")
                for diff in summary['key_differences']:
                    print(f"  • {diff}")
            
            if 'recommendation' in summary:
                print(f"\n🎯 最终建议:")
                print(f"  {summary['recommendation']}")
            
            if 'decision_factors' in summary:
                print(f"\n⚖️ 决策因素:")
                for factor in summary['decision_factors']:
                    print(f"  • {factor}")
        
        print(f"\n🎉 特征对比分析完成！")
        return True
        
    except Exception as e:
        print(f"❌ 特征对比分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
