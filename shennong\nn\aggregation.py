# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-27
# 描述: 神农框架聚合层

"""
神农框架聚合层

实现图特征的聚合操作。
"""

from typing import Dict, Any, Optional
import torch
import torch.nn as nn
import logging

logger = logging.getLogger(__name__)


class GraphAggregation(nn.Module):
    """
    图聚合层
    
    将节点特征聚合为图级别特征。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化图聚合层
        
        Args:
            config: 配置字典
        """
        super().__init__()
        
        self.hidden_size = config.get('hidden_size', 300)
        self.aggregation_type = config.get('aggregation_type', 'mean')
        
        # 聚合后的处理层
        self.post_aggregation = nn.Sequential(
            nn.Linear(self.hidden_size, self.hidden_size),
            nn.ReLU(),
            nn.Linear(self.hidden_size, self.hidden_size)
        )
        
        logger.info(f"初始化图聚合层: 聚合类型={self.aggregation_type}")
    
    def forward(self, node_features: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            node_features: 节点特征
            
        Returns:
            聚合后的图特征
        """
        # 简化的聚合操作
        if self.aggregation_type == 'mean':
            graph_features = node_features.mean(dim=1)
        elif self.aggregation_type == 'sum':
            graph_features = node_features.sum(dim=1)
        elif self.aggregation_type == 'max':
            graph_features = node_features.max(dim=1)[0]
        else:
            graph_features = node_features.mean(dim=1)
        
        return self.post_aggregation(graph_features)


class AttentionAggregation(nn.Module):
    """
    注意力聚合层
    
    使用注意力机制聚合特征。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化注意力聚合层
        
        Args:
            config: 配置字典
        """
        super().__init__()
        
        self.hidden_size = config.get('hidden_size', 300)
        
        # 注意力权重计算
        self.attention = nn.Sequential(
            nn.Linear(self.hidden_size, self.hidden_size // 2),
            nn.ReLU(),
            nn.Linear(self.hidden_size // 2, 1)
        )
        
        logger.info(f"初始化注意力聚合层")
    
    def forward(self, features: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            features: 输入特征
            
        Returns:
            注意力聚合后的特征
        """
        # 计算注意力权重
        attention_weights = self.attention(features)
        attention_weights = torch.softmax(attention_weights, dim=1)
        
        # 加权聚合
        aggregated = (features * attention_weights).sum(dim=1)
        
        return aggregated


class SetToSetAggregation(nn.Module):
    """
    集合到集合聚合层
    
    处理变长序列的聚合。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化集合聚合层
        
        Args:
            config: 配置字典
        """
        super().__init__()
        
        self.hidden_size = config.get('hidden_size', 300)
        
        # 集合处理层
        self.set_processor = nn.Sequential(
            nn.Linear(self.hidden_size, self.hidden_size),
            nn.ReLU(),
            nn.Linear(self.hidden_size, self.hidden_size)
        )
        
        logger.info(f"初始化集合聚合层")
    
    def forward(self, features: torch.Tensor, mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        前向传播
        
        Args:
            features: 输入特征
            mask: 掩码张量
            
        Returns:
            聚合后的特征
        """
        processed_features = self.set_processor(features)
        
        if mask is not None:
            # 应用掩码
            processed_features = processed_features * mask.unsqueeze(-1)
            # 计算有效元素的平均值
            aggregated = processed_features.sum(dim=1) / mask.sum(dim=1, keepdim=True)
        else:
            aggregated = processed_features.mean(dim=1)
        
        return aggregated
