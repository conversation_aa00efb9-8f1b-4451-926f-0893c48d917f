#!/bin/bash
# 神农框架包安装脚本
# 作者: ZK
# 日期: 2025-06-30

set -e  # 遇到错误立即退出

echo "🧬 开始安装神农框架依赖包..."

# 检查是否在shennong环境中
if [[ "$CONDA_DEFAULT_ENV" != "shennong" ]]; then
    echo "❌ 请先激活shennong环境: conda activate shennong"
    exit 1
fi

echo "✅ 当前环境: $CONDA_DEFAULT_ENV"

# 更新工具
echo "📦 更新conda和pip..."
conda update conda -y
pip install --upgrade pip

# 安装基础科学计算包
echo "🔢 安装基础科学计算包..."
pip install numpy pandas scipy matplotlib seaborn jupyter tqdm psutil memory_profiler

# 安装PyTorch (CUDA 11.8)
echo "🔥 安装PyTorch (CUDA 11.8)..."
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# 安装机器学习包
echo "🤖 安装机器学习包..."
pip install scikit-learn statsmodels

# 安装RDKit
echo "⚗️  安装RDKit..."
conda install -c conda-forge rdkit -y

# 安装化学信息学包
echo "🧪 安装化学信息学包..."
pip install mordred
pip install chemprop
pip install chembl_webresource_client pubchempy

# 安装AutoGluon
echo "🚀 安装AutoGluon..."
pip install autogluon

# 安装图神经网络包
echo "🕸️  安装图神经网络包..."
pip install torch-geometric

# 安装高级ML工具
echo "🔧 安装高级ML工具..."
pip install optuna shap tensorboard wandb

echo "✅ 所有包安装完成！"

# 运行验证
echo "🔍 运行环境验证..."
python -c "
import sys
packages = ['torch', 'numpy', 'pandas', 'sklearn', 'rdkit', 'mordred', 'chemprop', 'autogluon']
success = 0
for pkg in packages:
    try:
        if pkg == 'sklearn':
            import sklearn
        elif pkg == 'rdkit':
            from rdkit import Chem
        else:
            __import__(pkg)
        print(f'✅ {pkg}: OK')
        success += 1
    except ImportError:
        print(f'❌ {pkg}: FAILED')

print(f'\\n📊 安装成功率: {success}/{len(packages)} ({success/len(packages)*100:.1f}%)')

# CUDA检查
try:
    import torch
    print(f'🔥 CUDA可用: {torch.cuda.is_available()}')
    if torch.cuda.is_available():
        print(f'   GPU: {torch.cuda.get_device_name(0)}')
except:
    print('❌ PyTorch CUDA检查失败')
"

echo "🎉 神农框架环境配置完成！"
