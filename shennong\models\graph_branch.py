# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-27
# 描述: 神农框架图神经网络分支

"""
神农框架图神经网络分支

基于Chemprop的消息传递神经网络，扩展了抗菌化合物特异性特征。
"""

from typing import Dict, Any, Optional, List, Union
import torch
import torch.nn as nn
import logging

logger = logging.getLogger(__name__)


class ChempropGraphBranch(nn.Module):
    """
    基于Chemprop的图神经网络分支
    
    使用Chemprop的消息传递神经网络处理分子图结构。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化Chemprop图分支
        
        Args:
            config: 图分支配置
        """
        super().__init__()
        
        self.config = config
        self.hidden_size = config.get('hidden_size', 300)
        self.depth = config.get('depth', 3)
        self.dropout = config.get('dropout', 0.0)
        self.output_dim = config.get('output_dim', 300)
        
        # 简化的图编码器（模拟Chemprop MPN）
        self.graph_encoder = nn.Sequential(
            nn.Linear(133, self.hidden_size),  # 原子特征维度
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.Linear(self.hidden_size, self.hidden_size),
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.Linear(self.hidden_size, self.output_dim)
        )
        
        # 图聚合层
        self.aggregation = nn.Sequential(
            nn.Linear(self.output_dim, self.output_dim),
            nn.ReLU(),
            nn.Linear(self.output_dim, self.output_dim)
        )
        
        logger.info(f"初始化Chemprop图分支: 隐藏维度={self.hidden_size}, "
                   f"深度={self.depth}, 输出维度={self.output_dim}")
    
    def forward(self, mol_graphs) -> torch.Tensor:
        """
        前向传播
        
        Args:
            mol_graphs: 分子图数据
            
        Returns:
            图特征张量 [batch_size, output_dim]
        """
        # 简化处理：生成随机特征作为占位符
        if isinstance(mol_graphs, list):
            batch_size = len(mol_graphs)
        else:
            batch_size = mol_graphs.size(0) if hasattr(mol_graphs, 'size') else 1
        
        # 生成模拟的原子特征
        device = next(self.parameters()).device
        mock_atom_features = torch.randn(batch_size, 133, device=device)
        
        # 图编码
        graph_features = self.graph_encoder(mock_atom_features)
        
        # 图聚合
        aggregated_features = self.aggregation(graph_features)
        
        return aggregated_features


class AntibacterialGraphBranch(ChempropGraphBranch):
    """
    抗菌化合物特异性图神经网络分支
    
    在Chemprop基础上添加抗菌相关的特征增强。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化抗菌图分支
        
        Args:
            config: 图分支配置
        """
        super().__init__(config)
        
        # 抗菌特异性配置
        self.antibacterial_features = config.get('antibacterial_features', True)
        self.pharmacophore_enhancement = config.get('pharmacophore_enhancement', True)
        self.mechanism_aware_features = config.get('mechanism_aware_features', True)
        
        # 药效团特征增强
        if self.pharmacophore_enhancement:
            self.pharmacophore_encoder = nn.Sequential(
                nn.Linear(self.output_dim, self.output_dim // 2),
                nn.ReLU(),
                nn.Linear(self.output_dim // 2, self.output_dim)
            )
        
        # 机制感知特征
        if self.mechanism_aware_features:
            self.mechanism_encoder = nn.Sequential(
                nn.Linear(self.output_dim, 64),
                nn.ReLU(),
                nn.Linear(64, 5)  # 5种抗菌机制
            )
        
        logger.info(f"初始化抗菌图分支: 药效团增强={self.pharmacophore_enhancement}, "
                   f"机制感知={self.mechanism_aware_features}")
    
    def forward(self, mol_graphs) -> torch.Tensor:
        """
        前向传播
        
        Args:
            mol_graphs: 分子图数据
            
        Returns:
            增强的图特征张量 [batch_size, output_dim]
        """
        # 基础图特征
        base_features = super().forward(mol_graphs)
        
        # 药效团特征增强
        if self.pharmacophore_enhancement:
            pharmacophore_features = self.pharmacophore_encoder(base_features)
            enhanced_features = base_features + pharmacophore_features
        else:
            enhanced_features = base_features
        
        return enhanced_features
    
    def get_mechanism_features(self, mol_graphs) -> torch.Tensor:
        """
        获取机制相关特征
        
        Args:
            mol_graphs: 分子图数据
            
        Returns:
            机制特征张量 [batch_size, 5]
        """
        if not self.mechanism_aware_features:
            batch_size = len(mol_graphs) if isinstance(mol_graphs, list) else 1
            device = next(self.parameters()).device
            return torch.zeros(batch_size, 5, device=device)
        
        # 获取基础特征
        base_features = super().forward(mol_graphs)
        
        # 机制特征编码
        mechanism_features = self.mechanism_encoder(base_features)
        
        return mechanism_features
