# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-27
# 描述: 神农框架训练模块初始化

"""
神农框架训练模块

提供模型训练、验证和测试的完整流程。

主要组件:
- trainer: 训练器
- callbacks: 训练回调
- optimizers: 优化器配置
"""

from .trainer import ShennongTrainer
from .callbacks import AntibacterialInterpretationCallback, ModelCheckpointCallback
from .optimizers import create_optimizer, create_scheduler

__all__ = [
    # 训练器
    'ShennongTrainer',
    
    # 回调函数
    'AntibacterialInterpretationCallback',
    'ModelCheckpointCallback',
    
    # 优化器
    'create_optimizer',
    'create_scheduler',
]
