# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-27
# 描述: 神农框架基础模型类

"""
神农框架基础模型类

定义神农框架模型的基础接口和通用功能。
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Union
import torch
import torch.nn as nn
import lightning as L
import logging

logger = logging.getLogger(__name__)


class BaseShennongModel(L.LightningModule, ABC):
    """
    神农框架基础模型类
    
    所有神农框架模型的基类，提供通用的训练、验证、测试逻辑。
    集成PyTorch Lightning以简化训练流程。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化基础模型
        
        Args:
            config: 模型配置字典
        """
        super().__init__()
        self.save_hyperparameters(config)
        self.config = config
        
        # 模型组件（由子类实现）
        self.graph_branch = None
        self.expert_branch = None
        self.fusion_module = None
        self.predictors = None
        
        # 训练配置
        self.learning_rate = config.get('learning_rate', 1e-3)
        self.weight_decay = config.get('weight_decay', 1e-4)
        self.scheduler_config = config.get('scheduler', {})
        
        # 损失权重
        self.loss_weights = config.get('loss_weights', {})
        
        # 指标追踪
        self.train_metrics = {}
        self.val_metrics = {}
        self.test_metrics = {}
        
        logger.info(f"初始化基础神农模型: {self.__class__.__name__}")
    
    @abstractmethod
    def forward(self, batch: Dict[str, Any]) -> Dict[str, torch.Tensor]:
        """
        前向传播（由子类实现）
        
        Args:
            batch: 输入批次数据
            
        Returns:
            模型输出字典
        """
        pass
    
    @abstractmethod
    def compute_loss(
        self, 
        outputs: Dict[str, torch.Tensor], 
        batch: Dict[str, Any]
    ) -> Dict[str, torch.Tensor]:
        """
        计算损失（由子类实现）
        
        Args:
            outputs: 模型输出
            batch: 批次数据
            
        Returns:
            损失字典
        """
        pass
    
    def training_step(self, batch: Dict[str, Any], batch_idx: int) -> torch.Tensor:
        """训练步骤"""
        outputs = self(batch)
        losses = self.compute_loss(outputs, batch)
        
        # 记录损失
        for loss_name, loss_value in losses.items():
            self.log(f'train_{loss_name}', loss_value, on_step=True, on_epoch=True, prog_bar=True)
        
        # 计算总损失
        total_loss = self._compute_total_loss(losses)
        self.log('train_loss', total_loss, on_step=True, on_epoch=True, prog_bar=True)
        
        return total_loss
    
    def validation_step(self, batch: Dict[str, Any], batch_idx: int) -> Dict[str, torch.Tensor]:
        """验证步骤"""
        outputs = self(batch)
        losses = self.compute_loss(outputs, batch)
        
        # 记录损失
        for loss_name, loss_value in losses.items():
            self.log(f'val_{loss_name}', loss_value, on_step=False, on_epoch=True, prog_bar=True)
        
        # 计算总损失
        total_loss = self._compute_total_loss(losses)
        self.log('val_loss', total_loss, on_step=False, on_epoch=True, prog_bar=True)
        
        return {'val_loss': total_loss, 'outputs': outputs, 'batch': batch}
    
    def test_step(self, batch: Dict[str, Any], batch_idx: int) -> Dict[str, torch.Tensor]:
        """测试步骤"""
        outputs = self(batch)
        losses = self.compute_loss(outputs, batch)
        
        # 记录损失
        for loss_name, loss_value in losses.items():
            self.log(f'test_{loss_name}', loss_value, on_step=False, on_epoch=True)
        
        # 计算总损失
        total_loss = self._compute_total_loss(losses)
        self.log('test_loss', total_loss, on_step=False, on_epoch=True)
        
        return {'test_loss': total_loss, 'outputs': outputs, 'batch': batch}
    
    def predict_step(self, batch: Dict[str, Any], batch_idx: int) -> Dict[str, torch.Tensor]:
        """预测步骤"""
        outputs = self(batch)
        return outputs
    
    def configure_optimizers(self):
        """配置优化器和学习率调度器"""
        # 优化器
        optimizer = torch.optim.AdamW(
            self.parameters(),
            lr=self.learning_rate,
            weight_decay=self.weight_decay
        )
        
        # 学习率调度器
        if not self.scheduler_config:
            return optimizer
        
        scheduler_type = self.scheduler_config.get('type', 'cosine')
        
        if scheduler_type == 'cosine':
            scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
                optimizer,
                T_max=self.scheduler_config.get('T_max', 100),
                eta_min=self.scheduler_config.get('eta_min', 1e-6)
            )
        elif scheduler_type == 'step':
            scheduler = torch.optim.lr_scheduler.StepLR(
                optimizer,
                step_size=self.scheduler_config.get('step_size', 30),
                gamma=self.scheduler_config.get('gamma', 0.1)
            )
        elif scheduler_type == 'plateau':
            scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
                optimizer,
                mode='min',
                factor=self.scheduler_config.get('factor', 0.5),
                patience=self.scheduler_config.get('patience', 10),
                min_lr=self.scheduler_config.get('min_lr', 1e-6)
            )
            return {
                'optimizer': optimizer,
                'lr_scheduler': {
                    'scheduler': scheduler,
                    'monitor': 'val_loss',
                    'interval': 'epoch',
                    'frequency': 1
                }
            }
        else:
            raise ValueError(f"不支持的调度器类型: {scheduler_type}")
        
        return {
            'optimizer': optimizer,
            'lr_scheduler': {
                'scheduler': scheduler,
                'interval': 'epoch',
                'frequency': 1
            }
        }
    
    def _compute_total_loss(self, losses: Dict[str, torch.Tensor]) -> torch.Tensor:
        """计算加权总损失"""
        total_loss = torch.tensor(0.0, device=self.device)
        
        for loss_name, loss_value in losses.items():
            weight = self.loss_weights.get(loss_name, 1.0)
            total_loss += weight * loss_value
        
        return total_loss
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        
        info = {
            'model_name': self.__class__.__name__,
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'config': self.config,
            'device': str(self.device)
        }
        
        # 添加分支信息
        if self.graph_branch is not None:
            info['graph_branch_params'] = sum(p.numel() for p in self.graph_branch.parameters())
        
        if self.expert_branch is not None:
            info['expert_branch_params'] = sum(p.numel() for p in self.expert_branch.parameters())
        
        if self.fusion_module is not None:
            info['fusion_module_params'] = sum(p.numel() for p in self.fusion_module.parameters())
        
        return info
    
    def freeze_branch(self, branch_name: str):
        """冻结指定分支的参数"""
        if branch_name == 'graph' and self.graph_branch is not None:
            for param in self.graph_branch.parameters():
                param.requires_grad = False
            logger.info("已冻结图分支参数")
        
        elif branch_name == 'expert' and self.expert_branch is not None:
            for param in self.expert_branch.parameters():
                param.requires_grad = False
            logger.info("已冻结专家分支参数")
        
        elif branch_name == 'fusion' and self.fusion_module is not None:
            for param in self.fusion_module.parameters():
                param.requires_grad = False
            logger.info("已冻结融合模块参数")
        
        else:
            logger.warning(f"未知的分支名称或分支不存在: {branch_name}")
    
    def unfreeze_branch(self, branch_name: str):
        """解冻指定分支的参数"""
        if branch_name == 'graph' and self.graph_branch is not None:
            for param in self.graph_branch.parameters():
                param.requires_grad = True
            logger.info("已解冻图分支参数")
        
        elif branch_name == 'expert' and self.expert_branch is not None:
            for param in self.expert_branch.parameters():
                param.requires_grad = True
            logger.info("已解冻专家分支参数")
        
        elif branch_name == 'fusion' and self.fusion_module is not None:
            for param in self.fusion_module.parameters():
                param.requires_grad = True
            logger.info("已解冻融合模块参数")
        
        else:
            logger.warning(f"未知的分支名称或分支不存在: {branch_name}")
    
    def save_model(self, path: str, include_optimizer: bool = False):
        """保存模型"""
        checkpoint = {
            'model_state_dict': self.state_dict(),
            'config': self.config,
            'model_class': self.__class__.__name__,
            'shennong_version': '1.0.0'
        }
        
        if include_optimizer and hasattr(self, 'optimizers'):
            checkpoint['optimizer_state_dict'] = self.optimizers().state_dict()
        
        torch.save(checkpoint, path)
        logger.info(f"模型已保存到: {path}")
    
    @classmethod
    def load_model(cls, path: str, map_location: Optional[str] = None):
        """加载模型"""
        checkpoint = torch.load(path, map_location=map_location)
        
        # 创建模型实例
        config = checkpoint['config']
        model = cls(config)
        
        # 加载权重
        model.load_state_dict(checkpoint['model_state_dict'])
        
        logger.info(f"模型已从 {path} 加载")
        return model
    
    def summary(self) -> str:
        """获取模型摘要"""
        info = self.get_model_info()
        
        summary = f"""
神农框架模型摘要:
- 模型名称: {info['model_name']}
- 总参数数: {info['total_parameters']:,}
- 可训练参数: {info['trainable_parameters']:,}
- 设备: {info['device']}
"""
        
        if 'graph_branch_params' in info:
            summary += f"- 图分支参数: {info['graph_branch_params']:,}\n"
        
        if 'expert_branch_params' in info:
            summary += f"- 专家分支参数: {info['expert_branch_params']:,}\n"
        
        if 'fusion_module_params' in info:
            summary += f"- 融合模块参数: {info['fusion_module_params']:,}\n"
        
        return summary
