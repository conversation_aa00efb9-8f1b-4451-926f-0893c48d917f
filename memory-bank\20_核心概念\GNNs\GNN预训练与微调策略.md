# GNN预训练与微调策略

## 概述

在分子性质预测任务中，由于数据集规模有限(如我们的8k化合物数据集)，预训练策略成为提高模型性能和泛化能力的关键技术。本文档分析了GNN预训练的理论基础、实施策略和最佳实践。

## 理论基础

### 小数据集的挑战

在分子预测任务中，小数据集面临的主要问题：
1. **过拟合风险**: 模型容易记住训练数据而非学习泛化规律
2. **表示学习不充分**: 有限样本难以学习到丰富的分子表示
3. **收敛困难**: 梯度信息稀疏，训练不稳定

### 预训练的优势

**理论依据**: 迁移学习理论表明，在大规模数据上预训练的模型能够学习到更好的初始表示，为下游任务提供良好的起点。

**具体优势**:
- **更好的初始化**: 预训练权重提供有意义的特征表示
- **减少过拟合**: 预训练模型具有更好的正则化效果
- **加速收敛**: 从更好的起点开始训练
- **提高泛化**: 预训练表示包含更丰富的分子化学知识

## 预训练策略分类

### 1. 自监督预训练 (Self-Supervised Pre-training)

#### 节点级任务
- **原子掩码预测**: 随机掩码原子类型，预测被掩码的原子
- **原子性质预测**: 预测原子的化学性质(电负性、原子半径等)

```python
# 原子掩码预测示例
class AtomMaskingTask:
    def __init__(self, mask_ratio=0.15):
        self.mask_ratio = mask_ratio
    
    def create_masked_input(self, mol_graph):
        # 随机选择要掩码的原子
        masked_atoms = random.sample(
            range(mol_graph.num_atoms), 
            int(mol_graph.num_atoms * self.mask_ratio)
        )
        
        # 创建掩码
        masked_graph = mol_graph.copy()
        for atom_idx in masked_atoms:
            masked_graph.atoms[atom_idx] = MASK_TOKEN
        
        return masked_graph, masked_atoms
```

#### 图级任务
- **分子性质预测**: 预测基本物理化学性质(分子量、logP等)
- **分子对比学习**: 学习相似分子的表示接近，不相似分子的表示远离

#### 边级任务
- **键类型预测**: 预测化学键的类型和性质
- **键掩码重构**: 掩码部分化学键，重构完整分子图

### 2. 多任务预训练 (Multi-task Pre-training)

在多个相关任务上同时训练，共享底层表示：

```python
class MultiTaskPretraining:
    def __init__(self):
        self.shared_encoder = MPNNEncoder()
        self.task_heads = {
            'solubility': nn.Linear(hidden_dim, 1),
            'toxicity': nn.Linear(hidden_dim, 1),
            'bioavailability': nn.Linear(hidden_dim, 1)
        }
    
    def forward(self, mol_graph, task_name):
        # 共享编码器
        mol_repr = self.shared_encoder(mol_graph)
        # 任务特定头
        output = self.task_heads[task_name](mol_repr)
        return output
```

### 3. 对比学习预训练 (Contrastive Learning)

学习分子的语义表示，使化学相似的分子在表示空间中更接近：

```python
class MolecularContrastiveLearning:
    def __init__(self, temperature=0.1):
        self.temperature = temperature
        self.encoder = MPNNEncoder()
    
    def contrastive_loss(self, mol_batch):
        # 编码分子
        representations = self.encoder(mol_batch)
        
        # 计算相似度矩阵
        sim_matrix = torch.matmul(representations, representations.T)
        sim_matrix = sim_matrix / self.temperature
        
        # 对比损失
        labels = torch.eye(mol_batch.size(0))
        loss = F.cross_entropy(sim_matrix, labels)
        return loss
```

## 微调策略

### 1. 全模型微调 (Full Fine-tuning)

解冻所有预训练参数，在目标任务上重新训练：

```python
# 全模型微调配置
fine_tuning_config = {
    'learning_rate': 1e-4,  # 比预训练学习率小
    'epochs': 50,
    'freeze_layers': None,  # 不冻结任何层
    'gradient_clipping': 1.0
}
```

### 2. 层级微调 (Layer-wise Fine-tuning)

分层解冻，逐步微调模型：

```python
class LayerwiseFineTuning:
    def __init__(self, model, unfreeze_schedule):
        self.model = model
        self.unfreeze_schedule = unfreeze_schedule
    
    def unfreeze_layers(self, epoch):
        if epoch in self.unfreeze_schedule:
            layer_to_unfreeze = self.unfreeze_schedule[epoch]
            for param in self.model.layers[layer_to_unfreeze].parameters():
                param.requires_grad = True
```

### 3. 特征提取 (Feature Extraction)

冻结预训练编码器，仅训练分类头：

```python
# 冻结编码器参数
for param in model.encoder.parameters():
    param.requires_grad = False

# 仅训练分类头
optimizer = torch.optim.Adam(model.classifier.parameters(), lr=1e-3)
```

## 适合神农框架的预训练策略

### 推荐策略组合

基于我们8k化合物的抗菌活性数据集，推荐以下预训练策略：

#### 1. 阶段一：大规模自监督预训练
- **数据源**: ChEMBL、PubChem等大规模分子数据库(>100万分子)
- **任务**: 原子掩码预测 + 分子性质预测
- **持续时间**: 100-200 epochs

#### 2. 阶段二：领域适应预训练
- **数据源**: 抗菌相关分子数据集(如ChEMBL抗菌化合物)
- **任务**: 抗菌活性相关性质预测
- **持续时间**: 50-100 epochs

#### 3. 阶段三：目标任务微调
- **数据源**: 我们的8k抗菌化合物数据集
- **策略**: 层级微调，从顶层开始逐步解冻
- **特殊设计**: 保持化学导向注意力机制的可训练性

### 实施细节

```python
# 神农框架预训练配置
class ShennongPretrainingConfig:
    # 阶段一：大规模预训练
    stage1 = {
        'data_source': 'chembl_large',
        'tasks': ['atom_masking', 'molecular_properties'],
        'batch_size': 256,
        'learning_rate': 1e-3,
        'epochs': 200
    }
    
    # 阶段二：领域适应
    stage2 = {
        'data_source': 'antimicrobial_subset',
        'tasks': ['mic_prediction', 'mechanism_classification'],
        'batch_size': 128,
        'learning_rate': 5e-4,
        'epochs': 100
    }
    
    # 阶段三：目标微调
    stage3 = {
        'data_source': 'our_8k_dataset',
        'strategy': 'layerwise_unfreezing',
        'batch_size': 64,
        'learning_rate': 1e-4,
        'epochs': 50
    }
```

## 评估指标

### 预训练效果评估
1. **下游任务性能**: 在目标任务上的准确率、AUC等
2. **收敛速度**: 达到目标性能所需的训练步数
3. **数据效率**: 在不同数据集大小下的性能表现
4. **表示质量**: t-SNE可视化、聚类分析等

### 消融研究
- **无预训练 vs 预训练**: 验证预训练的必要性
- **不同预训练任务**: 比较各种预训练任务的效果
- **预训练数据规模**: 分析数据规模对效果的影响

## 相关文献

### 核心文献
1. **Hu, W. et al.** "Strategies for Pre-training Graph Neural Networks." *ICLR* 2020
   - 提出了图神经网络预训练的系统性策略
   
2. **Rong, Y. et al.** "Self-Supervised Graph Transformer on Large-Scale Molecular Data." *NeurIPS* 2020
   - 分子图的自监督学习方法

3. **Wang, Y. et al.** "Molecular Contrastive Learning of Representations via Graph Neural Networks." *Nature Machine Intelligence* 2022
   - 分子对比学习的最新进展

### 实施参考
- **Chemprop预训练**: MIT团队的预训练权重和方法
- **MolCLR**: 分子对比学习的开源实现
- **GraphMAE**: 图掩码自编码器预训练

## 实施计划

### 短期目标 (1-2个月)
1. 实现基础的原子掩码预训练
2. 在ChEMBL子集上验证预训练效果
3. 集成到神农框架中

### 中期目标 (3-4个月)
1. 开发多任务预训练流程
2. 实施领域适应预训练
3. 优化微调策略

### 长期目标 (6个月+)
1. 构建大规模预训练模型库
2. 开发自适应预训练策略
3. 发布预训练权重供社区使用

## 相关文件

- [[20_核心概念/GNNs/Chemprop模型架构分析]]
- [[30_神农框架/30.02_架构重构计划 (Shennong v2.0)]]
- [[90_论文写作/实验设计协议]]

---

*创建时间: 2024-01-XX*
*最后更新: 2024-01-XX*
*状态: 策略制定完成* 