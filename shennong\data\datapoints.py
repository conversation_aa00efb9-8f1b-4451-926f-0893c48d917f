# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-27
# 描述: 神农框架数据点定义

"""
神农框架数据点类定义

提供支持双模态输入的数据点类，包含分子图和专家特征描述符。
支持抗菌活性相关的元数据和机制标签。
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Union
import numpy as np
import torch
from rdkit import Chem

from .molgraph import ShennongMolGraph


@dataclass
class ShennongDatapoint:
    """
    神农框架数据点类，支持双模态输入
    
    包含分子图表示和专家特征描述符，以及抗菌活性相关的元数据。
    
    Attributes:
        smiles: SMILES分子字符串
        targets: 目标值字典，支持多任务
        mol_graph: 分子图表示，如果为None则自动生成
        descriptors: 专家特征描述符数组
        descriptor_config: 描述符配置信息
        mechanism_labels: 抗菌机制标签列表
        metadata: 额外的元数据信息
        mol: RDKit分子对象，用于缓存
    """
    
    smiles: str
    targets: Dict[str, float]
    mol_graph: Optional[ShennongMolGraph] = None
    descriptors: Optional[np.ndarray] = None
    descriptor_config: Optional[Dict[str, Any]] = None
    mechanism_labels: Optional[List[str]] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    mol: Optional[Chem.Mol] = field(default=None, init=False)
    
    def __post_init__(self):
        """初始化后处理，自动生成缺失的组件"""
        # 验证SMILES有效性
        if not self._validate_smiles():
            raise ValueError(f"Invalid SMILES: {self.smiles}")
        
        # 自动生成分子图
        if self.mol_graph is None:
            self.mol_graph = self._create_mol_graph()
        
        # 自动计算描述符（如果未提供）
        if self.descriptors is None and self.descriptor_config is not None:
            self.descriptors = self._compute_descriptors()
        
        # 验证数据一致性
        self._validate_data_consistency()
    
    def _validate_smiles(self) -> bool:
        """验证SMILES字符串的有效性"""
        try:
            self.mol = Chem.MolFromSmiles(self.smiles)
            return self.mol is not None
        except Exception:
            return False
    
    def _create_mol_graph(self) -> ShennongMolGraph:
        """创建分子图表示"""
        if self.mol is None:
            self.mol = Chem.MolFromSmiles(self.smiles)
        
        return ShennongMolGraph.from_mol(self.mol)
    
    def _compute_descriptors(self) -> np.ndarray:
        """计算分子描述符"""
        from ..featurizers.molecule import MoleculeFeaturizer
        
        if self.mol is None:
            self.mol = Chem.MolFromSmiles(self.smiles)
        
        featurizer = MoleculeFeaturizer(self.descriptor_config)
        return featurizer.featurize(self.mol)
    
    def _validate_data_consistency(self):
        """验证数据一致性"""
        # 检查目标值
        if not isinstance(self.targets, dict):
            raise ValueError("targets must be a dictionary")
        
        # 检查描述符维度
        if self.descriptors is not None:
            if not isinstance(self.descriptors, np.ndarray):
                self.descriptors = np.array(self.descriptors)
            
            if self.descriptors.ndim != 1:
                raise ValueError("descriptors must be 1-dimensional array")
        
        # 检查机制标签
        if self.mechanism_labels is not None:
            if not isinstance(self.mechanism_labels, list):
                raise ValueError("mechanism_labels must be a list")
    
    def get_target(self, task_name: str) -> Optional[float]:
        """获取指定任务的目标值"""
        return self.targets.get(task_name)
    
    def has_target(self, task_name: str) -> bool:
        """检查是否包含指定任务的目标值"""
        return task_name in self.targets and self.targets[task_name] is not None
    
    def get_descriptor_dim(self) -> int:
        """获取描述符维度"""
        if self.descriptors is None:
            return 0
        return len(self.descriptors)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'smiles': self.smiles,
            'targets': self.targets,
            'descriptors': self.descriptors.tolist() if self.descriptors is not None else None,
            'descriptor_config': self.descriptor_config,
            'mechanism_labels': self.mechanism_labels,
            'metadata': self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ShennongDatapoint':
        """从字典创建数据点"""
        descriptors = data.get('descriptors')
        if descriptors is not None:
            descriptors = np.array(descriptors)
        
        return cls(
            smiles=data['smiles'],
            targets=data['targets'],
            descriptors=descriptors,
            descriptor_config=data.get('descriptor_config'),
            mechanism_labels=data.get('mechanism_labels'),
            metadata=data.get('metadata', {})
        )
    
    def clone(self) -> 'ShennongDatapoint':
        """创建数据点的深拷贝"""
        import copy
        return copy.deepcopy(self)
    
    def __repr__(self) -> str:
        """字符串表示"""
        target_info = f"{len(self.targets)} targets" if self.targets else "no targets"
        desc_info = f"{self.get_descriptor_dim()}D descriptors" if self.descriptors is not None else "no descriptors"
        mechanism_info = f"{len(self.mechanism_labels)} mechanisms" if self.mechanism_labels else "no mechanisms"
        
        return (f"ShennongDatapoint(smiles='{self.smiles[:20]}...', "
                f"{target_info}, {desc_info}, {mechanism_info})")


@dataclass
class AntibacterialDatapoint(ShennongDatapoint):
    """
    抗菌化合物专用数据点类
    
    扩展基础数据点类，添加抗菌活性相关的特殊字段和方法。
    """
    
    # 抗菌活性相关字段
    mic_value: Optional[float] = None  # 最小抑菌浓度
    bacterial_strain: Optional[str] = None  # 细菌菌株
    assay_conditions: Optional[Dict[str, Any]] = None  # 实验条件
    resistance_profile: Optional[Dict[str, bool]] = None  # 耐药性谱
    
    def __post_init__(self):
        """抗菌数据点的初始化后处理"""
        super().__post_init__()
        
        # 自动设置MIC相关的目标值
        if self.mic_value is not None and 'mic' not in self.targets:
            self.targets['mic'] = self.mic_value
        
        # 验证抗菌相关数据
        self._validate_antibacterial_data()
    
    def _validate_antibacterial_data(self):
        """验证抗菌相关数据的有效性"""
        # 验证MIC值
        if self.mic_value is not None:
            if self.mic_value <= 0:
                raise ValueError("MIC value must be positive")
        
        # 验证细菌菌株
        if self.bacterial_strain is not None:
            known_strains = [
                'E.coli', 'S.aureus', 'P.aeruginosa', 'K.pneumoniae',
                'A.baumannii', 'E.faecalis', 'S.pneumoniae'
            ]
            if self.bacterial_strain not in known_strains:
                import warnings
                warnings.warn(f"Unknown bacterial strain: {self.bacterial_strain}")
    
    def get_activity_class(self, threshold: float = 10.0) -> str:
        """
        根据MIC值获取活性分类
        
        Args:
            threshold: MIC阈值 (μg/mL)
            
        Returns:
            活性分类: 'active', 'inactive', 'unknown'
        """
        if self.mic_value is None:
            return 'unknown'
        
        return 'active' if self.mic_value <= threshold else 'inactive'
    
    def is_broad_spectrum(self) -> bool:
        """判断是否为广谱抗菌化合物"""
        if self.resistance_profile is None:
            return False
        
        # 如果对多种细菌都有效，则认为是广谱
        active_count = sum(1 for is_active in self.resistance_profile.values() if is_active)
        return active_count >= 3
    
    def get_gram_type(self) -> Optional[str]:
        """获取细菌的革兰氏染色类型"""
        if self.bacterial_strain is None:
            return None
        
        gram_positive = ['S.aureus', 'E.faecalis', 'S.pneumoniae']
        gram_negative = ['E.coli', 'P.aeruginosa', 'K.pneumoniae', 'A.baumannii']
        
        if self.bacterial_strain in gram_positive:
            return 'gram_positive'
        elif self.bacterial_strain in gram_negative:
            return 'gram_negative'
        else:
            return 'unknown'
