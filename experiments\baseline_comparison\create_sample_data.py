#!/usr/bin/env python3
# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-30
# 描述: 创建抗菌化合物示例数据集

"""
创建抗菌化合物示例数据集
用于测试Shennong和ChemProp基线对比
"""

import pandas as pd
import numpy as np
from rdkit import Chem
from rdkit.Chem import Descriptors
import os

def create_sample_antibacterial_data(n_samples=1000, output_dir="../../data/examples"):
    """
    创建抗菌化合物示例数据集
    
    Args:
        n_samples: 样本数量
        output_dir: 输出目录
    """
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    print(f"🧬 创建包含{n_samples}个化合物的示例数据集...")
    
    # 抗菌活性化合物的SMILES示例
    active_smiles = [
        "CC(C)CC1=CC=C(C=C1)C(C)C(=O)O",  # 布洛芬类似物
        "CC1=CC=C(C=C1)C(=O)C2=CC=CC=C2",  # 苯甲酮类似物
        "CC(C)(C)C1=CC=C(C=C1)O",  # 酚类化合物
        "CC1=CC=CC=C1C(=O)O",  # 苯甲酸类似物
        "CC1=CC=C(C=C1)N",  # 苯胺类似物
        "CC1=CC=C(C=C1)S(=O)(=O)N",  # 磺胺类似物
        "CC1=CC=C(C=C1)C=O",  # 苯甲醛类似物
        "CC(C)C1=CC=C(C=C1)O",  # 酚类化合物2
        "CC1=CC=CC=C1N(C)C",  # 胺类化合物
        "CC1=CC=C(C=C1)C(=O)N",  # 酰胺类化合物
    ]
    
    # 非活性化合物的SMILES示例
    inactive_smiles = [
        "CCCCCCCCCCCCCCCC",  # 长链烷烃
        "CC(C)(C)C",  # 短链烷烃
        "C1=CC=CC=C1",  # 苯
        "CC=CC=CC=CC=CC=CC=CC=CC=CC=CC",  # 长链烯烃
        "CCCCCCCC",  # 辛烷
        "CC(C)C(C)C",  # 分支烷烃
        "C1CCCCC1",  # 环己烷
        "CC1=CC=CC=C1",  # 甲苯
        "CCO",  # 乙醇
        "CC(=O)O",  # 醋酸
    ]
    
    # 生成数据
    smiles_list = []
    activity_list = []
    mic_values = []
    
    np.random.seed(42)  # 确保可重现性
    
    for i in range(n_samples):
        if i < n_samples * 0.3:  # 30% 活性化合物
            base_smiles = np.random.choice(active_smiles)
            activity = 1
            # 活性化合物的MIC值较低 (1-16 μg/mL)
            mic = np.random.lognormal(mean=1.5, sigma=0.8)
            mic = max(0.5, min(mic, 16))  # 限制在0.5-16范围内
        else:  # 70% 非活性化合物
            base_smiles = np.random.choice(inactive_smiles)
            activity = 0
            # 非活性化合物的MIC值较高 (32-512 μg/mL)
            mic = np.random.lognormal(mean=4.5, sigma=0.8)
            mic = max(32, min(mic, 512))  # 限制在32-512范围内
        
        # 添加一些随机变化到SMILES
        try:
            mol = Chem.MolFromSmiles(base_smiles)
            if mol is not None:
                smiles = Chem.MolToSmiles(mol)
                smiles_list.append(smiles)
                activity_list.append(activity)
                mic_values.append(mic)
        except:
            continue
    
    # 创建DataFrame
    df = pd.DataFrame({
        'smiles': smiles_list,
        'activity': activity_list,
        'mic_ug_ml': mic_values
    })
    
    # 添加一些基本的分子描述符作为参考
    print("📊 计算分子描述符...")
    mw_list = []
    logp_list = []
    hbd_list = []
    hba_list = []
    
    for smiles in df['smiles']:
        try:
            mol = Chem.MolFromSmiles(smiles)
            if mol is not None:
                mw = Descriptors.MolWt(mol)
                logp = Descriptors.MolLogP(mol)
                hbd = Descriptors.NumHDonors(mol)
                hba = Descriptors.NumHAcceptors(mol)
            else:
                mw = logp = hbd = hba = np.nan
        except:
            mw = logp = hbd = hba = np.nan
        
        mw_list.append(mw)
        logp_list.append(logp)
        hbd_list.append(hbd)
        hba_list.append(hba)
    
    df['mol_weight'] = mw_list
    df['logp'] = logp_list
    df['hbd'] = hbd_list
    df['hba'] = hba_list
    
    # 移除无效的分子
    df = df.dropna().reset_index(drop=True)
    
    print(f"✅ 成功创建{len(df)}个有效化合物")
    print(f"   活性化合物: {sum(df['activity'])}个")
    print(f"   非活性化合物: {len(df) - sum(df['activity'])}个")
    
    # 保存完整数据集
    full_path = os.path.join(output_dir, "antibacterial_sample_data.csv")
    df.to_csv(full_path, index=False)
    print(f"💾 完整数据集已保存: {full_path}")
    
    # 创建训练/测试划分
    from sklearn.model_selection import train_test_split
    
    train_df, test_df = train_test_split(
        df, test_size=0.2, random_state=42, stratify=df['activity']
    )
    
    # 保存训练集
    train_path = os.path.join(output_dir, "train_data.csv")
    train_df.to_csv(train_path, index=False)
    print(f"💾 训练集已保存: {train_path} ({len(train_df)}个样本)")
    
    # 保存测试集
    test_path = os.path.join(output_dir, "test_data.csv")
    test_df.to_csv(test_path, index=False)
    print(f"💾 测试集已保存: {test_path} ({len(test_df)}个样本)")
    
    # 创建ChemProp格式的数据
    print("\n📋 创建ChemProp格式数据...")
    
    # ChemProp训练数据 (SMILES, activity)
    chemprop_train = train_df[['smiles', 'activity']].copy()
    chemprop_train_path = os.path.join(output_dir, "chemprop_train.csv")
    chemprop_train.to_csv(chemprop_train_path, index=False)
    print(f"💾 ChemProp训练数据: {chemprop_train_path}")
    
    # ChemProp测试数据 (只有SMILES)
    chemprop_test = test_df[['smiles']].copy()
    chemprop_test_path = os.path.join(output_dir, "chemprop_test.csv")
    chemprop_test.to_csv(chemprop_test_path, index=False)
    print(f"💾 ChemProp测试数据: {chemprop_test_path}")
    
    # 保存测试集真实标签
    test_labels = test_df[['smiles', 'activity']].copy()
    test_labels_path = os.path.join(output_dir, "test_labels.csv")
    test_labels.to_csv(test_labels_path, index=False)
    print(f"💾 测试集标签: {test_labels_path}")
    
    # 显示数据统计
    print(f"\n📊 数据集统计:")
    print(f"   总样本数: {len(df)}")
    print(f"   训练集: {len(train_df)} (活性: {sum(train_df['activity'])}, 非活性: {len(train_df) - sum(train_df['activity'])})")
    print(f"   测试集: {len(test_df)} (活性: {sum(test_df['activity'])}, 非活性: {len(test_df) - sum(test_df['activity'])})")
    print(f"   活性比例: {sum(df['activity'])/len(df)*100:.1f}%")
    
    print(f"\n🧬 示例数据创建完成！")
    
    return df, train_df, test_df

if __name__ == "__main__":
    # 创建示例数据
    df, train_df, test_df = create_sample_antibacterial_data(n_samples=1000)
    
    # 显示前几行数据
    print("\n📋 数据预览:")
    print(df.head())
    
    print("\n✅ 数据创建完成，可以开始基线对比实验！")
