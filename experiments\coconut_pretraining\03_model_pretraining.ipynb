{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# D-MPNN模型预训练\n", "# 作者: ZK\n", "# 邮箱: <EMAIL>\n", "# 日期: 2025-01-15\n", "# 描述: 基于CheMeleon架构的D-MPNN模型在COCONUT数据上的预训练"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 环境准备与配置"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import yaml\n", "import pickle\n", "import json\n", "import numpy as np\n", "import pandas as pd\n", "from pathlib import Path\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# PyTorch导入\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "from torch.utils.data import Dataset, DataLoader\n", "from torch.optim.lr_scheduler import ReduceLROnPlateau\n", "\n", "# 科学计算\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score\n", "from scipy.stats import pearsonr\n", "\n", "# 可视化\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from tqdm import tqdm\n", "\n", "# RDKit\n", "from rdkit import Chem\n", "from rdkit.Chem import rdMolDescriptors\n", "\n", "# 设置设备\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"使用设备: {device}\")\n", "\n", "if torch.cuda.is_available():\n", "    print(f\"GPU: {torch.cuda.get_device_name(0)}\")\n", "    print(f\"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB\")\n", "\n", "# 加载配置\n", "with open('configs/experiment_config.yaml', 'r', encoding='utf-8') as f:\n", "    config = yaml.safe_load(f)\n", "\n", "print(f\"\\n开始模型预训练: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "\n", "# 设置路径\n", "PROCESSED_DATA_DIR = Path('data/processed')\n", "MODELS_DIR = Path('models')\n", "RESULTS_DIR = Path('results')\n", "LOGS_DIR = Path('logs')\n", "\n", "for dir_path in [MODELS_DIR, RESULTS_DIR, LOGS_DIR]:\n", "    dir_path.mkdir(exist_ok=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 数据加载与预处理"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 加载预处理后的数据\n", "pretraining_data_path = PROCESSED_DATA_DIR / 'coconut_pretraining_data.pkl'\n", "\n", "if not pretraining_data_path.exists():\n", "    print(f\"✗ 未找到预训练数据: {pretraining_data_path}\")\n", "    print(\"请先运行 02_data_preprocessing.ipynb\")\n", "    sys.exit(1)\n", "\n", "print(f\"加载预训练数据: {pretraining_data_path}\")\n", "with open(pretraining_data_path, 'rb') as f:\n", "    data = pickle.load(f)\n", "\n", "print(f\"\\n数据统计:\")\n", "print(f\"- 分子数量: {len(data['smiles']):,}\")\n", "print(f\"- 描述符维度: {data['descriptors'].shape[1]:,}\")\n", "print(f\"- 分子描述符: {data['mol_descriptor_count']:,}\")\n", "print(f\"- 骨架描述符: {data['scaffold_descriptor_count']:,}\")\n", "\n", "# 提取数据\n", "smiles_list = data['smiles']\n", "descriptors = data['descriptors']\n", "descriptor_names = data['descriptor_names']\n", "\n", "print(f\"\\n描述符统计:\")\n", "print(f\"- 形状: {descriptors.shape}\")\n", "print(f\"- 数据类型: {descriptors.dtype}\")\n", "print(f\"- 范围: [{descriptors.min():.3f}, {descriptors.max():.3f}]\")\n", "print(f\"- 均值: {descriptors.mean():.3f}\")\n", "print(f\"- 标准差: {descriptors.std():.3f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 分子图特征提取"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def mol_to_graph_features(smiles):\n", "    \"\"\"\n", "    将SMILES转换为图特征\n", "    \n", "    返回:\n", "    - atom_features: 原子特征矩阵 [num_atoms, atom_feature_dim]\n", "    - bond_features: 键特征矩阵 [num_bonds, bond_feature_dim] \n", "    - adjacency: 邻接矩阵 [num_atoms, num_atoms]\n", "    \"\"\"\n", "    mol = Chem.Mo<PERSON>rom<PERSON>(smiles)\n", "    if mol is None:\n", "        return None\n", "    \n", "    # 原子特征\n", "    atom_features = []\n", "    for atom in mol.GetAtoms():\n", "        features = [\n", "            atom.GetAtomicNum(),\n", "            atom.GetDegree(),\n", "            atom.GetFormalCharge(),\n", "            int(atom.GetHybridization()),\n", "            int(atom.GetIsAromatic()),\n", "            atom.GetNumRadicalElectrons(),\n", "            atom.GetTotalNumHs(),\n", "            int(atom.IsInRing()),\n", "            atom.GetMass()\n", "        ]\n", "        atom_features.append(features)\n", "    \n", "    # 键特征和邻接矩阵\n", "    num_atoms = len(atom_features)\n", "    adjacency = np.zeros((num_atoms, num_atoms))\n", "    bond_features = []\n", "    \n", "    for bond in mol.GetBonds():\n", "        i = bond.GetBeginAtomIdx()\n", "        j = bond.GetEndAtomIdx()\n", "        \n", "        bond_feature = [\n", "            int(bond.GetBondType()),\n", "            int(bond.GetIsConjugated()),\n", "            int(bond.IsInRing()),\n", "            int(bond.GetStereo())\n", "        ]\n", "        \n", "        bond_features.append(bond_feature)\n", "        adjacency[i, j] = 1\n", "        adjacency[j, i] = 1\n", "    \n", "    return {\n", "        'atom_features': np.array(atom_features, dtype=np.float32),\n", "        'bond_features': np.array(bond_features, dtype=np.float32) if bond_features else np.empty((0, 4), dtype=np.float32),\n", "        'adjacency': adjacency.astype(np.float32),\n", "        'num_atoms': num_atoms\n", "    }\n", "\n", "print(\"提取分子图特征...\")\n", "\n", "# 处理分子图特征（采样处理以节省时间）\n", "SAMPLE_SIZE = min(10000, len(smiles_list))  # 采样处理\n", "print(f\"采样处理 {SAMPLE_SIZE:,} 个分子\")\n", "\n", "sample_indices = np.random.choice(len(smiles_list), SAMPLE_SIZE, replace=False)\n", "sample_smiles = [smiles_list[i] for i in sample_indices]\n", "sample_descriptors = descriptors[sample_indices]\n", "\n", "graph_features = []\n", "valid_indices = []\n", "\n", "for i, smiles in enumerate(tqdm(sample_smiles, desc=\"提取图特征\")):\n", "    features = mol_to_graph_features(smiles)\n", "    if features is not None:\n", "        graph_features.append(features)\n", "        valid_indices.append(i)\n", "\n", "print(f\"\\n图特征提取完成:\")\n", "print(f\"- 成功提取: {len(graph_features):,}\")\n", "print(f\"- 失败数量: {SAMPLE_SIZE - len(graph_features):,}\")\n", "print(f\"- 成功率: {len(graph_features)/SAMPLE_SIZE*100:.1f}%\")\n", "\n", "# 过滤有效数据\n", "valid_descriptors = sample_descriptors[valid_indices]\n", "valid_smiles = [sample_smiles[i] for i in valid_indices]\n", "\n", "print(f\"有效训练样本: {len(valid_descriptors):,}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 数据集类定义"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class MolecularDataset(Dataset):\n", "    \"\"\"\n", "    分子数据集类\n", "    \"\"\"\n", "    def __init__(self, graph_features, descriptors, transform=None):\n", "        self.graph_features = graph_features\n", "        self.descriptors = descriptors\n", "        self.transform = transform\n", "        \n", "    def __len__(self):\n", "        return len(self.graph_features)\n", "    \n", "    def __getitem__(self, idx):\n", "        graph = self.graph_features[idx]\n", "        target = self.descriptors[idx]\n", "        \n", "        if self.transform:\n", "            target = self.transform(target)\n", "        \n", "        return {\n", "            'atom_features': torch.FloatTensor(graph['atom_features']),\n", "            'bond_features': torch.FloatTensor(graph['bond_features']),\n", "            'adjacency': torch.FloatTensor(graph['adjacency']),\n", "            'num_atoms': graph['num_atoms'],\n", "            'target': torch.FloatTensor(target)\n", "        }\n", "\n", "def collate_fn(batch):\n", "    \"\"\"\n", "    自定义批处理函数\n", "    \"\"\"\n", "    # 找到最大原子数\n", "    max_atoms = max([item['num_atoms'] for item in batch])\n", "    \n", "    batch_atom_features = []\n", "    batch_adjacency = []\n", "    batch_targets = []\n", "    batch_masks = []\n", "    \n", "    for item in batch:\n", "        num_atoms = item['num_atoms']\n", "        \n", "        # 填充原子特征\n", "        atom_features = item['atom_features']\n", "        if num_atoms < max_atoms:\n", "            padding = torch.zeros(max_atoms - num_atoms, atom_features.size(1))\n", "            atom_features = torch.cat([atom_features, padding], dim=0)\n", "        \n", "        # 填充邻接矩阵\n", "        adjacency = item['adjacency']\n", "        if num_atoms < max_atoms:\n", "            padding = torch.zeros(max_atoms, max_atoms)\n", "            padding[:num_atoms, :num_atoms] = adjacency\n", "            adjacency = padding\n", "        \n", "        # 创建掩码\n", "        mask = torch.zeros(max_atoms)\n", "        mask[:num_atoms] = 1\n", "        \n", "        batch_atom_features.append(atom_features)\n", "        batch_adjacency.append(adjacency)\n", "        batch_targets.append(item['target'])\n", "        batch_masks.append(mask)\n", "    \n", "    return {\n", "        'atom_features': torch.stack(batch_atom_features),\n", "        'adjacency': torch.stack(batch_adjacency),\n", "        'targets': torch.stack(batch_targets),\n", "        'masks': torch.stack(batch_masks)\n", "    }\n", "\n", "# 标准化描述符\n", "print(\"标准化描述符...\")\n", "scaler = StandardScaler()\n", "scaled_descriptors = scaler.fit_transform(valid_descriptors)\n", "\n", "print(f\"标准化后描述符统计:\")\n", "print(f\"- 均值: {scaled_descriptors.mean():.6f}\")\n", "print(f\"- 标准差: {scaled_descriptors.std():.6f}\")\n", "print(f\"- 范围: [{scaled_descriptors.min():.3f}, {scaled_descriptors.max():.3f}]\")\n", "\n", "# 划分训练集和验证集\n", "train_graphs, val_graphs, train_desc, val_desc = train_test_split(\n", "    graph_features, scaled_descriptors, \n", "    test_size=0.1, random_state=42\n", ")\n", "\n", "print(f\"\\n数据划分:\")\n", "print(f\"- 训练集: {len(train_graphs):,}\")\n", "print(f\"- 验证集: {len(val_graphs):,}\")\n", "\n", "# 创建数据集\n", "train_dataset = MolecularDataset(train_graphs, train_desc)\n", "val_dataset = MolecularDataset(val_graphs, val_desc)\n", "\n", "# 创建数据加载器\n", "batch_size = config['training_config']['pretraining']['batch_size']\n", "train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, collate_fn=collate_fn)\n", "val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, collate_fn=collate_fn)\n", "\n", "print(f\"\\n数据加载器创建完成:\")\n", "print(f\"- 批大小: {batch_size}\")\n", "print(f\"- 训练批次: {len(train_loader)}\")\n", "print(f\"- 验证批次: {len(val_loader)}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}