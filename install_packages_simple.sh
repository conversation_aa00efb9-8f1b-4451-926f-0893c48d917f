#!/bin/bash
# 神农框架包安装脚本 (简化版)
# 作者: ZK
# 日期: 2025-06-30

echo "🧬 开始安装神农框架依赖包..."

# 检查是否在shennong环境中
if [[ "$CONDA_DEFAULT_ENV" != "shennong" ]]; then
    echo "❌ 请先激活shennong环境: conda activate shennong"
    exit 1
fi

echo "✅ 当前环境: $CONDA_DEFAULT_ENV"

# 更新pip
echo "📦 更新pip..."
pip install --upgrade pip

# 安装基础科学计算包
echo "🔢 安装基础科学计算包..."
pip install numpy pandas scipy matplotlib seaborn jupyter tqdm psutil memory_profiler

echo "✅ 基础包安装完成，验证..."
python -c "import numpy, pandas, scipy; print('基础包验证成功')"

# 安装PyTorch (CUDA 11.8)
echo "🔥 安装PyTorch (CUDA 11.8)..."
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

echo "✅ PyTorch安装完成，验证..."
python -c "import torch; print(f'PyTorch: {torch.__version__}, CUDA: {torch.cuda.is_available()}')"

# 安装机器学习包
echo "🤖 安装机器学习包..."
pip install scikit-learn statsmodels

echo "✅ ML包安装完成，验证..."
python -c "import sklearn, statsmodels; print('ML包验证成功')"

# 安装RDKit
echo "⚗️  安装RDKit..."
conda install -c conda-forge rdkit -y

echo "✅ RDKit安装完成，验证..."
python -c "from rdkit import Chem; print('RDKit验证成功')"

# 安装化学信息学包
echo "🧪 安装化学信息学包..."
pip install mordred

echo "✅ Mordred安装完成，验证..."
python -c "from mordred import Calculator; print('Mordred验证成功')"

# 安装ChemProp
echo "🧬 安装ChemProp..."
pip install chemprop

echo "✅ ChemProp安装完成，验证..."
python -c "import chemprop; print('ChemProp验证成功')"

# 安装AutoGluon
echo "🚀 安装AutoGluon..."
pip install autogluon

echo "✅ AutoGluon安装完成，验证..."
python -c "from autogluon.tabular import TabularPredictor; print('AutoGluon验证成功')"

# 安装图神经网络包
echo "🕸️  安装图神经网络包..."
pip install torch-geometric

echo "✅ 图神经网络包安装完成，验证..."
python -c "import torch_geometric; print('PyG验证成功')"

# 安装高级ML工具
echo "🔧 安装高级ML工具..."
pip install optuna shap tensorboard

echo "✅ 高级工具安装完成，验证..."
python -c "import optuna, shap; print('高级工具验证成功')"

echo "🎉 所有包安装完成！"

# 运行最终验证
echo "🔍 运行最终验证..."
python -c "
import sys
packages = ['torch', 'numpy', 'pandas', 'sklearn', 'mordred', 'chemprop', 'autogluon']
success = 0
print('📦 包验证结果:')
for pkg in packages:
    try:
        if pkg == 'sklearn':
            import sklearn
            print(f'✅ {pkg}: {sklearn.__version__}')
        elif pkg == 'rdkit':
            from rdkit import Chem
            print(f'✅ {pkg}: OK')
        else:
            module = __import__(pkg)
            version = getattr(module, '__version__', 'OK')
            print(f'✅ {pkg}: {version}')
        success += 1
    except ImportError as e:
        print(f'❌ {pkg}: FAILED - {e}')

print(f'\\n📊 安装成功率: {success}/{len(packages)} ({success/len(packages)*100:.1f}%)')

# CUDA检查
try:
    import torch
    print(f'\\n🔥 CUDA检查:')
    print(f'   CUDA可用: {torch.cuda.is_available()}')
    if torch.cuda.is_available():
        print(f'   CUDA版本: {torch.version.cuda}')
        print(f'   GPU数量: {torch.cuda.device_count()}')
        if torch.cuda.device_count() > 0:
            print(f'   GPU名称: {torch.cuda.get_device_name(0)}')
except Exception as e:
    print(f'❌ PyTorch CUDA检查失败: {e}')

# RDKit功能测试
try:
    from rdkit import Chem
    mol = Chem.MolFromSmiles('CCO')
    if mol:
        print('\\n⚗️  RDKit功能测试: ✅ 通过')
    else:
        print('\\n⚗️  RDKit功能测试: ❌ 失败')
except Exception as e:
    print(f'\\n⚗️  RDKit功能测试: ❌ 失败 - {e}')
"

echo ""
echo "🧬 神农框架环境配置完成！"
echo "🚀 下一步: 运行 python quick_env_check.py 进行完整验证"
