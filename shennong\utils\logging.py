# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-27
# 描述: 神农框架日志管理

"""
神农框架日志管理

提供统一的日志配置和管理功能。
"""

import logging
import sys
from pathlib import Path
from typing import Optional, Union
import colorlog


def setup_logging(
    level: Union[str, int] = logging.INFO,
    log_file: Optional[Union[str, Path]] = None,
    log_dir: Optional[Union[str, Path]] = None,
    format_string: Optional[str] = None,
    use_colors: bool = True
) -> logging.Logger:
    """
    设置日志配置
    
    Args:
        level: 日志级别
        log_file: 日志文件名
        log_dir: 日志目录
        format_string: 自定义格式字符串
        use_colors: 是否使用彩色输出
        
    Returns:
        根日志记录器
    """
    # 获取根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    
    # 清除现有处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 默认格式
    if format_string is None:
        format_string = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(level)
    
    if use_colors:
        try:
            # 彩色格式
            color_format = (
                '%(log_color)s%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            formatter = colorlog.ColoredFormatter(
                color_format,
                datefmt='%Y-%m-%d %H:%M:%S',
                log_colors={
                    'DEBUG': 'cyan',
                    'INFO': 'green',
                    'WARNING': 'yellow',
                    'ERROR': 'red',
                    'CRITICAL': 'red,bg_white',
                }
            )
        except ImportError:
            # 如果colorlog不可用，使用标准格式
            formatter = logging.Formatter(format_string, datefmt='%Y-%m-%d %H:%M:%S')
    else:
        formatter = logging.Formatter(format_string, datefmt='%Y-%m-%d %H:%M:%S')
    
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # 文件处理器
    if log_file or log_dir:
        if log_dir:
            log_dir = Path(log_dir)
            log_dir.mkdir(parents=True, exist_ok=True)
            
            if log_file:
                log_path = log_dir / log_file
            else:
                log_path = log_dir / 'shennong.log'
        else:
            log_path = Path(log_file)
            log_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.FileHandler(log_path, encoding='utf-8')
        file_handler.setLevel(level)
        
        # 文件格式（不使用颜色）
        file_formatter = logging.Formatter(format_string, datefmt='%Y-%m-%d %H:%M:%S')
        file_handler.setFormatter(file_formatter)
        root_logger.addHandler(file_handler)
        
        root_logger.info(f"日志文件: {log_path}")
    
    root_logger.info("日志系统初始化完成")
    return root_logger


def get_logger(name: str) -> logging.Logger:
    """
    获取指定名称的日志记录器
    
    Args:
        name: 日志记录器名称
        
    Returns:
        日志记录器
    """
    return logging.getLogger(name)


def set_log_level(level: Union[str, int], logger_name: Optional[str] = None):
    """
    设置日志级别
    
    Args:
        level: 日志级别
        logger_name: 日志记录器名称，None表示根记录器
    """
    if logger_name:
        logger = logging.getLogger(logger_name)
    else:
        logger = logging.getLogger()
    
    logger.setLevel(level)
    
    # 同时设置所有处理器的级别
    for handler in logger.handlers:
        handler.setLevel(level)


def disable_logging(logger_name: Optional[str] = None):
    """
    禁用日志输出
    
    Args:
        logger_name: 日志记录器名称，None表示根记录器
    """
    if logger_name:
        logger = logging.getLogger(logger_name)
    else:
        logger = logging.getLogger()
    
    logger.disabled = True


def enable_logging(logger_name: Optional[str] = None):
    """
    启用日志输出
    
    Args:
        logger_name: 日志记录器名称，None表示根记录器
    """
    if logger_name:
        logger = logging.getLogger(logger_name)
    else:
        logger = logging.getLogger()
    
    logger.disabled = False
