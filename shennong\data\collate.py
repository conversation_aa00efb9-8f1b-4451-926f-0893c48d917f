# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-27
# 描述: 神农框架批处理数据整理模块

"""
神农框架批处理数据整理

提供自定义的批处理函数，处理分子图和描述符的批量组织。
支持变长数据的填充和掩码生成。
"""

from typing import List, Dict, Any, Optional, Union
import torch
import numpy as np
from torch.nn.utils.rnn import pad_sequence

# Chemprop导入
try:
    from chemprop.data import BatchMolGraph
except ImportError:
    raise ImportError(
        "Chemprop未安装或版本不兼容。请安装: "
        "pip install git+https://github.com/chemprop/chemprop.git@v2.0.0"
    )


def shennong_collate_fn(batch: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    神农框架自定义批处理函数
    
    处理包含分子图和描述符的批量数据，生成适合神经网络训练的批次。
    
    Args:
        batch: 批量数据列表，每个元素包含分子图、描述符、目标值等
        
    Returns:
        批处理后的数据字典
    """
    # 提取各个组件
    smiles_list = [item['smiles'] for item in batch]
    mol_graphs = [item['mol_graph'] for item in batch]
    descriptors_list = [item['descriptors'] for item in batch if item['descriptors'] is not None]
    targets_list = [item['targets'] for item in batch]
    metadata_list = [item.get('metadata', {}) for item in batch]
    
    # 处理分子图批处理
    batch_mol_graph = _create_batch_mol_graph(mol_graphs)
    
    # 处理描述符批处理
    batch_descriptors = _create_batch_descriptors(descriptors_list, len(batch))
    
    # 处理目标值批处理
    batch_targets, target_mask = _create_batch_targets(targets_list)
    
    # 处理机制标签（如果存在）
    mechanism_labels = None
    if 'mechanism_labels' in batch[0] and batch[0]['mechanism_labels'] is not None:
        mechanism_labels = _create_batch_mechanism_labels(
            [item.get('mechanism_labels', []) for item in batch]
        )
    
    # 构建批处理结果
    collated_batch = {
        'smiles': smiles_list,
        'mol_graphs': batch_mol_graph,
        'descriptors': batch_descriptors,
        'targets': batch_targets,
        'target_mask': target_mask,
        'metadata': metadata_list,
        'batch_size': len(batch)
    }
    
    if mechanism_labels is not None:
        collated_batch['mechanism_labels'] = mechanism_labels
    
    return collated_batch


def _create_batch_mol_graph(mol_graphs: List[Any]) -> BatchMolGraph:
    """
    创建分子图批处理
    
    Args:
        mol_graphs: 分子图列表
        
    Returns:
        批处理分子图对象
    """
    try:
        # 使用Chemprop的BatchMolGraph
        return BatchMolGraph(mol_graphs)
    except Exception as e:
        # 如果Chemprop的BatchMolGraph不可用，使用简化版本
        return _create_simple_batch_mol_graph(mol_graphs)


def _create_simple_batch_mol_graph(mol_graphs: List[Any]) -> Dict[str, torch.Tensor]:
    """
    创建简化的分子图批处理（备用方案）
    
    Args:
        mol_graphs: 分子图列表
        
    Returns:
        简化的批处理分子图字典
    """
    # 收集所有原子和键特征
    all_atom_features = []
    all_bond_features = []
    atom_offsets = [0]
    bond_offsets = [0]
    
    for mol_graph in mol_graphs:
        # 原子特征
        atom_features = torch.tensor(mol_graph.atom_features, dtype=torch.float32)
        all_atom_features.append(atom_features)
        atom_offsets.append(atom_offsets[-1] + len(atom_features))
        
        # 键特征
        if mol_graph.bond_features:
            bond_features = torch.tensor(mol_graph.bond_features, dtype=torch.float32)
            all_bond_features.append(bond_features)
            bond_offsets.append(bond_offsets[-1] + len(bond_features))
        else:
            bond_offsets.append(bond_offsets[-1])
    
    # 拼接特征
    batched_atom_features = torch.cat(all_atom_features, dim=0) if all_atom_features else torch.empty(0, 0)
    batched_bond_features = torch.cat(all_bond_features, dim=0) if all_bond_features else torch.empty(0, 0)
    
    return {
        'atom_features': batched_atom_features,
        'bond_features': batched_bond_features,
        'atom_offsets': torch.tensor(atom_offsets[:-1], dtype=torch.long),
        'bond_offsets': torch.tensor(bond_offsets[:-1], dtype=torch.long),
        'num_molecules': len(mol_graphs)
    }


def _create_batch_descriptors(
    descriptors_list: List[np.ndarray], 
    batch_size: int
) -> Optional[torch.Tensor]:
    """
    创建描述符批处理
    
    Args:
        descriptors_list: 描述符列表
        batch_size: 批次大小
        
    Returns:
        批处理描述符张量，如果没有描述符则返回None
    """
    if not descriptors_list:
        return None
    
    # 检查维度一致性
    descriptor_dim = len(descriptors_list[0])
    for i, desc in enumerate(descriptors_list):
        if len(desc) != descriptor_dim:
            raise ValueError(f"描述符维度不一致: 样本{i}的维度为{len(desc)}, 期望{descriptor_dim}")
    
    # 如果某些样本缺少描述符，用零填充
    if len(descriptors_list) < batch_size:
        for _ in range(batch_size - len(descriptors_list)):
            descriptors_list.append(np.zeros(descriptor_dim))
    
    # 转换为张量
    descriptors_array = np.array(descriptors_list)
    return torch.tensor(descriptors_array, dtype=torch.float32)


def _create_batch_targets(targets_list: List[Dict[str, float]]) -> tuple[Dict[str, torch.Tensor], Dict[str, torch.Tensor]]:
    """
    创建目标值批处理
    
    Args:
        targets_list: 目标值字典列表
        
    Returns:
        (批处理目标值字典, 目标值掩码字典)
    """
    # 收集所有任务名称
    all_task_names = set()
    for targets in targets_list:
        all_task_names.update(targets.keys())
    
    all_task_names = sorted(list(all_task_names))
    
    # 为每个任务创建批处理张量
    batch_targets = {}
    target_mask = {}
    
    for task_name in all_task_names:
        task_values = []
        task_mask = []
        
        for targets in targets_list:
            if task_name in targets and targets[task_name] is not None:
                task_values.append(targets[task_name])
                task_mask.append(1.0)
            else:
                task_values.append(0.0)  # 填充值
                task_mask.append(0.0)    # 掩码标记为无效
        
        batch_targets[task_name] = torch.tensor(task_values, dtype=torch.float32)
        target_mask[task_name] = torch.tensor(task_mask, dtype=torch.float32)
    
    return batch_targets, target_mask


def _create_batch_mechanism_labels(mechanism_labels_list: List[List[str]]) -> Dict[str, torch.Tensor]:
    """
    创建机制标签批处理
    
    Args:
        mechanism_labels_list: 机制标签列表的列表
        
    Returns:
        批处理机制标签字典
    """
    # 收集所有机制类型
    all_mechanisms = set()
    for labels in mechanism_labels_list:
        all_mechanisms.update(labels)
    
    all_mechanisms = sorted(list(all_mechanisms))
    
    # 创建多热编码
    batch_mechanism_labels = torch.zeros(len(mechanism_labels_list), len(all_mechanisms))
    
    for i, labels in enumerate(mechanism_labels_list):
        for j, mechanism in enumerate(all_mechanisms):
            if mechanism in labels:
                batch_mechanism_labels[i, j] = 1.0
    
    return {
        'labels': batch_mechanism_labels,
        'mechanism_names': all_mechanisms
    }


def create_padding_mask(lengths: List[int], max_length: Optional[int] = None) -> torch.Tensor:
    """
    创建填充掩码
    
    Args:
        lengths: 序列长度列表
        max_length: 最大长度，如果为None则使用最大的长度
        
    Returns:
        填充掩码张量 (batch_size, max_length)
    """
    if max_length is None:
        max_length = max(lengths)
    
    batch_size = len(lengths)
    mask = torch.zeros(batch_size, max_length, dtype=torch.bool)
    
    for i, length in enumerate(lengths):
        mask[i, :length] = True
    
    return mask


def pad_sequences(
    sequences: List[torch.Tensor], 
    padding_value: float = 0.0,
    batch_first: bool = True
) -> torch.Tensor:
    """
    填充序列到相同长度
    
    Args:
        sequences: 序列张量列表
        padding_value: 填充值
        batch_first: 是否批次维度在前
        
    Returns:
        填充后的张量
    """
    return pad_sequence(
        sequences, 
        batch_first=batch_first, 
        padding_value=padding_value
    )


class ShennongBatchSampler:
    """
    神农框架批次采样器
    
    支持基于分子大小或描述符维度的智能批次采样，
    优化GPU内存使用和训练效率。
    """
    
    def __init__(
        self,
        dataset_size: int,
        batch_size: int,
        molecule_sizes: Optional[List[int]] = None,
        sort_by_size: bool = False,
        drop_last: bool = False
    ):
        """
        初始化批次采样器
        
        Args:
            dataset_size: 数据集大小
            batch_size: 批次大小
            molecule_sizes: 分子大小列表（原子数）
            sort_by_size: 是否按分子大小排序
            drop_last: 是否丢弃最后一个不完整的批次
        """
        self.dataset_size = dataset_size
        self.batch_size = batch_size
        self.molecule_sizes = molecule_sizes
        self.sort_by_size = sort_by_size
        self.drop_last = drop_last
        
        self._create_batches()
    
    def _create_batches(self):
        """创建批次索引"""
        indices = list(range(self.dataset_size))
        
        if self.sort_by_size and self.molecule_sizes:
            # 按分子大小排序
            size_index_pairs = list(zip(self.molecule_sizes, indices))
            size_index_pairs.sort(key=lambda x: x[0])
            indices = [idx for _, idx in size_index_pairs]
        
        # 创建批次
        self.batches = []
        for i in range(0, len(indices), self.batch_size):
            batch_indices = indices[i:i + self.batch_size]
            
            if len(batch_indices) == self.batch_size or not self.drop_last:
                self.batches.append(batch_indices)
    
    def __iter__(self):
        """迭代批次"""
        for batch in self.batches:
            yield batch
    
    def __len__(self):
        """批次数量"""
        return len(self.batches)
