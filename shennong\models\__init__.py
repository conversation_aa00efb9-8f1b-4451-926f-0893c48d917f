# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-27
# 描述: 神农框架模型模块初始化

"""
神农框架模型模块

包含神农框架的核心模型定义，包括主模型、分支模型和融合模块。

主要组件:
- shennong_core: 神农框架核心模型
- graph_branch: 图神经网络分支
- expert_branch: 专家特征分支
- fusion: 特征融合模块
- base: 基础模型类
"""

from .shennong_core import ShennongFramework
from .graph_branch import AntibacterialGraphBranch, ChempropGraphBranch
from .expert_branch import AdaptiveExpertBranch, ExpertFeatureBranch
from .fusion import FeatureFusionModule
from .base import BaseShennongModel

__all__ = [
    # 核心模型
    'ShennongFramework',
    
    # 分支模型
    'AntibacterialGraphBranch',
    'ChempropGraphBranch',
    'AdaptiveExpertBranch',
    'ExpertFeatureBranch',
    
    # 融合模块
    'FeatureFusionModule',
    
    # 基础类
    'BaseShennongModel',
]
