# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-29
# 描述: 神农框架Mordred描述符分析器

"""
神农框架Mordred描述符分析器

将Mordred描述符转化为化学家能理解的分子性质解释。
"""

import logging
import numpy as np
from typing import Dict, List, Any, Optional, Tuple

logger = logging.getLogger(__name__)


class DescriptorAnalyzer:
    """
    Mordred描述符分析器

    分析Mordred描述符的化学意义，识别对抗菌活性重要的分子性质。
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化描述符分析器

        Args:
            config: 配置参数
        """
        self.config = config or {}

        # 重要性阈值
        self.importance_threshold = self.config.get('importance_threshold', 0.1)

        # 最大分析描述符数量
        self.max_descriptors = self.config.get('max_descriptors', 20)

        # 初始化描述符分类
        self.descriptor_categories = self._initialize_descriptor_categories()

        logger.info("Mordred描述符分析器初始化完成")

    def analyze_descriptors(
        self,
        smiles: str,
        descriptor_values: np.ndarray,
        descriptor_names: List[str],
        predicted_activity: float
    ) -> Dict[str, Any]:
        """
        分析描述符的化学意义

        Args:
            smiles: 分子SMILES
            descriptor_values: 描述符数值
            descriptor_names: 描述符名称
            predicted_activity: 预测活性

        Returns:
            描述符的化学分析结果
        """
        if len(descriptor_values) != len(descriptor_names):
            return {'error': '描述符数值与名称数量不匹配'}

        # 过滤有效描述符
        valid_indices = ~(np.isnan(descriptor_values) | np.isinf(descriptor_values))
        valid_values = descriptor_values[valid_indices]
        valid_names = [descriptor_names[i] for i in range(len(descriptor_names)) if valid_indices[i]]

        if len(valid_values) == 0:
            return {'error': '没有有效的描述符数值'}

        analysis = {
            'descriptor_quality': self._assess_descriptor_quality(descriptor_values),
            'important_descriptors': self._identify_important_descriptors(
                valid_values, valid_names, predicted_activity
            ),
            'category_analysis': self._analyze_by_category(valid_values, valid_names),
            'chemical_interpretation': self._interpret_chemical_meaning(
                valid_values, valid_names, smiles
            ),
            'activity_correlation': self._analyze_activity_correlation(
                valid_values, valid_names, predicted_activity
            )
        }

        return analysis

    def _initialize_descriptor_categories(self) -> Dict[str, Dict[str, Any]]:
        """初始化描述符分类"""
        return {
            'constitutional': {
                'keywords': ['MW', 'nAtom', 'nBond', 'nRing', 'Weight'],
                'description': '分子组成描述符',
                'chemical_meaning': '描述分子的基本组成，如原子数、键数、分子量等'
            },
            'topological': {
                'keywords': ['Zagreb', 'Wiener', 'Randic', 'Kappa', 'Chi'],
                'description': '拓扑描述符',
                'chemical_meaning': '描述分子的拓扑结构特征，反映分子的连接性和形状'
            },
            'geometric': {
                'keywords': ['GRAV', 'GEOM', 'RDF', 'MORSE', '3D'],
                'description': '几何描述符',
                'chemical_meaning': '描述分子的三维几何特征，如分子体积、表面积等'
            },
            'electronic': {
                'keywords': ['HOMO', 'LUMO', 'Dipole', 'Polar', 'Charge'],
                'description': '电子结构描述符',
                'chemical_meaning': '描述分子的电子性质，如偶极矩、极化率等'
            },
            'pharmacophore': {
                'keywords': ['HBD', 'HBA', 'LogP', 'TPSA', 'Lipinski'],
                'description': '药效团描述符',
                'chemical_meaning': '描述与药物活性相关的分子性质'
            },
            'fragment': {
                'keywords': ['fr_', 'Fragment', 'Substructure'],
                'description': '片段描述符',
                'chemical_meaning': '描述分子中特定化学片段的存在和数量'
            },
            'autocorrelation': {
                'keywords': ['ATS', 'ATSC', 'MATS', 'GATS'],
                'description': '自相关描述符',
                'chemical_meaning': '描述分子中原子性质的空间分布模式'
            },
            'matrix': {
                'keywords': ['SpMax', 'SpMin', 'SpAD', 'EE_', 'VE_'],
                'description': '矩阵描述符',
                'chemical_meaning': '基于分子图矩阵计算的描述符'
            }
        }

    def _assess_descriptor_quality(self, descriptor_values: np.ndarray) -> Dict[str, Any]:
        """评估描述符质量"""
        total_count = len(descriptor_values)
        nan_count = np.isnan(descriptor_values).sum()
        inf_count = np.isinf(descriptor_values).sum()
        zero_count = (descriptor_values == 0).sum()

        valid_count = total_count - nan_count - inf_count
        success_rate = valid_count / total_count if total_count > 0 else 0

        quality_score = success_rate
        if zero_count > total_count * 0.8:  # 超过80%为零值
            quality_score *= 0.5

        return {
            'total_descriptors': int(total_count),
            'valid_descriptors': int(valid_count),
            'nan_count': int(nan_count),
            'inf_count': int(inf_count),
            'zero_count': int(zero_count),
            'success_rate': float(success_rate),
            'quality_score': float(quality_score)
        }

    def _identify_important_descriptors(
        self,
        values: np.ndarray,
        names: List[str],
        activity: float
    ) -> List[Dict[str, Any]]:
        """识别重要的描述符"""

        # 计算描述符的重要性分数
        importance_scores = []

        for i, (value, name) in enumerate(zip(values, names)):
            # 基于数值大小的重要性（归一化）
            magnitude_score = abs(value) / (np.max(np.abs(values)) + 1e-8)

            # 基于描述符类型的重要性
            category_score = self._get_category_importance(name)

            # 基于与活性的潜在相关性
            activity_score = self._estimate_activity_relevance(name, value, activity)

            # 综合重要性分数
            total_score = (magnitude_score * 0.3 +
                          category_score * 0.4 +
                          activity_score * 0.3)

            importance_scores.append({
                'index': i,
                'name': name,
                'value': float(value),
                'importance_score': float(total_score),
                'category': self._classify_descriptor(name),
                'chemical_meaning': self._get_descriptor_meaning(name)
            })

        # 按重要性排序
        importance_scores.sort(key=lambda x: x['importance_score'], reverse=True)

        # 返回前N个重要描述符
        return importance_scores[:self.max_descriptors]

    def _analyze_by_category(
        self,
        values: np.ndarray,
        names: List[str]
    ) -> Dict[str, Any]:
        """按类别分析描述符"""

        category_stats = {}

        for category, info in self.descriptor_categories.items():
            # 找到属于该类别的描述符
            category_indices = []
            for i, name in enumerate(names):
                if any(keyword in name for keyword in info['keywords']):
                    category_indices.append(i)

            if category_indices:
                category_values = values[category_indices]
                valid_values = category_values[~(np.isnan(category_values) | np.isinf(category_values))]

                if len(valid_values) > 0:
                    category_stats[category] = {
                        'count': len(category_indices),
                        'valid_count': len(valid_values),
                        'mean': float(np.mean(valid_values)),
                        'std': float(np.std(valid_values)),
                        'min': float(np.min(valid_values)),
                        'max': float(np.max(valid_values)),
                        'description': info['description'],
                        'chemical_meaning': info['chemical_meaning']
                    }

        return category_stats

    def _interpret_chemical_meaning(
        self,
        values: np.ndarray,
        names: List[str],
        smiles: str
    ) -> Dict[str, Any]:
        """解释描述符的化学意义"""

        interpretations = []

        # 分析关键的化学性质描述符
        key_descriptors = {
            'MW': '分子量',
            'LogP': '脂水分配系数',
            'TPSA': '拓扑极性表面积',
            'nHBD': '氢键供体数',
            'nHBA': '氢键受体数',
            'nRing': '环数',
            'nAromRing': '芳香环数'
        }

        for i, name in enumerate(names):
            for key, meaning in key_descriptors.items():
                if key in name:
                    value = values[i]
                    interpretation = self._interpret_descriptor_value(key, value, meaning)
                    if interpretation:
                        interpretations.append(interpretation)
                    break

        # 分析分子复杂性
        complexity_analysis = self._analyze_molecular_complexity(values, names)

        # 分析药物相似性相关描述符
        drug_likeness_analysis = self._analyze_drug_likeness_descriptors(values, names)

        return {
            'key_properties': interpretations,
            'complexity_analysis': complexity_analysis,
            'drug_likeness_analysis': drug_likeness_analysis
        }

    def _analyze_activity_correlation(
        self,
        values: np.ndarray,
        names: List[str],
        activity: float
    ) -> Dict[str, Any]:
        """分析描述符与活性的相关性"""

        # 基于已知的构效关系分析
        activity_relevant = []

        # 抗菌活性相关的描述符模式
        antibacterial_patterns = {
            'lipophilicity': ['LogP', 'MLogP', 'ALOGP'],
            'molecular_size': ['MW', 'nAtom', 'nBond'],
            'polarity': ['TPSA', 'PSA', 'Polar'],
            'hydrogen_bonding': ['HBD', 'HBA', 'nOH', 'nNH'],
            'aromaticity': ['nAromRing', 'AromaticRatio', 'Aromatic'],
            'flexibility': ['nRotB', 'RotatableBond', 'Flexibility']
        }

        for pattern_name, keywords in antibacterial_patterns.items():
            pattern_values = []
            pattern_names = []

            for i, name in enumerate(names):
                if any(keyword in name for keyword in keywords):
                    pattern_values.append(values[i])
                    pattern_names.append(name)

            if pattern_values:
                avg_value = np.mean(pattern_values)
                relevance_score = self._calculate_activity_relevance(
                    pattern_name, avg_value, activity
                )

                activity_relevant.append({
                    'pattern': pattern_name,
                    'descriptors': pattern_names,
                    'average_value': float(avg_value),
                    'relevance_score': float(relevance_score),
                    'interpretation': self._interpret_activity_pattern(
                        pattern_name, avg_value, activity
                    )
                })

        return {
            'activity_relevant_patterns': activity_relevant,
            'overall_activity_support': np.mean([p['relevance_score'] for p in activity_relevant])
        }

    def _classify_descriptor(self, name: str) -> str:
        """分类描述符"""
        for category, info in self.descriptor_categories.items():
            if any(keyword in name for keyword in info['keywords']):
                return category
        return 'unknown'

    def _get_category_importance(self, name: str) -> float:
        """获取描述符类别的重要性分数"""
        category_importance = {
            'pharmacophore': 1.0,
            'constitutional': 0.8,
            'electronic': 0.7,
            'topological': 0.6,
            'fragment': 0.8,
            'geometric': 0.5,
            'autocorrelation': 0.4,
            'matrix': 0.3
        }

        category = self._classify_descriptor(name)
        return category_importance.get(category, 0.5)

    def _estimate_activity_relevance(self, name: str, value: float, activity: float) -> float:
        """估计描述符与活性的相关性"""
        # 基于已知的构效关系
        if 'LogP' in name:
            # 适中的脂溶性有利于抗菌活性
            optimal_logp = 2.5
            deviation = abs(value - optimal_logp)
            return max(0, 1 - deviation / 5)

        elif 'MW' in name:
            # 分子量在200-500范围内较好
            if 200 <= value <= 500:
                return 0.8
            else:
                return max(0, 0.8 - abs(value - 350) / 500)

        elif 'TPSA' in name:
            # 极性表面积在40-120范围内较好
            if 40 <= value <= 120:
                return 0.8
            else:
                return max(0, 0.8 - abs(value - 80) / 100)

        elif any(keyword in name for keyword in ['HBD', 'HBA']):
            # 氢键数量适中
            if 0 <= value <= 5:
                return 0.7
            else:
                return max(0, 0.7 - abs(value - 2.5) / 10)

        else:
            return 0.5  # 默认中等相关性

    def _get_descriptor_meaning(self, name: str) -> str:
        """获取描述符的化学意义"""
        meanings = {
            'MW': '分子量，影响药物的吸收和分布',
            'LogP': '脂水分配系数，影响膜透过性',
            'TPSA': '拓扑极性表面积，影响生物膜透过性',
            'HBD': '氢键供体数，影响与靶点的结合',
            'HBA': '氢键受体数，影响分子间相互作用',
            'nRing': '环数，影响分子刚性和稳定性',
            'nAromRing': '芳香环数，影响π-π相互作用'
        }

        for key, meaning in meanings.items():
            if key in name:
                return meaning

        category = self._classify_descriptor(name)
        return self.descriptor_categories.get(category, {}).get('chemical_meaning', '未知化学意义')

    def _interpret_descriptor_value(self, key: str, value: float, meaning: str) -> Optional[Dict[str, Any]]:
        """解释具体描述符数值"""
        if np.isnan(value) or np.isinf(value):
            return None

        interpretations = {
            'MW': self._interpret_molecular_weight(value),
            'LogP': self._interpret_logp(value),
            'TPSA': self._interpret_tpsa(value),
            'nHBD': self._interpret_hbd(value),
            'nHBA': self._interpret_hba(value)
        }

        interpretation = interpretations.get(key, f'{meaning}: {value:.2f}')

        return {
            'descriptor': key,
            'value': float(value),
            'meaning': meaning,
            'interpretation': interpretation
        }

    def _interpret_molecular_weight(self, mw: float) -> str:
        """解释分子量"""
        if mw < 200:
            return f'分子量较小({mw:.1f} Da)，可能具有良好的膜透过性'
        elif mw <= 500:
            return f'分子量适中({mw:.1f} Da)，符合药物相似性要求'
        else:
            return f'分子量较大({mw:.1f} Da)，可能影响膜透过性'

    def _interpret_logp(self, logp: float) -> str:
        """解释脂水分配系数"""
        if logp < 0:
            return f'亲水性强(LogP={logp:.2f})，膜透过性可能较差'
        elif logp <= 3:
            return f'脂水平衡良好(LogP={logp:.2f})，有利于药物活性'
        elif logp <= 5:
            return f'脂溶性较高(LogP={logp:.2f})，需注意溶解性'
        else:
            return f'脂溶性过高(LogP={logp:.2f})，可能影响水溶性'

    def _interpret_tpsa(self, tpsa: float) -> str:
        """解释拓扑极性表面积"""
        if tpsa <= 60:
            return f'极性表面积较小({tpsa:.1f} Ų)，膜透过性好'
        elif tpsa <= 120:
            return f'极性表面积适中({tpsa:.1f} Ų)，平衡膜透过性和溶解性'
        else:
            return f'极性表面积较大({tpsa:.1f} Ų)，膜透过性可能受限'

    def _interpret_hbd(self, hbd: float) -> str:
        """解释氢键供体数"""
        if hbd <= 5:
            return f'氢键供体数适中({int(hbd)})，符合Lipinski规则'
        else:
            return f'氢键供体数过多({int(hbd)})，可能影响膜透过性'

    def _interpret_hba(self, hba: float) -> str:
        """解释氢键受体数"""
        if hba <= 10:
            return f'氢键受体数适中({int(hba)})，符合Lipinski规则'
        else:
            return f'氢键受体数过多({int(hba)})，可能影响膜透过性'

    def _analyze_molecular_complexity(self, values: np.ndarray, names: List[str]) -> Dict[str, Any]:
        """分析分子复杂性"""
        complexity_indicators = ['nRing', 'nBond', 'nAtom', 'Complexity']
        complexity_values = []

        for i, name in enumerate(names):
            if any(indicator in name for indicator in complexity_indicators):
                complexity_values.append(values[i])

        if complexity_values:
            avg_complexity = np.mean(complexity_values)
            if avg_complexity < 10:
                level = '简单'
            elif avg_complexity < 30:
                level = '中等'
            else:
                level = '复杂'

            return {
                'complexity_level': level,
                'average_complexity': float(avg_complexity),
                'interpretation': f'分子结构{level}，复杂度指标平均值为{avg_complexity:.1f}'
            }

        return {'complexity_level': '未知', 'interpretation': '无法评估分子复杂性'}

    def _analyze_drug_likeness_descriptors(self, values: np.ndarray, names: List[str]) -> Dict[str, Any]:
        """分析药物相似性相关描述符"""
        drug_like_descriptors = ['MW', 'LogP', 'HBD', 'HBA', 'TPSA', 'RotB']
        drug_like_values = {}

        for i, name in enumerate(names):
            for descriptor in drug_like_descriptors:
                if descriptor in name:
                    drug_like_values[descriptor] = values[i]
                    break

        violations = []
        if 'MW' in drug_like_values and drug_like_values['MW'] > 500:
            violations.append('分子量超标')
        if 'LogP' in drug_like_values and drug_like_values['LogP'] > 5:
            violations.append('脂溶性过高')
        if 'HBD' in drug_like_values and drug_like_values['HBD'] > 5:
            violations.append('氢键供体过多')
        if 'HBA' in drug_like_values and drug_like_values['HBA'] > 10:
            violations.append('氢键受体过多')

        drug_likeness_score = max(0, 1 - len(violations) / 4)

        return {
            'drug_like_values': {k: float(v) for k, v in drug_like_values.items()},
            'violations': violations,
            'drug_likeness_score': float(drug_likeness_score),
            'interpretation': f'药物相似性评分: {drug_likeness_score:.2f}'
        }

    def _calculate_activity_relevance(self, pattern: str, value: float, activity: float) -> float:
        """计算活性相关性分数"""
        # 基于经验的活性相关性评估
        relevance_rules = {
            'lipophilicity': lambda v: max(0, 1 - abs(v - 2.5) / 3),
            'molecular_size': lambda v: max(0, 1 - abs(v - 350) / 200) if 'MW' in str(v) else 0.5,
            'polarity': lambda v: max(0, 1 - abs(v - 80) / 60),
            'hydrogen_bonding': lambda v: max(0, 1 - abs(v - 3) / 5),
            'aromaticity': lambda v: min(1, v / 3) if v >= 0 else 0,
            'flexibility': lambda v: max(0, 1 - v / 10) if v >= 0 else 0
        }

        rule = relevance_rules.get(pattern, lambda v: 0.5)
        return rule(value)

    def _interpret_activity_pattern(self, pattern: str, value: float, activity: float) -> str:
        """解释活性模式"""
        interpretations = {
            'lipophilicity': f'脂溶性({value:.2f})对膜透过性的影响',
            'molecular_size': f'分子大小({value:.1f})对药物性质的影响',
            'polarity': f'极性({value:.1f})对溶解性和透过性的影响',
            'hydrogen_bonding': f'氢键能力({value:.1f})对靶点结合的影响',
            'aromaticity': f'芳香性({value:.1f})对分子稳定性的影响',
            'flexibility': f'分子柔性({value:.1f})对构象的影响'
        }

        return interpretations.get(pattern, f'{pattern}: {value:.2f}')
