# 神农框架实验计划文件夹

**作者**: ZK  
**邮箱**: <EMAIL>  
**创建日期**: 2025-06-30  
**版本**: v1.0

## 📁 文件夹结构

```
experiment_plans/
├── README.md                           # 本文件 - 总览
├── 01_framework_validation_plan.md     # 完整学术验证计划
├── 02_simplified_validation_plan.md    # 简化验证计划
├── 03_practical_action_plan.md         # 实用行动计划
├── 04_literature_analysis.md           # 文献分析
├── 05_experiment_timeline.md           # 详细时间线
├── 06_data_requirements.md             # 数据需求说明
├── 07_technical_specifications.md      # 技术规格说明
├── 08_evaluation_metrics.md            # 评估指标定义
├── 09_risk_mitigation.md               # 风险缓解策略
└── 10_success_criteria.md              # 成功标准定义
```

## 🎯 实验计划概述

### 核心目标
验证神农框架在抗菌化合物分类任务上相对于ChemProp和AutoGluon的优势，并证明其在药物筛选中的实用价值。

### 主要假设
**H1**: 双模态注意力融合 (图+Mordred+注意力) > 单模态方法  
**H2**: 神农框架在虚拟筛选任务中具有实用价值  
**H3**: 预计算特征策略提供显著的效率优势  

### 对比方法
1. **ChemProp**: 图神经网络基线 (D-MPNN)
2. **AutoGluon**: 自动ML + Mordred特征
3. **Shennong**: 图+Mordred+注意力融合 (我们的方法)

### 评估任务
- **主任务**: 抗菌化合物二分类 (活性/非活性)
- **应用验证**: 虚拟筛选模拟
- **效率测试**: 推理速度和内存使用

## 📋 实验计划选择指南

### 🔴 推荐方案: 简化验证计划
**文件**: `02_simplified_validation_plan.md` + `03_practical_action_plan.md`

**适用场景**:
- 时间有限 (4周内完成)
- 专注实用价值
- 主要与ChemProp/AutoGluon对比
- 面向药物筛选应用

**核心特点**:
- 3个对比组，3次独立运行
- 分类任务为主
- 虚拟筛选应用验证
- 4周完成时间线

### 🟡 完整方案: 学术验证计划
**文件**: `01_framework_validation_plan.md` + `04_literature_analysis.md`

**适用场景**:
- 追求学术严谨性
- 时间充裕 (6个月)
- 目标顶级期刊
- 需要全面的SOTA对比

**核心特点**:
- 7-8个SOTA方法对比
- 完整的消融实验
- 深度理论分析
- 多数据集验证

## ⏰ 推荐执行顺序

### Phase 1: 立即执行 (本周)
1. 阅读 `03_practical_action_plan.md`
2. 查看 `06_data_requirements.md`
3. 按照 `05_experiment_timeline.md` 开始数据准备

### Phase 2: 实验实施 (2-4周)
1. 参考 `07_technical_specifications.md` 进行实现
2. 使用 `08_evaluation_metrics.md` 定义的指标
3. 按照 `09_risk_mitigation.md` 处理问题

### Phase 3: 结果分析 (最后1周)
1. 对照 `10_success_criteria.md` 评估结果
2. 根据结果决定是否需要调整计划

## 🎯 快速开始

### 如果您想立即开始实验:
```bash
# 1. 阅读核心计划
cat 03_practical_action_plan.md

# 2. 检查数据需求  
cat 06_data_requirements.md

# 3. 开始第一周任务
# - 收集ChEMBL数据
# - 安装ChemProp和AutoGluon
# - 生成Mordred特征
```

### 如果您想深入了解理论基础:
```bash
# 1. 文献分析
cat 04_literature_analysis.md

# 2. 完整验证计划
cat 01_framework_validation_plan.md

# 3. 技术规格
cat 07_technical_specifications.md
```

## 📊 预期产出

### 实验结果
- 性能对比表格 (AUC, F1, Precision, Recall)
- 虚拟筛选富集曲线
- 统计显著性检验结果
- 效率对比分析

### 学术产出
- 实验报告 (技术文档)
- 论文初稿 (学术发表)
- 代码和数据 (开源发布)

### 实用产出
- 药物筛选工具
- 性能基准测试
- 最佳实践指南

## 🚨 重要提醒

### 实验前必读
1. **数据准备**: 确保有足够的抗菌活性数据
2. **环境配置**: 安装所有必要的依赖包
3. **计算资源**: 确认GPU/CPU资源充足
4. **时间规划**: 根据实际情况选择合适的计划

### 常见问题
1. **Q**: 应该选择哪个实验计划？
   **A**: 如果时间有限且专注应用，选择简化计划；如果追求学术严谨，选择完整计划。

2. **Q**: 实验失败怎么办？
   **A**: 参考 `09_risk_mitigation.md` 中的应对策略。

3. **Q**: 如何判断实验成功？
   **A**: 参考 `10_success_criteria.md` 中的成功标准。

## 📞 支持与更新

### 文档维护
- 实验过程中根据实际情况更新计划
- 记录遇到的问题和解决方案
- 保持文档的时效性和准确性

### 版本控制
- v1.0: 初始版本 (2025-06-30)
- 后续版本将根据实验进展更新

---

**开始实验前，请务必仔细阅读相关计划文档，确保理解实验目标和执行步骤。**

**🧬 神农尝百草，AI识良药 - 让实验更有条理！** ✨
