#!/usr/bin/env python3
# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-30
# 描述: 神农框架核心假设验证实验

"""
神农框架核心假设验证实验

验证核心假设: 双模态注意力融合 > 简单拼接 > 单模态

这是最关键的实验，将决定整个研究的学术价值。
"""

import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from scipy.stats import spearmanr, ttest_rel, wilcoxon
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Any
import json
from datetime import datetime

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ExperimentConfig:
    """实验配置类"""
    
    def __init__(self):
        # 实验基本设置
        self.n_runs = 5  # 独立运行次数
        self.random_seeds = [42, 123, 456, 789, 1024]
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 数据设置
        self.test_size = 0.2
        self.val_size = 0.1
        
        # 模型设置
        self.batch_size = 64
        self.epochs = 100
        self.learning_rate = 1e-3
        self.early_stopping_patience = 20
        
        # 评估指标
        self.metrics = ['r2', 'rmse', 'mae', 'spearman']
        
        # 统计分析
        self.significance_level = 0.05
        self.bonferroni_correction = True

class BaselineModels:
    """基准模型定义"""
    
    @staticmethod
    def create_gnn_only_model(graph_dim: int, output_dim: int = 1):
        """仅图神经网络模型"""
        return nn.Sequential(
            nn.Linear(graph_dim, 256),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(128, output_dim)
        )
    
    @staticmethod
    def create_mordred_only_model(mordred_dim: int, output_dim: int = 1):
        """仅Mordred描述符模型"""
        return nn.Sequential(
            nn.Linear(mordred_dim, 512),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Linear(128, output_dim)
        )
    
    @staticmethod
    def create_simple_concat_model(graph_dim: int, mordred_dim: int, output_dim: int = 1):
        """简单拼接模型"""
        total_dim = graph_dim + mordred_dim
        return nn.Sequential(
            nn.Linear(total_dim, 512),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Linear(128, output_dim)
        )

class FusionModels:
    """融合模型定义"""
    
    @staticmethod
    def create_weighted_fusion_model(graph_dim: int, mordred_dim: int, output_dim: int = 1):
        """加权融合模型"""
        class WeightedFusion(nn.Module):
            def __init__(self, graph_dim, mordred_dim, output_dim):
                super().__init__()
                self.graph_encoder = nn.Sequential(
                    nn.Linear(graph_dim, 128),
                    nn.ReLU(),
                    nn.Dropout(0.3)
                )
                self.mordred_encoder = nn.Sequential(
                    nn.Linear(mordred_dim, 128),
                    nn.ReLU(),
                    nn.Dropout(0.3)
                )
                # 可学习权重
                self.fusion_weights = nn.Parameter(torch.tensor([0.5, 0.5]))
                self.predictor = nn.Sequential(
                    nn.Linear(128, 64),
                    nn.ReLU(),
                    nn.Linear(64, output_dim)
                )
            
            def forward(self, graph_features, mordred_features):
                graph_encoded = self.graph_encoder(graph_features)
                mordred_encoded = self.mordred_encoder(mordred_features)
                
                # 加权融合
                weights = torch.softmax(self.fusion_weights, dim=0)
                fused = weights[0] * graph_encoded + weights[1] * mordred_encoded
                
                return self.predictor(fused)
        
        return WeightedFusion(graph_dim, mordred_dim, output_dim)
    
    @staticmethod
    def create_gate_fusion_model(graph_dim: int, mordred_dim: int, output_dim: int = 1):
        """门控融合模型"""
        class GateFusion(nn.Module):
            def __init__(self, graph_dim, mordred_dim, output_dim):
                super().__init__()
                self.graph_encoder = nn.Sequential(
                    nn.Linear(graph_dim, 128),
                    nn.ReLU(),
                    nn.Dropout(0.3)
                )
                self.mordred_encoder = nn.Sequential(
                    nn.Linear(mordred_dim, 128),
                    nn.ReLU(),
                    nn.Dropout(0.3)
                )
                # 门控机制
                self.gate = nn.Sequential(
                    nn.Linear(256, 128),
                    nn.Sigmoid()
                )
                self.predictor = nn.Sequential(
                    nn.Linear(128, 64),
                    nn.ReLU(),
                    nn.Linear(64, output_dim)
                )
            
            def forward(self, graph_features, mordred_features):
                graph_encoded = self.graph_encoder(graph_features)
                mordred_encoded = self.mordred_encoder(mordred_features)
                
                # 门控融合
                concat_features = torch.cat([graph_encoded, mordred_encoded], dim=1)
                gate_weights = self.gate(concat_features)
                
                gated_graph = gate_weights * graph_encoded
                gated_mordred = (1 - gate_weights) * mordred_encoded
                fused = gated_graph + gated_mordred
                
                return self.predictor(fused)
        
        return GateFusion(graph_dim, mordred_dim, output_dim)
    
    @staticmethod
    def create_cross_attention_model(graph_dim: int, mordred_dim: int, output_dim: int = 1):
        """交叉注意力模型"""
        class CrossAttention(nn.Module):
            def __init__(self, graph_dim, mordred_dim, output_dim):
                super().__init__()
                self.graph_encoder = nn.Sequential(
                    nn.Linear(graph_dim, 128),
                    nn.ReLU(),
                    nn.Dropout(0.3)
                )
                self.mordred_encoder = nn.Sequential(
                    nn.Linear(mordred_dim, 128),
                    nn.ReLU(),
                    nn.Dropout(0.3)
                )
                
                # 交叉注意力
                self.attention = nn.MultiheadAttention(
                    embed_dim=128, 
                    num_heads=8, 
                    batch_first=True
                )
                
                self.predictor = nn.Sequential(
                    nn.Linear(128, 64),
                    nn.ReLU(),
                    nn.Linear(64, output_dim)
                )
            
            def forward(self, graph_features, mordred_features):
                graph_encoded = self.graph_encoder(graph_features).unsqueeze(1)
                mordred_encoded = self.mordred_encoder(mordred_features).unsqueeze(1)
                
                # 交叉注意力 (graph attend to mordred)
                attended_features, _ = self.attention(
                    graph_encoded, mordred_encoded, mordred_encoded
                )
                
                return self.predictor(attended_features.squeeze(1))
        
        return CrossAttention(graph_dim, mordred_dim, output_dim)

class ExperimentRunner:
    """实验运行器"""
    
    def __init__(self, config: ExperimentConfig):
        self.config = config
        self.results = {}
        
    def evaluate_model(self, y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, float]:
        """评估模型性能"""
        metrics = {}
        
        # 回归指标
        metrics['r2'] = r2_score(y_true, y_pred)
        metrics['rmse'] = np.sqrt(mean_squared_error(y_true, y_pred))
        metrics['mae'] = mean_absolute_error(y_true, y_pred)
        
        # Spearman相关系数
        spearman_corr, _ = spearmanr(y_true, y_pred)
        metrics['spearman'] = spearman_corr
        
        return metrics
    
    def run_single_experiment(self, model, train_loader, val_loader, test_loader, 
                            model_name: str, seed: int) -> Dict[str, float]:
        """运行单次实验"""
        logger.info(f"运行 {model_name} (seed={seed})")
        
        # 设置随机种子
        torch.manual_seed(seed)
        np.random.seed(seed)
        
        # 训练模型
        model = model.to(self.config.device)
        optimizer = torch.optim.Adam(model.parameters(), lr=self.config.learning_rate)
        criterion = nn.MSELoss()
        
        best_val_loss = float('inf')
        patience_counter = 0
        
        for epoch in range(self.config.epochs):
            # 训练阶段
            model.train()
            train_loss = 0
            for batch in train_loader:
                optimizer.zero_grad()
                
                # 根据模型类型处理输入
                if model_name == 'GNN_only':
                    outputs = model(batch['graph_features'])
                elif model_name == 'Mordred_only':
                    outputs = model(batch['mordred_features'])
                elif model_name == 'Simple_concat':
                    concat_features = torch.cat([
                        batch['graph_features'], 
                        batch['mordred_features']
                    ], dim=1)
                    outputs = model(concat_features)
                else:  # 融合模型
                    outputs = model(batch['graph_features'], batch['mordred_features'])
                
                loss = criterion(outputs.squeeze(), batch['targets'])
                loss.backward()
                optimizer.step()
                train_loss += loss.item()
            
            # 验证阶段
            model.eval()
            val_loss = 0
            with torch.no_grad():
                for batch in val_loader:
                    if model_name == 'GNN_only':
                        outputs = model(batch['graph_features'])
                    elif model_name == 'Mordred_only':
                        outputs = model(batch['mordred_features'])
                    elif model_name == 'Simple_concat':
                        concat_features = torch.cat([
                            batch['graph_features'], 
                            batch['mordred_features']
                        ], dim=1)
                        outputs = model(concat_features)
                    else:
                        outputs = model(batch['graph_features'], batch['mordred_features'])
                    
                    loss = criterion(outputs.squeeze(), batch['targets'])
                    val_loss += loss.item()
            
            val_loss /= len(val_loader)
            
            # 早停检查
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
                # 保存最佳模型
                torch.save(model.state_dict(), f'best_model_{model_name}_{seed}.pth')
            else:
                patience_counter += 1
                if patience_counter >= self.config.early_stopping_patience:
                    break
        
        # 加载最佳模型进行测试
        model.load_state_dict(torch.load(f'best_model_{model_name}_{seed}.pth'))
        model.eval()
        
        # 测试阶段
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for batch in test_loader:
                if model_name == 'GNN_only':
                    outputs = model(batch['graph_features'])
                elif model_name == 'Mordred_only':
                    outputs = model(batch['mordred_features'])
                elif model_name == 'Simple_concat':
                    concat_features = torch.cat([
                        batch['graph_features'], 
                        batch['mordred_features']
                    ], dim=1)
                    outputs = model(concat_features)
                else:
                    outputs = model(batch['graph_features'], batch['mordred_features'])
                
                all_predictions.extend(outputs.squeeze().cpu().numpy())
                all_targets.extend(batch['targets'].cpu().numpy())
        
        # 评估性能
        metrics = self.evaluate_model(
            np.array(all_targets), 
            np.array(all_predictions)
        )
        
        logger.info(f"{model_name} (seed={seed}) - R²: {metrics['r2']:.4f}")
        
        return metrics
    
    def run_full_experiment(self, data_loaders: Dict) -> Dict[str, Any]:
        """运行完整实验"""
        logger.info("开始核心假设验证实验")
        
        # 定义所有模型
        models_to_test = {
            'GNN_only': lambda: BaselineModels.create_gnn_only_model(300),
            'Mordred_only': lambda: BaselineModels.create_mordred_only_model(1613),
            'Simple_concat': lambda: BaselineModels.create_simple_concat_model(300, 1613),
            'Weighted_fusion': lambda: FusionModels.create_weighted_fusion_model(300, 1613),
            'Gate_fusion': lambda: FusionModels.create_gate_fusion_model(300, 1613),
            'Cross_attention': lambda: FusionModels.create_cross_attention_model(300, 1613),
            # 'Our_method': lambda: ShennongFramework(config)  # 需要实现
        }
        
        # 存储所有结果
        all_results = {model_name: [] for model_name in models_to_test.keys()}
        
        # 运行多次实验
        for seed in self.config.random_seeds:
            for model_name, model_factory in models_to_test.items():
                model = model_factory()
                metrics = self.run_single_experiment(
                    model, 
                    data_loaders['train'], 
                    data_loaders['val'], 
                    data_loaders['test'],
                    model_name, 
                    seed
                )
                all_results[model_name].append(metrics)
        
        # 统计分析
        statistical_results = self.perform_statistical_analysis(all_results)
        
        return {
            'raw_results': all_results,
            'statistical_analysis': statistical_results,
            'experiment_config': self.config.__dict__
        }
    
    def perform_statistical_analysis(self, results: Dict[str, List[Dict]]) -> Dict[str, Any]:
        """执行统计分析"""
        logger.info("执行统计分析")
        
        statistical_results = {}
        
        # 计算每个模型的统计量
        for model_name, model_results in results.items():
            stats = {}
            for metric in self.config.metrics:
                values = [result[metric] for result in model_results]
                stats[metric] = {
                    'mean': np.mean(values),
                    'std': np.std(values),
                    'min': np.min(values),
                    'max': np.max(values),
                    'values': values
                }
            statistical_results[model_name] = stats
        
        # 配对t检验
        pairwise_tests = {}
        model_names = list(results.keys())
        
        for i, model1 in enumerate(model_names):
            for j, model2 in enumerate(model_names[i+1:], i+1):
                for metric in self.config.metrics:
                    values1 = [result[metric] for result in results[model1]]
                    values2 = [result[metric] for result in results[model2]]
                    
                    # 配对t检验
                    t_stat, p_value = ttest_rel(values1, values2)
                    
                    # Wilcoxon符号秩检验
                    w_stat, w_p_value = wilcoxon(values1, values2)
                    
                    test_key = f"{model1}_vs_{model2}_{metric}"
                    pairwise_tests[test_key] = {
                        't_test': {'statistic': t_stat, 'p_value': p_value},
                        'wilcoxon': {'statistic': w_stat, 'p_value': w_p_value},
                        'significant': p_value < self.config.significance_level
                    }
        
        statistical_results['pairwise_tests'] = pairwise_tests
        
        return statistical_results
    
    def save_results(self, results: Dict[str, Any], output_path: str):
        """保存实验结果"""
        output_path = Path(output_path)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 保存完整结果
        with open(output_path / 'experiment_results.json', 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        # 生成结果报告
        self.generate_report(results, output_path / 'experiment_report.md')
        
        logger.info(f"实验结果已保存到: {output_path}")
    
    def generate_report(self, results: Dict[str, Any], report_path: str):
        """生成实验报告"""
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# 神农框架核心假设验证实验报告\n\n")
            f.write(f"**实验日期**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # 实验配置
            f.write("## 实验配置\n\n")
            config = results['experiment_config']
            f.write(f"- 独立运行次数: {config['n_runs']}\n")
            f.write(f"- 随机种子: {config['random_seeds']}\n")
            f.write(f"- 显著性水平: {config['significance_level']}\n\n")
            
            # 性能结果
            f.write("## 性能结果\n\n")
            f.write("| 模型 | R² (均值±标准差) | RMSE | MAE | Spearman |\n")
            f.write("|------|------------------|------|-----|----------|\n")
            
            stats = results['statistical_analysis']
            for model_name in stats.keys():
                if model_name != 'pairwise_tests':
                    model_stats = stats[model_name]
                    f.write(f"| {model_name} |")
                    for metric in ['r2', 'rmse', 'mae', 'spearman']:
                        mean = model_stats[metric]['mean']
                        std = model_stats[metric]['std']
                        f.write(f" {mean:.4f}±{std:.4f} |")
                    f.write("\n")
            
            # 统计显著性
            f.write("\n## 统计显著性检验\n\n")
            pairwise_tests = stats['pairwise_tests']
            
            significant_comparisons = []
            for test_name, test_result in pairwise_tests.items():
                if test_result['significant']:
                    significant_comparisons.append(test_name)
            
            if significant_comparisons:
                f.write("### 显著差异 (p < 0.05):\n\n")
                for comparison in significant_comparisons:
                    f.write(f"- {comparison}\n")
            else:
                f.write("未发现显著差异。\n")
            
            f.write("\n## 结论\n\n")
            f.write("基于以上实验结果，我们可以得出以下结论：\n\n")
            f.write("1. [需要根据实际结果填写]\n")
            f.write("2. [需要根据实际结果填写]\n")
            f.write("3. [需要根据实际结果填写]\n")

def main():
    """主函数 - 实验入口"""
    print("🧬 神农框架核心假设验证实验")
    print("=" * 60)
    
    # 创建实验配置
    config = ExperimentConfig()
    
    # 创建实验运行器
    runner = ExperimentRunner(config)
    
    # 注意：这里需要实际的数据加载器
    # data_loaders = load_your_data()  # 需要实现
    
    print("⚠️  注意：需要先实现数据加载器才能运行实验")
    print("请参考 framework_validation_plan.md 中的详细实验设计")
    
    # 运行实验
    # results = runner.run_full_experiment(data_loaders)
    
    # 保存结果
    # runner.save_results(results, 'experiments/results/core_hypothesis')
    
    print("✅ 实验框架已准备就绪")

if __name__ == "__main__":
    main()
