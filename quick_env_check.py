#!/usr/bin/env python3
# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-30
# 描述: 神农框架环境快速检查脚本

"""
神农框架环境快速检查脚本

用于验证conda环境中的所有必要包是否正确安装。
"""

import sys
import importlib
import subprocess
from typing import List, Tuple, Dict

def get_package_version(package_name: str, import_name: str = None) -> Tuple[bool, str]:
    """
    获取包版本信息
    
    Args:
        package_name: 包名
        import_name: 导入名（如果与包名不同）
    
    Returns:
        (是否成功, 版本信息)
    """
    if import_name is None:
        import_name = package_name
    
    try:
        module = importlib.import_module(import_name)
        version = getattr(module, '__version__', 'unknown')
        return True, version
    except ImportError as e:
        return False, str(e)

def check_cuda_support() -> Dict[str, any]:
    """检查CUDA支持"""
    cuda_info = {
        'available': False,
        'version': 'N/A',
        'device_count': 0,
        'device_name': 'N/A'
    }
    
    try:
        import torch
        cuda_info['available'] = torch.cuda.is_available()
        if cuda_info['available']:
            cuda_info['version'] = torch.version.cuda
            cuda_info['device_count'] = torch.cuda.device_count()
            if cuda_info['device_count'] > 0:
                cuda_info['device_name'] = torch.cuda.get_device_name(0)
    except ImportError:
        pass
    
    return cuda_info

def test_rdkit_functionality() -> bool:
    """测试RDKit基本功能"""
    try:
        from rdkit import Chem
        from rdkit.Chem import Descriptors
        
        # 测试SMILES解析
        mol = Chem.MolFromSmiles('CCO')
        if mol is None:
            return False
        
        # 测试描述符计算
        mw = Descriptors.MolWt(mol)
        if mw <= 0:
            return False
        
        return True
    except Exception:
        return False

def test_mordred_functionality() -> bool:
    """测试Mordred基本功能"""
    try:
        from mordred import Calculator, descriptors
        from rdkit import Chem
        
        # 创建分子
        mol = Chem.MolFromSmiles('CCO')
        if mol is None:
            return False
        
        # 创建计算器
        calc = Calculator(descriptors, ignore_3D=True)
        
        # 计算描述符
        result = calc(mol)
        
        # 检查结果
        if len(result) == 0:
            return False
        
        return True
    except Exception:
        return False

def test_chemprop_functionality() -> bool:
    """测试ChemProp基本功能"""
    try:
        import chemprop
        # 简单的导入测试
        return True
    except Exception:
        return False

def test_autogluon_functionality() -> bool:
    """测试AutoGluon基本功能"""
    try:
        from autogluon.tabular import TabularPredictor
        # 简单的导入测试
        return True
    except Exception:
        return False

def main():
    """主函数"""
    print("🧬 神农框架环境快速检查")
    print("=" * 50)
    
    # Python版本信息
    python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
    print(f"🐍 Python版本: {python_version}")
    
    # 检查conda环境
    try:
        conda_env = subprocess.check_output(['conda', 'info', '--envs'], 
                                          universal_newlines=True)
        if 'shennong' in conda_env:
            print("✅ 检测到shennong conda环境")
        else:
            print("⚠️  未检测到shennong环境，请确认已激活正确环境")
    except:
        print("⚠️  无法检查conda环境")
    
    print("\n📦 核心包检查:")
    print("-" * 30)
    
    # 定义要检查的包
    core_packages = [
        ('torch', 'torch'),
        ('numpy', 'numpy'),
        ('pandas', 'pandas'),
        ('scikit-learn', 'sklearn'),
        ('matplotlib', 'matplotlib'),
        ('seaborn', 'seaborn'),
        ('jupyter', 'jupyter'),
        ('scipy', 'scipy'),
        ('statsmodels', 'statsmodels'),
        ('tqdm', 'tqdm')
    ]
    
    chemistry_packages = [
        ('rdkit', 'rdkit'),
        ('mordred', 'mordred'),
        ('chemprop', 'chemprop')
    ]
    
    ml_packages = [
        ('autogluon', 'autogluon'),
        ('torch-geometric', 'torch_geometric'),
        ('optuna', 'optuna'),
        ('shap', 'shap')
    ]
    
    # 检查核心包
    success_count = 0
    total_count = 0
    
    for package_name, import_name in core_packages:
        total_count += 1
        success, version = get_package_version(package_name, import_name)
        if success:
            print(f"✅ {package_name}: {version}")
            success_count += 1
        else:
            print(f"❌ {package_name}: 未安装或导入失败")
    
    print(f"\n🧪 化学信息学包检查:")
    print("-" * 30)
    
    for package_name, import_name in chemistry_packages:
        total_count += 1
        success, version = get_package_version(package_name, import_name)
        if success:
            print(f"✅ {package_name}: {version}")
            success_count += 1
        else:
            print(f"❌ {package_name}: 未安装或导入失败")
    
    print(f"\n🤖 机器学习包检查:")
    print("-" * 30)
    
    for package_name, import_name in ml_packages:
        total_count += 1
        success, version = get_package_version(package_name, import_name)
        if success:
            print(f"✅ {package_name}: {version}")
            success_count += 1
        else:
            print(f"❌ {package_name}: 未安装或导入失败")
    
    # CUDA支持检查
    print(f"\n🔥 CUDA支持检查:")
    print("-" * 30)
    
    cuda_info = check_cuda_support()
    if cuda_info['available']:
        print(f"✅ CUDA可用: {cuda_info['version']}")
        print(f"✅ GPU设备数: {cuda_info['device_count']}")
        print(f"✅ GPU名称: {cuda_info['device_name']}")
    else:
        print("❌ CUDA不可用")
        print("   这可能影响深度学习模型的训练速度")
    
    # 功能测试
    print(f"\n🧪 功能测试:")
    print("-" * 30)
    
    # RDKit功能测试
    if test_rdkit_functionality():
        print("✅ RDKit分子处理功能正常")
    else:
        print("❌ RDKit分子处理功能异常")
    
    # Mordred功能测试
    if test_mordred_functionality():
        print("✅ Mordred描述符计算功能正常")
    else:
        print("❌ Mordred描述符计算功能异常")
    
    # ChemProp功能测试
    if test_chemprop_functionality():
        print("✅ ChemProp导入正常")
    else:
        print("❌ ChemProp导入异常")
    
    # AutoGluon功能测试
    if test_autogluon_functionality():
        print("✅ AutoGluon导入正常")
    else:
        print("❌ AutoGluon导入异常")
    
    # 总结
    print(f"\n📊 检查总结:")
    print("=" * 50)
    print(f"包安装成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
    
    if success_count == total_count and cuda_info['available']:
        print("🎉 环境检查完全通过！神农框架开发环境已完美配置。")
        print("\n🚀 下一步:")
        print("   1. 开始数据准备: 参考 experiment_plans/06_data_requirements.md")
        print("   2. 运行实验: 参考 experiment_plans/03_practical_action_plan.md")
        print("   3. 生成特征: python scripts/generate_features.py --help")
    elif success_count >= total_count * 0.8:
        print("✅ 环境基本配置正确，可以开始实验。")
        if not cuda_info['available']:
            print("⚠️  CUDA不可用，建议检查GPU驱动和PyTorch安装。")
        print("\n🔧 建议:")
        print("   1. 重新安装失败的包")
        print("   2. 检查CUDA配置")
        print("   3. 参考 manual_setup_guide.md 进行故障排除")
    else:
        print("❌ 环境配置存在较多问题，建议重新安装。")
        print("\n🔧 解决方案:")
        print("   1. 运行 bash setup_environment.sh 自动安装")
        print("   2. 参考 manual_setup_guide.md 手动安装")
        print("   3. 检查网络连接和权限设置")
    
    print(f"\n🧬 神农尝百草，AI识良药 - 环境检查完成！")

if __name__ == "__main__":
    main()
