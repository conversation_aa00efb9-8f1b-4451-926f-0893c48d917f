{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# COCONUT预训练实验环境配置\n", "# 作者: ZK\n", "# 邮箱: <EMAIL>\n", "# 日期: 2025-01-15\n", "# 描述: COCONUT天然产物预训练实验的完整环境配置和依赖检查"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 实验目录结构创建"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "from pathlib import Path\n", "\n", "# 创建实验目录结构\n", "experiment_dirs = [\n", "    'experiments/coconut_pretraining',\n", "    'experiments/coconut_pretraining/data',\n", "    'experiments/coconut_pretraining/data/raw',\n", "    'experiments/coconut_pretraining/data/processed',\n", "    'experiments/coconut_pretraining/models',\n", "    'experiments/coconut_pretraining/models/pretrained',\n", "    'experiments/coconut_pretraining/models/finetuned',\n", "    'experiments/coconut_pretraining/results',\n", "    'experiments/coconut_pretraining/results/figures',\n", "    'experiments/coconut_pretraining/results/metrics',\n", "    'experiments/coconut_pretraining/logs',\n", "    'experiments/coconut_pretraining/configs',\n", "    'experiments/coconut_pretraining/exp-memory-bank'\n", "]\n", "\n", "for dir_path in experiment_dirs:\n", "    Path(dir_path).mkdir(parents=True, exist_ok=True)\n", "    print(f\"✓ 创建目录: {dir_path}\")\n", "\n", "print(\"\\n实验目录结构创建完成！\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 依赖包检查与安装"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 检查关键依赖包\n", "required_packages = {\n", "    'rdkit': 'rdkit-pypi>=2023.9.1',\n", "    'mordred': 'mordred>=1.2.0', \n", "    'torch': 'torch>=2.0.0',\n", "    'torch_geometric': 'torch-geometric>=2.4.0',\n", "    'sklearn': 'scikit-learn>=1.3.0',\n", "    'pandas': 'pandas>=2.0.0',\n", "    'numpy': 'numpy>=1.24.0',\n", "    'matplotlib': 'matplotlib>=3.7.0',\n", "    'seaborn': 'seaborn>=0.12.0',\n", "    'plotly': 'plotly>=5.15.0',\n", "    'tqdm': 'tqdm>=4.65.0',\n", "    'requests': 'requests>=2.31.0'\n", "}\n", "\n", "missing_packages = []\n", "installed_packages = []\n", "\n", "for package, version_spec in required_packages.items():\n", "    try:\n", "        if package == 'rdkit':\n", "            import rdkit\n", "            installed_packages.append(f\"✓ {package}: {rdkit.__version__}\")\n", "        elif package == 'torch_geometric':\n", "            import torch_geometric\n", "            installed_packages.append(f\"✓ {package}: {torch_geometric.__version__}\")\n", "        elif package == 'sklearn':\n", "            import sklearn\n", "            installed_packages.append(f\"✓ {package}: {sklearn.__version__}\")\n", "        else:\n", "            exec(f\"import {package}\")\n", "            version = eval(f\"{package}.__version__\")\n", "            installed_packages.append(f\"✓ {package}: {version}\")\n", "    except ImportError:\n", "        missing_packages.append(version_spec)\n", "\n", "print(\"已安装的包:\")\n", "for pkg in installed_packages:\n", "    print(pkg)\n", "\n", "if missing_packages:\n", "    print(\"\\n缺失的包:\")\n", "    for pkg in missing_packages:\n", "        print(f\"✗ {pkg}\")\n", "    print(\"\\n请运行以下命令安装缺失的包:\")\n", "    print(f\"pip install {' '.join(missing_packages)}\")\n", "else:\n", "    print(\"\\n✓ 所有依赖包已正确安装！\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. GPU环境检查"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "\n", "print(\"PyTorch版本:\", torch.__version__)\n", "print(\"CUDA可用:\", torch.cuda.is_available())\n", "\n", "if torch.cuda.is_available():\n", "    print(\"CUDA版本:\", torch.version.cuda)\n", "    print(\"GPU数量:\", torch.cuda.device_count())\n", "    for i in range(torch.cuda.device_count()):\n", "        gpu_name = torch.cuda.get_device_name(i)\n", "        gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3\n", "        print(f\"GPU {i}: {gpu_name} ({gpu_memory:.1f} GB)\")\n", "    \n", "    # 测试GPU内存\n", "    device = torch.device('cuda:0')\n", "    test_tensor = torch.randn(1000, 1000).to(device)\n", "    print(f\"✓ GPU测试成功，当前设备: {device}\")\n", "    del test_tensor\n", "    torch.cuda.empty_cache()\n", "else:\n", "    print(\"⚠️ 未检测到CUDA GPU，将使用CPU进行计算（速度较慢）\")\n", "    device = torch.device('cpu')\n", "\n", "print(f\"\\n推荐使用设备: {device}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 实验配置文件创建"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import yaml\n", "from datetime import datetime\n", "\n", "# 创建实验配置\n", "experiment_config = {\n", "    'experiment_info': {\n", "        'name': 'COCONUT_Pretraining_Experiment',\n", "        'version': '1.0',\n", "        'created_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),\n", "        'author': '<PERSON><PERSON>',\n", "        'email': '<EMAIL>',\n", "        'description': 'COCONUT天然产物数据库预训练实验'\n", "    },\n", "    'data_config': {\n", "        'coconut_url': 'https://coconut.naturalproducts.net/download/coconut_DB.zip',\n", "        'chembl_version': '33',\n", "        'target_organism': 'bacteria',\n", "        'activity_types': ['IC50', 'MIC', 'Ki', 'EC50'],\n", "        'activity_threshold': 10.0,  # μM\n", "        'train_val_test_split': [0.8, 0.1, 0.1],\n", "        'split_method': 'scaffold'\n", "    },\n", "    'model_config': {\n", "        'architecture': 'D-MPNN',\n", "        'hidden_dim': 300,\n", "        'num_layers': 3,\n", "        'dropout': 0.1,\n", "        'activation': 'ReLU',\n", "        'aggregation': 'mean'\n", "    },\n", "    'training_config': {\n", "        'pretraining': {\n", "            'batch_size': 64,\n", "            'learning_rate': 1e-4,\n", "            'max_epochs': 100,\n", "            'early_stopping_patience': 20,\n", "            'optimizer': '<PERSON>',\n", "            'scheduler': 'ReduceLROnPlateau'\n", "        },\n", "        'finetuning': {\n", "            'batch_size': 32,\n", "            'learning_rate': 1e-5,\n", "            'max_epochs': 50,\n", "            'early_stopping_patience': 10,\n", "            'optimizer': '<PERSON>',\n", "            'scheduler': 'ReduceLROnPlateau'\n", "        }\n", "    },\n", "    'evaluation_config': {\n", "        'metrics': ['RMSE', 'MAE', 'R2', 'PearsonR'],\n", "        'num_repeats': 5,\n", "        'random_seeds': [42, 123, 456, 789, 999],\n", "        'baselines': ['from_scratch', 'random_forest', 'original_chemeleon'],\n", "        'visualization': ['tsne', 'activity_cliff', 'novelty_analysis']\n", "    },\n", "    'computational_config': {\n", "        'device': str(device),\n", "        'num_workers': 4,\n", "        'pin_memory': True,\n", "        'mixed_precision': True\n", "    }\n", "}\n", "\n", "# 保存配置文件\n", "config_path = 'experiments/coconut_pretraining/configs/experiment_config.yaml'\n", "with open(config_path, 'w', encoding='utf-8') as f:\n", "    yaml.dump(experiment_config, f, default_flow_style=False, allow_unicode=True)\n", "\n", "print(f\"✓ 实验配置文件已保存: {config_path}\")\n", "print(\"\\n配置内容预览:\")\n", "print(yaml.dump(experiment_config, default_flow_style=False, allow_unicode=True)[:500] + \"...\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 实验记忆库初始化"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 创建实验专属记忆库文件\n", "memory_bank_files = {\n", "    'experiment_overview.md': '''# COCONUT预训练实验概览\n", "\n", "## 实验目标\n", "构建基于COCONUT天然产物数据库的分子预训练模型，用于抗菌活性预测。\n", "\n", "## 核心假设\n", "1. 天然产物特异性预训练能够学习到更好的分子表示\n", "2. 多模态融合（图结构+描述符）能够提升性能\n", "3. 严格的scaffold split评估能够真实反映泛化能力\n", "\n", "## 实验状态\n", "- [ ] 数据下载与预处理\n", "- [ ] 模型架构实现\n", "- [ ] 预训练阶段\n", "- [ ] 微调与评估\n", "- [ ] 结果分析与可视化\n", "''',\n", "    \n", "    'data_processing_log.md': '''# 数据处理日志\n", "\n", "## COCONUT数据处理\n", "- 数据来源: COCONUT数据库\n", "- 预期分子数量: ~400,000\n", "- 处理状态: 待开始\n", "\n", "## ChEMBL数据处理\n", "- 目标: 抗菌活性数据\n", "- 活性类型: IC50, MIC, Ki, EC50\n", "- 处理状态: 待开始\n", "''',\n", "    \n", "    'model_architecture.md': '''# 模型架构设计\n", "\n", "## 预训练模型\n", "- 基础架构: D-MPNN\n", "- 输入: 分子图结构\n", "- 输出: Mord<PERSON>描述符向量\n", "- 损失函数: MSE Loss\n", "\n", "## 微调模型\n", "- 预训练权重: COCONUT预训练模型\n", "- 预测头: MLP (1-2层)\n", "- 输出: pIC50回归值\n", "''',\n", "    \n", "    'experiment_results.md': '''# 实验结果记录\n", "\n", "## 预训练结果\n", "- 训练损失: 待记录\n", "- 验证损失: 待记录\n", "- 收敛情况: 待记录\n", "\n", "## 微调结果\n", "- 测试集RMSE: 待记录\n", "- 测试集R²: 待记录\n", "- 基线对比: 待记录\n", "'''\n", "}\n", "\n", "# 创建记忆库文件\n", "memory_bank_dir = 'experiments/coconut_pretraining/exp-memory-bank'\n", "for filename, content in memory_bank_files.items():\n", "    file_path = os.path.join(memory_bank_dir, filename)\n", "    with open(file_path, 'w', encoding='utf-8') as f:\n", "        f.write(content)\n", "    print(f\"✓ 创建记忆库文件: {filename}\")\n", "\n", "print(\"\\n✓ 实验记忆库初始化完成！\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 环境配置验证"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 最终环境验证\n", "print(\"=\" * 60)\n", "print(\"COCONUT预训练实验环境配置完成\")\n", "print(\"=\" * 60)\n", "\n", "print(\"\\n📁 实验目录结构:\")\n", "for root, dirs, files in os.walk('experiments/coconut_pretraining'):\n", "    level = root.replace('experiments/coconut_pretraining', '').count(os.sep)\n", "    indent = ' ' * 2 * level\n", "    print(f\"{indent}{os.path.basename(root)}/\")\n", "    subindent = ' ' * 2 * (level + 1)\n", "    for file in files[:3]:  # 只显示前3个文件\n", "        print(f\"{subindent}{file}\")\n", "    if len(files) > 3:\n", "        print(f\"{subindent}... (+{len(files)-3} more files)\")\n", "\n", "print(\"\\n🔧 系统配置:\")\n", "print(f\"- Python版本: {sys.version.split()[0]}\")\n", "print(f\"- PyTorch版本: {torch.__version__}\")\n", "print(f\"- 计算设备: {device}\")\n", "print(f\"- 工作目录: {os.getcwd()}\")\n", "\n", "print(\"\\n📋 下一步操作:\")\n", "print(\"1. 运行 01_data_collection.ipynb 进行数据收集\")\n", "print(\"2. 运行 02_data_preprocessing.ipynb 进行数据预处理\")\n", "print(\"3. 运行 03_model_pretraining.ipynb 进行模型预训练\")\n", "print(\"4. 运行 04_model_finetuning.ipynb 进行模型微调\")\n", "print(\"5. 运行 05_evaluation_analysis.ipynb 进行结果评估\")\n", "\n", "print(\"\\n✅ 环境配置验证完成，可以开始实验！\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}