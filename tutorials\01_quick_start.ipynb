{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🧬 神农框架快速开始教程\n", "\n", "**作者**: ZK  \n", "**邮箱**: <EMAIL>  \n", "**日期**: 2025-06-29\n", "\n", "欢迎使用神农框架！这是一个基于深度学习的抗菌化合物预测系统。\n", "\n", "## 📋 教程概述\n", "\n", "在这个快速开始教程中，您将学会：\n", "1. 安装和配置神农框架\n", "2. 准备示例数据\n", "3. 训练您的第一个模型\n", "4. 进行抗菌活性预测\n", "5. 分析结果和注意力权重\n", "\n", "预计完成时间：**15-20分钟**"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔧 环境检查和安装\n", "\n", "首先，让我们检查环境并安装必要的依赖。"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Python版本: 3.11.8 (tags/v3.11.8:db85d51, Feb  6 2024, 22:03:32) [MSC v.1937 64 bit (AMD64)]\n", "PyTorch版本: 2.7.1+cu118\n", "CUDA可用: True\n", "RDKit版本: 2025.03.3\n"]}], "source": ["# 检查Python版本\n", "import sys\n", "print(f\"Python版本: {sys.version}\")\n", "\n", "# 检查关键依赖\n", "try:\n", "    import torch\n", "    print(f\"PyTorch版本: {torch.__version__}\")\n", "    print(f\"CUDA可用: {torch.cuda.is_available()}\")\n", "except ImportError:\n", "    print(\"❌ PyTorch未安装\")\n", "\n", "try:\n", "    import rdkit\n", "    print(f\"RDKit版本: {rdkit.__version__}\")\n", "except ImportError:\n", "    print(\"❌ RDKit未安装\")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["E:\\新建文件夹\\Shennong\n"]}], "source": ["cd E:\\新建文件夹\\Shennong"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Obtaining file:///E:/%E6%96%B0%E5%BB%BA%E6%96%87%E4%BB%B6%E5%A4%B9/Shennong\n", "  Installing build dependencies: started\n", "  Installing build dependencies: finished with status 'done'\n", "  Checking if build backend supports build_editable: started\n", "  Checking if build backend supports build_editable: finished with status 'done'\n", "  Getting requirements to build editable: started\n", "  Getting requirements to build editable: finished with status 'error'\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  error: subprocess-exited-with-error\n", "  \n", "  × Getting requirements to build editable did not run successfully.\n", "  │ exit code: 1\n", "  ╰─> [1 lines of output]\n", "      error in shennong-framework setup command: 'extras_require' must be a dictionary whose values are strings or lists of strings containing valid project/version requirement specifiers.\n", "      [end of output]\n", "  \n", "  note: This error originates from a subprocess, and is likely not a problem with pip.\n", "error: subprocess-exited-with-error\n", "\n", "× Getting requirements to build editable did not run successfully.\n", "│ exit code: 1\n", "╰─> See above for output.\n", "\n", "note: This error originates from a subprocess, and is likely not a problem with pip.\n"]}], "source": ["!pip install -e ."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["  error: subprocess-exited-with-error\n", "  \n", "  × Getting requirements to build editable did not run successfully.\n", "  │ exit code: 1\n", "  ╰─> [1 lines of output]\n", "      error in shennong-framework setup command: 'extras_require' must be a dictionary whose values are strings or lists of strings containing valid project/version requirement specifiers.\n", "      [end of output]\n", "  \n", "  note: This error originates from a subprocess, and is likely not a problem with pip.\n", "error: subprocess-exited-with-error\n", "\n", "× Getting requirements to build editable did not run successfully.\n", "│ exit code: 1\n", "╰─> See above for output.\n", "\n", "note: This error originates from a subprocess, and is likely not a problem with pip.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Obtaining file:///E:/%E6%96%B0%E5%BB%BA%E6%96%87%E4%BB%B6%E5%A4%B9/Shennong\n", "  Installing build dependencies: started\n", "  Installing build dependencies: finished with status 'done'\n", "  Checking if build backend supports build_editable: started\n", "  Checking if build backend supports build_editable: finished with status 'done'\n", "  Getting requirements to build editable: started\n", "  Getting requirements to build editable: finished with status 'error'\n"]}], "source": ["# 安装神农框架（如果尚未安装）\n", "!pip install -e .\n", "\n", "# 导入神农框架\n", "try:\n", "    import shennong\n", "    print(f\"✅ 神农框架版本: {shennong.__version__}\")\n", "except ImportError as e:\n", "    print(f\"❌ 神农框架导入失败: {e}\")\n", "    print(\"请确保已正确安装神农框架\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📊 创建示例数据\n", "\n", "让我们创建一些示例抗菌化合物数据用于演示。"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["d:\\python\\Lib\\site-packages\\tqdm\\auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}, {"ename": "NameError", "evalue": "name 'logger' is not defined", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[1]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01ms<PERSON><PERSON>g\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mdata\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mloaders\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m create_sample_data\n\u001b[32m      2\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpanda<PERSON>\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpd\u001b[39;00m\n\u001b[32m      3\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mp<PERSON><PERSON>b\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m Path\n", "\u001b[36mFile \u001b[39m\u001b[32mE:\\新建文件夹\\Shennong\\shennong\\__init__.py:42\u001b[39m\n\u001b[32m     39\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m.\u001b[39;00m\u001b[34;01mantibacterial\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01minterpretation\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m BiologicalInterpreter\n\u001b[32m     41\u001b[39m \u001b[38;5;66;03m# CLI模块\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m42\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m.\u001b[39;00m\u001b[34;01mcli\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mmain\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m main \u001b[38;5;28;01mas\u001b[39;00m cli_main\n\u001b[32m     44\u001b[39m \u001b[38;5;66;03m# 版本兼容性检查\u001b[39;00m\n\u001b[32m     45\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mcheck_dependencies\u001b[39m():\n", "\u001b[36mFile \u001b[39m\u001b[32mE:\\新建文件夹\\Shennong\\shennong\\cli\\__init__.py:19\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;66;03m# 作者: Z<PERSON>\u001b[39;00m\n\u001b[32m      2\u001b[39m \u001b[38;5;66;03m# 邮箱: <EMAIL>\u001b[39;00m\n\u001b[32m      3\u001b[39m \u001b[38;5;66;03m# 日期: 2025-06-29\u001b[39;00m\n\u001b[32m      4\u001b[39m \u001b[38;5;66;03m# 描述: 神农框架CLI模块初始化\u001b[39;00m\n\u001b[32m      6\u001b[39m \u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m      7\u001b[39m \u001b[33;03m神农框架命令行接口模块\u001b[39;00m\n\u001b[32m      8\u001b[39m \n\u001b[32m   (...)\u001b[39m\u001b[32m     16\u001b[39m \u001b[33;03m- shennong fingerprint: 提取分子指纹\u001b[39;00m\n\u001b[32m     17\u001b[39m \u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m19\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m.\u001b[39;00m\u001b[34;01mmain\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m main, construct_parser\n\u001b[32m     21\u001b[39m __all__ = [\n\u001b[32m     22\u001b[39m     \u001b[33m'\u001b[39m\u001b[33mmain\u001b[39m\u001b[33m'\u001b[39m,\n\u001b[32m     23\u001b[39m     \u001b[33m'\u001b[39m\u001b[33mconstruct_parser\u001b[39m\u001b[33m'\u001b[39m\n\u001b[32m     24\u001b[39m ]\n\u001b[32m     26\u001b[39m __version__ = \u001b[33m\"\u001b[39m\u001b[33m1.0.0\u001b[39m\u001b[33m\"\u001b[39m\n", "\u001b[36mFile \u001b[39m\u001b[32mE:\\新建文件夹\\Shennong\\shennong\\cli\\main.py:23\u001b[39m\n\u001b[32m     20\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mImportError\u001b[39;00m:\n\u001b[32m     21\u001b[39m     \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mar<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m ArgumentParser\n\u001b[32m---> \u001b[39m\u001b[32m23\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m.\u001b[39;00m\u001b[34;01mtrain\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m TrainSubcommand\n\u001b[32m     24\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m.\u001b[39;00m\u001b[34;01mpredict\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m PredictSubcommand\n\u001b[32m     25\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m.\u001b[39;00m\u001b[34;01mevaluate\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m EvaluateSubcommand\n", "\u001b[36mFile \u001b[39m\u001b[32mE:\\新建文件夹\\Shennong\\shennong\\cli\\train.py:24\u001b[39m\n\u001b[32m     22\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m.\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mtraining\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mtrainer\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m ShennongTrainer\n\u001b[32m     23\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m.\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mdata\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mdatasets\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m AntibacterialDataset\n\u001b[32m---> \u001b[39m\u001b[32m24\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m.\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mdata\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mloaders\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m load_csv_data\n\u001b[32m     25\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m.\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mutils\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mconfig\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m ShennongConfig\n\u001b[32m     27\u001b[39m logger = logging.getLogger(\u001b[34m__name__\u001b[39m)\n", "\u001b[36mFile \u001b[39m\u001b[32mE:\\新建文件夹\\Shennong\\shennong\\data\\loaders.py:20\u001b[39m\n\u001b[32m     18\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m.\u001b[39;00m\u001b[34;01mdatapoints\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m ShennongDatapoint\n\u001b[32m     19\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m.\u001b[39;00m\u001b[34;01mdatasets\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m AntibacterialDataset, ShennongDataset\n\u001b[32m---> \u001b[39m\u001b[32m20\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m.\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mfeaturizers\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mmolecule\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m MordredFeaturizer\n\u001b[32m     22\u001b[39m logger = logging.getLogger(\u001b[34m__name__\u001b[39m)\n\u001b[32m     25\u001b[39m \u001b[38;5;28;01mclass\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mCSVDataLoader\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32mE:\\新建文件夹\\Shennong\\shennong\\featurizers\\__init__.py:33\u001b[39m\n\u001b[32m     30\u001b[39m     AntibacterialBondFeaturizer = \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m     31\u001b[39m     EnhancedBondFeaturizer = \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m33\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m.\u001b[39;00m\u001b[34;01mmolecule\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mi<PERSON>rt\u001b[39;00m <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MordredDescriptorManager\n\u001b[32m     35\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m     36\u001b[39m     \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m.\u001b[39;00m\u001b[34;01madaptive_descriptors\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m AdaptiveDescriptorProcessor\n", "\u001b[36mFile \u001b[39m\u001b[32mE:\\新建文件夹\\Shennong\\shennong\\featurizers\\molecule.py:33\u001b[39m\n\u001b[32m     31\u001b[39m     MORDRED_AVAILABLE = \u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[32m     32\u001b[39m     MORDRED_VERSION = \u001b[38;5;28mgetattr\u001b[39m(mordred, \u001b[33m'\u001b[39m\u001b[33m__version__\u001b[39m\u001b[33m'\u001b[39m, \u001b[33m'\u001b[39m\u001b[33munknown\u001b[39m\u001b[33m'\u001b[39m)\n\u001b[32m---> \u001b[39m\u001b[32m33\u001b[39m     \u001b[43mlogger\u001b[49m.info(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mMordred库已加载，版本: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mMORDRED_VERSION\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n\u001b[32m     34\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mImportError\u001b[39;00m:\n\u001b[32m     35\u001b[39m     MORDRED_AVAILABLE = \u001b[38;5;28;01mFalse\u001b[39;00m\n", "\u001b[31mNameError\u001b[39m: name 'logger' is not defined"]}], "source": ["from shennong.data.loaders import create_sample_data\n", "import pandas as pd\n", "from pathlib import Path\n", "\n", "# 创建示例数据\n", "data_dir = Path(\"data\")\n", "data_dir.mkdir(exist_ok=True)\n", "\n", "sample_file = create_sample_data(\n", "    output_path=data_dir / \"antibacterial_sample.csv\",\n", "    num_samples=50\n", ")\n", "\n", "# 查看数据\n", "df = pd.read_csv(sample_file)\n", "print(f\"✅ 创建了包含 {len(df)} 个样本的数据集\")\n", "print(\"\\n前5行数据:\")\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 数据统计\n", "print(\"📊 数据集统计:\")\n", "print(f\"总样本数: {len(df)}\")\n", "print(f\"活性值范围: {df['activity'].min():.2f} - {df['activity'].max():.2f}\")\n", "print(f\"平均活性: {df['activity'].mean():.2f}\")\n", "\n", "print(\"\\n抗菌机制分布:\")\n", "mechanism_counts = df['mechanism'].value_counts()\n", "for mechanism, count in mechanism_counts.items():\n", "    print(f\"  {mechanism}: {count} 个样本\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🚀 使用CLI进行快速训练\n", "\n", "神农框架提供了便捷的命令行接口。让我们使用CLI来训练一个模型。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 使用CLI训练模型\n", "import subprocess\n", "import os\n", "\n", "# 设置输出目录\n", "output_dir = Path(\"models/quick_start\")\n", "output_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "# 构建训练命令\n", "train_cmd = [\n", "    \"shennong\", \"train\",\n", "    \"--data-path\", str(sample_file),\n", "    \"--target-columns\", \"activity\",\n", "    \"--output-dir\", str(output_dir),\n", "    \"--epochs\", \"10\",\n", "    \"--batch-size\", \"8\",\n", "    \"--verbose\"\n", "]\n", "\n", "print(\"🚀 开始训练模型...\")\n", "print(f\"命令: {' '.join(train_cmd)}\")\n", "\n", "# 注意：在实际环境中运行\n", "# result = subprocess.run(train_cmd, capture_output=True, text=True)\n", "# print(result.stdout)\n", "# if result.stderr:\n", "#     print(\"错误信息:\", result.stderr)\n", "\n", "print(\"⚠️ 请在终端中运行上述命令进行实际训练\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔮 使用Python API进行预测\n", "\n", "让我们使用Python API来演示预测功能。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from shennong import ShennongFramework\n", "from shennong.data import AntibacterialDataset\n", "from shennong.data.loaders import CSVDataLoader\n", "\n", "# 创建一个简单的演示模型配置\n", "demo_config = {\n", "    'model': {\n", "        'hidden_size': 128,\n", "        'num_layers': 2,\n", "        'dropout': 0.1\n", "    },\n", "    'training': {\n", "        'learning_rate': 1e-3,\n", "        'batch_size': 8\n", "    }\n", "}\n", "\n", "# 创建模型实例\n", "print(\"🧬 创建神农框架模型...\")\n", "model = ShennongFramework(demo_config)\n", "print(\"✅ 模型创建成功\")\n", "\n", "# 显示模型摘要\n", "print(\"\\n📋 模型摘要:\")\n", "print(model.summary())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 演示预测功能（使用随机权重）\n", "test_smiles = [\n", "    \"CCN(CC)CCCC(C)NC1=C2C=CC(=CC2=NC=C1)CF3\",  # 氯喹类似物\n", "    \"CC(C)CC1=CC=C(C=C1)C(C)C(=O)O\",           # 布洛芬\n", "    \"CN1CCN(CC1)C2=C(C=C3C(=C2F)N(C=C(C3=O)C(=O)O)C4CC4)F\"  # 环丙沙星\n", "]\n", "\n", "print(\"🔮 演示预测功能...\")\n", "print(f\"测试分子数量: {len(test_smiles)}\")\n", "\n", "try:\n", "    # 注意：这里使用的是未训练的模型，结果仅用于演示\n", "    predictions = model.predict(\n", "        test_smiles,\n", "        return_attention=True,\n", "        return_mechanism=True\n", "    )\n", "    \n", "    print(\"✅ 预测完成\")\n", "    print(f\"预测结果形状: {predictions['activity'].shape if 'activity' in predictions else 'N/A'}\")\n", "    \n", "except Exception as e:\n", "    print(f\"⚠️ 预测演示跳过: {e}\")\n", "    print(\"这是正常的，因为模型尚未训练\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📈 数据可视化\n", "\n", "让我们可视化示例数据的一些特征。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# 设置绘图风格\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "# 创建图表\n", "fig, axes = plt.subplots(1, 2, figsize=(12, 5))\n", "\n", "# 活性分布直方图\n", "axes[0].hist(df['activity'], bins=15, alpha=0.7, color='skyblue', edgecolor='black')\n", "axes[0].set_xlabel('抗菌活性 (MIC, μg/mL)')\n", "axes[0].set_ylabel('频次')\n", "axes[0].set_title('抗菌活性分布')\n", "axes[0].grid(True, alpha=0.3)\n", "\n", "# 机制分布饼图\n", "mechanism_counts = df['mechanism'].value_counts()\n", "axes[1].pie(mechanism_counts.values, labels=mechanism_counts.index, autopct='%1.1f%%')\n", "axes[1].set_title('抗菌机制分布')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"📊 数据可视化完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🧪 分子特征分析\n", "\n", "让我们分析一些分子的基本特征。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from rdkit import Chem\n", "from rdkit.Chem import Descriptors, Draw\n", "from IPython.display import display\n", "\n", "# 分析前几个分子的特征\n", "print(\"🧪 分子特征分析:\")\n", "print(\"=\" * 60)\n", "\n", "for i, row in df.head(3).iterrows():\n", "    smiles = row['smiles']\n", "    activity = row['activity']\n", "    mechanism = row['mechanism']\n", "    \n", "    mol = Chem.Mo<PERSON>rom<PERSON>(smiles)\n", "    if mol is not None:\n", "        # 计算分子描述符\n", "        mw = Descriptors.MolWt(mol)\n", "        logp = Descriptors.MolLogP(mol)\n", "        hbd = Descriptors.NumHDonors(mol)\n", "        hba = Descriptors.NumHAcceptors(mol)\n", "        \n", "        print(f\"\\n分子 {i+1}:\")\n", "        print(f\"  SMILES: {smiles}\")\n", "        print(f\"  活性: {activity:.2f} μg/mL\")\n", "        print(f\"  机制: {mechanism}\")\n", "        print(f\"  分子量: {mw:.1f} Da\")\n", "        print(f\"  LogP: {logp:.2f}\")\n", "        print(f\"  氢键供体: {hbd}\")\n", "        print(f\"  氢键受体: {hba}\")\n", "        \n", "        # 显示分子结构（如果在Jupyter中）\n", "        try:\n", "            img = Draw.MolToImage(mol, size=(200, 200))\n", "            display(img)\n", "        except:\n", "            print(\"  (分子结构显示需要在Jupyter环境中)\")\n", "\n", "print(\"\\n✅ 分子特征分析完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🎯 下一步\n", "\n", "恭喜！您已经完成了神农框架的快速开始教程。\n", "\n", "### 您学到了什么：\n", "- ✅ 如何安装和配置神农框架\n", "- ✅ 如何准备和加载数据\n", "- ✅ 如何使用CLI进行模型训练\n", "- ✅ 如何使用Python API进行预测\n", "- ✅ 如何进行基本的数据分析和可视化\n", "\n", "### 推荐的下一步教程：\n", "1. **02_data_preparation.ipynb** - 深入学习数据准备和预处理\n", "2. **03_model_training.ipynb** - 详细的模型训练和调优\n", "3. **04_prediction_evaluation.ipynb** - 预测和模型评估\n", "4. **05_attention_analysis.ipynb** - 注意力机制分析\n", "5. **06_mechanism_interpretation.ipynb** - 抗菌机制解释\n", "\n", "### 获取帮助：\n", "- 📖 查看完整文档\n", "- 💬 在GitHub上提出问题\n", "- 📧 联系作者：<EMAIL>\n", "\n", "**神农尝百草，AI识良药** 🌿"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 4}