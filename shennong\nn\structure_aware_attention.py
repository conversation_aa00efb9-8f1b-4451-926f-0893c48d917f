# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-27
# 描述: 基于化学结构的注意力机制

"""
基于化学结构的注意力机制

完全基于分子结构特征的注意力机制，无需外部生物学标签。
通过化学直觉和结构模式学习重要性权重。
"""

from typing import Dict, List, Any, Optional, Tuple, Union
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import logging
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)

try:
    from rdkit import Chem
    from rdkit.Chem import rdMolDescriptors, Descriptors
    RDKIT_AVAILABLE = True
except ImportError:
    RDKIT_AVAILABLE = False


class StructureAwareAttentionBase(nn.Module, ABC):
    """
    基于结构的注意力机制基类

    定义了所有结构感知注意力机制的通用接口。
    """

    def __init__(self, feature_dim: int, dropout: float = 0.1):
        super().__init__()
        self.feature_dim = feature_dim
        self.dropout = nn.Dropout(dropout)

    @abstractmethod
    def compute_structure_weights(
        self,
        features: torch.Tensor,
        structure_info: Optional[Dict[str, Any]] = None
    ) -> torch.Tensor:
        """计算基于结构的注意力权重"""
        pass

    def forward(
        self,
        features: torch.Tensor,
        structure_info: Optional[Dict[str, Any]] = None,
        return_attention: bool = False
    ) -> Tuple[torch.Tensor, Optional[torch.Tensor]]:
        """前向传播"""
        # 计算结构感知权重
        attention_weights = self.compute_structure_weights(features, structure_info)

        # 应用注意力
        attended_features = features * attention_weights.unsqueeze(-1)
        attended_features = self.dropout(attended_features)

        if return_attention:
            return attended_features, attention_weights
        return attended_features, None


class PharmacophoreAttention(StructureAwareAttentionBase):
    """
    基于药效团的注意力机制

    根据分子中的药效团特征分配注意力权重，
    重点关注与抗菌活性相关的化学基团。
    """

    def __init__(
        self,
        feature_dim: int,
        pharmacophore_dim: int = 32,
        dropout: float = 0.1
    ):
        super().__init__(feature_dim, dropout)

        self.pharmacophore_dim = pharmacophore_dim

        # 药效团特征提取器
        self.pharmacophore_extractor = nn.Sequential(
            nn.Linear(feature_dim, pharmacophore_dim * 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(pharmacophore_dim * 2, pharmacophore_dim)
        )

        # 药效团重要性评估器
        self.importance_scorer = nn.Sequential(
            nn.Linear(pharmacophore_dim, pharmacophore_dim // 2),
            nn.ReLU(),
            nn.Linear(pharmacophore_dim // 2, 1),
            nn.Sigmoid()
        )

        # 预定义的抗菌药效团模式
        self.register_buffer('antibacterial_patterns', self._init_antibacterial_patterns())

        logger.info(f"初始化药效团注意力: 特征维度={feature_dim}, 药效团维度={pharmacophore_dim}")

    def _init_antibacterial_patterns(self) -> torch.Tensor:
        """初始化抗菌药效团模式"""
        # 基于已知抗菌药物的药效团模式
        patterns = torch.randn(8, self.pharmacophore_dim)  # 8种常见抗菌药效团
        return F.normalize(patterns, dim=-1)

    def compute_structure_weights(
        self,
        features: torch.Tensor,
        structure_info: Optional[Dict[str, Any]] = None
    ) -> torch.Tensor:
        """
        计算基于药效团的注意力权重

        Args:
            features: 输入特征 [batch_size, seq_len, feature_dim]
            structure_info: 结构信息（可选）

        Returns:
            注意力权重 [batch_size, seq_len]
        """
        batch_size, seq_len, _ = features.shape

        # 提取药效团特征
        pharmacophore_features = self.pharmacophore_extractor(features)  # [B, L, P]

        # 计算与已知抗菌药效团的相似性
        # pharmacophore_features: [B, L, P], antibacterial_patterns: [8, P]
        similarity = torch.matmul(
            F.normalize(pharmacophore_features, dim=-1),
            self.antibacterial_patterns.t()
        )  # [B, L, 8]

        # 取最大相似性作为药效团匹配度
        max_similarity, _ = similarity.max(dim=-1)  # [B, L]

        # 计算重要性分数
        importance_scores = self.importance_scorer(pharmacophore_features).squeeze(-1)  # [B, L]

        # 结合相似性和重要性
        attention_weights = max_similarity * importance_scores

        # 归一化
        attention_weights = F.softmax(attention_weights, dim=-1)

        return attention_weights


class ScaffoldSimilarityAttention(StructureAwareAttentionBase):
    """
    基于分子骨架相似性的注意力机制

    根据分子骨架与已知抗菌化合物骨架的相似性分配注意力权重。
    """

    def __init__(
        self,
        feature_dim: int,
        scaffold_dim: int = 64,
        num_reference_scaffolds: int = 16,
        dropout: float = 0.1
    ):
        super().__init__(feature_dim, dropout)

        self.scaffold_dim = scaffold_dim
        self.num_reference_scaffolds = num_reference_scaffolds

        # 骨架特征编码器
        self.scaffold_encoder = nn.Sequential(
            nn.Linear(feature_dim, scaffold_dim * 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(scaffold_dim * 2, scaffold_dim),
            nn.Tanh()
        )

        # 参考骨架嵌入（可学习的抗菌骨架原型）
        self.reference_scaffolds = nn.Parameter(
            torch.randn(num_reference_scaffolds, scaffold_dim)
        )

        # 相似性权重计算器
        self.similarity_projector = nn.Sequential(
            nn.Linear(scaffold_dim, scaffold_dim // 2),
            nn.ReLU(),
            nn.Linear(scaffold_dim // 2, 1)
        )

        logger.info(f"初始化骨架相似性注意力: 参考骨架数={num_reference_scaffolds}")

    def compute_structure_weights(
        self,
        features: torch.Tensor,
        structure_info: Optional[Dict[str, Any]] = None
    ) -> torch.Tensor:
        """
        计算基于骨架相似性的注意力权重

        Args:
            features: 输入特征 [batch_size, seq_len, feature_dim]
            structure_info: 结构信息（可选）

        Returns:
            注意力权重 [batch_size, seq_len]
        """
        batch_size, seq_len, _ = features.shape

        # 编码骨架特征
        scaffold_features = self.scaffold_encoder(features)  # [B, L, S]

        # 计算与参考骨架的相似性
        # scaffold_features: [B, L, S], reference_scaffolds: [R, S]
        similarities = torch.matmul(
            scaffold_features,
            self.reference_scaffolds.t()
        )  # [B, L, R]

        # 使用注意力机制聚合相似性
        similarity_weights = F.softmax(similarities, dim=-1)  # [B, L, R]
        aggregated_similarity = torch.sum(
            similarity_weights * similarities, dim=-1
        )  # [B, L]

        # 通过投影器计算最终权重
        final_weights = self.similarity_projector(scaffold_features).squeeze(-1)  # [B, L]

        # 结合相似性和投影权重
        attention_weights = torch.sigmoid(aggregated_similarity + final_weights)

        # 归一化
        attention_weights = F.softmax(attention_weights, dim=-1)

        return attention_weights


class AtomImportanceAttention(StructureAwareAttentionBase):
    """
    基于原子重要性的局部注意力机制

    根据原子的化学环境和连接性评估其对抗菌活性的重要性。
    """

    def __init__(
        self,
        feature_dim: int,
        atom_dim: int = 32,
        bond_dim: int = 16,
        dropout: float = 0.1
    ):
        super().__init__(feature_dim, dropout)

        self.atom_dim = atom_dim
        self.bond_dim = bond_dim

        # 原子特征提取器
        self.atom_extractor = nn.Sequential(
            nn.Linear(feature_dim, atom_dim * 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(atom_dim * 2, atom_dim)
        )

        # 键特征提取器
        self.bond_extractor = nn.Sequential(
            nn.Linear(feature_dim, bond_dim * 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(bond_dim * 2, bond_dim)
        )

        # 原子重要性评估器
        self.atom_importance = nn.Sequential(
            nn.Linear(atom_dim + bond_dim, atom_dim),
            nn.ReLU(),
            nn.Linear(atom_dim, 1),
            nn.Sigmoid()
        )

        # 抗菌相关原子类型权重
        self.register_buffer('atom_type_weights', self._init_atom_type_weights())

        logger.info(f"初始化原子重要性注意力: 原子维度={atom_dim}, 键维度={bond_dim}")

    def _init_atom_type_weights(self) -> torch.Tensor:
        """初始化原子类型权重"""
        # 基于化学直觉的原子重要性权重
        # 顺序: C, N, O, S, P, F, Cl, Br, I, 其他
        weights = torch.tensor([
            1.0,  # C: 基础骨架
            1.5,  # N: 氢键供体/受体，正电荷
            1.3,  # O: 氢键受体，极性
            1.4,  # S: 疏水相互作用，可氧化
            1.6,  # P: 磷酸基团，负电荷
            1.2,  # F: 强电负性，代谢稳定
            1.3,  # Cl: 疏水，适中大小
            1.1,  # Br: 疏水，较大
            1.0,  # I: 疏水，很大
            0.8   # 其他: 较低重要性
        ])
        return weights

    def compute_structure_weights(
        self,
        features: torch.Tensor,
        structure_info: Optional[Dict[str, Any]] = None
    ) -> torch.Tensor:
        """
        计算基于原子重要性的注意力权重

        Args:
            features: 输入特征 [batch_size, seq_len, feature_dim]
            structure_info: 结构信息，包含原子类型等

        Returns:
            注意力权重 [batch_size, seq_len]
        """
        batch_size, seq_len, _ = features.shape

        # 提取原子和键特征
        atom_features = self.atom_extractor(features)  # [B, L, A]
        bond_features = self.bond_extractor(features)  # [B, L, Bo]

        # 结合原子和键特征
        combined_features = torch.cat([atom_features, bond_features], dim=-1)  # [B, L, A+Bo]

        # 计算原子重要性
        importance_scores = self.atom_importance(combined_features).squeeze(-1)  # [B, L]

        # 如果有原子类型信息，应用先验权重
        if structure_info and 'atom_types' in structure_info:
            atom_types = structure_info['atom_types']  # [B, L]
            type_weights = self.atom_type_weights[atom_types]  # [B, L]
            importance_scores = importance_scores * type_weights

        # 归一化
        attention_weights = F.softmax(importance_scores, dim=-1)

        return attention_weights


class DescriptorCorrelationAttention(StructureAwareAttentionBase):
    """
    基于分子描述符相关性的注意力机制

    根据描述符之间的相关性和对目标任务的重要性动态调整注意力权重。
    """

    def __init__(
        self,
        feature_dim: int,
        correlation_dim: int = 64,
        num_descriptor_groups: int = 8,
        dropout: float = 0.1
    ):
        super().__init__(feature_dim, dropout)

        self.correlation_dim = correlation_dim
        self.num_descriptor_groups = num_descriptor_groups

        # 描述符分组器
        self.descriptor_grouper = nn.Sequential(
            nn.Linear(feature_dim, correlation_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(correlation_dim, num_descriptor_groups),
            nn.Softmax(dim=-1)
        )

        # 组内相关性计算器
        self.correlation_computer = nn.Sequential(
            nn.Linear(feature_dim, correlation_dim),
            nn.Tanh()
        )

        # 重要性权重学习器
        self.importance_learner = nn.Sequential(
            nn.Linear(correlation_dim + num_descriptor_groups, correlation_dim),
            nn.ReLU(),
            nn.Linear(correlation_dim, 1),
            nn.Sigmoid()
        )

        logger.info(f"初始化描述符相关性注意力: 分组数={num_descriptor_groups}")

    def compute_structure_weights(
        self,
        features: torch.Tensor,
        structure_info: Optional[Dict[str, Any]] = None
    ) -> torch.Tensor:
        """
        计算基于描述符相关性的注意力权重

        Args:
            features: 输入特征 [batch_size, seq_len, feature_dim]
            structure_info: 结构信息（可选）

        Returns:
            注意力权重 [batch_size, seq_len]
        """
        batch_size, seq_len, _ = features.shape

        # 计算描述符分组
        group_assignments = self.descriptor_grouper(features)  # [B, L, G]

        # 计算相关性特征
        correlation_features = self.correlation_computer(features)  # [B, L, C]

        # 结合分组和相关性信息
        combined_features = torch.cat([correlation_features, group_assignments], dim=-1)  # [B, L, C+G]

        # 计算重要性权重
        importance_weights = self.importance_learner(combined_features).squeeze(-1)  # [B, L]

        # 计算组内相关性增强
        group_correlation = torch.matmul(group_assignments, group_assignments.transpose(-2, -1))  # [B, L, L]
        correlation_enhancement = torch.diagonal(group_correlation, dim1=-2, dim2=-1)  # [B, L]

        # 结合重要性和相关性
        final_weights = importance_weights * correlation_enhancement

        # 归一化
        attention_weights = F.softmax(final_weights, dim=-1)

        return attention_weights


class MultiScaleStructureAttention(nn.Module):
    """
    多尺度结构注意力机制

    整合原子级、片段级和分子级的注意力，提供层次化的结构感知。
    """

    def __init__(
        self,
        feature_dim: int,
        atom_dim: int = 32,
        fragment_dim: int = 64,
        molecule_dim: int = 128,
        dropout: float = 0.1
    ):
        super().__init__()

        self.feature_dim = feature_dim
        self.atom_dim = atom_dim
        self.fragment_dim = fragment_dim
        self.molecule_dim = molecule_dim

        # 原子级注意力
        self.atom_attention = AtomImportanceAttention(
            feature_dim=feature_dim,
            atom_dim=atom_dim,
            dropout=dropout
        )

        # 片段级注意力
        self.fragment_attention = PharmacophoreAttention(
            feature_dim=feature_dim,
            pharmacophore_dim=fragment_dim,
            dropout=dropout
        )

        # 分子级注意力
        self.molecule_attention = ScaffoldSimilarityAttention(
            feature_dim=feature_dim,
            scaffold_dim=molecule_dim,
            dropout=dropout
        )

        # 尺度融合器
        self.scale_fusion = nn.Sequential(
            nn.Linear(3, 16),  # 3个尺度
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(16, 3),
            nn.Softmax(dim=-1)
        )

        # 自适应权重学习
        self.adaptive_weights = nn.Parameter(torch.ones(3) / 3)

        logger.info(f"初始化多尺度结构注意力: 原子={atom_dim}, 片段={fragment_dim}, 分子={molecule_dim}")

    def forward(
        self,
        features: torch.Tensor,
        structure_info: Optional[Dict[str, Any]] = None,
        return_attention: bool = False
    ) -> Tuple[torch.Tensor, Optional[Dict[str, torch.Tensor]]]:
        """
        前向传播

        Args:
            features: 输入特征 [batch_size, seq_len, feature_dim]
            structure_info: 结构信息
            return_attention: 是否返回注意力权重

        Returns:
            (输出特征, 注意力权重字典)
        """
        batch_size, seq_len, _ = features.shape

        # 计算各尺度注意力权重
        atom_weights = self.atom_attention.compute_structure_weights(features, structure_info)
        fragment_weights = self.fragment_attention.compute_structure_weights(features, structure_info)
        molecule_weights = self.molecule_attention.compute_structure_weights(features, structure_info)

        # 堆叠权重用于融合计算
        all_weights = torch.stack([atom_weights, fragment_weights, molecule_weights], dim=-1)  # [B, L, 3]

        # 计算自适应融合权重
        fusion_weights = self.scale_fusion(all_weights)  # [B, L, 3]

        # 应用可学习的全局权重
        global_weights = F.softmax(self.adaptive_weights, dim=0)  # [3]
        fusion_weights = fusion_weights * global_weights.unsqueeze(0).unsqueeze(0)  # [B, L, 3]

        # 融合多尺度注意力权重
        final_weights = torch.sum(all_weights * fusion_weights, dim=-1)  # [B, L]

        # 应用注意力到特征
        attended_features = features * final_weights.unsqueeze(-1)  # [B, L, F]

        attention_dict = None
        if return_attention:
            attention_dict = {
                'atom_weights': atom_weights,
                'fragment_weights': fragment_weights,
                'molecule_weights': molecule_weights,
                'fusion_weights': fusion_weights,
                'final_weights': final_weights
            }

        return attended_features, attention_dict
