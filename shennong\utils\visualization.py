# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-27
# 描述: 神农框架可视化工具

"""
神农框架可视化工具

提供注意力权重、训练曲线等的可视化功能。
"""

from typing import Dict, List, Optional, Any, Union
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


def plot_attention_weights(
    attention_weights: Dict[str, Any],
    save_path: Optional[Union[str, Path]] = None,
    figsize: tuple = (12, 8),
    title: str = "Attention Weights Analysis"
) -> plt.Figure:
    """
    可视化注意力权重
    
    Args:
        attention_weights: 注意力权重字典
        save_path: 保存路径
        figsize: 图片大小
        title: 图片标题
        
    Returns:
        matplotlib图形对象
    """
    fig, axes = plt.subplots(2, 2, figsize=figsize)
    fig.suptitle(title, fontsize=16)
    
    # 扁平化axes以便迭代
    axes_flat = axes.flatten()
    
    plot_idx = 0
    for component_name, weights in attention_weights.items():
        if plot_idx >= len(axes_flat):
            break
        
        ax = axes_flat[plot_idx]
        
        try:
            if hasattr(weights, 'detach'):
                weights_np = weights.detach().cpu().numpy()
            else:
                weights_np = np.array(weights)
            
            if weights_np.ndim == 2:
                # 2D权重矩阵
                im = ax.imshow(weights_np, cmap='viridis', aspect='auto')
                plt.colorbar(im, ax=ax)
                ax.set_title(f'{component_name}')
                ax.set_xlabel('Feature Index')
                ax.set_ylabel('Sample Index')
            
            elif weights_np.ndim == 1:
                # 1D权重向量
                ax.bar(range(len(weights_np)), weights_np)
                ax.set_title(f'{component_name}')
                ax.set_xlabel('Feature Index')
                ax.set_ylabel('Attention Weight')
            
            else:
                # 其他维度，显示统计信息
                ax.text(0.5, 0.5, f'{component_name}\nShape: {weights_np.shape}\nMean: {weights_np.mean():.4f}',
                       ha='center', va='center', transform=ax.transAxes)
                ax.set_title(f'{component_name} (Stats)')
        
        except Exception as e:
            ax.text(0.5, 0.5, f'{component_name}\nError: {str(e)}',
                   ha='center', va='center', transform=ax.transAxes)
            ax.set_title(f'{component_name} (Error)')
        
        plot_idx += 1
    
    # 隐藏未使用的子图
    for idx in range(plot_idx, len(axes_flat)):
        axes_flat[idx].set_visible(False)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"注意力权重图已保存: {save_path}")
    
    return fig


def plot_training_curves(
    train_losses: List[float],
    val_losses: Optional[List[float]] = None,
    train_metrics: Optional[Dict[str, List[float]]] = None,
    val_metrics: Optional[Dict[str, List[float]]] = None,
    save_path: Optional[Union[str, Path]] = None,
    figsize: tuple = (15, 10)
) -> plt.Figure:
    """
    绘制训练曲线
    
    Args:
        train_losses: 训练损失列表
        val_losses: 验证损失列表
        train_metrics: 训练指标字典
        val_metrics: 验证指标字典
        save_path: 保存路径
        figsize: 图片大小
        
    Returns:
        matplotlib图形对象
    """
    # 计算子图数量
    num_plots = 1  # 损失曲线
    if train_metrics:
        num_plots += len(train_metrics)
    
    # 计算子图布局
    if num_plots <= 2:
        nrows, ncols = 1, num_plots
    elif num_plots <= 4:
        nrows, ncols = 2, 2
    elif num_plots <= 6:
        nrows, ncols = 2, 3
    else:
        nrows, ncols = 3, 3
    
    fig, axes = plt.subplots(nrows, ncols, figsize=figsize)
    if num_plots == 1:
        axes = [axes]
    else:
        axes = axes.flatten()
    
    plot_idx = 0
    
    # 绘制损失曲线
    ax = axes[plot_idx]
    epochs = range(1, len(train_losses) + 1)
    
    ax.plot(epochs, train_losses, 'b-', label='Training Loss', linewidth=2)
    if val_losses:
        ax.plot(epochs, val_losses, 'r-', label='Validation Loss', linewidth=2)
    
    ax.set_xlabel('Epoch')
    ax.set_ylabel('Loss')
    ax.set_title('Training and Validation Loss')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    plot_idx += 1
    
    # 绘制指标曲线
    if train_metrics:
        for metric_name, train_values in train_metrics.items():
            if plot_idx >= len(axes):
                break
            
            ax = axes[plot_idx]
            epochs = range(1, len(train_values) + 1)
            
            ax.plot(epochs, train_values, 'b-', label=f'Training {metric_name}', linewidth=2)
            
            if val_metrics and metric_name in val_metrics:
                val_values = val_metrics[metric_name]
                ax.plot(epochs, val_values, 'r-', label=f'Validation {metric_name}', linewidth=2)
            
            ax.set_xlabel('Epoch')
            ax.set_ylabel(metric_name.upper())
            ax.set_title(f'{metric_name.upper()} Curves')
            ax.legend()
            ax.grid(True, alpha=0.3)
            
            plot_idx += 1
    
    # 隐藏未使用的子图
    for idx in range(plot_idx, len(axes)):
        axes[idx].set_visible(False)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"训练曲线图已保存: {save_path}")
    
    return fig


def plot_prediction_scatter(
    true_values: np.ndarray,
    predicted_values: np.ndarray,
    save_path: Optional[Union[str, Path]] = None,
    title: str = "Prediction vs True Values",
    figsize: tuple = (8, 8)
) -> plt.Figure:
    """
    绘制预测值vs真实值散点图
    
    Args:
        true_values: 真实值
        predicted_values: 预测值
        save_path: 保存路径
        title: 图片标题
        figsize: 图片大小
        
    Returns:
        matplotlib图形对象
    """
    fig, ax = plt.subplots(figsize=figsize)
    
    # 散点图
    ax.scatter(true_values, predicted_values, alpha=0.6, s=50)
    
    # 对角线（完美预测线）
    min_val = min(true_values.min(), predicted_values.min())
    max_val = max(true_values.max(), predicted_values.max())
    ax.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2, label='Perfect Prediction')
    
    # 计算R²
    from sklearn.metrics import r2_score
    r2 = r2_score(true_values, predicted_values)
    
    ax.set_xlabel('True Values')
    ax.set_ylabel('Predicted Values')
    ax.set_title(f'{title}\nR² = {r2:.3f}')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 设置相等的坐标轴范围
    ax.set_xlim(min_val, max_val)
    ax.set_ylim(min_val, max_val)
    ax.set_aspect('equal')
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"预测散点图已保存: {save_path}")
    
    return fig


def plot_mechanism_distribution(
    mechanism_predictions: Dict[str, float],
    save_path: Optional[Union[str, Path]] = None,
    title: str = "Mechanism Prediction Distribution",
    figsize: tuple = (10, 6)
) -> plt.Figure:
    """
    绘制机制预测分布图
    
    Args:
        mechanism_predictions: 机制预测字典
        save_path: 保存路径
        title: 图片标题
        figsize: 图片大小
        
    Returns:
        matplotlib图形对象
    """
    fig, ax = plt.subplots(figsize=figsize)
    
    mechanisms = list(mechanism_predictions.keys())
    probabilities = list(mechanism_predictions.values())
    
    # 中文机制名称映射
    mechanism_names_cn = {
        'cell_wall_synthesis': '细胞壁合成抑制',
        'protein_synthesis': '蛋白质合成抑制',
        'dna_replication': 'DNA复制抑制',
        'cell_membrane': '细胞膜破坏',
        'metabolic_pathway': '代谢途径抑制'
    }
    
    # 转换为中文名称
    cn_mechanisms = [mechanism_names_cn.get(m, m) for m in mechanisms]
    
    # 条形图
    bars = ax.bar(cn_mechanisms, probabilities, color='skyblue', alpha=0.7)
    
    # 添加数值标签
    for bar, prob in zip(bars, probabilities):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
               f'{prob:.3f}', ha='center', va='bottom')
    
    ax.set_ylabel('Prediction Probability')
    ax.set_title(title)
    ax.set_ylim(0, 1.0)
    
    # 旋转x轴标签
    plt.xticks(rotation=45, ha='right')
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"机制分布图已保存: {save_path}")
    
    return fig


def setup_plot_style():
    """设置绘图样式"""
    plt.style.use('seaborn-v0_8')
    sns.set_palette("husl")
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 设置图片质量
    plt.rcParams['figure.dpi'] = 100
    plt.rcParams['savefig.dpi'] = 300
    plt.rcParams['savefig.bbox'] = 'tight'
