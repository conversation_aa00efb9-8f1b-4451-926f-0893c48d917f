# 数据需求说明

**作者**: ZK  
**邮箱**: <EMAIL>  
**日期**: 2025-06-30  

## 📊 数据需求概述

### 核心数据需求
```python
data_requirements = {
    'primary_dataset': 'ChEMBL抗菌活性数据',
    'minimum_size': '5000个化合物',
    'recommended_size': '10000-20000个化合物',
    'data_quality': '高质量MIC数据',
    'chemical_diversity': '结构多样性良好'
}
```

### 数据类型
1. **分子结构**: SMILES字符串
2. **活性数据**: MIC值 (μg/mL)
3. **元数据**: 细菌种类、实验条件等

## 🎯 具体数据收集计划

### 1. ChEMBL数据库
**推荐数据源**: ChEMBL Database (https://www.ebi.ac.uk/chembl/)

#### 搜索策略
```sql
-- ChEMBL查询示例
SELECT 
    compound_structures.canonical_smiles,
    activities.standard_value as mic_value,
    activities.standard_units,
    assays.description,
    target_dictionary.organism
FROM activities
JOIN compound_structures ON activities.molregno = compound_structures.molregno
JOIN assays ON activities.assay_id = assays.assay_id
JOIN target_dictionary ON assays.tid = target_dictionary.tid
WHERE 
    activities.standard_type = 'MIC'
    AND activities.standard_units = 'ug.mL-1'
    AND activities.standard_value IS NOT NULL
    AND target_dictionary.organism LIKE '%bacteria%'
    AND compound_structures.canonical_smiles IS NOT NULL
```

#### 目标细菌种类
```python
target_bacteria = [
    'Escherichia coli',
    'Staphylococcus aureus', 
    'Pseudomonas aeruginosa',
    'Enterococcus faecalis',
    'Klebsiella pneumoniae',
    'Acinetobacter baumannii',
    'Enterobacter cloacae'
]
```

### 2. 数据质量标准

#### 分子结构质量
```python
smiles_quality_criteria = {
    'validity': 'RDKit可解析的有效SMILES',
    'molecular_weight': '100-1000 Da',
    'heavy_atoms': '5-50个重原子',
    'duplicates': '去除重复结构',
    'salts': '去除盐类和溶剂'
}
```

#### 活性数据质量
```python
activity_quality_criteria = {
    'mic_range': '0.01-1000 μg/mL',
    'units': '统一为 μg/mL',
    'outliers': '去除明显异常值',
    'confidence': '优选高置信度数据',
    'replicates': '处理重复实验数据'
}
```

### 3. 数据预处理流程

#### Step 1: 原始数据清洗
```python
def clean_raw_data(df):
    """原始数据清洗"""
    # 1. 去除缺失值
    df = df.dropna(subset=['smiles', 'mic_value'])
    
    # 2. SMILES标准化
    df['smiles'] = df['smiles'].apply(standardize_smiles)
    
    # 3. MIC值标准化
    df['mic_value'] = df['mic_value'].apply(standardize_mic)
    
    # 4. 去除重复
    df = df.drop_duplicates(subset=['smiles'])
    
    return df
```

#### Step 2: 分子过滤
```python
def filter_molecules(df):
    """分子结构过滤"""
    from rdkit import Chem
    from rdkit.Chem import Descriptors
    
    valid_molecules = []
    for idx, row in df.iterrows():
        mol = Chem.MolFromSmiles(row['smiles'])
        if mol is not None:
            mw = Descriptors.MolWt(mol)
            heavy_atoms = mol.GetNumHeavyAtoms()
            
            if 100 <= mw <= 1000 and 5 <= heavy_atoms <= 50:
                valid_molecules.append(idx)
    
    return df.loc[valid_molecules]
```

#### Step 3: 活性标签生成
```python
def create_activity_labels(df, threshold=16.0):
    """创建二分类标签"""
    # 活性定义: MIC < 16 μg/mL
    df['activity_binary'] = (df['mic_value'] < threshold).astype(int)
    
    # 多分类标签 (可选)
    df['activity_class'] = pd.cut(
        df['mic_value'], 
        bins=[0, 1, 16, 64, float('inf')],
        labels=['高活性', '中活性', '低活性', '无活性']
    )
    
    return df
```

## 📁 数据文件结构

### 标准数据格式
```csv
# antibacterial_dataset.csv
smiles,mic_value,activity_binary,activity_class,organism,assay_id
CCN(CC)CCCC(C)NC1=C2C=CC(=CC2=NC=C1)CF3,0.5,1,高活性,E.coli,CHEMBL001
O=C(O)C1=CN(C2CC2)C3=C(C=C(F)C(N4CCNCC4)=C3F)C1=O,2.0,1,中活性,S.aureus,CHEMBL002
...
```

### 数据划分文件
```python
# data_splits.json
{
    "train_indices": [0, 1, 2, ...],
    "val_indices": [100, 101, 102, ...],
    "test_indices": [200, 201, 202, ...],
    "split_method": "scaffold_split",
    "split_ratio": [0.7, 0.15, 0.15],
    "random_seed": 42
}
```

### 特征文件
```python
# features.npz (由generate_features.py生成)
{
    'mol_ids': array(['mol_0', 'mol_1', ...]),
    'features_morgan': array([[0, 1, 0, ...], ...]),
    'features_mordred': array([[1.23, 4.56, ...], ...]),
    'feature_types': ['morgan', 'mordred'],
    'metadata': {...}
}
```

## 🔍 数据收集实施步骤

### Week 1 Day 1-2: 数据下载
```bash
# 1. 访问ChEMBL网站
# https://www.ebi.ac.uk/chembl/

# 2. 使用ChEMBL Web Services API
pip install chembl_webresource_client

# 3. 下载脚本示例
python scripts/download_chembl_data.py \
  --target_type bacteria \
  --activity_type MIC \
  --min_compounds 5000 \
  --output_path data/raw_chembl_data.csv
```

### Week 1 Day 3: 数据清洗
```bash
# 运行数据清洗脚本
python scripts/clean_antibacterial_data.py \
  --input_path data/raw_chembl_data.csv \
  --output_path data/cleaned_antibacterial_data.csv \
  --min_compounds 5000 \
  --mic_threshold 16.0
```

### Week 1 Day 4: 特征生成
```bash
# 生成Mordred特征
python scripts/generate_features.py \
  --data_path data/cleaned_antibacterial_data.csv \
  --save_path data/features/mordred_features.npz \
  --feature_generator mordred \
  --smiles_column smiles \
  --normalize standard
```

## 📊 数据质量检查清单

### 基本统计
- [ ] 化合物总数 ≥ 5000
- [ ] 活性化合物比例: 20-80%
- [ ] SMILES有效性: 100%
- [ ] MIC值范围合理: 0.01-1000 μg/mL

### 化学多样性
- [ ] 分子量分布: 100-1000 Da
- [ ] Tanimoto相似性: 平均 < 0.3
- [ ] 分子骨架多样性: > 1000种骨架
- [ ] 官能团覆盖: 常见抗菌药物官能团

### 数据平衡性
- [ ] 训练/验证/测试比例: 70/15/15
- [ ] 各集合中活性比例相似
- [ ] 化学空间分布均匀
- [ ] 无数据泄露

## 🚨 常见问题与解决方案

### 问题1: ChEMBL数据下载困难
**解决方案**:
- 使用ChEMBL FTP下载完整数据库
- 联系ChEMBL技术支持
- 寻找替代数据源 (PubChem, BindingDB)

### 问题2: 数据量不足
**解决方案**:
- 扩大搜索范围 (包含更多细菌)
- 降低数据质量要求
- 合并多个数据源
- 使用数据增强技术

### 问题3: 数据不平衡
**解决方案**:
- 调整活性阈值
- 使用重采样技术
- 应用类别权重
- 分层采样

### 问题4: 化学多样性不足
**解决方案**:
- 使用多样性采样
- 扩大化学空间覆盖
- 包含天然产物数据
- 添加合成化合物

## 📋 数据验证检查表

### 下载完成后检查
- [ ] 文件大小合理 (>10MB)
- [ ] 数据格式正确
- [ ] 列名和数据类型正确
- [ ] 无明显错误或异常

### 清洗完成后检查
- [ ] 数据量满足最低要求
- [ ] SMILES全部有效
- [ ] MIC值分布合理
- [ ] 活性标签正确

### 特征生成后检查
- [ ] 特征文件大小合理
- [ ] 特征维度正确
- [ ] 无缺失值或异常值
- [ ] 特征与分子对应正确

---

**重要提醒**: 数据质量直接影响实验结果的可信度。请严格按照质量标准进行数据收集和预处理。
