#!/usr/bin/env python3
# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-27
# 描述: 神农框架快速开始示例

"""
神农框架快速开始示例

演示如何使用神农框架进行抗菌化合物活性预测。
包括数据准备、模型训练、预测和结果分析。
"""

import os
import sys
import numpy as np
import pandas as pd
import torch
import yaml
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入神农框架
try:
    from shennong import ShennongFramework
    from shennong.data import AntibacterialDataset, ShennongDatapoint
    from shennong.training import ShennongTrainer
    from shennong.utils.config import ShennongConfig
    print("✅ 神农框架导入成功")
except ImportError as e:
    print(f"❌ 神农框架导入失败: {e}")
    print("请确保已正确安装神农框架和所有依赖")
    sys.exit(1)


def create_sample_data():
    """创建示例数据"""
    print("📊 创建示例抗菌化合物数据...")

    # 示例抗菌化合物SMILES和活性数据
    sample_data = [
        # 简单的有效SMILES
        ("CCO", 2.5, "cell_wall_synthesis"),                                       # 乙醇（简单测试）
        ("CC(=O)O", 8.2, "cell_wall_synthesis"),                                   # 乙酸

        # 蛋白质合成抑制剂
        ("CN(C)C1=CC=C(C=C1)C(=O)O", 15.6, "protein_synthesis"),                  # 对二甲氨基苯甲酸
        ("CC(C)CC1=CC=C(C=C1)C(C)C(=O)O", 32.1, "protein_synthesis"),             # 异丁苯丙酸

        # DNA复制抑制剂
        ("CCN1C=C(C(=O)C2=CC(=C(C=C21)N3CCNCC3)F)C(=O)O", 1.8, "dna_replication"), # 环丙沙星
        ("CC1COC2=C1C=C(C=C2)C(=O)C3=CC=CC=C3", 45.3, "dna_replication"),          # 苯并呋喃衍生物

        # 细胞膜破坏剂
        ("CCCCCCCCCCCCCCCC(=O)O", 12.7, "cell_membrane"),                          # 棕榈酸
        ("CC(C)(C)C1=CC=C(C=C1)O", 89.4, "cell_membrane"),                        # 对叔丁基苯酚

        # 代谢途径抑制剂
        ("CC1=CC(=NO1)C2=CC=CC=C2C(=O)O", 25.8, "metabolic_pathway"),              # 异噁唑衍生物
        ("CN1C=NC2=C1C(=O)N(C(=O)N2C)C", 67.9, "metabolic_pathway"),               # 咖啡因类似物
    ]

    # 创建数据点
    datapoints = []
    for smiles, mic_value, mechanism in sample_data:
        # 计算log(MIC)作为回归目标
        log_mic = np.log10(mic_value)

        datapoint = ShennongDatapoint(
            smiles=smiles,
            targets={"activity": log_mic, "mic": mic_value},
            mechanism_labels=[mechanism],
            metadata={
                "mechanism": mechanism,
                "mic_value": mic_value,
                "activity_class": "active" if mic_value <= 10.0 else "inactive"
            }
        )
        datapoints.append(datapoint)

    print(f"✅ 创建了 {len(datapoints)} 个示例数据点")
    return datapoints


def load_config():
    """加载配置文件"""
    print("⚙️ 加载配置文件...")

    config_path = project_root / "configs" / "default_config.yaml"

    if not config_path.exists():
        print(f"❌ 配置文件不存在: {config_path}")
        return None

    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)

        # 调整配置以适应示例数据
        config['training']['max_epochs'] = 5  # 快速演示
        config['training']['batch_size'] = 4   # 小批次
        config['data']['dataloader']['num_workers'] = 0  # 避免多进程问题

        print("✅ 配置文件加载成功")
        return config

    except Exception as e:
        print(f"❌ 配置文件加载失败: {e}")
        return None


def demonstrate_prediction():
    """演示预测功能"""
    print("\n🔮 演示预测功能...")

    # 创建一个简单的模型配置
    simple_config = {
        'graph_config': {
            'hidden_size': 128,
            'depth': 2,
            'output_dim': 128
        },
        'expert_config': {
            'descriptor_dim': 100,  # 简化的描述符维度
            'output_dim': 64,
            'adaptive_architecture': False,
            'hidden_dims': [64]
        },
        'fusion_config': {
            'output_dim': 128,
            'attention_heads': 2,
            'mechanism_aware': False  # 简化配置
        },
        'task_config': {
            'num_tasks': 1,
            'task_type': 'regression'
        },
        'learning_rate': 1e-3
    }

    try:
        # 创建模型
        model = ShennongFramework(simple_config)
        print(f"✅ 模型创建成功")
        print(model.summary())

        # 测试预测（使用随机权重）
        test_smiles = [
            "CCN1C=C(C(=O)C2=CC(=C(C=C21)N3CCNCC3)F)C(=O)O",  # 环丙沙星
            "CC(C)CC1=CC=C(C=C1)C(C)C(=O)O"                    # 布洛芬
        ]

        print(f"\n🧪 测试预测 {len(test_smiles)} 个化合物...")

        # 注意：这里只是演示API，实际预测需要训练好的模型
        # results = model.predict(test_smiles, return_attention=True)
        # print("预测结果:", results)

        print("✅ 预测接口测试完成（需要训练后的模型才能获得有意义的结果）")

    except Exception as e:
        print(f"❌ 预测演示失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    print("🧬 神农框架快速开始示例")
    print("=" * 50)

    # 检查环境
    print(f"Python版本: {sys.version}")
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")

    # 创建示例数据
    datapoints = create_sample_data()

    # 创建数据集
    print("\n📚 创建抗菌数据集...")
    try:
        dataset = AntibacterialDataset(datapoints, validate_data=True)
        print("✅ 数据集创建成功")
        print(dataset.get_antibacterial_summary())
    except Exception as e:
        print(f"❌ 数据集创建失败: {e}")
        import traceback
        traceback.print_exc()
        return

    # 加载配置
    config = load_config()
    if config is None:
        print("⚠️ 使用默认配置继续...")

    # 演示预测功能
    demonstrate_prediction()

    print("\n🎉 神农框架快速开始示例完成！")
    print("\n📖 下一步:")
    print("1. 准备真实的抗菌化合物数据集")
    print("2. 配置模型参数和训练设置")
    print("3. 运行完整的训练流程")
    print("4. 评估模型性能和可解释性")
    print("5. 部署模型进行实际预测")

    print(f"\n📁 项目结构:")
    print(f"- 配置文件: {project_root}/configs/")
    print(f"- 示例代码: {project_root}/examples/")
    print(f"- 文档: {project_root}/docs/")
    print(f"- 测试: {project_root}/tests/")


if __name__ == "__main__":
    main()
