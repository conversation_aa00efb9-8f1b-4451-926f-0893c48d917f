# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-29
# 描述: 神农框架主命令行入口

"""
神农框架主命令行接口

提供统一的命令行入口，支持多个子命令。
参考ChemProp的CLI架构设计。
"""

import logging
import sys
from pathlib import Path
from typing import List, Optional

try:
    from configargparse import ArgumentParser
except ImportError:
    from argparse import ArgumentParser

from .train import TrainSubcommand
from .predict import PredictSubcommand
from .evaluate import EvaluateSubcommand
from .fingerprint import FingerprintSubcommand
from .explain import ExplainSubcommand
from .utils import setup_logging, pop_attr

logger = logging.getLogger(__name__)

# 支持的子命令列表
SUBCOMMANDS = [
    TrainSubcommand,
    PredictSubcommand,
    EvaluateSubcommand,
    FingerprintSubcommand,
    ExplainSubcommand,
]


def construct_parser() -> ArgumentParser:
    """
    构建主命令行解析器

    Returns:
        配置好的ArgumentParser对象
    """
    parser = ArgumentParser(
        prog='shennong',
        description='神农框架 - 基于深度学习的抗菌化合物预测系统'
    )

    # 添加全局参数
    parser.add_argument(
        '--version',
        action='version',
        version='神农框架 v1.0.0'
    )

    # 创建子命令解析器
    subparsers = parser.add_subparsers(
        title="可用命令",
        dest="mode",
        required=True,
        help="选择要执行的操作"
    )

    # 创建父解析器，包含通用参数
    parent = ArgumentParser(add_help=False)
    parent.add_argument(
        "--logfile",
        "--log",
        nargs="?",
        const="default",
        help="日志文件路径 (默认: logs/MODE/TIMESTAMP.log)"
    )
    parent.add_argument(
        "-v", "--verbose",
        action="store_true",
        help="启用详细日志输出"
    )
    parent.add_argument(
        "-q", "--quiet",
        action="count",
        default=0,
        help="减少日志输出 (可重复使用)"
    )
    parent.add_argument(
        "--config",
        type=str,
        help="配置文件路径 (支持YAML/TOML格式)"
    )

    # 注册所有子命令
    parents = [parent]
    for subcommand in SUBCOMMANDS:
        subcommand.add(subparsers, parents)

    return parser


def main(args: Optional[List[str]] = None):
    """
    主入口函数

    Args:
        args: 命令行参数列表，如果为None则从sys.argv获取
    """
    parser = construct_parser()
    parsed_args = parser.parse_args(args)

    # 提取全局参数
    logfile = pop_attr(parsed_args, 'logfile')
    verbose = pop_attr(parsed_args, 'verbose')
    quiet = pop_attr(parsed_args, 'quiet')
    mode = pop_attr(parsed_args, 'mode')
    func = pop_attr(parsed_args, 'func')

    # 参数验证
    if verbose and quiet:
        parser.error("--verbose 和 --quiet 选项不能同时使用")

    # 设置日志
    try:
        setup_logging(
            logfile=logfile,
            mode=mode,
            verbose=verbose,
            quiet=quiet
        )
    except Exception as e:
        print(f"日志设置失败: {e}", file=sys.stderr)
        sys.exit(1)

    # 执行子命令
    try:
        logger.info(f"开始执行神农框架 {mode} 命令")
        func(parsed_args)
        logger.info(f"神农框架 {mode} 命令执行完成")
    except KeyboardInterrupt:
        logger.info("用户中断操作")
        sys.exit(1)
    except Exception as e:
        logger.error(f"执行失败: {e}")
        if verbose:
            logger.exception("详细错误信息:")
        sys.exit(1)


if __name__ == "__main__":
    main()
