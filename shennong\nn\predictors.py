# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-27
# 描述: 神农框架预测器

"""
神农框架预测器

实现各种预测任务的输出层。
"""

from typing import Dict, Any, Optional
import torch
import torch.nn as nn
import logging

logger = logging.getLogger(__name__)


class ActivityPredictor(nn.Module):
    """
    活性预测器
    
    预测抗菌活性值。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化活性预测器
        
        Args:
            config: 配置字典
        """
        super().__init__()
        
        self.input_dim = config.get('input_dim', 256)
        self.num_tasks = config.get('num_tasks', 1)
        self.dropout = config.get('dropout', 0.2)
        
        # 预测网络
        self.predictor = nn.Sequential(
            nn.Linear(self.input_dim, self.input_dim // 2),
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.Linear(self.input_dim // 2, self.num_tasks)
        )
        
        logger.info(f"初始化活性预测器: 任务数={self.num_tasks}")
    
    def forward(self, features: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            features: 输入特征
            
        Returns:
            活性预测值
        """
        return self.predictor(features)


class MechanismClassifier(nn.Module):
    """
    机制分类器
    
    预测抗菌机制类别。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化机制分类器
        
        Args:
            config: 配置字典
        """
        super().__init__()
        
        self.input_dim = config.get('input_dim', 256)
        self.num_mechanisms = config.get('num_mechanisms', 5)
        self.dropout = config.get('dropout', 0.2)
        
        # 分类网络
        self.classifier = nn.Sequential(
            nn.Linear(self.input_dim, self.input_dim // 2),
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.Linear(self.input_dim // 2, self.num_mechanisms)
        )
        
        logger.info(f"初始化机制分类器: 机制数={self.num_mechanisms}")
    
    def forward(self, features: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            features: 输入特征
            
        Returns:
            机制分类logits
        """
        return self.classifier(features)


class MultiTaskPredictor(nn.Module):
    """
    多任务预测器
    
    同时预测多个任务。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化多任务预测器
        
        Args:
            config: 配置字典
        """
        super().__init__()
        
        self.input_dim = config.get('input_dim', 256)
        self.task_configs = config.get('task_configs', {})
        
        # 为每个任务创建预测头
        self.task_heads = nn.ModuleDict()
        
        for task_name, task_config in self.task_configs.items():
            task_type = task_config.get('type', 'regression')
            output_dim = task_config.get('output_dim', 1)
            
            if task_type == 'regression':
                self.task_heads[task_name] = ActivityPredictor({
                    'input_dim': self.input_dim,
                    'num_tasks': output_dim
                })
            elif task_type == 'classification':
                self.task_heads[task_name] = MechanismClassifier({
                    'input_dim': self.input_dim,
                    'num_mechanisms': output_dim
                })
        
        logger.info(f"初始化多任务预测器: 任务数={len(self.task_configs)}")
    
    def forward(self, features: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        前向传播
        
        Args:
            features: 输入特征
            
        Returns:
            各任务的预测结果
        """
        outputs = {}
        
        for task_name, head in self.task_heads.items():
            outputs[task_name] = head(features)
        
        return outputs


class UncertaintyPredictor(nn.Module):
    """
    不确定性预测器
    
    预测预测结果的不确定性。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化不确定性预测器
        
        Args:
            config: 配置字典
        """
        super().__init__()
        
        self.input_dim = config.get('input_dim', 256)
        self.num_tasks = config.get('num_tasks', 1)
        self.uncertainty_type = config.get('uncertainty_type', 'aleatoric')
        
        # 不确定性预测网络
        self.uncertainty_predictor = nn.Sequential(
            nn.Linear(self.input_dim, self.input_dim // 4),
            nn.ReLU(),
            nn.Linear(self.input_dim // 4, self.num_tasks),
            nn.Softplus()  # 确保输出为正值
        )
        
        logger.info(f"初始化不确定性预测器: 类型={self.uncertainty_type}")
    
    def forward(self, features: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            features: 输入特征
            
        Returns:
            不确定性估计
        """
        return self.uncertainty_predictor(features)
