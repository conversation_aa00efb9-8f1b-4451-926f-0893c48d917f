# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-27
# 描述: 神农框架数据集类定义

"""
神农框架数据集类

提供支持双模态输入的数据集类，扩展Chemprop的数据集功能。
支持自适应描述符维度检测和抗菌化合物特异性处理。
"""

from typing import List, Optional, Dict, Any, Union, Tuple
import pandas as pd
import numpy as np
import torch
from torch.utils.data import Dataset
import logging
from pathlib import Path

# Chemprop导入
try:
    from chemprop.data import MoleculeDataset, MoleculeDatapoint
except ImportError:
    raise ImportError(
        "Chemprop未安装或版本不兼容。请安装: "
        "pip install git+https://github.com/chemprop/chemprop.git@v2.0.0"
    )

from .datapoints import ShennongDatapoint, AntibacterialDatapoint

logger = logging.getLogger(__name__)


class ShennongDataset(Dataset):
    """
    神农框架基础数据集类

    支持自适应描述符维度的数据集，可以处理不同来源和维度的分子描述符。
    提供数据验证、统计分析和批处理功能。
    """

    def __init__(
        self,
        data: List[ShennongDatapoint],
        descriptor_config: Optional[Dict[str, Any]] = None,
        featurizer_config: Optional[Dict[str, Any]] = None,
        task_names: Optional[List[str]] = None,
        validate_data: bool = True,
        features_path: Optional[str] = None
    ):
        """
        初始化神农数据集

        Args:
            data: 数据点列表
            descriptor_config: 描述符配置
            featurizer_config: 特征化器配置
            task_names: 任务名称列表
            validate_data: 是否验证数据
            features_path: 预计算特征文件路径 (.npz格式)
        """
        self.data = data
        self.descriptor_config = descriptor_config or {}
        self.featurizer_config = featurizer_config or {}
        self.task_names = task_names or self._infer_task_names()
        self.features_path = features_path

        # 预计算特征相关
        self.precomputed_features = None
        self.feature_mapping = None
        self.use_precomputed_features = False

        # 加载预计算特征
        if features_path:
            self._load_precomputed_features()

        # 数据验证和预处理
        if validate_data:
            self._validate_data_consistency()

        # 检测描述符维度和类型
        if not self.use_precomputed_features:
            self._detect_descriptor_dimensions()
        else:
            self._detect_precomputed_feature_dimensions()

        # 计算数据集统计信息
        self._compute_statistics()

        feature_info = f"{self.descriptor_dim}维描述符"
        if self.use_precomputed_features:
            feature_info = f"预计算特征({self.descriptor_dim}维)"

        logger.info(f"初始化神农数据集: {len(self.data)}个样本, "
                   f"{len(self.task_names)}个任务, "
                   f"{feature_info}")

    def _infer_task_names(self) -> List[str]:
        """推断任务名称"""
        if not self.data:
            return []

        # 从第一个数据点获取所有目标键
        all_keys = set()
        for datapoint in self.data:
            all_keys.update(datapoint.targets.keys())

        return sorted(list(all_keys))

    def _validate_data_consistency(self):
        """验证数据一致性"""
        if not self.data:
            raise ValueError("数据集不能为空")

        # 检查描述符维度一致性
        descriptor_dims = []
        for i, datapoint in enumerate(self.data):
            if datapoint.descriptors is not None:
                dim = datapoint.get_descriptor_dim()
                descriptor_dims.append(dim)

                if len(descriptor_dims) > 1 and dim != descriptor_dims[0]:
                    raise ValueError(
                        f"描述符维度不一致: 样本{i}的维度为{dim}, "
                        f"期望维度为{descriptor_dims[0]}"
                    )

        # 检查任务一致性
        for i, datapoint in enumerate(self.data):
            for task_name in self.task_names:
                if not datapoint.has_target(task_name):
                    logger.warning(f"样本{i}缺少任务'{task_name}'的目标值")

    def _detect_descriptor_dimensions(self):
        """动态检测描述符维度和类型"""
        # 找到第一个有描述符的样本
        sample_descriptors = None
        for datapoint in self.data:
            if datapoint.descriptors is not None:
                sample_descriptors = datapoint.descriptors
                break

        if sample_descriptors is not None:
            self.descriptor_dim = len(sample_descriptors)
            self.descriptor_type = self._infer_descriptor_type(self.descriptor_dim)
        else:
            self.descriptor_dim = 0
            self.descriptor_type = 'none'

        logger.info(f"检测到描述符类型: {self.descriptor_type}, 维度: {self.descriptor_dim}")

    def _load_precomputed_features(self):
        """加载预计算特征"""
        try:
            logger.info(f"加载预计算特征: {self.features_path}")

            # 加载特征文件
            feature_data = np.load(self.features_path, allow_pickle=True)

            # 获取分子ID和特征
            mol_ids = feature_data['mol_ids']
            feature_types = feature_data['feature_types'].tolist()

            # 构建分子ID到索引的映射
            self.feature_mapping = {mol_id: idx for idx, mol_id in enumerate(mol_ids)}

            # 加载所有特征类型
            self.precomputed_features = {}
            total_dim = 0

            for feat_type in feature_types:
                feat_key = f'features_{feat_type}'
                if feat_key in feature_data:
                    self.precomputed_features[feat_type] = feature_data[feat_key]
                    total_dim += feature_data[feat_key].shape[1]
                    logger.info(f"   {feat_type}: {feature_data[feat_key].shape}")

            # 组合所有特征
            if len(feature_types) > 1:
                combined_features = []
                for i in range(len(mol_ids)):
                    feat_vector = []
                    for feat_type in feature_types:
                        feat_vector.append(self.precomputed_features[feat_type][i])
                    combined_features.append(np.concatenate(feat_vector))
                self.precomputed_features['combined'] = np.array(combined_features)
            else:
                # 单一特征类型
                feat_type = feature_types[0]
                self.precomputed_features['combined'] = self.precomputed_features[feat_type]

            self.use_precomputed_features = True
            logger.info(f"✅ 成功加载预计算特征: {len(mol_ids)}个分子, {total_dim}维特征")

        except Exception as e:
            logger.error(f"❌ 加载预计算特征失败: {e}")
            logger.warning("将使用原始描述符计算方法")
            self.use_precomputed_features = False

    def _detect_precomputed_feature_dimensions(self):
        """检测预计算特征维度"""
        if self.precomputed_features and 'combined' in self.precomputed_features:
            self.descriptor_dim = self.precomputed_features['combined'].shape[1]
            self.descriptor_type = 'precomputed'
        else:
            self.descriptor_dim = 0
            self.descriptor_type = 'none'

    def _infer_descriptor_type(self, dim: int) -> str:
        """根据维度推断描述符类型"""
        if dim == 0:
            return 'none'
        elif 1500 <= dim <= 1900:
            return 'mordred_full'
        elif 200 <= dim <= 800:
            return 'mordred_selected'
        elif 100 <= dim <= 200:
            return 'rdkit_descriptors'
        elif dim < 100:
            return 'custom_small'
        else:
            return 'custom_large'

    def _compute_statistics(self):
        """计算数据集统计信息"""
        self.statistics = {
            'num_samples': len(self.data),
            'num_tasks': len(self.task_names),
            'descriptor_dim': self.descriptor_dim,
            'descriptor_type': self.descriptor_type,
            'task_statistics': {}
        }

        # 计算每个任务的统计信息
        for task_name in self.task_names:
            values = []
            for datapoint in self.data:
                if datapoint.has_target(task_name):
                    values.append(datapoint.get_target(task_name))

            if values:
                self.statistics['task_statistics'][task_name] = {
                    'count': len(values),
                    'mean': np.mean(values),
                    'std': np.std(values),
                    'min': np.min(values),
                    'max': np.max(values),
                    'missing_rate': 1 - len(values) / len(self.data)
                }

    def __len__(self) -> int:
        """数据集大小"""
        return len(self.data)

    def __getitem__(self, idx: int) -> Dict[str, Any]:
        """获取单个数据点"""
        if idx >= len(self.data):
            raise IndexError(f"索引{idx}超出数据集大小{len(self.data)}")

        datapoint = self.data[idx]

        # 获取描述符
        descriptors = None
        if self.use_precomputed_features:
            # 使用预计算特征
            mol_id = getattr(datapoint, 'mol_id', f'mol_{idx}')
            if mol_id in self.feature_mapping:
                feat_idx = self.feature_mapping[mol_id]
                descriptors = self.precomputed_features['combined'][feat_idx]
            else:
                # 如果找不到对应的预计算特征，使用零向量
                descriptors = np.zeros(self.descriptor_dim)
                logger.warning(f"未找到分子{mol_id}的预计算特征，使用零向量")
        else:
            # 使用原始描述符
            descriptors = datapoint.descriptors

        # 构建返回字典
        item = {
            'smiles': datapoint.smiles,
            'mol_graph': datapoint.mol_graph,
            'descriptors': descriptors,
            'targets': datapoint.targets,
            'metadata': datapoint.metadata
        }

        # 添加机制标签（如果存在）
        if hasattr(datapoint, 'mechanism_labels') and datapoint.mechanism_labels:
            item['mechanism_labels'] = datapoint.mechanism_labels

        return item

    def get_task_targets(self, task_name: str) -> List[Optional[float]]:
        """获取指定任务的所有目标值"""
        if task_name not in self.task_names:
            raise ValueError(f"任务'{task_name}'不存在")

        return [datapoint.get_target(task_name) for datapoint in self.data]

    def get_descriptors_matrix(self) -> Optional[np.ndarray]:
        """获取描述符矩阵"""
        if self.descriptor_dim == 0:
            return None

        descriptors = []
        for datapoint in self.data:
            if datapoint.descriptors is not None:
                descriptors.append(datapoint.descriptors)
            else:
                # 用零填充缺失的描述符
                descriptors.append(np.zeros(self.descriptor_dim))

        return np.array(descriptors)

    def filter_by_task(self, task_name: str, remove_missing: bool = True) -> 'ShennongDataset':
        """根据任务过滤数据集"""
        if task_name not in self.task_names:
            raise ValueError(f"任务'{task_name}'不存在")

        filtered_data = []
        for datapoint in self.data:
            if remove_missing and not datapoint.has_target(task_name):
                continue
            filtered_data.append(datapoint)

        return ShennongDataset(
            data=filtered_data,
            descriptor_config=self.descriptor_config,
            featurizer_config=self.featurizer_config,
            task_names=[task_name],
            validate_data=False
        )

    def split_by_indices(self, indices: List[int]) -> 'ShennongDataset':
        """根据索引分割数据集"""
        split_data = [self.data[i] for i in indices]

        return ShennongDataset(
            data=split_data,
            descriptor_config=self.descriptor_config,
            featurizer_config=self.featurizer_config,
            task_names=self.task_names,
            validate_data=False
        )

    @classmethod
    def from_csv(
        cls,
        csv_path: Union[str, Path],
        smiles_column: str = 'smiles',
        target_columns: Optional[List[str]] = None,
        descriptor_columns: Optional[List[str]] = None,
        **kwargs
    ) -> 'ShennongDataset':
        """从CSV文件加载数据集"""
        df = pd.read_csv(csv_path)

        # 自动检测目标列
        if target_columns is None:
            # 排除SMILES列和已知的非目标列
            exclude_columns = {smiles_column, 'id', 'name', 'dataset', 'split'}
            target_columns = [col for col in df.columns if col not in exclude_columns]

        # 创建数据点列表
        data = []
        for _, row in df.iterrows():
            # 提取目标值
            targets = {}
            for col in target_columns:
                if col in df.columns and pd.notna(row[col]):
                    targets[col] = float(row[col])

            # 提取描述符
            descriptors = None
            if descriptor_columns:
                desc_values = []
                for col in descriptor_columns:
                    if col in df.columns:
                        desc_values.append(row[col] if pd.notna(row[col]) else 0.0)
                if desc_values:
                    descriptors = np.array(desc_values)

            # 创建数据点
            datapoint = ShennongDatapoint(
                smiles=row[smiles_column],
                targets=targets,
                descriptors=descriptors
            )
            data.append(datapoint)

        return cls(data=data, **kwargs)

    def to_csv(self, csv_path: Union[str, Path]):
        """保存数据集到CSV文件"""
        rows = []
        for datapoint in self.data:
            row = {'smiles': datapoint.smiles}
            row.update(datapoint.targets)

            # 添加描述符列
            if datapoint.descriptors is not None:
                for i, desc_val in enumerate(datapoint.descriptors):
                    row[f'descriptor_{i}'] = desc_val

            rows.append(row)

        df = pd.DataFrame(rows)
        df.to_csv(csv_path, index=False)
        logger.info(f"数据集已保存到: {csv_path}")

    def get_statistics_summary(self) -> str:
        """获取数据集统计摘要"""
        summary = f"""
神农数据集统计摘要:
- 样本数量: {self.statistics['num_samples']}
- 任务数量: {self.statistics['num_tasks']}
- 描述符维度: {self.statistics['descriptor_dim']}
- 描述符类型: {self.statistics['descriptor_type']}

任务统计:
"""

        for task_name, stats in self.statistics['task_statistics'].items():
            summary += f"""
  {task_name}:
    - 有效样本: {stats['count']}
    - 缺失率: {stats['missing_rate']:.2%}
    - 均值: {stats['mean']:.3f}
    - 标准差: {stats['std']:.3f}
    - 范围: [{stats['min']:.3f}, {stats['max']:.3f}]
"""

        return summary


class AntibacterialDataset(ShennongDataset):
    """
    抗菌化合物专用数据集类

    扩展基础数据集类，添加抗菌活性相关的特殊功能：
    - 细菌菌株分类
    - MIC值处理
    - 抗菌机制标注
    - 耐药性分析
    """

    def __init__(self, data: List[AntibacterialDatapoint], **kwargs):
        """初始化抗菌数据集"""
        super().__init__(data, **kwargs)

        # 抗菌特异性统计
        self._compute_antibacterial_statistics()

    def _compute_antibacterial_statistics(self):
        """计算抗菌特异性统计信息"""
        # 细菌菌株统计
        strain_counts = {}
        mechanism_counts = {}
        activity_distribution = {'active': 0, 'inactive': 0, 'unknown': 0}

        for datapoint in self.data:
            if hasattr(datapoint, 'bacterial_strain') and datapoint.bacterial_strain:
                strain_counts[datapoint.bacterial_strain] = strain_counts.get(datapoint.bacterial_strain, 0) + 1

            if hasattr(datapoint, 'mechanism_labels') and datapoint.mechanism_labels:
                for mechanism in datapoint.mechanism_labels:
                    mechanism_counts[mechanism] = mechanism_counts.get(mechanism, 0) + 1

            if hasattr(datapoint, 'get_activity_class'):
                activity_class = datapoint.get_activity_class()
                activity_distribution[activity_class] += 1

        self.antibacterial_statistics = {
            'strain_distribution': strain_counts,
            'mechanism_distribution': mechanism_counts,
            'activity_distribution': activity_distribution
        }

    def get_antibacterial_summary(self) -> str:
        """获取抗菌数据集摘要"""
        base_summary = self.get_statistics_summary()

        antibacterial_summary = f"""

抗菌特异性统计:

细菌菌株分布:
"""
        for strain, count in self.antibacterial_statistics['strain_distribution'].items():
            percentage = count / len(self.data) * 100
            antibacterial_summary += f"  - {strain}: {count} ({percentage:.1f}%)\n"

        antibacterial_summary += "\n活性分布:\n"
        for activity, count in self.antibacterial_statistics['activity_distribution'].items():
            percentage = count / len(self.data) * 100
            antibacterial_summary += f"  - {activity}: {count} ({percentage:.1f}%)\n"

        return base_summary + antibacterial_summary
