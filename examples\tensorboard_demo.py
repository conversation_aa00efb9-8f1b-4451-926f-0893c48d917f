#!/usr/bin/env python3
# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-29
# 描述: 神农框架TensorBoard演示

"""
神农框架TensorBoard演示

展示如何使用神农框架的TensorBoard集成功能，
包括训练监控、注意力可视化、化学解释等。
"""

import sys
import os
from pathlib import Path
import subprocess
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def check_tensorboard_installation():
    """检查TensorBoard是否已安装"""
    try:
        import tensorboard
        print("✅ TensorBoard已安装")
        return True
    except ImportError:
        print("❌ TensorBoard未安装，请运行: pip install tensorboard")
        return False

def start_tensorboard_server(log_dir: str, port: int = 6006):
    """启动TensorBoard服务器"""
    try:
        print(f"🚀 启动TensorBoard服务器...")
        print(f"   日志目录: {log_dir}")
        print(f"   端口: {port}")
        
        # 启动TensorBoard
        cmd = f"tensorboard --logdir={log_dir} --port={port} --host=0.0.0.0"
        process = subprocess.Popen(cmd, shell=True)
        
        print(f"✅ TensorBoard已启动!")
        print(f"   访问地址: http://localhost:{port}")
        print(f"   进程ID: {process.pid}")
        
        return process
        
    except Exception as e:
        print(f"❌ 启动TensorBoard失败: {e}")
        return None

def demonstrate_tensorboard_features():
    """演示TensorBoard功能"""
    print("🧬 神农框架TensorBoard功能演示")
    print("=" * 60)
    
    # 检查安装
    if not check_tensorboard_installation():
        return
    
    # 检查日志目录
    log_dirs = [
        "logs",
        "models/antibacterial/logs", 
        "experiments/logs"
    ]
    
    available_logs = []
    for log_dir in log_dirs:
        if Path(log_dir).exists():
            available_logs.append(log_dir)
    
    if not available_logs:
        print("📝 创建示例日志目录...")
        demo_log_dir = Path("logs/demo_experiment")
        demo_log_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建示例日志文件
        create_demo_logs(demo_log_dir)
        available_logs.append(str(demo_log_dir))
    
    print(f"\n📊 发现的日志目录:")
    for i, log_dir in enumerate(available_logs):
        print(f"   {i+1}. {log_dir}")
    
    # 选择日志目录
    if len(available_logs) == 1:
        selected_log = available_logs[0]
    else:
        try:
            choice = input(f"\n请选择日志目录 (1-{len(available_logs)}): ")
            selected_log = available_logs[int(choice) - 1]
        except (ValueError, IndexError):
            selected_log = available_logs[0]
    
    print(f"\n🎯 使用日志目录: {selected_log}")
    
    # 启动TensorBoard
    process = start_tensorboard_server(selected_log)
    
    if process:
        print("\n📋 TensorBoard功能说明:")
        print("   🔹 SCALARS: 训练损失、验证损失、学习率等")
        print("   🔹 GRAPHS: 神农框架模型计算图")
        print("   🔹 IMAGES: 注意力权重热图、分子可视化")
        print("   🔹 HISTOGRAMS: 参数分布、梯度分布")
        print("   🔹 PROJECTOR: 分子嵌入向量可视化")
        
        print("\n🎨 神农框架专有功能:")
        print("   ✨ 化学导向注意力权重监控")
        print("   ✨ 分子结构注意力热图")
        print("   ✨ 官能团重要性分析")
        print("   ✨ Mordred描述符相关性")
        print("   ✨ 抗菌活性预测精度跟踪")
        
        print(f"\n🌐 请在浏览器中访问: http://localhost:6006")
        print("   按 Ctrl+C 停止TensorBoard服务器")
        
        try:
            # 等待用户中断
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 停止TensorBoard服务器...")
            process.terminate()
            print("✅ 已停止")

def create_demo_logs(log_dir: Path):
    """创建演示日志"""
    print(f"📝 创建演示日志到: {log_dir}")
    
    try:
        from torch.utils.tensorboard import SummaryWriter
        import numpy as np
        
        writer = SummaryWriter(log_dir)
        
        # 模拟训练数据
        epochs = 50
        for epoch in range(epochs):
            # 基础损失
            train_loss = 2.0 * np.exp(-epoch/20) + 0.1 * np.random.random()
            val_loss = 2.2 * np.exp(-epoch/18) + 0.15 * np.random.random()
            
            writer.add_scalar('Loss/Train', train_loss, epoch)
            writer.add_scalar('Loss/Validation', val_loss, epoch)
            
            # 学习率
            lr = 0.001 * (0.95 ** (epoch // 10))
            writer.add_scalar('Learning_Rate', lr, epoch)
            
            # 注意力权重统计（神农框架特有）
            if epoch % 5 == 0:
                # 官能团注意力
                fg_attention_mean = 0.3 + 0.2 * np.sin(epoch/10) + 0.1 * np.random.random()
                writer.add_scalar('Attention/Functional_Group/Mean', fg_attention_mean, epoch)
                
                # 分子性质注意力
                prop_attention_mean = 0.25 + 0.15 * np.cos(epoch/8) + 0.1 * np.random.random()
                writer.add_scalar('Attention/Molecular_Property/Mean', prop_attention_mean, epoch)
                
                # 活性相关注意力
                act_attention_mean = 0.35 + 0.1 * np.sin(epoch/12) + 0.1 * np.random.random()
                writer.add_scalar('Attention/Activity_Related/Mean', act_attention_mean, epoch)
                
                # 注意力稀疏性
                sparsity = 0.6 + 0.2 * np.sin(epoch/15)
                writer.add_scalar('Attention/Sparsity', sparsity, epoch)
            
            # 抗菌活性指标（神农框架特有）
            if epoch % 10 == 0:
                # MIC预测精度
                mic_r2 = 0.5 + 0.4 * (1 - np.exp(-epoch/25)) + 0.05 * np.random.random()
                writer.add_scalar('Antibacterial/MIC_R2', mic_r2, epoch)
                
                # 活性分类准确率
                activity_acc = 0.6 + 0.35 * (1 - np.exp(-epoch/20)) + 0.05 * np.random.random()
                writer.add_scalar('Antibacterial/Activity_Accuracy', activity_acc, epoch)
        
        # 添加模拟的注意力权重直方图
        for epoch in [10, 20, 30, 40]:
            attention_weights = np.random.beta(2, 5, 1000)  # 模拟注意力权重分布
            writer.add_histogram('Attention_Weights/Distribution', attention_weights, epoch)
        
        writer.close()
        print("✅ 演示日志创建完成")
        
    except ImportError:
        print("❌ 需要安装tensorboard: pip install tensorboard")
    except Exception as e:
        print(f"❌ 创建演示日志失败: {e}")

def show_tensorboard_commands():
    """显示TensorBoard常用命令"""
    print("\n📚 TensorBoard常用命令:")
    print("=" * 40)
    
    commands = [
        ("启动TensorBoard", "tensorboard --logdir=logs"),
        ("指定端口", "tensorboard --logdir=logs --port=6007"),
        ("指定主机", "tensorboard --logdir=logs --host=0.0.0.0"),
        ("比较多个实验", "tensorboard --logdir=name1:path1,name2:path2"),
        ("重新加载数据", "tensorboard --logdir=logs --reload_interval=1"),
        ("启用调试模式", "tensorboard --logdir=logs --debugger_port=6064"),
    ]
    
    for desc, cmd in commands:
        print(f"  {desc}:")
        print(f"    {cmd}")
        print()

def main():
    """主函数"""
    print("🧬 神农框架TensorBoard集成演示")
    print("=" * 60)
    print("神农框架提供了比ChemProp更强大的TensorBoard集成功能")
    print("包括化学导向的注意力监控和分子可视化")
    print()
    
    # 显示功能选项
    options = [
        "1. 启动TensorBoard服务器",
        "2. 显示TensorBoard命令",
        "3. 创建演示日志",
        "4. 退出"
    ]
    
    for option in options:
        print(option)
    
    try:
        choice = input("\n请选择功能 (1-4): ")
        
        if choice == "1":
            demonstrate_tensorboard_features()
        elif choice == "2":
            show_tensorboard_commands()
        elif choice == "3":
            demo_log_dir = Path("logs/demo_experiment")
            demo_log_dir.mkdir(parents=True, exist_ok=True)
            create_demo_logs(demo_log_dir)
            print(f"\n✅ 演示日志已创建到: {demo_log_dir}")
            print(f"   运行命令查看: tensorboard --logdir={demo_log_dir}")
        elif choice == "4":
            print("👋 再见!")
        else:
            print("❌ 无效选择")
            
    except KeyboardInterrupt:
        print("\n👋 再见!")
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    main()
