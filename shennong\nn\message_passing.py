# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-27
# 描述: 神农框架消息传递层

"""
神农框架消息传递层

实现抗菌化合物特异性的消息传递神经网络层。
"""

from typing import Dict, Any, Optional
import torch
import torch.nn as nn
import logging

logger = logging.getLogger(__name__)


class AntibacterialMessagePassing(nn.Module):
    """
    抗菌化合物特异性消息传递层
    
    基于Chemprop的消息传递机制，增加抗菌相关特征。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化消息传递层
        
        Args:
            config: 配置字典
        """
        super().__init__()
        
        self.hidden_size = config.get('hidden_size', 300)
        self.depth = config.get('depth', 3)
        self.dropout = config.get('dropout', 0.0)
        
        # 简化的消息传递层
        self.message_layers = nn.ModuleList([
            nn.Linear(self.hidden_size, self.hidden_size)
            for _ in range(self.depth)
        ])
        
        self.activation = nn.ReLU()
        self.dropout_layer = nn.Dropout(self.dropout)
        
        logger.info(f"初始化抗菌消息传递层: 隐藏维度={self.hidden_size}, 深度={self.depth}")
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: 输入特征
            
        Returns:
            输出特征
        """
        for layer in self.message_layers:
            x = layer(x)
            x = self.activation(x)
            x = self.dropout_layer(x)
        
        return x


class EnhancedBondMessagePassing(nn.Module):
    """
    增强的键消息传递层
    
    专门处理化学键的消息传递。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化键消息传递层
        
        Args:
            config: 配置字典
        """
        super().__init__()
        
        self.bond_dim = config.get('bond_dim', 14)
        self.hidden_size = config.get('hidden_size', 300)
        
        # 键特征编码器
        self.bond_encoder = nn.Sequential(
            nn.Linear(self.bond_dim, self.hidden_size),
            nn.ReLU(),
            nn.Linear(self.hidden_size, self.hidden_size)
        )
        
        logger.info(f"初始化增强键消息传递层: 键维度={self.bond_dim}")
    
    def forward(self, bond_features: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            bond_features: 键特征
            
        Returns:
            编码后的键特征
        """
        return self.bond_encoder(bond_features)
