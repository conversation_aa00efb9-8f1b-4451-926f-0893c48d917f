# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-27
# 描述: 神农框架配置管理

"""
神农框架配置管理

提供配置文件的加载、保存和验证功能。
"""

from typing import Dict, Any, Optional, Union
import yaml
import json
from pathlib import Path
import logging

# 尝试导入TOML支持
try:
    import toml
    TOML_AVAILABLE = True
except ImportError:
    TOML_AVAILABLE = False

logger = logging.getLogger(__name__)


class ShennongConfig:
    """
    神农框架配置类

    管理模型、训练、数据等各种配置。
    """

    def __init__(self, config_dict: Optional[Dict[str, Any]] = None):
        """
        初始化配置

        Args:
            config_dict: 配置字典
        """
        self.config = config_dict or {}
        self._validate_config()

    def _validate_config(self):
        """验证配置的有效性"""
        # 基本配置验证
        required_sections = ['model', 'training', 'data']
        for section in required_sections:
            if section not in self.config:
                self.config[section] = {}

        # 设置默认值
        self._set_defaults()

    def _set_defaults(self):
        """设置默认配置值"""
        # 模型默认配置
        model_defaults = {
            'graph_config': {
                'hidden_size': 300,
                'depth': 3,
                'output_dim': 300
            },
            'expert_config': {
                'descriptor_dim': 1613,
                'output_dim': 128,
                'adaptive_architecture': True
            },
            'fusion_config': {
                'output_dim': 256,
                'attention_heads': 4,
                'mechanism_aware': True
            },
            'task_config': {
                'num_tasks': 1,
                'task_type': 'regression'
            }
        }

        # 训练默认配置
        training_defaults = {
            'learning_rate': 1e-3,
            'weight_decay': 1e-4,
            'batch_size': 64,
            'max_epochs': 100,
            'patience': 20
        }

        # 数据默认配置
        data_defaults = {
            'splitting': {
                'method': 'scaffold',
                'train_ratio': 0.8,
                'val_ratio': 0.1,
                'test_ratio': 0.1
            },
            'dataloader': {
                'num_workers': 4,
                'pin_memory': True
            }
        }

        # 应用默认值
        for section, defaults in [
            ('model', model_defaults),
            ('training', training_defaults),
            ('data', data_defaults)
        ]:
            for key, value in defaults.items():
                if key not in self.config[section]:
                    self.config[section][key] = value

    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值

        Args:
            key: 配置键，支持点分隔的嵌套键
            default: 默认值

        Returns:
            配置值
        """
        keys = key.split('.')
        value = self.config

        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default

    def set(self, key: str, value: Any):
        """
        设置配置值

        Args:
            key: 配置键，支持点分隔的嵌套键
            value: 配置值
        """
        keys = key.split('.')
        config = self.config

        # 创建嵌套字典
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]

        config[keys[-1]] = value

    def update(self, other_config: Union[Dict[str, Any], 'ShennongConfig']):
        """
        更新配置

        Args:
            other_config: 其他配置
        """
        if isinstance(other_config, ShennongConfig):
            other_config = other_config.config

        self._deep_update(self.config, other_config)
        self._validate_config()

    def _deep_update(self, base_dict: Dict[str, Any], update_dict: Dict[str, Any]):
        """深度更新字典"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return self.config.copy()

    def save(self, path: Union[str, Path]):
        """
        保存配置到文件

        Args:
            path: 文件路径
        """
        path = Path(path)

        if path.suffix.lower() == '.json':
            with open(path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
        elif path.suffix.lower() == '.toml' and TOML_AVAILABLE:
            with open(path, 'w', encoding='utf-8') as f:
                toml.dump(self.config, f)
        else:
            # 默认保存为YAML
            with open(path, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True)

        logger.info(f"配置已保存到: {path}")

    @classmethod
    def load(cls, path: Union[str, Path]) -> 'ShennongConfig':
        """
        从文件加载配置

        Args:
            path: 文件路径

        Returns:
            配置实例
        """
        path = Path(path)

        if not path.exists():
            raise FileNotFoundError(f"配置文件不存在: {path}")

        if path.suffix.lower() == '.json':
            with open(path, 'r', encoding='utf-8') as f:
                config_dict = json.load(f)
        elif path.suffix.lower() == '.toml' and TOML_AVAILABLE:
            with open(path, 'r', encoding='utf-8') as f:
                config_dict = toml.load(f)
        else:
            # 默认为YAML
            with open(path, 'r', encoding='utf-8') as f:
                config_dict = yaml.safe_load(f)

        logger.info(f"配置已从 {path} 加载")
        return cls(config_dict)

    @staticmethod
    def merge_configs(base_config: Dict[str, Any], override_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        合并两个配置字典

        Args:
            base_config: 基础配置
            override_config: 覆盖配置

        Returns:
            合并后的配置
        """
        return merge_configs(base_config, override_config)

    @staticmethod
    def load_config(path: Union[str, Path]) -> 'ShennongConfig':
        """
        加载配置文件的静态方法

        Args:
            path: 配置文件路径

        Returns:
            配置实例
        """
        return ShennongConfig.load(path)


def load_config(path: Union[str, Path]) -> ShennongConfig:
    """
    加载配置文件

    Args:
        path: 配置文件路径

    Returns:
        配置实例
    """
    return ShennongConfig.load(path)


def save_config(config: Union[ShennongConfig, Dict[str, Any]], path: Union[str, Path]):
    """
    保存配置到文件

    Args:
        config: 配置对象或字典
        path: 保存路径
    """
    if isinstance(config, dict):
        config = ShennongConfig(config)

    config.save(path)


def merge_configs(base_config: Dict[str, Any], override_config: Dict[str, Any]) -> Dict[str, Any]:
    """
    合并两个配置字典

    Args:
        base_config: 基础配置
        override_config: 覆盖配置

    Returns:
        合并后的配置
    """
    merged = base_config.copy()

    def _deep_merge(base: Dict[str, Any], override: Dict[str, Any]):
        for key, value in override.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                _deep_merge(base[key], value)
            else:
                base[key] = value

    _deep_merge(merged, override_config)
    return merged


def create_default_config() -> ShennongConfig:
    """
    创建默认配置

    Returns:
        默认配置实例
    """
    return ShennongConfig()


def merge_configs(*configs: Union[ShennongConfig, Dict[str, Any]]) -> ShennongConfig:
    """
    合并多个配置

    Args:
        *configs: 配置列表

    Returns:
        合并后的配置
    """
    merged = ShennongConfig()

    for config in configs:
        merged.update(config)

    return merged
