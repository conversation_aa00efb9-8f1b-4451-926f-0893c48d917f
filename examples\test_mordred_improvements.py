# 作者: ZK
# 邮箱: <EMAIL>
# 日期: 2025-06-27
# 描述: 测试Mordred描述符改进

"""
测试Mordred描述符改进

验证新的Mordred描述符管理器和特征化器的功能。
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

import numpy as np
import logging
from typing import List, Dict, Any

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_descriptor_manager():
    """测试描述符管理器"""
    print("\n" + "="*60)
    print("🔬 测试Mordred描述符管理器")
    print("="*60)

    try:
        from shennong.featurizers.molecule import MordredDescriptorManager

        # 创建管理器实例
        manager = MordredDescriptorManager()

        print(f"Mordred可用性: {'✅' if manager.available else '❌'}")
        if not manager.available:
            print("❌ Mordred不可用，跳过测试")
            return False

        print(f"Mordred版本: {manager.version}")

        # 获取描述符数量
        count_2d = manager.get_descriptor_count(ignore_3D=True)
        count_3d = manager.get_descriptor_count(ignore_3D=False)

        print(f"2D描述符数量: {count_2d}")
        print(f"3D描述符数量: {count_3d}")
        print(f"硬编码1613是否准确: {'✅' if count_2d == 1613 else '❌'}")

        if count_2d != 1613:
            print(f"⚠️ 建议更新配置中的descriptor_dim从1613改为{count_2d}")

        # 测试计算器创建
        calc_all = manager.create_calculator('all', ignore_3D=True)
        calc_safe = manager.create_calculator('safe', ignore_3D=True)

        print(f"全部描述符计算器: {'✅' if calc_all else '❌'}")
        print(f"安全描述符计算器: {'✅' if calc_safe else '❌'}")

        if calc_all and calc_safe:
            print(f"全部描述符数量: {len(calc_all.descriptors)}")
            print(f"安全描述符数量: {len(calc_safe.descriptors)}")

        # 获取版本信息
        version_info = manager.get_version_info()
        print(f"失败的描述符类: {len(version_info['failed_descriptors'])}")

        return True

    except Exception as e:
        print(f"❌ 描述符管理器测试失败: {e}")
        return False


def test_enhanced_featurizer():
    """测试增强的特征化器"""
    print("\n" + "="*60)
    print("🧪 测试增强的Mordred特征化器")
    print("="*60)

    try:
        from shennong.featurizers.molecule import MordredFeaturizer
        from rdkit import Chem

        # 测试分子
        test_molecules = [
            ("水", "O"),
            ("乙醇", "CCO"),
            ("苯", "c1ccccc1"),
            ("阿司匹林", "CC(=O)OC1=CC=CC=C1C(=O)O"),
            ("无效SMILES", "INVALID"),
        ]

        # 创建特征化器
        config = {
            'ignore_3D': True,
            'selected_descriptors': 'all',
            'handle_errors': 'warn',
            'use_dynamic_detection': True
        }

        featurizer = MordredFeaturizer(config)

        print(f"特征化器初始化: ✅")
        print(f"特征维度: {featurizer.feature_dim}")

        # 获取描述符信息
        desc_info = featurizer.get_descriptor_info()
        print(f"管理器版本: {desc_info['manager_info']['version']}")
        print(f"实际2D描述符: {desc_info['manager_info']['total_2d']}")
        print(f"实际3D描述符: {desc_info['manager_info']['total_3d']}")

        # 测试特征计算
        print(f"\n测试特征计算:")
        for name, smiles in test_molecules:
            try:
                mol = Chem.MolFromSmiles(smiles) if smiles != "INVALID" else None

                # 计算特征（带详细信息）
                features, failed_info = featurizer.featurize(mol, return_failed=True)

                print(f"  {name:12} | 维度: {features.shape[0]:4d} | "
                      f"成功率: {failed_info.get('success_rate', 0):.2%} | "
                      f"失败: {failed_info.get('failed_count', 0):3d}")

                # 检查特征质量
                if len(features) > 0:
                    nan_count = np.isnan(features).sum()
                    inf_count = np.isinf(features).sum()
                    zero_count = (features == 0).sum()

                    if nan_count > 0 or inf_count > 0:
                        print(f"    ⚠️ 质量问题: NaN={nan_count}, Inf={inf_count}")

                    if zero_count > len(features) * 0.8:
                        print(f"    ⚠️ 零值过多: {zero_count}/{len(features)} ({zero_count/len(features):.1%})")

            except Exception as e:
                print(f"  {name:12} | ❌ 错误: {e}")

        return True

    except Exception as e:
        print(f"❌ 增强特征化器测试失败: {e}")
        return False


def test_descriptor_validation():
    """测试描述符验证工具"""
    print("\n" + "="*60)
    print("🔍 测试描述符验证工具")
    print("="*60)

    try:
        from shennong.utils.descriptor_validator import run_descriptor_validation

        # 运行验证
        report = run_descriptor_validation("test_validation_reports")

        # 检查报告内容
        if 'environment_diagnosis' in report:
            env = report['environment_diagnosis']
            print(f"环境诊断: {'✅' if env['overall_status'] == 'healthy' else '❌'}")

        if 'descriptor_count_validation' in report:
            count_val = report['descriptor_count_validation']
            print(f"数量验证: {'✅' if count_val.get('matches_expected', False) else '❌'}")

        if 'calculation_validation' in report:
            calc_val = report['calculation_validation']
            if 'overall_statistics' in calc_val:
                success_rate = calc_val['overall_statistics'].get('overall_success_rate', 0)
                print(f"计算验证: {'✅' if success_rate > 0.8 else '❌'} ({success_rate:.2%})")

        print(f"建议数量: {len(report.get('recommendations', []))}")

        return True

    except Exception as e:
        print(f"❌ 描述符验证测试失败: {e}")
        return False


def test_integration_with_shennong():
    """测试与神农框架的集成"""
    print("\n" + "="*60)
    print("🔗 测试与神农框架的集成")
    print("="*60)

    try:
        from shennong.data.datapoints import ShennongDatapoint
        from shennong.data.datasets import ShennongDataset

        # 创建测试数据
        test_data = [
            ("CCO", 2.5, "cell_wall_synthesis"),
            ("CC(=O)O", 8.2, "cell_wall_synthesis"),
            ("c1ccccc1", 15.6, "protein_synthesis"),
        ]

        datapoints = []
        for smiles, activity, mechanism in test_data:
            try:
                datapoint = ShennongDatapoint(
                    smiles=smiles,
                    targets={'activity': activity}
                )
                datapoints.append(datapoint)
                print(f"  ✅ 数据点创建成功: {smiles}")
            except Exception as e:
                print(f"  ❌ 数据点创建失败: {smiles} - {e}")

        if datapoints:
            # 创建数据集
            dataset = ShennongDataset(datapoints)
            print(f"✅ 数据集创建成功: {len(dataset)} 个样本")

            # 检查描述符维度
            if hasattr(dataset, 'descriptor_dim'):
                print(f"数据集描述符维度: {dataset.descriptor_dim}")

            # 测试数据加载
            if len(dataset) > 0:
                sample = dataset[0]
                if 'expert_features' in sample:
                    expert_dim = sample['expert_features'].shape[0] if sample['expert_features'] is not None else 0
                    print(f"专家特征维度: {expert_dim}")

                    # 检查维度一致性
                    if hasattr(dataset, 'descriptor_dim') and expert_dim != dataset.descriptor_dim:
                        print(f"⚠️ 维度不一致: 数据集={dataset.descriptor_dim}, 实际={expert_dim}")

        return True

    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🧬 神农框架Mordred描述符改进测试")
    print("="*80)

    tests = [
        ("描述符管理器", test_descriptor_manager),
        ("增强特征化器", test_enhanced_featurizer),
        ("描述符验证", test_descriptor_validation),
        ("框架集成", test_integration_with_shennong),
    ]

    results = {}

    for test_name, test_func in tests:
        try:
            print(f"\n🔬 运行测试: {test_name}")
            success = test_func()
            results[test_name] = success
            print(f"{'✅ 通过' if success else '❌ 失败'}")
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results[test_name] = False

    # 总结
    print("\n" + "="*80)
    print("📊 测试总结")
    print("="*80)

    passed = sum(results.values())
    total = len(results)

    for test_name, success in results.items():
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name:20} | {status}")

    print(f"\n总体结果: {passed}/{total} 测试通过 ({passed/total:.1%})")

    if passed == total:
        print("🎉 所有测试通过！Mordred描述符改进成功！")
    else:
        print("⚠️ 部分测试失败，请检查相关问题")

    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
